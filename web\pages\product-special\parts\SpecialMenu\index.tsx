import React, { useContext } from "react";
import { IContext } from "ssr-types-react";
import { TmMenuBox } from "@/components/TmMenuBox";
import { ItemType } from "antd/lib/menu/hooks/useItems";
import { useStoreContext } from "ssr-common-utils";

interface SProps {
  width?: string;
}

export default function SpecialMenu(props: SProps) {
  const { width = "200px" } = props;
  const { state } = useContext<IContext>(useStoreContext());

  const navItems: ItemType[] = state?.topicCategories?.map(item => {
    return {
      label: <a href={`/product-special/${item.categoryCode}`}>{item.title}</a>,
      key: `${item.categoryCode}`,
    };
  });

  return <TmMenuBox style={{ width }} navItems={navItems} />;
}
