import { Controller, Get, Inject, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IProductCoa } from "@/service/ghtech/coa.service";
import { IProductMsds } from "@/service/ghtech/msds.service";
import { ICOAParams, ICOARelationParams, IMSDSParams, IMSDSRelationParams } from "~/typings/data/ghtech/ghtech";

@Controller("/api/ghtech")
export class GhtechController extends BaseController {
  @Inject("CoaService")
  coaService: IProductCoa;

  @Inject("MsdsService")
  msdsService: IProductMsds;

  /**
   * @desc 产品coa列表
   */
  @Get("/coa")
  async getProductCoa(@Query() params: ICOAParams) {
    const res = await this.coaService.getCoaByProductName(params);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 产品msds报告
   */
  @Get("/msds")
  async getProductMsds(@Query() params: IMSDSParams) {
    const res = await this.msdsService.getMsdsByProductName(params);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 根据cas+productName查询对应的msds
   */
  @Get("/msds-relation")
  async getProductMSDSByRelation(@Query() params: IMSDSRelationParams) {
    const res = await this.msdsService.getProductMSDSByRelation(params);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 产品详情-COA获取 - 通过产品名称与批次完全匹配COA
   */
  @Get("/coa-relation")
  async getProductCOAByRelation(@Query() params: ICOARelationParams) {
    const res = await this.coaService.getProductCOAByRelation(params);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

}
