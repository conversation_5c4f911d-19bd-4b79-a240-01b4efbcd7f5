import { Controller, Get, Inject, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { IOrderService } from "@/service/order/order.service";
import { IWalletService } from "@/service/member/wallet.service";
import { MidwayHttpError } from "@midwayjs/core";
import { IMemberService } from "@/service/member/member.service";

@Controller("/pay", { middleware: [AuthenticationMiddleware] })
export class PayController extends BaseController {
  @Inject("OrderService")
  orderService: IOrderService;

  @Inject("WalletService")
  walletService: IWalletService;

  @Inject("MemberService")
  memberService: IMemberService;

  /**
   * 订单确定页
   */
  @Get("/confirm")
  async confirm(@Query("way") way?: string): Promise<void> {
    const { ctx } = this;
    ctx.way = way;
    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  /**
   * 支付付款页
   */
  @Get("/payment")
  async payment(@Query("sn") sn: string): Promise<any> {
    const { ctx } = this;
    if (!sn) {
      return ctx.redirect("/404");
    }
    const result = await this.orderService.show(this.getMemberId(), sn);
    ctx.order = result.data;
    if (ctx.order?.orderState !== 20) {
      throw new MidwayHttpError("当前订单：" + sn + "非待支付状态，请联系您的业务处理，请不要进行支付操作！", 403, "BadRequest");
    }
    const walletResult = await this.walletService.getWallet(this.getMemberId());
    ctx.wallet = walletResult.data;
    // 开户信息
    ctx.bankAccount = {
      bankName: "中国银行股份有限公司汕头科技支行",
      accountName: "广东光华科技股份有限公司",
      accountNo: "6743 5775 5281",
    };

    // 返回客户联系人信息(联系邮箱)
    const memberContactRes = await this.memberService.getMemberConsultant(this.getMemberId());
    ctx.memberContact = memberContactRes?.data;

    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
