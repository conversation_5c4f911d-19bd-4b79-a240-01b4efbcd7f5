.wrapper {
  :global {
    .image-big-box {
      text-align: center;
      background-color: #fff;
      // overflow: hidden;
      // padding: 18px;
      img{
        border-radius: 7px;
        border: 1px solid #eaeaea;
      }
    }
    .ant-carousel {
      .image-list-box {
        display: inline-flex !important;
        justify-content: flex-start;
        flex-wrap: wrap;
        height: 90px;
        overflow-y: hidden;
        gap: 13px;
        margin-top: 15px;
        transition: all 0.5s ease;
        &-item {
          width: 90px;
          height: 90px;
          border: 1px solid #eaeaea;
          cursor: pointer;
          display: inline-block;
          border-radius: 3px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .image-item-active {
          border: 1px solid red;
        }
      }
    }
    // 滑动按钮
    .nav-btn-wrapper {
      width: 100%;
      position: relative;
      bottom: 76px;
      position: relative;
      .nav-icon {
        position: absolute;
        top: 0;
        width: 16px;
        height: 73px;
        background: rgba(186, 186, 186, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        &:hover {
          background: rgba(186, 186, 186, 0.7);
        }
        &:active {
          background: rgba(186, 186, 186, 0.5);
        }
        &.list-to-left {
          left: -20px;
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
        }
        &.list-to-right {
          right: -20px;
          border-top-right-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }
    }
  }
}
