import { Rule, RuleType } from "@midwayjs/validate";

export class TradeCashierPayDto {
  @Rule(RuleType.string().required())
  sn: string;

  @Rule(RuleType.string().default("ORDER"))
  /**
   * 交易类型：TRADE-转账,ORDER-订单,RECHARGE-充值
   */
  orderType: string = "ORDER";

  @Rule(RuleType.string())
  /**
   * H5-移动端;PC-PC端;WECHAT_MP-小程序端;APP-移动应用端;UNKNOWN-未知
   */
  clientType: string = "PC";

  @Rule(RuleType.string())
  paymentClient: string;

  /**
   * 会员ID
   */
  mid: string = "default";

  /**
   * 支付后返回地址
   */
  @Rule(RuleType.string())
  returnUrl: string = "default";
}
