FROM node:lts-alpine3.15 AS node_lts_build

MAINTAINER tmtc

ENV TZ=Asia/Shanghai \
    APP_PATH=/app

COPY ./sources.list.stretch /etc/apt/sources.list

#设置当前路径，也就是下面所有命令的执行都是在这个路径
WORKDIR $APP_PATH

# 拷贝package.json文件到工作目录
# !!重要：package.json需要单独添加。
# Docker在构建镜像的时候，是一层一层构建的，仅当这一层有变化时，重新构建对应的层。
# 如果package.json和源代码一起添加到镜像，则每次修改源码都需要重新安装npm模块，这样木有必要。
# 所以，正确的顺序是: 添加package.json；安装npm模块；添加源代码。

COPY package.json ./
COPY yarn.lock ./

# 安装依赖包

RUN npm config -g set registry "https://registry.npmmirror.com/"  && \
    npm install yarn -g --force && yarn config set registry "https://registry.npmmirror.com/" && \
    yarn install --network-timeout 1000000

#拷贝当前目录下面的指定文件到目标目录下
COPY . .

# 构建
RUN yarn build

# can use

#RUN npm config set registry https://registry.npm.taobao.org && \
#    npm install && npm i pm2 -g && npm run build

# custom log
RUN echo "docker-compose ghmall portal container build success!"

#对外暴露的端口，此处可以省略，在docker-compose.yaml处理
#EXPOSE 3001

# =========================== 改造使用 docker multistage ===========================
FROM node:16-alpine3.16

ARG WORK_PATH=/app

WORKDIR $WORK_PATH

ENV TZ="Asia/Shanghai"

RUN apk add --no-cache tzdata

COPY --from=node_lts_build $WORK_PATH/package.json ./
COPY --from=node_lts_build $WORK_PATH/yarn.lock ./

RUN npm config -g set registry "https://registry.npmmirror.com/"  && \
        npm install yarn -g --force && npm install pm2 -g --force &&  \
        yarn config set registry "https://registry.npmmirror.com/" && \
        yarn install --production --network-timeout 1000000

COPY --from=node_lts_build $WORK_PATH/dist ./dist
COPY --from=node_lts_build $WORK_PATH/public ./public
COPY --from=node_lts_build $WORK_PATH/build ./build
COPY --from=node_lts_build $WORK_PATH/bootstrap.js ./
COPY --from=node_lts_build $WORK_PATH/pm2.prod.config.js ./pm2.config.js

RUN echo "[multistage]docker-compose ghmall-portal container build success!"

# pm2-runtime 启动
CMD ["sh", "-c", "pm2-runtime start pm2.config.js && pm2 list"]
