import AbstractStockStrategy from "@/service/impl/proxy-brand-stock/kit/abstract.stock.strategy";
import { httpError } from "@midwayjs/core";

export class MerckStrategy extends AbstractStockStrategy {
  private signName: string;
  private token: string;
  private readonly crypto = require("crypto");

  private static readonly ACCOUNT_USERNAME: string = "JINHUADA";
  private static readonly ACCOUNT_STEP: string = "1";
  private static readonly ACCOUNT_SECRET_KEY = "1F555E24-D5BA-4518-9A38-6147EEA92F67";
  private static readonly ACCOUNT_REQUEST_TOKEN_URL = "https://lsdms.merck.cn/mservice.aspx?action=getToken";
  /* https://mercklsdmp-test.sigmaaldrich.cn/mservice.aspx?action=queryStock */
  private static readonly ACCOUNT_REQUEST_STOCK_URL = "https://lsdms.merck.cn/mservice.aspx?action=queryStock";

  async query(sku: string, quantity: number | undefined): Promise<any> {
    return await this.getProductInfo(sku, quantity);
  }

  private async getProductInfo(sku, quantity): Promise<any> {
    await this.getMerckToken();
    const quantity1 = quantity ?? "1";
    const products = [
      {
        ProductCode: sku,
        Quantity: quantity1,
      },
    ];
    const requestData = {
      token: this.token,
      signData: this.signName,
      productInfo: products,
    };
    const response = await this.post(MerckStrategy.ACCOUNT_REQUEST_STOCK_URL, requestData);
    const res = response.data;
    if (res.success) {
      const materials = res.result;
      console.log("this.materials result is:", materials);
      if (!materials[0].ErrorMessage) {
        return `有现货, ${quantity1}EA 货期25天`;
      } else {
        return `无现货, ${quantity1}EA 货期75天`;
      }
    } else {
      throw new httpError.BadRequestError(res?.resultMessage ?? "获取产品库存出错了！");
    }
  }

  private async getMerckToken(): Promise<void> {
    await this.generateSignName();
    const requestData = {
      Username: "JINHUADA",
      Password: "147258369ww@@",
    };
    const response = await this.post(MerckStrategy.ACCOUNT_REQUEST_TOKEN_URL, requestData);
    this.token = response?.data?.token ?? "";
  }

  /* 签名数据：e9a4621e36b68f685ada5c308cd0cee1 */
  private async generateSignName(): Promise<void> {
    const tmp: string = `${MerckStrategy.ACCOUNT_USERNAME}${MerckStrategy.ACCOUNT_STEP}`;
    const md5 = this.crypto.createHash("md5");
    const md5str = `${md5.update(tmp).digest("hex")}${MerckStrategy.ACCOUNT_SECRET_KEY}`;
    this.signName = this.crypto.createHash("md5").update(md5str).digest("hex");
  }
}
