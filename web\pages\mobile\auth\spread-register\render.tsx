import React, { useContext } from "react";
import { Image } from "antd-mobile";
import styles from "./index.module.less";
import { SProps } from "ssr";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import logo2 from "@@img/logo.svg";
import { ISalesmanSpreadInfoType } from "@/typings/member.interface";
import { useOss } from "@/hooks/useOss";
import RegisterCnpt from "../component/register";

export default function SpreadRegisterMobile(props?: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const salesmanSpreadInfo: ISalesmanSpreadInfoType = state.spreadInfo;

  const ossService = useOss();
  // 前往主页
  const handleGo2homepage = () => {
    window.location.href = "/";
  };
  /** ======================================== method end ======================================= */
  return (
    <>
      <div className={styles.wrapper}>
        <div className="auth-page">
          <div className="auth-shading">
            <div className="mall-logo acea-row row-center-wrapper">
              <Image
                placeholder={<img src={logo2} alt="" />}
                fallback={<img src={logo2} alt="" />}
                src={salesmanSpreadInfo?.spreadAvatar ? ossService.generateOssFullFilepath(salesmanSpreadInfo?.spreadAvatar) : logo2}
                alt=""
                onClick={handleGo2homepage}
              />
            </div>
          </div>
          <RegisterCnpt customerCategory={state?.customerCategory} salesmanSpreadInfo={salesmanSpreadInfo} />
        </div>
      </div>
    </>
  );
}
