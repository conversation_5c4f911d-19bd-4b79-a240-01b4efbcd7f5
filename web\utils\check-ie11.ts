export function getIEVersion() {
  const userAgent = navigator.userAgent; // 取得浏览器的userAgent字符串
  const isIE = userAgent.includes("compatible") && userAgent.includes("MSIE"); // 判断是否是IE11以下浏览器
  const isEdge = userAgent.includes("Edge") && !isIE; // 判断是否是Edge浏览器
  const isIE11 = userAgent.includes("Trident") && userAgent.includes("rv:11.0");
  if (isIE) {
    const reIE = new RegExp("MSIE (\\d+\\.\\d+);");
    reIE.test(userAgent);
    const IEVersion = parseFloat(RegExp["$1"]);
    if (IEVersion === 7) {
      return 7;
    } else if (IEVersion === 8) {
      return 8;
    } else if (IEVersion === 9) {
      return 9;
    } else if (IEVersion === 10) {
      return 10;
    } else {
      return 6; // IE版本<=7
    }
  } else if (isEdge) {
    return "edge"; // Edge
  } else if (isIE11) {
    return 11; // IE11
  } else {
    return -1; // 不是IE浏览器
  }
}

/** IE11 以下浏览器升级提醒 */
export function checkIE11Upgrade() {
  const versions = getIEVersion();
  if (Number(versions) !== -1 || String(versions) === "edge") {
    alert("检测到你正在使用 IE11 以下版本的浏览器，为了您的用户体验，请安装 Chrome 或 Edge 浏览器！！！");
  }
}
