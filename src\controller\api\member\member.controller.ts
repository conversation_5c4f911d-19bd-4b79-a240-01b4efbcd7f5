import { Body, Controller, Get, Inject, Post, Put, File } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IAuthService } from "@/service/auth.service";
import { Validate } from "@midwayjs/validate";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { ReBindEmailVo, ReBindPhoneVo, updateMemberVo, updatePasswordVo } from "~/typings/data/member/member";
import { IMemberService } from "@/service/member/member.service";
import { IUploadParams } from "~/typings/data/file-remote";
import { PlatformSmsVerifyDto } from "@/dto/platform-sms-verify.dto";
import { httpError } from "@midwayjs/core";
import { ISmsService } from "@/service/platform/sms.service";

@Controller("/api/ucenter", { middleware: [AuthenticationMiddleware] })
export class MemberController extends BaseController {
  @Inject("AuthService")
  authService: IAuthService;

  @Inject("MemberService")
  memberService: IMemberService;

  @Inject("PlatformSmsService")
  smsService: ISmsService;

  /**
   * @desc 获取登录信息,用于csr渲染
   */
  @Get("/auth-info")
  async getAuthInfoData() {
    const { ctx } = this;
    const resData = {
      userData: ctx.session?.userData,
      userLoginState: ctx?.session?.userLoginState ?? false,
    };
    return this.ctx.getResponseInstance(this.ctx).setResponseData(resData).send();
  }

  /**
   * 更新会员
   *
   * @param updateVo 会员dto对象
   */
  @Put("/update-member")
  @Validate()
  async updateMember(@Body() updateVo: updateMemberVo) {
    const { ctx } = this;
    const res = await this.memberService.updateMember(this.getMemberId(), updateVo);
    const { data } = await this.memberService.getInfoByMemberId(this.getMemberId());
    // 更新session
    await this.authService.saveOrUpdateSession(ctx, data);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * 更新会员密码：旧密码方式更改
   *
   * @param updateVo 会员dto对象
   */
  @Put("/update-password")
  @Validate()
  async updateMemberPassword(@Body() updateVo: updatePasswordVo) {
    const { ctx } = this;
    const res = await this.memberService.updatePassword(this.getMemberId(), updateVo);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * 更新头像
   *
   * @param file /
   */
  @Post("/update-avatar")
  async updateMemberAvatar(@File("file") file) {
    const { ctx } = this;
    if (!file) {
      return ctx.getResponseInstance(ctx).sendFail("上传文件不能为空！", 400);
    }
    const uploadData: IUploadParams = {
      filed: "upload_file",
      uploadFilepath: file.data,
    };

    const res = await this.memberService.updateMemberAvatar(this.getMemberId(), uploadData);
    // 更新session
    const { data } = await this.memberService.getInfoByMemberId(this.getMemberId());
    await this.authService.saveOrUpdateSession(ctx, data);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * 获取客户资料详情
   */
  @Get("/show")
  async getInfoByMemberId() {
    const { ctx } = this;
    const res = await this.memberService.getInfoByMemberId(this.getMemberId());
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * 换绑或绑定邮箱
   *
   * @param bindVo 变更vo对象
   */
  @Put("/rebind-email")
  @Validate()
  async rebindEmail(@Body() bindVo: ReBindEmailVo) {
    const { ctx } = this;
    const res = await this.memberService.rebindEmail(this.getMemberId(), bindVo);
    await this.updateSession(ctx, res);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * 换绑或绑定手机号
   *
   * @param bindVo 变更vo对象
   */
  @Put("/rebind-phone")
  @Validate()
  async rebindPhone(@Body() bindVo: ReBindPhoneVo) {
    // 验证短信验证码是否通过
    const smsVerifyDto: PlatformSmsVerifyDto = new PlatformSmsVerifyDto();
    smsVerifyDto.phone = bindVo.newPhone;
    smsVerifyDto.smsCode = bindVo.smsCode;
    const result1 = await this.smsService.checkVerifyCode(smsVerifyDto);
    if (!result1.data.verify) {
      throw new httpError.BadRequestError({ message: "短信验证码不正确！" });
    }
    const { ctx } = this;
    const res = await this.memberService.rebindPhone(this.getMemberId(), bindVo);
    await this.updateSession(ctx, res);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  private async updateSession(ctx: any, res: any) {
    const mid = this.getMemberId();
    const httpCode = res?.statusCode.toString() ?? "";
    if (mid && httpCode.startsWith("20")) {
      // 更新session
      const { data } = await this.memberService.getInfoByMemberId(mid);
      await this.authService.saveOrUpdateSession(ctx, data);
    }
  }
}
