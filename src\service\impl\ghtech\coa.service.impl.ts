import { Provide, Scope, <PERSON>opeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ICOAParams, ICOARelationParams } from "~/typings/data/ghtech/ghtech";
import { IProductCoa } from "@/service/ghtech/coa.service";

@Provide("CoaService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class CoaServiceImpl extends BaseService implements IProductCoa {
  /** 产品名称获取msds */
  async getCoaByProductName(params: ICOAParams): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/ghtech/products/coa`, params), "获取数据出错了，请稍后再试！");
  }

  /** 产品详情-COA获取 */
  async getProductCOAByRelation(params: ICOARelationParams): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/ghtech/products/coa/relation-coa`, params), "获取数据出错了，请稍后再试！");
  }
}
