import collectionCrud from "@/apis/product/collection";
import { ICollectionCreate } from "~/typings/data/product/collection";
import { useLoading } from "@/hooks/useLoading";
import { message, notification } from "antd";

// 收藏hook
export const useProductCollection = () => {
  const loadingHooks = useLoading();

  // 新增
  const create = async (resource: ICollectionCreate) => {
    loadingHooks.showLoading();
    const [err, res] = await collectionCrud
      .create(resource)
      .then(res => [null, res])
      .catch(err => [err, null]);
    loadingHooks.hideLoading();
    if (err) {
      notification.error({ message: err.data.message || "收藏失败，请重试" });
      return false;
    }
    message.success(res.message || "收藏成功");
    return true;
  };

  // 移除
  const deleteBySkuId = async (skuId: number) => {
    loadingHooks.showLoading();
    const [err, res] = await collectionCrud
      .deleteBySkuId(skuId)
      .then(res => [null, res])
      .catch(err => [err, null]);
    loadingHooks.hideLoading();
    if (err) {
      notification.error({ message: err.data.message || "取消失败，请重试" });
      return false;
    }
    message.success(res.message || "已取消收藏");
    return true;
  };

  // 是否收藏产品
  const isCollection = async (sku_id: number) => {
    const [err, res] = await collectionCrud
      .isCollection(sku_id)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err) {
      return res.data.is_collected;
    }
  };

  const deleteAll = async () => {
    loadingHooks.showLoading();
    const [err, res] = await collectionCrud
      .deleteAll()
      .then(res => [null, res])
      .catch(err => [err, null]);
    loadingHooks.hideLoading();
    if (err) {
      notification.error({ message: err.data.message || "清空失败，请重试" });
      return false;
    }
    message.success(res.message || "已清空成功");
    return true;
  };

  return {
    create,
    isCollection,
    deleteBySkuId,
    deleteAll,
  };
};
