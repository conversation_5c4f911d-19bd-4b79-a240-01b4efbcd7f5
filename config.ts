import type { UserConfig } from 'ssr-types'
import PkgConfig from 'vite-plugin-package-config'
import OptimizationPersist from 'vite-plugin-optimize-persist'
import requireTransform from 'vite-plugin-require-transform';
import * as dotenv from "dotenv";
import * as webpack from 'webpack'

const path = require('path');
const resolve = dir => path.resolve(__dirname, dir);
const isProduction = process.env.NODE_ENV === "production";
const viteLessConfig = {
  // define global scss variable
  less: {
    charset: false,
    additionalData: '@import "@/assets/css/reset.less";@import "@/assets/css/common.less";',
    javascriptEnabled: true,
  },
}
// 处理 MaxListenersExceededWarning
require('events').EventEmitter.defaultMaxListeners = 0;
// 详细【配置参考】 http://doc.ssr-fc.com/docs/api$config#/应用配置
const userConfig: UserConfig = {
  stream: true,
  serverPort: 3001, // 默认:3000
  alias: {
    "@@": resolve('../web'),
    "@@img": resolve('../web/assets/images')
  },
  viteConfig: () => {
    return {
      common: {
        extraPlugin: [
          requireTransform({ fileRegex: /.ts$|.tsx$|.jsx$|.js$/ })]
      },
      client: {
        extraPlugin: [PkgConfig(), OptimizationPersist()],
        otherConfig: {
          build: {
            minify: "terser",
            terserOptions: {
              compress: {
                drop_console: !!isProduction, // 去掉console
                drop_debugger: !!isProduction // 去掉debugger
              }
            }
          },
          css: {
            // css预处理器
            preprocessorOptions: {
              ...viteLessConfig
            },
          }
        }
      },
      server: {
        // 只在服务端生效的配置
        otherConfig: {
          css: {
            // css预处理器
            preprocessorOptions: {
              // define global scss variable
              ...viteLessConfig
            },
          }
        }
      }
    }
  },
  // 加载公共less文件，避免导出@import
  chainBaseConfig: (chain, isServer) => {
    chain.module
      .rule('less')
      .use('style-resource')
      .loader('style-resources-loader')
      .options({
        patterns: [resolve("../web/assets/css/reset.less"),resolve("../web/assets/fonts/iconfont.css")]
      })
      .end()

    // set svg-sprite-loader
    chain.module
      .rule('images')
      .exclude
      .add(resolve("../web/assets/icons"))
      .end()

    chain.module
      .rule('svg')
      .test(/\.(svg)(\?.*)?$/)
      .include.add(resolve("../web/assets/icons"))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({ symbolId: 'icon-[name]' })
      .end()
  },
  css: () => {
    return {
      loaderOptions: {
        less: {
          // 透传参数给 less-loader
          lessOptions: {
            modifyVars: {
              // 配置antd主题颜色
              'primary-color': '#f5222d',
              'link-color': '#ff7875',
              // 初始化，全局主题变量文件引入，引入后全局可直接使用变量
              hack: `true;@import "@@/assets/css/common.less";`
            },
            javascriptEnabled: true
          },
        }
      }
    }
  },
  // 浏览器兼容
  corejs: true,
  babelOptions: {
    // 替换babelExtraModule配置
    include: [
      /javascript-stringify/,
      /sockjs-client/,
      /react-refresh/,
      /antd/,
      /antd-img-crop/,
      /react-refresh-runtime.development.js/,
      /ssr-mini-css-extract-plugin/,
      /jsencrypt/,
      /js-cookie/,
      /react-image-zoom/,
      /svg-captcha/,
      /compare-versions/,
      /[/\\]node_modules[/\\](.+?)?antd-img-crop(.*)[/\\]/,
      /[/\\]node_modules[/\\](.+?)?sockjs-client(.*)[/\\]/,
    ], // 需要额外处理的第三方模块
    exclude: [/core-js/],// 业务代码不需要处理的文件，通常用于指定纯 js 已经构建过一次的文件二次使用
    presets: ["@babel/preset-react","@babel/preset-typescript"], // 比较少用
    plugins: [
      ["import",
        {
          libraryName: "antd-mobile",
          libraryDirectory: "es/components",
          style: false
        },
        'antd-mobile'
      ]
    ] // 通常使用该配置新增 plugin
  },
  whiteList: [/antd-mobile/],
  define: {
    // todo 未解决
    base: new webpack.DefinePlugin({ "process.env": { ...dotenv.config().parsed,test1: JSON.stringify('HELLO') } })
  }
}
// 浏览器兼容配置处理
const generateCorejsOptions = () => {
  const semver = require('semver')
  const corejsVersion = semver.coerce(require('core-js/package.json').version).major
  return userConfig.corejs ? {
    debug: false,
    corejs: {
      version: corejsVersion,
      proposals: corejsVersion === 3
    },
    targets: {
      // chrome: '60',
      // firefox: '60',
      // safari: '10',
      // edge: '17',
      ie: '11',
    },
    useBuiltIns: 'usage', // entry usage
    shippedProposals: corejsVersion === 2,
    ...userConfig.corejsOptions
  } : {}
}
userConfig.corejsOptions = generateCorejsOptions()

export { userConfig }
