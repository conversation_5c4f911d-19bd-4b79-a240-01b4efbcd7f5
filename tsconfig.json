{"compileOnSave": true, "compilerOptions": {"baseUrl": ".", "target": "ES2018", "module": "commonjs", "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "inlineSourceMap": true, "noImplicitThis": true, "noUnusedLocals": true, "stripInternal": true, "skipLibCheck": true, "pretty": true, "declaration": true, "outDir": "dist", "typeRoots": ["./typings", "./node_modules/@types"], "paths": {"@/*": ["./src/*"], "~/*": ["./*"]}, "esModuleInterop": true, "resolveJsonModule": true}, "include": ["src"]}