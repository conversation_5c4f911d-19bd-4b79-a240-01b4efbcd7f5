import React, { memo, useContext } from "react";
import style from "./index.module.less";
import { Card, Menu } from "antd";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import { ItemType } from "antd/lib/menu/hooks/useItems";

interface IProps {
  defaultOpenKeys?: string[];
  navItems?: ItemType[];
  style?: object;
}

export const TmMenuBox = (prop: IProps) => {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  return (
    <>
      <div className={style.wrapper}>
        <Card className="tm-menu-box" style={prop.style}>
          <Menu items={prop.navItems} mode={"inline"} selectedKeys={state?.navSelectedKeys} defaultOpenKeys={prop.defaultOpenKeys ?? []} />
        </Card>
      </div>
    </>
  );
};

export default memo(TmMenuBox);
