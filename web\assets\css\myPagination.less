.my-pagination-wrapper {
  padding: 20px 0;
  // 上下页
  .pagination-btn-prev,
  .pagination-btn-next {
    background-color: #e7e7e7;
    padding: 8px 16px;
    border-radius: 2px;
    transition-duration: 0ms;
    &:hover {
      background-color: #ff7474;
      border-color: #ff7474;
      color: #ffffff;
    }
  }
  // 禁用时恢复
  .ant-pagination-prev.ant-pagination-disabled,
  .ant-pagination-next.ant-pagination-disabled {
    a {
      background-color: #e7e7e7;
      border-color: #e7e7e7;
      color: #b3b3b3;
    }
  }

  // 修改antd-pagination默认样式
  .ant-pagination {
    .flex-row(normal, center);
    color: #6d7278;
    a {
      color: #6d7278;
    }
    .ant-pagination-item {
      background-color: #e7e7e7;
      text-align: center;
      border-color: #e7e7e7;
      &:hover {
        background-color: #ff7474;
        border-color: #ff7474;
        a {
          color: #ffffff;
        }
      }
    }
    .ant-pagination-item-active {
      background-color: #ff7474;
      border-color: #ff7474;
      a {
        color: #ffffff;
      }
    }
    // 总条数
    .ant-pagination-total-text {
      //line-height: 32px;
    }
    // 分条数
    .ant-pagination-options {
      .flex-row(normal, center);
      border-radius: 4px;
      // 鼠标移入hover边框色
      .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
        border-color: #e7e7e7;
      }
      .ant-select {
        color: #6d7278;
        .ant-select-selector {
          box-shadow: unset;
          border: 1px solid #e7e7e7;
          border-radius: 2px;
          //background-color: #e7e7e7;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          background-color: #ffffff;
        }
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
          background-color: unset;
        }
        .ant-select-item-option-content {
          color: #6a6a6a;
        }
      }
    }
  }
}
