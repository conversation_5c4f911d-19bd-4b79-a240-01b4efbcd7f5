import { Provide, <PERSON>ope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IHonorService } from "@/service/honor.service";

@Provide("HonorService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class HonorServiceImpl extends BaseService implements IHonorService {
  /** 获取 荣誉资质信息 */
  async getHonorData(): Promise<any> {
    const result = await this.easyHttp.get("/api/website/classification/honors");
    return this.easyResponse(result, "获取数据出错了，请重试");
  }
}
