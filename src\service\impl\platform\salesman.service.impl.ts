import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ISalesmanService } from "@/service/platform/salesman.service";

@Provide("SalesmanService")
@Scope(ScopeEnum.Request)
export class SalesmanServiceImpl extends BaseService implements ISalesmanService {
  /** 获取注册推荐业务员 */
  async recommend(): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/api/platform/salesman/recommend"));
  }

  /**
   * <p>通过推荐码获取业务员</p>
   *
   * @param spreadCode 推荐码
   */
  async getSalesmanBySpreadCode(spreadCode: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/salesman/${spreadCode}`));
  }
}
