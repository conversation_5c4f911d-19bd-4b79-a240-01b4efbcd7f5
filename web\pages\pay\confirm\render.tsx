import React, { useContext, useEffect, useRef, useState } from "react";
import { IContext, SProps } from "ssr-types-react";
import style from "./index.module.less";
import { Card, Tag, message, Table, Tooltip, Form, Input, Space, Modal, notification, FormInstance, Steps, Button, AutoComplete } from "antd";
import * as crudAddress from "@/apis/member/address";
import { CloseCircleOutlined, CheckOutlined, DownOutlined, PlusCircleOutlined, UpOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { ColumnsType } from "antd/es/table";
import { price2Thousand, ghmallGuidePrice2Show } from "@/utils/price-format.util";
import AddressEditModal from "@/components/AddressEditModal";
import { IReceiptType, IReceivingAddressType } from "@/typings/member.interface";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import { ICreateVo } from "~/typings/data/trade/trade";
import * as tradeCrud from "@/apis/trade/trade";
import crudReceipt from "@/apis/member/receipt";
import { tradeCartDataType } from "@/typings/shopping-cart.interface";
import ReceiptFormModal from "@/components/ReceiptEditModal";
import { useLoading } from "@/hooks/useLoading";
import { useStoreContext } from "ssr-common-utils";
import { useSendTypeRadio } from "./cnpts/useSendTypeRadio";
import TmConfirmModal from "@/components/TmConfirmModal";
import * as orderCrud from "@/apis/order/order";
import SalesHistoryDialog from "@/components/SalesHistoryDialog";
const { Search } = Input;

// 备注Input组件
interface EditableRowRemarkProps {
  record: any;
  callbackFunc?: any;
}
const RemarkInputCnpt: React.FC<EditableRowRemarkProps> = ({ record, ...props }) => {
  const remarkRef = useRef<any>(null);
  const onChangeEvent = e => {
    record.remark = e.target.value;
    props?.callbackFunc && props.callbackFunc(e.target.value);
  };
  return (
    <>
      <Input allowClear maxLength={200} onChange={e => onChangeEvent(e)} ref={remarkRef} placeholder="可输入您对产品的需求…" />
    </>
  );
};
const notNeedReceipt = {
  receiptType: 2,
  id: null,
};
export default function PayConfirm(props: SProps) {
  // 产品列配置
  const productColumns: ColumnsType<tradeCartDataType> = [
    {
      title: "行号",
      dataIndex: "rowNum",
      width: 60,
      align: "center",
      key: "idx",
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "SKU",
      dataIndex: "productSku",
      key: "productSku",
      align: "center",
      render: (text, record, index) => `${record.product.sku}`,
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      key: "brandName",
      align: "center",
      render: (text, record) => <span>{record.product.brandName}</span>,
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      key: "productName",
      align: "center",
      ellipsis: {
        showTitle: false,
      },
      render: (_, record, index) => (
        <Tooltip title={`${record.product.skuName ? record.product.skuName : record.product.productNameZh} - ${record.product.id}`}>
          <div className="text-red-highlight">{record.product.skuName ? record.product.skuName : record.product.productNameZh}</div>
        </Tooltip>
      ),
    },
    {
      title: "包装",
      dataIndex: "productPacking",
      key: "productPacking",
      width: 120,
      align: "center",
      render: (text, record, index) => <span>{!record.product?.packing || record.product.packing === "1" ? "-" : record.product.packing}</span>,
    },
    {
      title: "单价(￥)",
      width: 120,
      dataIndex: "discountPrice", // discountPrice
      key: "discountPrice",
      align: "center",
      render: (_, record, index) => (
        <>
          <span className="text-red-highlight">{ghmallGuidePrice2Show(record.product.discountPrice)}</span>
          <br />
          <Button style={{ fontSize: "12px" }} type="link" onClick={() => handleViewHistoryPrice(record)} className="ml-2" size="small">
            历史价
          </Button>
        </>
      ),
    },
    {
      title: "数量",
      width: 80,
      dataIndex: "productQuantity",
      key: "productQuantity",
      align: "center",
      render: (_, record) => record.quantity,
    },
    {
      title: "小计(￥)",
      width: 150,
      key: "totalPrice",
      align: "center",
      render: (_, record, index) => <span className="text-red-highlight">{record.product.discountPrice === 0 ? "询价" : price2Thousand(record.product.discountPrice * record.quantity)}</span>,
    },
    {
      title: "备注",
      width: 225,
      key: "remark",
      align: "center",
      render: (text, record) => (!shoppingCartLoading ? <RemarkInputCnpt record={record} /> : null),
    },
  ];
  /* 展示更多地址 */
  const [showMoreAddress, setShowMoreAddress] = useState<boolean>(false);
  /* 展示编辑按钮 */
  const [showAddressEditBtn, setShowAddressEditBtn] = useState<any>("");
  /* 地址列表 */
  const [addresses, setAddresses] = useState<any>([]);
  // 购物车 loading
  const [shoppingCartLoading, setShoppingCartLoading] = useState<boolean>(false);
  /* 选中的地址 */
  const [selectedAddress, setSelectedAddress] = useState<any>({});
  /* 地址编辑弹窗-开关 */
  const [addressModalVisible, setAddressModalVisible] = useState<boolean>(false);
  /* 地址编辑弹窗-新增/编辑标记 */
  const [addressMode, setAddressMode] = useState<"add" | "edit">("add");
  /* 地址修改-选中项 */
  const [currentAddress, setCurrentAddress] = useState<IReceivingAddressType>();
  /* 购物车选中的产品项 */
  const [tableDataSource, setTableDataSource] = useState<tradeCartDataType[]>([]);
  // 产品数量
  const [selectedNum, setSelectedNum] = useState<number>(0);
  // 产品金额
  const [selectedFlowPrice, setSelectedFlowPrice] = useState<number>(0);
  const shoppingCartHook = useShoppingCart();
  const orderRemarkFormRef = useRef<FormInstance>(null);
  // 开票信息
  const [receiptList, setReceiptList] = useState<IReceiptType[]>();
  // 普通发票
  const [receiptNormal, setReceiptNormal] = useState<IReceiptType>();
  // 增值税发票
  const [receiptSpecial, setReceiptSpecial] = useState<IReceiptType>();
  // 选中的开票信息
  const [selectedReceipt, setSelectedReceipt] = useState<any>(notNeedReceipt);
  /* 展示开票编辑按钮 */
  const [showReceiptEditBtn, setShowReceiptEditBtn] = useState<any>("");
  const [receiptMode, setReceiptMode] = useState<"add" | "edit">("add");
  const [currentReceipt, setCurrentReceipt] = useState<IReceiptType>();
  const [receiptModalVisible, setReceiptModalVisible] = useState<boolean>(false);
  const [receiptType, setReceiptType] = useState<number>(0);
  // 展示操作指南
  const [showGuide, setShowGuide] = useState<Boolean>(true);
  const { state } = useContext<IContext>(useStoreContext());
  const { way } = state;
  // 开票信息情况
  const receiptTypeObj: any = [
    {
      typeName: "normal",
      typeValue: 1,
      name: "普通增值税发票",
      data: receiptNormal,
    },
    {
      typeName: "special",
      typeValue: 0,
      name: "13%增值税专用发票",
      data: receiptSpecial,
    },
    {
      typeName: "none",
      typeValue: 2,
      name: "不开票",
      data: notNeedReceipt,
    },
  ];
  const addressRef = useRef<any>();
  const payConfirmGuideRef = useRef<any>();
  // 提交页收集配送选项
  const [selectedSendType, setSelectedSendType] = useState(1);
  const [deliveryOptions, setDeliveryOptions] = useState<string[]>([]);
  /* ======================================= hook start ======================================= */
  const useLoadingHook = useLoading();
  const useSendTypeRadioHook = useSendTypeRadio({
    sendTypeChangeCallback: val => {
      setSelectedSendType(val);
    },
  });
  /* ======================================= hook end ======================================= */

  /* ======================================= 监听 start ======================================= */
  useEffect(() => {
    if (selectedAddress?.districtInfo?.name) {
      useSendTypeRadioHook.refresh(selectedAddress?.districtInfo?.name);
    }
  }, [selectedAddress?.districtInfo?.name]);
  /* ======================================= 监听 end ======================================= */

  /* ======================================= 方法 start======================================= */
  const fetchMemberAddresses = async (queryName?) => {
    const [err, res] = await crudAddress
      .getList({ size: 200, queryName })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return false;
    }
    const addressList = res?.data?.content ?? [];
    setAddresses(addressList);
    addressList.forEach((e, index) => {
      if (e.id === selectedAddress.id && index > 2) {
        setShowMoreAddress(true);
      }
      if (e.isDefault) {
        setSelectedAddress(e);
      }
    });
  };

  const fetchMemberReceipt = async () => {
    const [err, res] = await crudReceipt
      .getList()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return;
    }
    const receipts = res.data;
    setReceiptList(receipts);
    setReceiptNormal(receipts.find(item => item.receiptType === 1));
    setReceiptSpecial(receipts.find(item => item.receiptType === 0));
    receipts.forEach(e => {
      if (e.isDefault) {
        setSelectedReceipt(e);
      }
    });
  };

  // 选择地址
  const selectAddressEvent = async item => {
    setSelectedAddress(item);
  };

  // 修改地址
  const editAddressEvent = address => {
    setCurrentAddress(address);
    setAddressModalVisible(true);
    setAddressMode("edit");
  };

  /** 检测单品是否包含危化品标识 */
  const checkProductHasDangerFlag = product => {
    if (!product) {
      return false;
    }
    return product?.isDanger || product?.isPoison || product?.isExplode || product?.spec?.includes("L") || product?.spec?.includes("l");
  };

  /** 检测下单产品是否包含危化品 或 液体 */
  const handleCheckHasDangerInOrderProducts = () => {
    return tableDataSource?.some(item => item.isDanger) || false;
  };

  // 删除地址
  const delAddressEvent = addressId => {
    Modal.confirm({
      content: "确定删除此条地址信息吗？",
      title: "警告",
      type: "warning",
      centered: true,
      closable: true,
      onOk: async () => {
        if (!addressId) {
          return;
        }
        const [err, res] = await crudAddress
          .remove(addressId)
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (err) {
          message.error(err.data.message || "请求异常，删除失败！");
          return;
        }
        message.success("删除成功" || res.message);
        await fetchMemberAddresses();
      },
    });
  };

  // 新增地址
  const addAddressEvent = () => {
    setAddressModalVisible(true);
    setAddressMode("add");
  };

  // 选择开票事件
  const selectedReceiptEvent = item => {
    item?.data && setSelectedReceipt(item?.data);
  };

  // 开票编辑
  const editReceiptEvent = item => {
    setCurrentReceipt(item.data);
    setReceiptMode("edit");
    setReceiptModalVisible(true);
  };

  // 开票新增
  const addReceiptEvent = receiptType => {
    setReceiptMode("add");
    setReceiptModalVisible(true);
    setReceiptType(receiptType);
  };
  const handleReceiptCallBackFunc = async () => {
    await fetchMemberReceipt();
  };

  // 获取购物车产品数据
  const fetchShoppingCartSelectedList = async () => {
    setShoppingCartLoading(true);
    const carts = await shoppingCartHook.getSelectedLists(way);
    setShoppingCartLoading(false);
    // 若购物车为空则提示不允许下单
    if (carts.length === 0) {
      Modal.error({
        content: "当前暂无需要下单的产品，即将离开此页面！",
        type: "error",
        centered: true,
        closable: false,
        maskClosable: false,
        keyboard: false,
        okText: "前往首页",
        onOk: async () => {
          window.location.href = "/";
        },
      });
      return false;
    }
    let sum = 0;
    let flowPrice = 0;

    carts.forEach(item => {
      item.isDanger = checkProductHasDangerFlag(item?.product) || false;
      sum += Number(item.quantity);
      flowPrice += Number(item.product.discountPrice * item.quantity);
    });

    // 购物车选购情况
    setSelectedNum(sum);
    setSelectedFlowPrice(flowPrice);
    setTableDataSource(carts);
  };

  // 支付确定提交,创建订单
  const go2paySubmitEvent = async () => {
    // 表单校验
    const form = orderRemarkFormRef?.current?.getFieldsValue(true);

    // 弹窗拦截
    const confirm = await TmConfirmModal({
      title: "温馨提醒",
      content: (
        <>
          {handleCheckHasDangerInOrderProducts() ? (
            <span>
              您的下单产品包含: <span style={{ color: "red" }}>液体/危化学品</span>，如果您未提供过相关资质材料，请及时联系业务员/客服~
            </span>
          ) : (
            <span>商品等信息，已确认无误</span>
          )}
        </>
      ),
      confirmText: "提交订单",
    });
    if (confirm !== "confirm") {
      return;
    }
    // 组装产品数据
    const cartProducts = tableDataSource.map(item => {
      return {
        productId: item.product.productId,
        productQuantity: item.quantity,
        productSku: item.product.sku,
        price: item.product.discountPrice,
        remark: item.remark || "",
        skuId: item.productSkuId,
        isDanger: item.isDanger,
      };
    });
    const tradeVo: Partial<ICreateVo> = {
      ...form,
      receivingAddressId: selectedAddress.id,
      receiptId: selectedReceipt.id ?? null,
      products: cartProducts,
      tradeType: way ?? "CART",
      sendType: selectedSendType,
    };

    if (!tradeVo.receivingAddressId) {
      addressRef?.current?.scrollIntoView();
      notification.warning({ message: addresses.length ? "请选择一个收货地址!" : "请新增收货地址" });
      return;
    }
    useLoadingHook.showLoading("订单提交中...");
    const [err, res] = await tradeCrud
      .create(tradeVo)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      useLoadingHook.hideLoading();
      return notification.error({ message: err?.data?.message || "创建订单失败，请稍后再试！" });
    }
    notification.success({ message: res?.message || "订单创建成功！" });
    setTimeout(async () => {
      useLoadingHook.hideLoading();
      // 弹窗拦截
      await TmConfirmModal({
        title: "温馨提醒",
        content: (
          <>
            <div>订单已提交成功，您的专属业务员会尽快审核订单，请耐心等待！</div>
          </>
        ),
        confirmText: "确认",
      });
      window.location.href = `/ucenter/trade/order?status=0`;
    }, 100);
  };

  // 获取历史订单送货要求选项
  const fetchDeliveryRequirements = async () => {
    try {
      const [err, res] = await orderCrud
        .getMemberOrderHistory()
        .then(res => [null, res])
        .catch(err => [err, null]);

      if (err) {
        console.error("获取历史订单数据失败", err);
        return;
      }

      // 从历史订单中提取不重复的送货要求
      const requirements = res?.data?.content?.map(order => order.deliveryRequirements).filter(req => req && req.trim() !== "");
      // 去重
      const uniqueRequirements = [...new Set(requirements)];
      setDeliveryOptions(uniqueRequirements as string[]);
    } catch (error) {
      console.error("获取历史订单送货要求失败", error);
    }
  };
  const [historyPriceVisible, setHistoryPriceVisible] = useState<boolean>(false);
  const [currentHistoryProduct, setCurrentHistoryProduct] = useState<any>(null);
  const handleViewHistoryPrice = record => {
    setCurrentHistoryProduct(record.product);
    setHistoryPriceVisible(true);
  };
  /* ======================================= 方法 end======================================= */

  /* ======================================= 监听 start======================================= */
  useEffect(() => {
    fetchShoppingCartSelectedList();
    fetchMemberReceipt();
    fetchMemberAddresses();
    fetchDeliveryRequirements();
  }, []);
  /* ======================================= 监听 end======================================= */

  const getDeliveryAddress = () => {
    let detailAddress = "";
    selectedAddress?.provinceInfo?.name && (detailAddress += selectedAddress.provinceInfo.name);
    selectedAddress?.cityInfo?.name && (detailAddress += selectedAddress.cityInfo.name);
    selectedAddress?.districtInfo?.name && (detailAddress += selectedAddress.districtInfo.name);
    selectedAddress?.address && (detailAddress += " " + selectedAddress?.address);
    return detailAddress;
  };
  /* 步骤条 */
  const defaultStepStatus: "wait" | "process" | "finish" | "error" = "process";
  const items = [
    {
      title: "选择收货地址",
      status: defaultStepStatus,
    },
    {
      title: "选择开票信息",
      status: defaultStepStatus,
    },
    {
      title: "填写订单备注",
      status: defaultStepStatus,
    },
    {
      title: "确认下单信息",
      status: defaultStepStatus,
    },
    {
      title: "客户提交订单",
      status: defaultStepStatus,
    },
    {
      title: "易购业务审核",
      status: defaultStepStatus,
    },
    {
      title: "通知客户支付",
      status: defaultStepStatus,
    },
  ];

  return (
    <div className={style.wrapper}>
      <div className="confirmWrapper">
        {/* ================操作指南步骤====================== */}
        {showGuide ? (
          <Card
            ref={payConfirmGuideRef}
            title="操作指南"
            className="pay-card-item pay-card-step"
            bodyStyle={{ padding: "10px 20px" }}
            extra={
              <Button className="go2address" size="small" onClick={() => setShowGuide(false)} icon={<CloseCircleOutlined />} type="link" block>
                我知道了
              </Button>
            }
          >
            <Steps status="process" size="small" current={-1} items={items} />
          </Card>
        ) : null}

        {/* ================收货地址====================== */}
        <Card
          ref={addressRef}
          title={
            <div style={{ lineHeight: "32px" }}>
              <span>收货地址</span>
              <Search placeholder="请输入关键词" onSearch={fetchMemberAddresses} allowClear style={{ width: 250, marginLeft: "20px" }} />
            </div>
          }
          className="pay-card-item"
          bodyStyle={{ padding: "10px 20px" }}
          extra={
            <a target="_blank" href={"/ucenter/member/address"} className="go2address">
              管理收货人地址
            </a>
          }
          bordered
        >
          <div className="address-manage">
            {addresses.map((item, idx) => {
              return (
                <div
                  key={idx}
                  onMouseEnter={() => setShowAddressEditBtn(idx)}
                  onMouseLeave={() => setShowAddressEditBtn("")}
                  onClick={async () => await selectAddressEvent(item)}
                  className={`address-manage-item ${selectedAddress.id === item.id ? "border-red" : undefined}`}
                  style={{ display: showMoreAddress || idx < 3 ? "block" : "none" }}
                >
                  <div>
                    <span>{item.nickname}</span>
                    {item.isDefault ? <Tag color="red">默认</Tag> : null}
                    {item.aliasName ? <Tag color="orange">{item.aliasName}</Tag> : null}
                  </div>
                  <div>
                    {item.phone}&nbsp;&nbsp;({item.companyName})
                  </div>
                  <div>
                    {`${item.provinceInfo?.name}${item.cityInfo?.name}${item.districtInfo?.name}`}&nbsp;{item.address}
                  </div>
                  <div className="edit-btn" style={{ display: showAddressEditBtn === idx ? "block" : "none" }}>
                    <span onClick={() => editAddressEvent(item)}>修改</span>
                    {!item.isDefault ? <span onClick={() => delAddressEvent(item.id)}>删除</span> : null}
                  </div>
                  <div className="corner-icon" style={{ display: selectedAddress.id === item.id ? "" : "none" }}>
                    <div />
                    <CheckOutlined />
                  </div>
                </div>
              );
            })}
            <div className="add-address" onClick={() => addAddressEvent()}>
              <PlusCircleOutlined />
              <div>添加新地址</div>
            </div>
          </div>
          {addresses.length > 3 ? (
            <div className="more-address" onClick={() => setShowMoreAddress(!showMoreAddress)}>
              {showMoreAddress ? (
                <>
                  收起地址
                  <UpOutlined />
                </>
              ) : (
                <>
                  更多地址
                  <DownOutlined />
                </>
              )}
            </div>
          ) : null}
        </Card>
        {/* ================发票信息====================== */}
        <Card
          title={
            <span>
              发票信息
              <span className="tips">
                <ExclamationCircleOutlined />
                &nbsp;开企业抬头发票须填写纳税人识别号，以免影响报销
              </span>
            </span>
          }
          extra={
            <a target="_blank" href={"/ucenter/member/receipt"} className="go2address">
              管理开票信息
            </a>
          }
          className="pay-card-item invoice-box"
          bodyStyle={{ padding: "10px 20px" }}
          bordered
        >
          <div className="invoice-box-content">
            {receiptTypeObj.map((item, idx) => {
              return (
                <div
                  key={idx}
                  onMouseEnter={() => setShowReceiptEditBtn(item.typeValue)}
                  onMouseLeave={() => setShowReceiptEditBtn("")}
                  onClick={() => selectedReceiptEvent(item)}
                  className={`invoice-box-content-item ${selectedReceipt.receiptType === item?.typeValue ? "border-red" : undefined}`}
                >
                  <>
                    <div className="type">
                      {item.name}&nbsp;{item?.data?.isDefault ? <Tag color="red">默认</Tag> : null}
                    </div>
                    <div className="vat-name">{item?.data ? `${item?.data?.vatName ?? ""}  ${item?.data?.vatId ?? ""}` : "暂未配置"}</div>
                    <div className="edit-btn" style={{ display: showReceiptEditBtn !== 2 && showReceiptEditBtn === item.typeValue ? "block" : "none" }}>
                      {item?.data ? <span onClick={() => editReceiptEvent(item)}>修改</span> : <span onClick={() => addReceiptEvent(item.typeValue)}>新增</span>}
                    </div>
                  </>
                  <div className="corner-icon" style={{ display: selectedReceipt.receiptType === item?.typeValue ? "" : "none" }}>
                    <div />
                    <CheckOutlined />
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
        {/* ================订单信息====================== */}
        <Card
          title={
            <span>
              订单备注<span className="sub-tip">(选填)</span>
            </span>
          }
          className="pay-card-item order-remark-box"
          bodyStyle={{ padding: "10px 20px" }}
          bordered
        >
          <Form ref={orderRemarkFormRef} name="orderRemarkForm" className="orderRemarkForm" autoComplete="off" size="large">
            <Form.Item label="异常联系人" tooltip="订单异常联系人，如：张三 1866666666">
              <Input.Group compact>
                <Form.Item name="exceptionContact" noStyle>
                  <Input style={{ width: "50%" }} placeholder="请输入异常联系人" />
                </Form.Item>
                <Form.Item name="exceptionContactPhone" noStyle>
                  <Input style={{ width: "50%" }} placeholder="请输入异常联系人电话" />
                </Form.Item>
              </Input.Group>
            </Form.Item>
            <Form.Item label="客户PO号" name="customerPo" tooltip="客户PO号，将显示在出货单上">
              <Input placeholder="请输入客户PO号" />
            </Form.Item>
            <Form.Item label="送货要求" name="deliveryRequirements">
              <AutoComplete placeholder="请输入或选择送货要求" options={deliveryOptions.map(item => ({ value: item, label: item }))} />
            </Form.Item>
            <Form.Item name="remark" label="订单备注">
              <Space.Compact block direction="vertical">
                <Input.TextArea style={{ width: "100%" }} rows={3} placeholder="订单备注" maxLength={200} />
                <div className="form-item-tip">提示：请勿填写有关支付、收货、发票方面的信息，填写对此订单的特殊处理要求。</div>
              </Space.Compact>
            </Form.Item>
          </Form>
        </Card>
        {/* ================订购产品====================== */}
        <Card
          title="订购产品"
          className="pay-card-item"
          bodyStyle={{ padding: "10px 20px" }}
          extra={
            <>
              <a className="go2address" onClick={async () => await fetchShoppingCartSelectedList()}>
                刷新
              </a>
              <a href={"/ucenter/trade/shopping-cart"} className="go2address">
                前往我的购物车
              </a>
            </>
          }
          bordered
        >
          <Table loading={shoppingCartLoading} rowKey="id" className="order-products" columns={productColumns} dataSource={tableDataSource} bordered={false} pagination={false} size={"large"} />
          {/* 订单价格汇总 */}
          <div className={`pay-price-box`}>
            {/* 客户手动选择配送方式 */}
            <div>
              <span>共 {selectedNum} 件商品，总商品金额：</span>
              <span>￥{tableDataSource.findIndex(item => item.product.discountPrice === 0) === -1 ? price2Thousand(selectedFlowPrice) : "询价"}</span>
            </div>
            <div className="freight-box">
              <div className="left-area">
                <div>
                  <span>预定运费：</span>
                  <span className="freight-price">￥0.00</span>
                </div>
                <div className="tip">（注意：实际运费或免运费，待业务员审核订单后确定！）</div>
              </div>
              {/* 可选配送选项 */}
              <div className="send-type-box">
                <useSendTypeRadioHook.renderSendTypeSelector />
              </div>
            </div>
            <div>
              <span>应付金额：</span>
              <span className="actual-price">￥{tableDataSource.findIndex(item => item.product.discountPrice === 0) === -1 ? price2Thousand(selectedFlowPrice) : "询价"}</span>
            </div>
          </div>
        </Card>
        {/* ================底部支付栏====================== */}
        <Card bodyStyle={{ padding: "0" }} className="pay-card-footer">
          <div className="pay-footer">
            <div className="pay-address">
              配送至：{getDeliveryAddress() || "-"}
              &nbsp;&nbsp;收货人：{selectedAddress?.nickname}&nbsp;{selectedAddress?.phone}
            </div>
            <div className="pay-submit" onClick={async () => await go2paySubmitEvent()}>
              提交订单
            </div>
          </div>
        </Card>
      </div>
      {/* 地址操作面板 */}
      <AddressEditModal
        callbackFunc={async () => await fetchMemberAddresses()}
        modalVisible={addressModalVisible}
        changeModalVisible={setAddressModalVisible}
        mode={addressMode}
        addressId={currentAddress?.id}
        companyName={state?.userData?.companyName}
      />
      {/* 开票操作弹窗面板 */}
      <ReceiptFormModal
        receiptType={currentReceipt?.receiptType ?? receiptType}
        callbackFunc={handleReceiptCallBackFunc}
        modalVisible={receiptModalVisible}
        changeModalVisible={setReceiptModalVisible}
        mode={receiptMode}
        receiptId={currentReceipt?.id}
      />
      {/* 历史售价弹窗 */}
      <SalesHistoryDialog visible={historyPriceVisible} currentProduct={currentHistoryProduct} onClose={() => setHistoryPriceVisible(false)} />
    </div>
  );
}
