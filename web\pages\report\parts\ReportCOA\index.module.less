.wrapper {
  :global {
    .coa {
      padding: 10px;
      background-color: @main-bg-color-white;
      .coa-search-bar {
        width: 100%;
        height: 50px;
        .ant-input-search-button {
          height: 39px !important;
        }
      }
      .coa-description {
        color: #999;
        font-size: 13px;
        padding-bottom: 8px;
      }
      .coa-result-box {
        .product-wrapper {
          border-bottom: 1px solid rgb(221, 221, 221);
          ul {
            text-align: left;
            li {
              background-color: #fafafa;
              border: 1px solid #f5f5f5;
              color: #666;
              cursor: pointer;
              display: inline-flex;
              font-size: 16px;
              height: 40px;
              line-height: 40px;
              margin: 12px;
              text-align: center;
              width: 208px;
              .coa-item {
                width: 100%;
                height: 100%;
                a {
                  width: 100%;
                  display: inline-block;
                  font-size: 14px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  text-align: left;
                  padding: 0 8px;
                }
                &:hover {
                  background: #ffecec;
                  color: #ff1b1b !important;
                }
              }
            }
          }
          .coa-show-more {
            position: relative;
            z-index: 10;
            margin-top: -11px;
            font-size: 14px;
            color: #111e36;
            line-height: 36px;
            text-align: center;
            background-color: #f9f5f5;
            cursor: pointer;
          }
        }
      }
      .result-box {
        max-height: 700px;
        overflow-y: scroll;
        border: 1px solid rgb(221, 221, 221);
        .result-tip {
          margin-bottom: 5px;
          padding: 10px;
          border-bottom: 1px solid rgb(221, 221, 221);
        }
      }
      // antd-empty
      .ant-empty {
        padding: 36px;
      }
    }
  }
}
