import { useEffect, useState } from "react";

// 获取滚动偏移量变化

// 定义偏移量对象
interface ScrollOffset {
  x: number;
  y: number;
}

const useWindowScroll = () => {
  // 兼容IE
  const [off, setOff] = useState<ScrollOffset>({
    x: window.scrollX ?? window.pageXOffset,
    y: window.scrollY ?? window.pageYOffset,
  });
  const handleWindowScroll = () => {
    setOff({
      x: window.scrollX ?? window.pageXOffset,
      y: window.scrollY ?? window.pageYOffset,
    });
  };
  useEffect(() => {
    // 监听
    window.addEventListener("scroll", handleWindowScroll);
    return () => {
      // 移除监听
      window.removeEventListener("scroll", handleWindowScroll);
    };
  });
  return off;
};
export default useWindowScroll;
