import { Catch } from "@midwayjs/decorator";
import { Context } from "@midwayjs/koa";
import { MidwayHttpError } from "@midwayjs/core";

@Catch()
export class DefaultErrorFilter {
  async catch(err: MidwayHttpError, ctx: Context) {
    ctx.status = err.status ?? 500;
    const reg = /^\/(api|papi)\/.*/;
    if (ctx.get("X-Requested-With") !== "XMLHttpRequest" && !reg.test(ctx.path)) {
      const originUrl = ctx.url;
      if (ctx.status === 404) {
        // 404 错误会到这里
        return ctx.redirect(`/404?origin=${originUrl}`);
      } else if (ctx.status === 403) {
        return ctx.redirect(`/403?origin=${encodeURIComponent(originUrl)}&message=${encodeURIComponent(err.message ?? "")}`);
      } else if (ctx.status === 500) {
        console.error(err);
        return ctx.redirect(`/500?origin=${encodeURIComponent(originUrl)}&message=${encodeURIComponent(err.message ?? "")}`);
      } else {
        return ctx.redirect(`/error?origin=${encodeURIComponent(originUrl)}&message=${encodeURIComponent(err.message ?? "")}`);
      }
    } else {
      return {
        status: ctx.status,
        message: err?.message,
        code: -1,
      };
    }
  }
}
