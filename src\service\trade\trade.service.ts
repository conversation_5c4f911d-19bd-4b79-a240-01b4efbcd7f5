import { ICreateVo } from "~/typings/data/trade/trade";
import { TradeCashierPayDto } from "@/dto/trade-cashier-pay.dto";

export interface ITradeService {
  // 创建交易
  createTrade: (orderCreateVo: ICreateVo) => Promise<any>;
  // 快速下单创建交易
  createQuickOrderTrade: (orderCreateVo: ICreateVo) => Promise<any>;
  // 编辑交易
  editTrade: (orderCreateVo: ICreateVo) => Promise<any>;
  // 支付网关
  payGateway: (paymentMethod: string, tradeCashierPayDto: TradeCashierPayDto) => Promise<any>;
}
