.wrapper {
  :global {
    .shopping-cart-box {
      ul {
        li {
          font-size: 12px;
          border-bottom: 1px dashed #999;
          margin: 0 10px;
          padding: 10px 0;
          display: flex;
          align-items: center;
          position: relative;
          &:last-child {
            border: none;
          }
          .title-box {
            .main-title {
              span {
                font-weight: 600;
              }
              font-size: 14px;
            }
            .price {
              color: #e02020;
              span {
                color: #999;
              }
              margin-top: 7px;
            }
          }
          .del {
            position: absolute;
            right: 10px;
            bottom: 3px;
            cursor: pointer;
            color: #595959;
            font-size: 12px;
          }
        }
      }
    }
  }
}
