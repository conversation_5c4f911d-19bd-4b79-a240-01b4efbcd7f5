import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IBrandService } from "@/service/product/brand.service";
import { brandQueryListDto } from "~/typings/data/product/brand";

@Provide("BrandService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class BrandServiceImpl extends BaseService implements IBrandService {
  async getPageList(criteria: Partial<brandQueryListDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/brands`, criteria));
  }
}
