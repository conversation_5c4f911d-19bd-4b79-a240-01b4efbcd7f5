import React, { useState } from "react";
import { getPrivateFileResource } from "@/apis/platform/resources";
import { Button, message } from "antd";
import { ArrowDownOutlined } from "@ant-design/icons";
import { saveAs } from "file-saver";
import { downloadOrderContract } from "@/apis/order/order"

interface orderAttachmentProps {
  orderNo: string;
  path: string;
  title: string;
}

 /** 渲染订单合同下载按钮 */
 const RenderDownloadOrderContractBtn = (orderAttachment: orderAttachmentProps) => {
  const [loading, setLoading] = useState<boolean>(false);

  const downloadContract = async () => {
    if (!orderAttachment?.orderNo) { return }

    setLoading(true)

    try {
      const res = await downloadOrderContract(orderAttachment.orderNo)
      const suffixMatch = orderAttachment.path.match(/\.[^.]+$/);
      const suffix = suffixMatch ? suffixMatch[0] : "";
      const fileName = `${orderAttachment.title ?? '订单合同'}${suffix}`;
      const blob = new Blob([res.data]);
      saveAs(blob, fileName);
    } catch (err) {
      message.warning(err?.data?.message || "文件下载失败")
    } finally {
      setLoading(false)
    }
  }

  return (
    (!!orderAttachment && orderAttachment?.path) ? <Button disabled={loading} loading={loading} onClick={downloadContract} icon={<ArrowDownOutlined />} className="del" ghost type="default">下载订单合同</Button> : null
  );
}

export default RenderDownloadOrderContractBtn;
