/** 前缀 */
const BUSINESS_PREFIX = "Business:";

/** 缓存键常量 */
const cacheKeyConstant = {
  /** 电商 */
  BUSINESS: {
    /** 新闻-缓存键 */
    PLATFORM_NEWS: `${BUSINESS_PREFIX}Platform_News`,
    /** 地区联级-缓存键 */
    PLATFORM_AREA_CASCADE: `${BUSINESS_PREFIX}Platform_AreaCascade`,
    /** 专题分类-缓存键 */
    PLATFORM_TOPIC_CATEGORY: `${BUSINESS_PREFIX}Platform_TopicCategory`,
    /** 产品分类-缓存键 */
    PLATFORM_PRODUCT_CATEGORY_CASCADE: `${BUSINESS_PREFIX}Platform_ProductCategoryCascade`,
    /** 热门品牌介绍数据-缓存键 */
    PLATFORM_BRAND_INTRODUCE: `${BUSINESS_PREFIX}Platform_Brand_Introduce`,
    /** 热门品牌介绍数据-缓存键 */
    CC_MALL_LOGIN: `${BUSINESS_PREFIX}cc_mall:quicklogin`,
    /* 微信js ticket */
    WX_JS_TICKET: `${BUSINESS_PREFIX}wx:ticket`,
  },
};

export default cacheKeyConstant;
