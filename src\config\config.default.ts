import { FORMAT, MidwayAppInfo, MidwayConfig } from "@midwayjs/core";
import * as redisStore from "cache-manager-ioredis";
import { join } from "path";
import { readFileSync } from "fs";
import { uploadWhiteList } from "@midwayjs/upload";
import { tmpdir } from "os";

export default (appInfo: MidwayAppInfo): MidwayConfig => {
  const fs = require("fs");
  const https = require("https");
  const isProduction = process.env.NODE_ENV === "production";
  return {
    // use for cookie sign key, should change to your own and keep security
    keys: "ghmall_dev_1650192482948_2252",
    isProduction,
    // 配置静态文件
    // 日志配置
    midwayLogger: {
      default: {
        dir: process.env.MIDWAYJS_LOG_DIR || "./logs",
        maxSize: "30m",
        maxFiles: "31d",
        level: "info",
        consoleLevel: "warn",
      },
      clients: {
        coreLogger: {
          level: "warn",
          consoleLevel: "warn",
        },
        appLogger: {
          level: "info",
          consoleLevel: "info",
          format: info => {
            return `${info.timestamp} ${info.LEVEL} ${info.pid} ${info.labelText}${info.message}`;
          },
        },
        // 自定义业务日志
        businessLogger: {
          fileLogName: "business.log",
          level: "info",
          consoleLevel: "info",
          format: info => {
            return `${info.timestamp} ${info.LEVEL} ${info.pid} ${info.labelText} ${info.message}`;
          },
          contextFormat: info => {
            // 不生效，需要配置KOA上下文日志
            const ctx = info.ctx;
            return `${info.timestamp} ${info.LEVEL} ${info.pid} [${Date.now() - ctx.startTime}ms ${ctx.method}] ${ctx.logger_unq_id} ${info.message}`;
          },
        },
      },
    },
    koa: {
      contextLoggerFormat: info => {
        const ctx = info.ctx;
        return `${info.timestamp} ${info.LEVEL} ${info.pid} [${ctx.userId} - ${ctx.request_ip} - ${Date.now() - ctx.startTime}ms ${ctx.method}] ${ctx.logger_unq_id}  ${info.message}`;
      },
      proxy: true,
    },
    // axios 配置
    axios: {
      baseURL: process.env.JAVA_BASE_API_URL || "http://**************:8080",
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
      timeout: FORMAT.MS.ONE_SECOND * (parseInt(process.env.AXIOS_REQUEST_TIMEOUT) || 300),
      withCredentials: false,
      // 忽略证书
      httpsAgent: new https.Agent({
        rejectUnauthorized: false,
      }),
    },
    // redis 配置
    redis: {
      client: {
        port: parseInt(process.env.REDIS_PORT) || 6379,
        host: process.env.REDIS_HOST || "127.0.0.1",
        username: process.env.REDIS_USERNAME || "",
        password: process.env.REDIS_PASSWORD || "",
        db: parseInt(process.env.REDIS_DATABASE) || 6,
        retryStrategy: (times: number) => {
          /**
           * retryStrategy是一个在连接丢失时将被调用的函数。
           * 该参数times表示这是进行的第 n 次重新连接，返回值表示等待重新连接的时间（以毫秒为单位）。
           * 当返回值不是数字时，ioredis 将停止尝试重新连接，如果用户不redis.connect()手动调用，连接将永远丢失。
           */
          console.warn(`[ghmall-portal] ioredis 重试连接第 ${times} 次数`);
          return 2000;
        },
        reconnectOnError(err) {
          const targetError = "READONLY";
          if (err.message.includes(targetError)) {
            // Only reconnect when the error contains "READONLY"
            return true; // or `return 1;`
          }
        },
        // 连接超时30秒，默认10秒
        connectTimeout: 30000,
      },
    },
    cache: {
      store: redisStore,
      options: {
        host: process.env.REDIS_HOST || "127.0.0.1",
        port: process.env.REDIS_PORT || 6379,
        username: process.env.REDIS_USERNAME || "",
        password: process.env.REDIS_PASSWORD || "",
        db: process.env.REDIS_DATABASE || 6,
        keyPrefix: "portal-nodejs-cache:",
      },
      ttl: parseInt(process.env.CACHE_TTL) || 60 * 60 * 2,
    },
    // 跨域配置
    cors: {
      credentials: () => {
        if (!isProduction) {
          return !!process.env.MIDWAYJS_CORS_CREDENTIALS;
        }
        return true;
      },
      origin: request => {
        if (!isProduction) {
          return process.env.MIDWAYJS_CORS_ORIGIN || "*";
        }
        const reg = /^https?:\/\/[\w\-\\.]*guanghuayigou.com/;
        if (reg.test(request.header["origin"])) {
          return request.header["origin"];
        }
        return "https://www.guanghuayigou.com";
      },
      allowMethods: ["GET", "OPTION", "POST", "PUT", "DELETE"],
    },
    // jwt 登录验证
    jwt: {
      secret: fs.readFileSync(join(__dirname, "jwt-secret.key")),
      expiresIn: "2d", // https://github.com/vercel/ms
    },
    // session 默认 maxAge 为 1天，设置 30 天的过期时间:FORMAT.MS.ONE_DAY * 30
    session: {
      // 延长用户 Session 有效期
      renew: true,
    },
    passport: {
      session: true,
    },
    validate: {
      validationOptions: {
        allowUnknown: true, // 全局生效
        stripUnknown: true,
      },
    },
    siteFile: {
      enable: true,
      favicon: readFileSync(join(__dirname, "../../public/favicon.ico")),
    },
    /* 多语言配置 */
    i18n: {
      // 默认语言  "en_US | zh_CN"
      defaultLocale: "zh_CN",
      // 把你的翻译文本放到这里
      localeTable: {
        en_US: {
          default: require("../locale/en_US"),
        },
        zh_CN: {
          default: require("../locale/zh_CN"),
        },
      },
      // 语言映射，可以用 * 号通配
      fallbacks: {
        //   'en_*': 'en_US',
        //   pt: 'pt-BR',
      },
      // 是否将请求参数写入 cookie
      writeCookie: true,
      resolver: {
        // url query 参数，默认是 "locale"
        queryField: "locale",
        cookieField: {
          // Cookie 里的 key，默认是 "locale"
          fieldName: "locale",
          // Cookie 域名，默认为空，代表当前域名有效
          cookieDomain: "",
          // Cookie 默认的过期时间，默认一年
          cookieMaxAge: FORMAT.MS.ONE_YEAR,
        },
      },
      localsField: "i18n",
    },
    // 安全配置
    security: {
      csrf: {
        enable: isProduction,
        type: "all",
        useSession: false,
        cookieName: "csrfToken",
        sessionName: "csrfToken",
        headerName: "x-csrf-token",
        bodyName: "_csrf",
        queryName: "_csrf",
        refererWhiteList: ["183.24.67.13:9086", "192.168.1.232:3001"],
      },
      xframe: {
        enable: true,
        value: "ALLOW-FROM  https://tongji.baidu.com/", // SAMEORIGIN 默认；为了百度统计开启 ‘ALLOW-FROM  https://tongji.baidu.com/’
      },
      csp: {
        enable: false,
      },
      hsts: {
        enable: false,
        maxAge: 365 * 24 * 3600,
        includeSubdomains: false,
      },
      noopen: {
        enable: false,
      },
      nosniff: {
        enable: false,
      },
      xssProtection: {
        enable: true,
        value: "1; mode=block",
      },
    },
    // 上传配置
    upload: {
      // mode: UploadMode, 默认为file，即上传到服务器临时目录，可以配置为 stream
      mode: "file",
      // fileSize: string, 最大上传文件大小，默认为 10mb
      fileSize: "10mb",
      // whitelist: string[]，文件扩展名白名单
      whitelist: uploadWhiteList.filter(ext => ext !== ".pdf"),
      // tmpdir: string，上传的文件临时存储路径
      tmpdir: join(tmpdir(), "midway-upload-files"),
      // cleanTimeout: number，上传的文件在临时目录中多久之后自动删除，默认为 5 分钟
      cleanTimeout: 5 * 60 * 1000,
      // base64: boolean，设置原始body是否是base64格式，默认为false，一般用于腾讯云的兼容
      base64: false,
    },
    // 模板渲染
    view: {
      defaultExtension: ".ejs",
      mapping: {
        ".ejs": "ejs",
      },
      rootDir: {
        default: join(__dirname, "../view"),
      },
    },
    // ejs config
    ejs: {},
    // 配置代理
    httpProxy: {
      default: {},
      strategy: {
        paymentNotify: {
          match: /\/papi\/payment\/cashier\/notify\/(.*)/,
          proxyTimeout: 0,
          target: process.env.JAVA_BASE_API_URL + "/api/payment/cashier/notify/$1",
        },
      },
    },
    /* system_modal 系统功能开关配置 */
    systemModalOpenConfig: {
      /* 是否开启ERP客户身份校验, 默认关闭 */
      checkMemberIsErpControl: process.env.OPEN_CHECK_MEMBER_IS_ERP_CONTROL === "true",
      /* 是否开启产品最小起订量控制, 默认关闭 */
      checkProductPackingRatioControl: process.env.OPEN_CHECK_PRODUCT_PACKING_RATIO_CONTROL === "true",
      /* 是否开启客户默认初始密码检测前往修改的控制, 默认开启 */
      checkMemberIsDefaultPwdModalControl: process.env.OPEN_CHECK_MEMBER_IS_DEFAULT_PWD_MODAL_CONTROL === "true",
      /* 是否开启旧商城一键登录 */
      ccOldMemberLoginOpen: process.env.CC_OLD_MEMBER_LOGIN_OPEN === "true",
    },
    systemConfig: {
      // oss 域名
      ossCdnDomain: process.env.ALI_OSS_CDN || "//ttimg.guanghuayigou.com/",
      // 旧cc商城前台域名
      ccMallFrontendDomain: process.env.CC_MALL_FRONTEND_DOMIAN || "http//www.guanghuayigou.com/",
    },
  };
};
