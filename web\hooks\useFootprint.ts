import { $localStorage, FOOTPRINT_CACHE } from "@/utils/storage.util";
import { IProductSkuType, ISku } from "@/typings/product.interface";
import { message } from "antd";
import { useContext } from "react";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";

/**
 * 浏览足迹hook - 记录浏览-产品spu / 产品sku
 */
export const useFootprint = () => {
  // 暂时改用每次操作都直接获取缓存的方式处理
  const FOOTPRINT_HISTORY_LIMIT = 200;

  /** 足迹记录-本地缓存键 + 当前用户id */
  const { state } = useContext<IContext>(useStoreContext());
  const FOOTPRINT_CACHE_KEY = FOOTPRINT_CACHE + (!state?.userData?.memberId ? "" : state.userData.memberId);

  const getHistory = (): Array<Partial<IProductSkuType>> => {
    if (__isBrowser__) {
      return $localStorage.get(FOOTPRINT_CACHE_KEY) || [];
    } else {
      return [];
    }
  };

  const refresh = () => {
    return getHistory();
  };

  /** 添加足迹记录 */
  const addHistory = async (product, skuInfo?: Partial<ISku>) => {
    if (!product) {
      return;
    }

    const history = getHistory();
    const has = history.find(item => item.originProductNo === product.productNo && item.skuId === skuInfo?.id);
    if (has) {
      return;
    }

    // 组装数据
    const temp: Partial<IProductSkuType> = {
      originProductNo: product.productNo,
      itemNo: product.itemNo,
      brandId: product.brandId,
      brandName: product.brandName,
      cas: product.cas,
      productName: product.productName,
      isDanger: product.isDanger,
      isExplode: product.isExplode,
      isExplosion: product.isExplosion,
      isPoison: product.isPoison,
    };
    if (skuInfo) {
      temp.sku = skuInfo.sku;
      temp.skuId = skuInfo.id;
      temp.guidePrice = skuInfo.guidePrice;
      temp.packing = skuInfo.packing;
      temp.unit = skuInfo.unit;
      temp.spec = skuInfo.spec;
      temp.headImage = skuInfo.image;
    }

    if (history.length >= FOOTPRINT_HISTORY_LIMIT) {
      history.pop();
    }

    history.unshift(temp);
    $localStorage.set(FOOTPRINT_CACHE_KEY, history);
  };

  /** 移除足迹记录 */
  const deleteHistory = async product => {
    const history = getHistory();
    const historyIndex = history.findIndex(item => item.originProductNo === product.originProductNo && item.skuId === product?.skuId);
    if (historyIndex === -1) {
      return false;
    }

    history.splice(historyIndex, 1);
    $localStorage.set(FOOTPRINT_CACHE_KEY, history);
    message.success("移除成功!");
    return true;
  };

  /** 清空足迹记录 */
  const clearHistory = () => {
    $localStorage.remove(FOOTPRINT_CACHE);
    $localStorage.remove(FOOTPRINT_CACHE_KEY);
  };

  return {
    limit: FOOTPRINT_HISTORY_LIMIT,
    history: getHistory(),
    refresh,
    addHistory,
    deleteHistory,
    clearHistory,
  };
};
