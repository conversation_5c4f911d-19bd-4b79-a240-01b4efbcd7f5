import { Controller, Get } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { render } from "ssr-core-react";
import { Readable } from "stream";

@Controller("/ucenter/member/receipt", { middleware: [AuthenticationMiddleware] })
export class ReceiptController extends BaseController {
  @Get("/")
  async index() {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
