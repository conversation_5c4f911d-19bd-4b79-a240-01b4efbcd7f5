import React, { useContext, useEffect, useState } from "react";
import { IContext, SProps } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import { Helmet } from "react-helmet";
import style from "./index.module.less";
import { Tag, Button } from "antd-mobile";
import { PhoneFill } from "antd-mobile-icons";

export default function MobileMessageRender(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const logistics = state.logistics;

  return (
    <>
      <Helmet>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      </Helmet>
      <div className={style.wrapper}>
        <header className={style.header}>
          {/* <i className={style.headerIcon}>
            <TruckOutline style={{ fontSize: 32 }} />
          </i> */}
          <div className={style.headerText}>
            <h1 className={style.headerTitle}>物流信息</h1>
            <p className={style.headerDesc}>「光华易购」平台订单物流出库信息</p>
          </div>
        </header>
        {logistics.length > 0 ? (
          <section className={style.main}>
            {logistics.map((message, index) => (
              <div className={style.card} key={message?.id}>
                <div className={style.cardHeader}>
                  <div className={style.cardHeaderLeft}>
                    <div className={style.cardHeaderLeft1}>
                      <p>光华易购</p>
                      <span>⇢</span>
                      <p>{message?.erpCompany}</p>
                    </div>
                    <div className={style.cardHeaderLeft2}>
                      <p className={style.cardHeaderLabel}>订单编号：</p>
                      <p className={style.cardHeaderValue}>
                        {message?.orderNo?.split(",").map((item, index) => (
                          <span key={index}>{item}</span>
                        ))}
                      </p>
                    </div>
                    <div className={style.cardHeaderLeft2}>
                      <p className={style.cardHeaderLabel}>货运路线：</p>
                      <p className={style.cardHeaderValue}>
                        <Tag color="#87d068">{message?.shipperRoute}</Tag>
                      </p>
                    </div>
                  </div>
                  <div className={style.cardHeaderRight}>
                    <p className={style.cardHeaderValue}>{message?.shipperArrivalDays === "#N/A" ? "--" : message?.shipperArrivalDays}</p>
                    <p className={style.cardHeaderLabel}>
                      到货天数
                      <br />
                      （不含当天）
                    </p>
                  </div>
                </div>
                <div className={style.cardBody}>
                  <div className={style.cardBodyLeft}>
                    <p className={style.cardBodyLeftValue}>{message?.shipperArrivalPhoneRemark}</p>
                    <p className={style.cardBodyLeftLabel}>物流联系方式</p>
                  </div>
                  <div className={style.cardBodyRight}>
                    <a className={style.cardBodyRightIcon} href={`tel:${message?.shipperArrivalPhoneRemark}`}>
                      <PhoneFill />
                    </a>
                  </div>
                </div>
                <div className={style.cardFooter}>
                  <div className={style.cardFooterItem}>
                    <p className={style.cardFooterItemValue}>{message?.shipperDeliveryDate}</p>
                    <p className={style.cardFooterItemLabel}>发货日期</p>
                  </div>
                  <div className={style.cardFooterItem}>
                    <p className={style.cardFooterItemValue}>{message?.shipperWeight}kg</p>
                    <p className={style.cardFooterItemLabel}>重量</p>
                  </div>
                  <div className={style.cardFooterItem}>
                    <p className={style.cardFooterItemValue}>{message?.shipperQuantity}件</p>
                    <p className={style.cardFooterItemLabel}>数量</p>
                  </div>
                </div>
              </div>
            ))}
          </section>
        ) : (
          <div className={style.emptyContainer}>暂无相关物流信息</div>
        )}
      </div>
    </>
  );
}
