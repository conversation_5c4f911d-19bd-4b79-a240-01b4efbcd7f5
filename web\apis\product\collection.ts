import request from "@/utils/request.util";
import { ICollectionCreate, ICollectionQuery } from "~/typings/data/product/collection";

export function isCollection(sku_id: number) {
  return request({
    url: "/api/product/collect/is-collection",
    method: "get",
    params: { sku_id },
  });
}

export function getLists(params: ICollectionQuery) {
  return request({
    url: "/api/product/collect",
    method: "get",
    params,
  });
}

export function create(resource: ICollectionCreate) {
  return request({
    url: "/api/product/collect",
    method: "post",
    data: resource,
  });
}

export function deleteByCollectId(collectId: number) {
  return request({
    url: `/api/product/collect/${collectId}`,
    method: "delete",
  });
}

export function deleteBySkuId(skuId: number) {
  return request({
    url: `/api/product/collect/sku/${skuId}`,
    method: "delete",
  });
}

export function deleteAll() {
  return request({
    url: `/api/product/collect/overall`,
    method: "delete",
  });
}

export default {
  getLists,
  create,
  deleteByCollectId,
  deleteBySkuId,
  deleteAll,
  isCollection,
};
