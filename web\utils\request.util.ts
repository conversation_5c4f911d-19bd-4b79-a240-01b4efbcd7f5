import axios from "axios";
import { notification } from "antd";
import { <PERSON>ieUtil } from "@/utils/cookie.util";

// 创建axios实例
const request = axios.create({
  // 当创建实例的时候配置默认配置
  baseURL: "/", // api 的 base_url
  timeout: 1000 * 60 * 2, // 请求超时时间，毫秒（默认2分钟）
  xsrfCookieName: "xsrf-token",
});

// request拦截器
request.interceptors.request.use(
  config => {
    config.headers["Content-Type"] = "application/json";
    const csrfToken = CookieUtil.getInstance().get("csrfToken");
    csrfToken && (config.headers["x-csrf-token"] = csrfToken);
    return config;
  },
  error => {
    Promise.reject(error);
  }
);

// response 拦截器
request.interceptors.response.use(
  response => {
    if (response.data instanceof Blob) {
      return response;
    }
    return response.data;
  },
  async error => {
    try {
      // 登录授权
      if (error.response.status === 401) {
        notification.error({
          message: error.response.data?.message || "未登录，即将跳转登录页！",
        });
        setTimeout(() => {
          const currentUrl = window.location.href;
          window.location.href = `/auth/login?redirect=${encodeURIComponent(currentUrl)}`;
        }, 3000);
      }
    } catch (err) {
      notification.error({
        message: "系统异常，请稍后重试！",
      });
    }
    return await Promise.reject(error.response);
  }
);
export default request;
