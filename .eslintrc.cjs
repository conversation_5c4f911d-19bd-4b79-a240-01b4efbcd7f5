module.exports = {
  root: true,
  extends: [
    'standard-react-ts'
  ],
  rules: {
    quotes: [0, 'single'], // 引号类型 `` "" ''
    "no-template-curly-in-string": "off", // 禁止在普通字符串中出现模板字符串表达式-允许
    'comma-dangle': 0, // 是否有末尾的逗号
    'comma-spacing': 0,
    semi: 0, // 每个语句末尾是否加分号，false只有在编译报错时才加
    'no-var': 'error', // 不能使用var声明变量
    indent: [0, 2], // error类型，缩进2个空格
    "@typescript-eslint/no-throw-literal": 0,
    "@typescript-eslint/restrict-plus-operands": "warn", // 加法，无需加法两边都为字符串 或 数字
    "@typescript-eslint/no-extraneous-class": "warn",
    "@typescript-eslint/member-delimiter-style": "off",
    "@typescript-eslint/space-before-function-paren": "off",
    '@typescript-eslint/quotes': [0, 'single'],
    '@typescript-eslint/semi': 0,
    '@typescript-eslint/comma-dangle': 0,
    '@typescript-eslint/comma-spacing': 0,
    '@typescript-eslint/indent': ['warn', 2],
    '@typescript-eslint/no-unused-vars': [
      'warn',
      {
        vars: 'all',
        args: 'none',
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_'
      }
    ],
    'no-unused-vars': [
      'warn',
      {
        vars: 'all',
        args: 'none',
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_'
      }
    ],
  },
  globals: {
    __isBrowser__: 'readonly'
  },
  parserOptions: {
    project:"./tsconfig.lint.json",
    parser: '@typescript-eslint/parser'
  }
}
