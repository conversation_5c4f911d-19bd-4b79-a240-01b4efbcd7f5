import jwtConstant from "@/common/constants/jwt.constant";
import { Context } from "@midwayjs/koa";
import { Provide } from "@midwayjs/decorator";

@Provide()
export class AuthTokenUtil {
  static setCookieToken(ctx: Context, token: string) {
    ctx.cookies.set(jwtConstant.cookie_token_key, token, {
      httpOnly: true,
      encrypt: true,
      expires: new Date(new Date().setDate(new Date().getDate() + 2)), // cookie失效时间
    });
  }

  static getCookieToken(ctx: Context) {
    ctx.cookies.get(jwtConstant.cookie_token_key);
  }
}
