import React from "react";
import AboutNavigation from "@/pages/about/components/AboutNavigation";
import style from "./index.module.less";
import "@/assets/css/myArticle.less";
import { GH_COMPANY_ADDRESS as GH_COMPANY_INFO } from "@/constants/gh-company";
import commonConstant from "@/constants/common";

export default function QualificationIndex() {
  return (
    <div className={style.wrapper}>
      <div className="contactus-wrapper">
        <AboutNavigation />
        <div className="contactus-container">
          <div className="article">
            <div className="article-paragraph">
              <h2 className="article-title">联系我们</h2>
              <div className="article-content contract-us-box">
                <a className="contactus-location" href={GH_COMPANY_INFO.locationMapUrl}>
                  <img src={commonConstant.COMMON_IMAGE_PATHS.ABOUT_CONTACT_MAP} alt="location" />
                </a>
                <ul className="contactu-info">
                  <li>
                    <span className="contactus-lable">名称: </span>
                    <span className="contactus-value article-link">{GH_COMPANY_INFO.companyName}</span>
                  </li>
                  <li>
                    <span className="contactus-lable">电话: </span>
                    <span className="contactus-value article-link">{GH_COMPANY_INFO.companyTellPhone}</span>
                  </li>
                  <li>
                    <span className="contactus-lable">传真: </span>
                    <span className="contactus-value article-link">{GH_COMPANY_INFO.companyFax}</span>
                  </li>
                  <li>
                    <span className="contactus-lable">邮箱: </span>
                    <span className="contactus-value article-link">{GH_COMPANY_INFO.companyEmail}</span>
                  </li>
                  <li>
                    <span className="contactus-lable">地址: </span>
                    <span className="contactus-value article-link">{GH_COMPANY_INFO.companyAddress}</span>
                  </li>
                  <li>
                    <span className="contactus-lable">邮编: </span>
                    <span className="contactus-value article-link">{GH_COMPANY_INFO.companyCode}</span>
                  </li>
                </ul>
              </div>
              <div className="article-content">
                <h3 className="article-subtitle">关于投诉</h3>
                <div className="artile-content">
                  JHD非常重视您的体验，如您在购物中途或购买产品之后遭遇了不公平对待，或对我们的服务不满、有新的建议，请您直接发送邮件给我们的 <strong>服务调查委员会(JSC)</strong>{" "}
                  ，我们将在最短周期内调查及评估您的投诉及建议，并会尽快与您进行点对点的回复及沟通，直至解决问题！
                </div>
              </div>
              <div className="article-content">
                <h3 className="article-subtitle">
                  JHD电子商务项目服务调查委员会(JSC)邮箱：<span className="article-link"><EMAIL></span>
                </h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
