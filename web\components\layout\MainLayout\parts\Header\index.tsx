import React, { memo, useContext, useEffect, useState } from "react";
import style from "@/components/layout/MainLayout/parts/Header/index.module.less";
import Language from "@/components/Language";
import { IContext } from "ssr-types-react";
import SearchBox from "@/components/SearchBox";
import { logout } from "@/apis/auth";
import { Button, Menu, MenuProps, message, notification } from "antd";
import ProductCategory from "@/components/ClassificationNavigation";
import { STORE_CONTEXT } from "_build/create-context";
import mascot from "@@img/mascot.png";
import { FileSearchOutlined } from "@ant-design/icons";

// 头部组件
export default memo(function Header() {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const [navigationActive, setNavigationActive] = useState(state?.navSelectedKeys[0] ?? "index");
  useEffect(() => {
    !navigationActive && setNavigationActive("index");
  }, []);
  const loginUrl = `/auth/login?redirect=${encodeURIComponent(state.redirect)}`;
  const headerQuickNav = [
    {
      name: "登录",
      href: loginUrl,
      className: "",
      show: !state.userLoginState,
    },
    {
      name: "注册",
      href: "/auth/register",
      className: "nav-link-register",
      show: !state.userLoginState,
    },
    {
      name: `「${state.userData?.nickname}」会员中心`,
      href: "/ucenter",
      className: "",
      show: state.userLoginState,
    },
    {
      name: "安全退出",
      href: `/auth/logout?redirect=${encodeURIComponent(state.redirect)}`,
      className: "",
      show: state.userLoginState,
    },
    {
      name: "联系我们",
      href: "/about/contactus",
      className: "nav-link-contract",
      iconName: <i className="icon contract-us" />,
      show: true,
    },
    {
      name: "荣誉资质",
      href: "/about/honor",
      className: "nav-link-honor",
      iconName: <i className="icon honor" />,
      show: true,
    },
    {
      name: "帮助中心",
      href: "/help",
      className: "nav-link-help",
      iconName: <i className="icon help-center" />,
      show: true,
    },
  ];

  /** 点击头部导航链接处理 */
  const handleSwitchNavRoute = async (href: string) => {
    if (href.includes("/auth/logout")) {
      const [err, res] = await logout()
        .then(res => [null, res])
        .catch(err => [err, null]);
      if (err) {
        notification.error({ message: err.data.message || "退出失败", duration: 3 });
      } else {
        await message.success("已安全登出" || res?.message, 1);
        window.location.reload();
      }
    } else {
      window.location.href = href;
    }
  };

  // 导航栏
  const [current, setCurrent] = useState<string>(state?.navSelectedKeys[0] ?? "index");
  const items: MenuProps["items"] = [
    {
      label: <a href="/">首页</a>,
      key: "index",
    },
    {
      label: <a href="/brand">品牌中心</a>,
      key: "brand",
    },
    {
      label: "行业应用",
      key: "industry",
      children: [
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010756265.html" target="_blank">
              科研与检测
            </a>
          ),
          key: "industry-10",
        },
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010726572.html" target="_blank">
              制药行业
            </a>
          ),
          key: "industry-7",
        },
        {
          label: (
            <a href="http://www.toneset.com/" target="_blank">
              印刷电路板
            </a>
          ),
          key: "industry-1",
        },
        // {
        //   label: (
        //     <a href="https://www.ghtech.com/" target="_blank">
        //       动力电池系统应用
        //     </a>
        //   ),
        //   key: "industry-2",
        // },
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010725373.html" target="_blank">
              新能源材料
            </a>
          ),
          key: "industry-3",
        },
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010762656.html" target="_blank">
              汽车零部件
            </a>
          ),
          key: "industry-4",
        },
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010791268.html" target="_blank">
              电子元器件
            </a>
          ),
          key: "industry-5",
        },
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010792816.html" target="_blank">
              光伏行业
            </a>
          ),
          key: "industry-6",
        },
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010736626.html" target="_blank">
              日化行业
            </a>
          ),
          key: "industry-8",
        },
        {
          label: (
            <a href="https://www.ghtech.com/Gapplication/gapplication_100000010741142.html" target="_blank">
              表面处理
            </a>
          ),
          key: "industry-9",
        },
      ],
    },
    {
      label: <a href="/product-special/default">产品专题</a>,
      key: "default",
    },
    {
      label: <a href="/report">COA/MSDS</a>,
      key: "report",
    },
    {
      label: "渠道专栏",
      key: "channel",
      children: [
        {
          label: "渠道招商",
          key: "channel-1",
        },
      ],
    },
    {
      label: "定制服务",
      key: "service",
      children: [
        {
          label: "专用化学品定制",
          key: "service-1",
        },
        {
          label: "增值服务",
          key: "service-2",
        },
        {
          label: "研究院",
          key: "service-3",
        },
      ],
    },
    {
      label: <span className="promotional">促销活动</span>,
      key: "activities",
    },
    {
      label: <a href="/about">关于我们</a>,
      key: "about",
    },
  ];
  const onClick: MenuProps["onClick"] = e => {
    setCurrent(e.key);
    setNavigationActive(e.key);
  };

  const needShowCategory = (): boolean => {
    return ["index", "industry", "channel", "service", "activities"].includes(navigationActive.split("-")[0]);
  };

  return (
    <div className={style.mainHeader}>
      <div className="header-wrapper">
        <div className="header-top-wrapper">
          <div className="header-top-box">
            <span className="header-left-welcome">
              欢迎&nbsp;<span className="welcome-company">{state.userData?.companyName ?? ""}</span>&nbsp;光临光华科技旗下电商平台——光华易购！
            </span>
            <span className="header-right-nav">
              {headerQuickNav.map((item, key) => {
                return item.show ? (
                  <a className={`nav-link ${item.className}`} key={key} onClick={async () => await handleSwitchNavRoute(item.href)}>
                    {item?.iconName ? item.iconName : null}
                    {item.name}
                  </a>
                ) : null;
              })}
              <Language />
            </span>
          </div>
        </div>
        <div className="header-search-wrapper">
          <div className="header-logo-wrapper">
            <a href={"/"} className="app-logo" />
            <span className="stock-code">证券代码: 002741</span>
          </div>
          <SearchBox />
          <div className="header-mascot">
            <img width={70} src={mascot} />
          </div>
        </div>
        <div className="header-nav-wrapper">
          <div className="header-nav-box">
            {/*  精选产品分类 */}
            <ProductCategory offsetTop={needShowCategory() ? 16 : 0} defaultOpenPanel={needShowCategory()} />
            <Menu onClick={onClick} selectedKeys={[current]} mode="horizontal" items={items} className="nav-box" />
          </div>
        </div>
      </div>
    </div>
  );
});
