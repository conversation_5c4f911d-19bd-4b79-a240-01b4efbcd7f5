import React, { memo } from "react";
import { LayoutProps } from "ssr-types-react";
import styles from "./index.module.less";
import useWxShare from "@/hooks/useWxShare";

interface Props extends LayoutProps {
  title?: string;
}

export default memo(function MobileAuthLayout(props: Props) {
  const { children, title } = props;
  // 微信分享页面
  const useWxShareHook = useWxShare();
  useWxShareHook?.wxInit();
  return (
    <>
      <div className={styles.wrapper}>
        <div className="m-container">
          <div className="m-container-content">{children}</div>
        </div>
      </div>
    </>
  );
});
