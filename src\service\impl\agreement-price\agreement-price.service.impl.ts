import { Provide, Scope, <PERSON>opeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IAgreementPriceService } from "@/service/agreement-price/agreement-price.service";

@Provide("AgreementPriceService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class AgreementPriceServiceImpl extends BaseService implements IAgreementPriceService {
  /**
   * 动态获取会员产品区间折扣
   * 
   * @param params 查询参数
   */
  async getMemberVolumeDiscount(params: {
    orderQuantity: number;
    brandId?: string;
    memberId?: string;
    productSkuId?: number;
  }): Promise<any> {
    const url = "/api/agreement-price/brands/mallAgreementPriceBrandSkuOrderQuantityConfiguration/memberVolumeDiscount";
    
    // 使用 BaseService 的 easyHttp 和 easyResponse 方法
    return this.easyResponse(
      await this.easyHttp.get(url, params, this.getDefaultHeaders()),
      "查询协议价格失败，请稍后再试！"
    );
  }
}
