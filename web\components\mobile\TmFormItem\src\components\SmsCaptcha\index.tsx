import React, { useState, useEffect } from "react";
import style from "./index.module.less";
import { sendSmsCode } from "@/apis/sms";
import { useCountDownCache } from "@/hooks/useCountDown";
import { getCaptchaCode } from "@/apis/auth";
import { $tools } from "@/utils/tools.util";
import { onlyCsr } from "ssr-hoc-react";
import { $localStorage } from "@/utils/storage.util";
import { SmsSendTypeEnum } from "@/enums/SmsSendTypeEnum";
import { Form, Input, Toast } from "antd-mobile";
import { useForm } from "rc-field-form";

/** 短信验证-组件数据类型 */
interface SmsCaptchaProps {
  countdownSeconds: number; // 倒计时单位秒,默认120秒
  captchaCode: string;
  needCaptchaCode: boolean; // 是否需要图形验证码
  captchaCodeLabel: string; // 图形验证码label
  smsSendType: SmsSendTypeEnum; // 短信验证码类型：register | login
  rules: object; // 校验规则
  smsCode: string | number;
  smsCodeLabel: string;
  uuid: string;
  showLineTransition: any; // 是否展示分割线动画，传入组件即可
  showRequired: boolean; // 是否展示必填标志
}

/** 转可选-短信验证模块类型 */
type SmsCaptchaPartialProps = Partial<SmsCaptchaProps> & { smsFormRef: typeof useForm };

const SmsCaptcha = (props: SmsCaptchaPartialProps) => {
  const smsFormRef: any = props.smsFormRef;
  const [formParams, setFormParams] = useState({
    validate: {
      smsCode: null,
      captchaCode: null,
    },
    rules: {
      smsCode: [{ required: true, whitespace: true, message: "短信码不能为空" }],
      captchaCode: [{ required: false, whitespace: true, message: "图像码不能为空" }],
    },
  });
  const rules = Object.assign({}, formParams.rules, props.rules);
  /* 是否倒计时处理中 */
  const [isCountDownIng, setIsCountDownIng] = useState(false);
  // 是否已触发发送
  const [isTrigger, setIsTrigger] = useState(false);
  //= ============= 客户端渲染才生效 ==============
  const cnptUuid = props.uuid ?? $tools.uid();
  const smsCountDownHook = useCountDownCache(props.countdownSeconds ?? 120, cnptUuid, () => {
    setIsCountDownIng(false);
  });
  useEffect(() => {
    if ($localStorage.get(`timerStartTime-${cnptUuid}`)) {
      setIsTrigger(true);
      setIsCountDownIng(true);
      smsCountDownHook.start();
    }
  }, []);
  //= ============= 客户端渲染才生效 ==============

  /** 获取短信验证码前 */
  const beforeGetSMSCodeEvent = async () => {
    // 校验手机号是否正确输入
    const errMap = await smsFormRef
      .validateFields(["account", "phone"])
      .then(() => null)
      .catch(err => err);
    if (errMap) {
      Toast.show({
        content: "请输入手机号码",
        maskClickable: false,
        duration: 2000,
      });
      return false;
    }
    // 开启时，是否输入图形验证码
    if (props.needCaptchaCode && !smsFormRef.getFieldValue("captchaCode")) {
      Toast.show({
        content: "请输入图形验证码",
        maskClickable: false,
        duration: 1500,
      });
      return false;
    }
    return true;
  };
  // 获取验证码事件 -
  const handleGetSMSCodeEvent = async () => {
    if (!(await beforeGetSMSCodeEvent())) {
      return;
    }

    if (isCountDownIng) {
      return false;
    }
    const phone = smsFormRef.getFieldValue("phone");
    const captchaCode = smsFormRef.getFieldValue("captchaCode");
    const smsSendType = props.smsSendType ?? SmsSendTypeEnum.REGISTER;

    const [err, res] = await sendSmsCode({ phone, captchaCode, type: smsSendType })
      .then(data => [null, data])
      .catch(err => [err, null]);
    await getCaptchaCodeEvent();
    if (err) {
      Toast.show({
        content: err.data?.message ?? "出错了，请稍后再试",
        maskClickable: false,
        duration: 1500,
      });
      return false;
    }

    setIsTrigger(true);
    setIsCountDownIng(true);
    smsCountDownHook.start();
    Toast.show({
      content: "验证码已发送" || res.message,
      maskClickable: false,
      duration: 1500,
      icon: "success",
    });
  };
  // 获取图形验证码
  const [captchaContent, setCaptchaContent] = useState("");
  const getCaptchaCodeEvent: any = async () => {
    const [_, res] = await getCaptchaCode()
      .then(data => [null, data])
      .catch(err => [err, null]);
    if (res) {
      setCaptchaContent(res);
    }
  };
  // 组件渲染，数据初始化
  useEffect(() => {
    if (props.needCaptchaCode) {
      getCaptchaCodeEvent();
    }
  }, []);
  const renderCaptchaCode = () => {
    return props.needCaptchaCode ? (
      <>
        <Form.Item
          required={props?.showRequired}
          name="captchaCode"
          label={props.captchaCodeLabel ?? ""}
          rules={rules.captchaCode}
          className={`${props?.showRequired ? "m-captcha-code" : ""} item`}
          extra={<img className="m-captcha-code-img" src={`data:image/svg+xml;utf8,${encodeURIComponent(captchaContent)}`} alt="captcha" onClick={getCaptchaCodeEvent} />}
        >
          <Input className="input" placeholder="请输入图像验证码" autoComplete="off" />
        </Form.Item>
        {props?.showLineTransition ? props?.showLineTransition : null}
      </>
    ) : null;
  };
  const renderSmsCode = () => {
    return (
      <span className={`sms-send-btn${isCountDownIng ? " sms-send-btn-ing" : ""}`} onClick={handleGetSMSCodeEvent}>
        {!isTrigger ? "获取验证码" : isCountDownIng ? `倒计时 ${smsCountDownHook.count} 秒` : "重新获取"}
      </span>
    );
  };
  return (
    <>
      <div className={style.mobileSmsCaptchaWrap}>
        {renderCaptchaCode()}
        <Form.Item required={props?.showRequired} name="smsCode" label={props.smsCodeLabel ?? ""} className="m-sms-code item" rules={rules.smsCode} extra={renderSmsCode()}>
          <Input className="input" placeholder="请输入短信验证码" autoComplete="off" />
        </Form.Item>
      </div>
    </>
  );
};

SmsCaptcha.defaultProps = {
  countdownSeconds: 120,
  needCaptchaCode: true,
  showRequired: true,
  captchaCodeLabel: "图形验证码",
  smsCodeLabel: "短信验证码",
  smsSendType: SmsSendTypeEnum.REGISTER,
};

export default onlyCsr(SmsCaptcha) as any;
