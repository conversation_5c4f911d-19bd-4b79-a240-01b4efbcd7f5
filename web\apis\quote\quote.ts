import request from "@/utils/request.util";

// 临时定义类型，避免路径问题
interface IQuoteRequest {
  id?: number;
  title: string;
  description?: string;
  contactName: string;
  contactPhone: string;
  contactEmail?: string;
  companyName?: string;
  status?: string;
  memberId?: string;
  createdAt?: string;
  updatedAt?: string;
  expiresAt?: string;
}

interface IQuoteQueryDto {
  page?: number;
  size?: number;
  status?: string;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  memberId?: string;
}

interface IQuoteProduct {
  id?: number;
  quoteRequestId?: number;
  productNo?: string;
  productName: string;
  sku: string;
  specification?: string;
  quantity: number;
  unit?: string;
  estimatedPrice?: number;
  quotedPrice?: number;
  remark?: string;
  matchStatus?: string;
  productSkuId?: number;
  brandId?: number;
  brandName?: string;
  productImage?: string;
  packingRatio?: number;
}

/**
 * 创建询报价单
 */
export function createQuoteRequest(data: Partial<IQuoteRequest>) {
  return request({
    url: "/api/quote-requests",
    method: "post",
    data,
  });
}

/**
 * 文件上传并匹配产品
 */
export function uploadFileAndMatchProducts(formData: FormData) {
  return request({
    url: "/api/quote-requests/upload-and-match",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 获取询报价单列表
 */
export function getQuoteRequestList(params: Partial<IQuoteQueryDto>) {
  return request({
    url: "/api/quote-requests",
    method: "get",
    params,
  });
}

/**
 * 获取询报价单详情
 */
export function getQuoteRequestDetail(quoteRequestId: number) {
  return request({
    url: `/api/quote-requests/${quoteRequestId}`,
    method: "get",
  });
}

/**
 * 更新询报价产品
 */
export function updateQuoteProducts(quoteRequestId: number, products: Partial<IQuoteProduct>[]) {
  return request({
    url: `/api/quote-requests/${quoteRequestId}/products`,
    method: "put",
    data: { products },
  });
}

/**
 * 生成报价单
 */
export function generateQuote(
  quoteRequestId: number,
  products: Partial<IQuoteProduct>[],
  validDays?: number,
  remark?: string
) {
  return request({
    url: `/api/quote-requests/${quoteRequestId}/generate-quote`,
    method: "post",
    data: {
      quoteRequestId,
      products,
      validDays,
      remark,
    },
  });
}

/**
 * 从询报价单创建快速订单
 */
export function createQuickOrderFromQuote(
  quoteRequestId: number,
  selectedProductIds: number[],
  orderData: {
    receivingAddressId: number;
    receiptId?: number;
    remark?: string;
    sendType?: string;
  }
) {
  return request({
    url: `/api/quote-requests/${quoteRequestId}/create-order`,
    method: "post",
    data: {
      selectedProductIds,
      ...orderData,
    },
  });
}

/**
 * 删除询报价单
 */
export function deleteQuoteRequest(quoteRequestId: number) {
  return request({
    url: `/api/quote-requests/${quoteRequestId}`,
    method: "delete",
  });
}

/**
 * 产品匹配
 */
export function matchProduct(productName: string, sku: string, quantity: number = 1) {
  return request({
    url: "/api/quote-requests/match-product",
    method: "get",
    params: {
      productName,
      sku,
      quantity,
    },
  });
}

export default {
  createQuoteRequest,
  uploadFileAndMatchProducts,
  getQuoteRequestList,
  getQuoteRequestDetail,
  updateQuoteProducts,
  generateQuote,
  createQuickOrderFromQuote,
  deleteQuoteRequest,
  matchProduct,
};
