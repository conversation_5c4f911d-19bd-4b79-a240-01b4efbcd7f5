import { BaseService } from "@/common/base/base.service";
import { IMemberService } from "@/service/member/member.service";
import { Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { ReBindEmailVo, ReBindPhoneVo, updateMemberVo, updatePasswordVo } from "~/typings/data/member/member";
import { IUploadParams } from "~/typings/data/file-remote";

@Provide("MemberService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class MemberServiceImpl extends BaseService implements IMemberService {
  async getInfoByMemberId(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/${memberId}`));
  }

  async updateMember(memberId: string, updateVo: Partial<updateMemberVo>): Promise<any> {
    return this.easyResponse(await this.easyHttp.put(`/api/members/${memberId}`, updateVo));
  }

  async updatePassword(memberId: string, updateVo: updatePasswordVo): Promise<any> {
    return this.easyResponse(await this.easyHttp.post(`/api/members/${memberId}/modify-password`, updateVo));
  }

  async updateMemberAvatar(memberId: string, uploadData: IUploadParams): Promise<any> {
    return this.easyResponse(await this.easyHttp.post(`/api/members/${memberId}/update-avatar`, uploadData, { "Content-Type": "multipart/form-data" }));
  }

  async getMemberConsultant(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/${memberId}/my-consultant`));
  }

  /**
   * 绑定或换绑邮箱
   *
   * @param memberId /
   * @param bindVo /
   */
  async rebindEmail(memberId: string, bindVo: ReBindEmailVo): Promise<any> {
    return this.easyResponse(await this.easyHttp.put(`/api/members/${memberId}/rebind-email`, bindVo));
  }

  /**
   * 绑定或换绑手机
   *
   * @param memberId /
   * @param bindVo /
   */
  async rebindPhone(memberId: string, bindVo: ReBindPhoneVo): Promise<any> {
    delete bindVo.smsCode;
    return this.easyResponse(await this.easyHttp.put(`/api/members/${memberId}/rebind-phone`, bindVo));
  }
}
