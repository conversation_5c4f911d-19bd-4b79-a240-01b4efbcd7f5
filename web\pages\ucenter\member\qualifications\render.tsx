import React, { useEffect, useRef, useState } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import style from "./index.module.less";
import { Button, Card, Descriptions, Space, Image, Empty, Alert, Spin } from "antd";
import { PlusOutlined, ReloadOutlined } from "@ant-design/icons";
import QualificationEditModal from "./cnpts/QualificationEditModal";
import qualificationConstant from "@/constants/qualification";
import commonConstant from "@/constants/common";
import { IMemberQualificationType } from "@/typings/member.interface";
import { initMemberQualification, qualificationDetailNameOptions, qualificationShowOptions } from "@/pages/ucenter/member/qualifications/qualification.util";

export default function MemberQualificationIndex(props: SProps) {
  const qualificationModalRef = useRef<any>(null);
  /** 客户资质信息 */
  const [memberQualification, setMemberQualification] = useState<Partial<IMemberQualificationType>>();
  const [initLoading, setInitLoading] = useState(false);

  /* ======================================= 组件初始化 ======================================= */
  useEffect(() => {
    onRefresh();
  }, []);
  /* ======================================= 组件初始化 ======================================= */

  /* ======================================= method start======================================= */
  /** 刷新页面 */
  const onRefresh = async () => {
    setInitLoading(true);
    const memberQualification = await initMemberQualification();
    setInitLoading(false);
    memberQualification && setMemberQualification(memberQualification);
  };

  /** 产品类型、客户类型、位置区域 - 根据数组配置选项,通过 value 匹配 类型对应的文本 */
  const findMemberLabel = (options: any[], value: any) => {
    return options.find(item => item.value === String(value))?.label || "";
  };

  /** 过滤控制-图片分栏-显示隐藏 */
  const needShowUploadComponent = (arr: string[] = []) => {
    if (!memberQualification?.selectedFields) {
      return false;
    }
    return arr.some(item => memberQualification.selectedFields?.includes(item));
  };

  /** 激活资质弹窗-编辑 */
  const openQualificationModalWithEdit = () => {
    if (!memberQualification || !memberQualification.memberId) {
      return;
    }
    qualificationModalRef?.current?.setVisible(true);
    qualificationModalRef?.current.setQualificationEditMember(memberQualification.memberId);
  };

  /** 激活资质弹窗-新增 */
  const openQualificationModalWithAdd = () => {
    qualificationModalRef?.current?.setVisible(true);
  };

  /** 客户资质弹窗关闭时的回调 */
  const handleModalExpandCallback = () => {
    onRefresh();
  };
  /* ======================================= method end ======================================= */
  /** 渲染-资质多图预处理 */
  const renderImage = (url: string) => {
    const isMultiple = url.includes(",") && url.split(",").length > 0;
    return !isMultiple ? (
      <Image
        className="qualification-img"
        src={`${commonConstant.QUALIFICATION_RESOURCE.QUALIFICATION_IMAGE_BASE_URL}${url}`}
        alt="资质图片"
        fallback={commonConstant.COMMON_IMAGE_PATHS.IMAGE_FAILED}
      />
    ) : (
      <div>
        {url.split(",").map((item, index) => (
          <Image
            key={index}
            className="qualification-img"
            src={`${commonConstant.QUALIFICATION_RESOURCE.QUALIFICATION_IMAGE_BASE_URL}${item}`}
            alt="资质图片"
            fallback={commonConstant.COMMON_IMAGE_PATHS.IMAGE_FAILED}
          />
        ))}
      </div>
    );
  };

  /** 渲染-资质描述信息 */
  const renderImageDescriptions = () => {
    return (
      <>
        {memberQualification &&
          qualificationShowOptions.map((child, idx) => {
            if (needShowUploadComponent(child.showFields)) {
              return (
                <Descriptions.Item key={idx} label={child.title} span={3} className="qualification-wrapper">
                  {child.showFields.map((item, index) => {
                    if (memberQualification[item]) {
                      return (
                        <div key={index} className="qualification-item">
                          {renderImage(memberQualification[item])}
                          <div className="qualification-tip">{qualificationDetailNameOptions[item]}</div>
                        </div>
                      );
                    }
                  })}
                </Descriptions.Item>
              );
            }
          })}
      </>
    );
  };

  return (
    <div className={style.wrapper}>
      <div className="qualification">
        <UCenterCard
          title={"我的资质"}
          extra={
            <div className="opt-action">
              <Space>
                <Button size="middle" icon={<ReloadOutlined />} onClick={onRefresh}>
                  刷新
                </Button>
                <Button disabled={!!memberQualification} size="middle" icon={<PlusOutlined />} onClick={openQualificationModalWithAdd}>
                  新增资质
                </Button>
              </Space>
            </div>
          }
        />

        {/* 资质明细 */}
        <Spin spinning={initLoading}>
          {memberQualification ? (
            <Card className="detail-container" bodyStyle={{ padding: "10px 20px" }} bordered>
              <Descriptions
                title="资质信息"
                column={3}
                bordered
                extra={
                  <>
                    {memberQualification.auditState === 2 && (
                      <Button disabled={memberQualification.auditState !== 2} onClick={openQualificationModalWithEdit}>
                        重新编辑
                      </Button>
                    )}
                  </>
                }
              >
                <Descriptions.Item label="客户身份" span={1}>
                  <span>{findMemberLabel(qualificationConstant.QUALIFICATION_MEMBER_TYPE, memberQualification?.customerIdentity)}</span>
                </Descriptions.Item>
                <Descriptions.Item label="客户位置" span={1}>
                  <span>{findMemberLabel(qualificationConstant.QUALIFICATION_LOCATION_TYPE, memberQualification?.customerPosition)}</span>
                </Descriptions.Item>
                <Descriptions.Item label="申请时间" span={1}>
                  <span>{memberQualification?.createdDate || "-"}</span>
                </Descriptions.Item>

                <Descriptions.Item label="产品类型" span={1}>
                  <span className="red-tip">{findMemberLabel(qualificationConstant.QUALIFICATION_PRODUCT_TYPE, memberQualification?.productType)}</span>
                </Descriptions.Item>
                <Descriptions.Item label="审核状态" span={1}>
                  <span className="blue-tip">{memberQualification?.auditState !== undefined && qualificationConstant.QUALIFICATION_AUDIT_OPTIONS[memberQualification?.auditState]}</span>
                </Descriptions.Item>
                <Descriptions.Item label="审核时间" span={1}>
                  <span>{memberQualification?.auditDate || "-"}</span>
                </Descriptions.Item>
                {/* 审核通过-有效时间 */}
                {memberQualification?.auditState === 1 && (
                  <Descriptions.Item label="资质有效期" span={3}>
                    <span>{memberQualification?.expiredDate || "-"}</span>
                  </Descriptions.Item>
                )}
                {/* 审核不通过-原因 */}
                {memberQualification?.auditState === 2 && (
                  <Descriptions.Item label="未通过原因" span={3}>
                    <span className="red-tip">{memberQualification?.auditorReason || "-"}</span>
                  </Descriptions.Item>
                )}
                {renderImageDescriptions()}
              </Descriptions>
            </Card>
          ) : (
            <div className="not-qualification">
              <Alert
                type={"warning"}
                message={
                  <Empty
                    description={
                      <div className="empty-wrapper">
                        <div className="empty-wrapper-tips">
                          <span>未找到您的资质信息</span>
                        </div>
                        <div className="empty-btn">
                          <Button size="middle" icon={<PlusOutlined />} danger type="primary" onClick={openQualificationModalWithAdd}>
                            新增资质
                          </Button>
                        </div>
                      </div>
                    }
                  />
                }
              />
            </div>
          )}
        </Spin>
      </div>
      {/* 根据资质返回值处理memberId 和 mode */}
      <QualificationEditModal ref={qualificationModalRef} modalActionSuccessCallback={handleModalExpandCallback} />
    </div>
  );
}
