@search-wrapper-width: 785px; // 搜索框整体-宽度 500px
@search-wrapper-text-color: @main-text-color; // 搜索框-整体字体颜色
@search-wrapper-hot-word-font-size: @font-size-12; // 热搜词-字体大小 12px
@search-wrapper-top: 20px; // 搜索框整体-上边距 20px

.wrapper {
  margin-left: 50px;
  :global {
    .order-btn {
      width: 125px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      margin-left: 15px;
    }
    .search-wrapper {
      color: @search-wrapper-text-color;
      padding-top: @search-wrapper-top;
      .search-box {
        position: relative;
        width: @search-wrapper-width;
        display: flex;
        align-items: center;
        .search-main {
          width: @search-wrapper-width;
          display: flex;
          .ant-input-affix-wrapper {
            input {
              height: 28px;
              font-size: 16px;
              padding-left: 4px;
              &:active,
              &:focus {
                border-color: #e6212a !important;
              }
            }
          }
          .search-btn {
            width: 125px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            margin-left: -1px;
          }
        }
        .search-suggest {
          position: absolute;
          left: 0;
          top: 36px;
          width: calc(@search-wrapper-width - 110px);
          height: auto;
          background-color: #fff;
          border: 1px solid #e6212a;
          border-radius: 0 0 5px 5px;
          border-top: none;
          z-index: 9;
          ul {
            margin: 7px 14px 0;
            padding: 8px 0 7px;
            background: 0 0;
            border-top: 2px solid #f5f5f6;
            li {
              display: block;
              position: relative;
              width: auto;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding-left: 14px;
              margin-left: -14px;
              margin-right: -14px;
              color: #626675;
              font-size: 14px;
              line-height: 38px;
              z-index: 10;
              &:hover {
                cursor: pointer;
                background-color: rgba(245, 34, 45, 0.05);
                color: #e6212a;
              }
            }
          }
        }
      }
    }
    // 热词链接
    .hotwords {
      width: 100%;
      height: 18px;
      margin-top: 4px;
      font-size: @search-wrapper-hot-word-font-size;
      font-weight: 400;
      color: @search-wrapper-text-color;
      .word {
        display: inline-block;
        padding: 0 4px;
        cursor: pointer;
        color: @search-wrapper-text-color;
        &:hover {
          color: #f3333d;
          text-decoration: underline;
        }
      }
    }
  }
}
