// 公用函数变量
/** flex布局 */
.flex(@direction: row, @justify: normal, @align: normal) {
  display: flex;
  flex-direction: @direction;
  justify-content: @justify;
  align-items: @align;
}

/** flex居中 */
.flex-center(@direction: row) {
  .flex(@direction, center, center);
}

/** flex 水平布局 */
.flex-row(@justify: normal, @align: normal) {
  .flex(row, @justify, @align);
}

/** flex 垂直布局 */
.flex-col(@justify: normal, @align: normal) {
  .flex(column, @justify, @align);
}

/** 清除浮动 */
.clearfix {
  display: block;
  zoom: 1;
  &:after {
    content: " ";
    display: block;
    font-size: 0;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}

/**
* 文本省略号<div | p>
* $line 控制文本行数
* 兼容范围: webkit
*/
.ellipsis(@line: 1) {
  -webkit-line-clamp: @line;
  word-break: break-all;
  word-wrap: break-word;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: normal;
}

/**
* @subtractHeight:屏幕减去的高度,如屏幕高度1080px，传入@subtractHeight=100px,则最高为:980px
*/
.antModalBodyScroller(@subtractHeight:0px) {
  .ant-modal-body {
    max-height: calc(80vh - @subtractHeight);
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #e3e3e6;
      border-radius: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 5px;
    }
  }
}
