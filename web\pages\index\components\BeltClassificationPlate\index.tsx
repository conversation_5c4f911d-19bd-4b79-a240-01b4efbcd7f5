import React, { memo, useEffect, useState } from "react";
import style from "./index.module.less";
import ProductItem from "@/pages/index/parts/ProductItem";
import RenderThemeLogo from "@/pages/index/parts/ThemeRenderLogo";
import { IHomepageDataType, IHomePagePlateProductType } from "@/typings/index.interface";
import { Empty } from "antd";

/** 子分类 */
interface IReagentCategoryType {
  id: number;
  title: string;
  classification: string;
}

/** 有子分类的板块布局 */
export default memo(function BeltClassificationPlate(props: { data: IHomepageDataType }) {
  // 板块数据
  const pageData: IHomepageDataType = props.data || {};
  // 子分类
  const reagentCategories: IReagentCategoryType[] = JSON.parse(pageData?.optionalJson || "[]").map((item, index) => {
    return { id: index + 1, title: item.name, classification: item.classification };
  });
  const [cateTabIndex, setCateTabIndex] = useState(0);
  const [currentActiveCateProducts, setCurrentActiveCateProducts] = useState<Array<Partial<IHomePagePlateProductType>>>([]);

  // 板块推荐logo配置
  const renderLogoOptions = {
    title: pageData.title,
    subTitle: pageData.subTitle,
    img: pageData.headImage,
    description: pageData.description,
    bgColor: "#C0DACB",
    color: "#333333",
    height: "428px",
    moreLink: pageData.moreLink,
    tags: JSON.parse(pageData?.optionalJson || "[]").map(item => item.name)
  };

  useEffect(() => {
    // 初始化时切换首项
    if (reagentCategories.length) {
      handleClickReagentCateItem(reagentCategories[0]);
    }
  }, []);

  /** 切换分类 */
  const handleClickReagentCateItem = async cate => {
    if (cate.id === cateTabIndex) {
      return;
    }
    const activeIndex = cate.id;
    await setCateTabIndex(activeIndex);
    // 切换分类-过滤除对应的产品数据
    const filterProducts = pageData.products.filter(product => product.classification === cate.classification);
    setCurrentActiveCateProducts(filterProducts);
  };

  const renderTitle = () => {
    return (
      <div className="hot-reagent-header">
        <span className="header-title">{renderLogoOptions.title}</span>
        <ul className="reagent-cates">
          {reagentCategories.map((cate, index) => (
            <li key={index} onClick={async () => await handleClickReagentCateItem(cate)}>
              <div className={`reagent-cate-item ${cateTabIndex === cate.id ? "active" : ""}`}>{cate.title}</div>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={style.wrapper}>
      <div className="hot-reagent">
        {renderTitle()}
        <div className="hot-reagent-container">
          <div className="logo-wrapper">
            {RenderThemeLogo(renderLogoOptions)}
          </div>
          {
            currentActiveCateProducts.length ? (
              <ul className="reagent-products clearfix">
                { currentActiveCateProducts.map((product, index) => <ProductItem layout="vertical" key={index} product={product} />) }
              </ul>
            ) : (
              <div className="not-products">
                <Empty description="..." />
              </div>
            )
          }
        </div>
      </div>
    </div>
  );
});
