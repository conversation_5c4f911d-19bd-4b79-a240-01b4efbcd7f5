export const FeRoutes = [
  {
    fetch: async () => await import(/* webpackChunkName: "spreadRegisterFetchMobile" */ "@/pages/mobile/auth/spread-register/fetch"),
    path: "/m/auth/spread-register",
    component: async function dynamicComponent() {
      // react 场景需要固定函数名称为 dynamicComponent
      return await import(/* webpackChunkName: "spreadRegisterMobile" */ "@/pages/mobile/auth/spread-register/render");
    },
    webpackChunkName: "spreadRegisterMobile",
  },
  {
    fetch: async () => await import(/* webpackChunkName: "spreadRegisterFetchPc" */ "@/pages/auth/spread-register/fetch"),
    path: "/auth/spread-register",
    component: async function dynamicComponent() {
      return await import(/* webpackChunkName: "spreadRegisterPc" */ "@/pages/auth/spread-register/render");
    },
    webpackChunkName: "spreadRegisterPc",
  },
  {
    fetch: async () => await import(/* webpackChunkName: "loginFetchMobile" */ "@/pages/mobile/auth/login/fetch"),
    path: "/m/auth/login",
    component: async function dynamicComponent() {
      // react 场景需要固定函数名称为 dynamicComponent
      return await import(/* webpackChunkName: "loginMobile" */ "@/pages/mobile/auth/login/render");
    },
    webpackChunkName: "loginMobile",
  },
  {
    fetch: async () => await import(/* webpackChunkName: "registerFetchMobile" */ "@/pages/mobile/auth/register/fetch"),
    path: "/m/auth/register",
    component: async function dynamicComponent() {
      // react 场景需要固定函数名称为 dynamicComponent
      return await import(/* webpackChunkName: "registerMobile" */ "@/pages/mobile/auth/register/render");
    },
    webpackChunkName: "registerMobile",
  },
  {
    fetch: async () => await import(/* webpackChunkName: "passwordResetFetchMobile" */ "@/pages/mobile/auth/password-reset/fetch"),
    path: "/m/auth/password-reset",
    component: async function dynamicComponent() {
      // react 场景需要固定函数名称为 dynamicComponent
      return await import(/* webpackChunkName: "passwordResetMobile" */ "@/pages/mobile/auth/password-reset/render");
    },
    webpackChunkName: "passwordResetMobile",
  },
];
