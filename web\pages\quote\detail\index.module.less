.wrapper {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  :global {
    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 8px;

      .ant-card-head {
        background: linear-gradient(135deg, #ff3b3b 0%, #ffa45a 100%);
        border-bottom: none;
        border-radius: 8px 8px 0 0;
        
        .ant-card-head-title {
          color: #fff;
          font-size: 18px;
          font-weight: 600;
        }

        .ant-card-extra {
          .ant-tag {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            font-weight: 500;
          }
        }
      }

      .ant-card-body {
        padding: 30px;
      }
    }

    .ant-descriptions {
      .ant-descriptions-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
      }

      .ant-descriptions-item-label {
        font-weight: 500;
        color: #666;
      }

      .ant-descriptions-item-content {
        color: #333;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #333;
      }

      .ant-table-tbody > tr > td {
        padding: 12px 16px;
      }

      .ant-table-selection-column {
        width: 50px;
      }
    }

    .ant-tag {
      border-radius: 4px;
      font-size: 12px;
      padding: 2px 8px;
      
      &.ant-tag-success {
        background: #f6ffed;
        border-color: #b7eb8f;
        color: #52c41a;
      }
      
      &.ant-tag-warning {
        background: #fff7e6;
        border-color: #ffd591;
        color: #fa8c16;
      }
      
      &.ant-tag-error {
        background: #fff2f0;
        border-color: #ffccc7;
        color: #ff4d4f;
      }
      
      &.ant-tag-processing {
        background: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
      }
    }

    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: linear-gradient(135deg, #ff3b3b 0%, #ffa45a 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        
        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
        }
      }
    }

    .ant-modal {
      .ant-modal-header {
        background: linear-gradient(135deg, #ff3b3b 0%, #ffa45a 100%);
        border-bottom: none;
        border-radius: 8px 8px 0 0;
        
        .ant-modal-title {
          color: #fff;
          font-weight: 600;
        }
      }

      .ant-modal-close {
        color: #fff;
        
        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
        }
      }

      .ant-modal-body {
        padding: 24px;
      }
    }

    .ant-form {
      .ant-form-item-label > label {
        font-weight: 500;
        color: #333;
      }

      .ant-input,
      .ant-input-number,
      .ant-select-selector {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        
        &:focus,
        &:hover {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }

      .ant-input-number {
        width: 100%;
      }
    }

    // 产品信息样式
    .product-info {
      .product-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .product-meta {
        font-size: 12px;
        color: #666;
        line-height: 1.4;
      }
    }

    // 价格样式
    .price {
      font-weight: 500;
      
      &.estimated {
        color: #f50;
      }
      
      &.quoted {
        color: #52c41a;
      }
    }

    // 操作按钮组
    .action-buttons {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 16px;

      .ant-btn {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      .container {
        padding: 0 10px;
      }

      .ant-card-body {
        padding: 20px !important;
      }

      .ant-descriptions {
        .ant-descriptions-item {
          padding-bottom: 12px;
        }
      }

      .action-buttons {
        flex-direction: column;
        align-items: stretch;

        .ant-btn {
          justify-content: center;
        }
      }

      .ant-table {
        .ant-table-content {
          overflow-x: auto;
        }
      }
    }
  }
}
