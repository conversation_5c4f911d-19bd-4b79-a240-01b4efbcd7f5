import { IResourceUploadParamDto } from "~/typings/data/resources";

export interface IReSourcesService {
  /** 私有图片-上传 */
  uploadPrivateImage: (uploadData: IResourceUploadParamDto) => Promise<any>;

  /** 私有文件-上传 */
  uploadPrivateFile: (uploadData: IResourceUploadParamDto) => Promise<any>;

  /** 私有图片-下载 */
  getPrivateImage: (filepath: string) => Promise<any>;

  /** 私有文件-下载 */
  getPrivateFile: (filepath: string) => Promise<any>;
}
