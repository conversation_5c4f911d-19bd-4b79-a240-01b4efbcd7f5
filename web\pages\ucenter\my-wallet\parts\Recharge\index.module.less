.wrapper {
  :global {
    .recharge-detail {
      .ant-descriptions-item {
        padding-bottom: 8px;
        .ant-descriptions-item-label {
          color: #666666;
        }
      }
      //.pay-btn-wrapper {
      //  .ant-descriptions-item-content {
      //    justify-content: center;
      //  }
      //}
    }
    .search-bar {
      margin: 16px 16px 16px 0;
      // 搜索表单
      .ant-form-item-label > label {
        height: 31px;
      }
    }
    .blue-tip {
      &:hover {
        text-decoration: underline;
        cursor: pointer;
      }
    }
    .btn-wrapper {
      .btn-has-end {
        width: 134px;
      }
      button {
        transform: scale(0.86);
        _:-ms-fullscreen,
        & {
          margin-right: 2px;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}
