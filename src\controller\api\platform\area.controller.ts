import { Controller, Get, Inject, Param } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IAreaService } from "@/service/platform/area.service";
import { CacheManager } from "@midwayjs/cache";
import { FORMAT } from "@midwayjs/core";
import cacheKeyConstant from "@/common/constants/cache.constant";

@Controller("/api/platform/area")
export class AreaController extends BaseController {
  @Inject("AreaService")
  areaService: IAreaService;

  @Inject()
  cacheManager: CacheManager;

  /**
   * 获取省市区层联数据接口
   */
  @Get("/cascade")
  async cascade() {
    const { ctx } = this;
    const cacheKey = cacheKeyConstant.BUSINESS.PLATFORM_AREA_CASCADE;
    let res: any = await this.cacheManager.get(cacheKey);
    if (!res) {
      const result = await this.areaService.getAreaCascade();
      if (result.data?.length) {
        await this.cacheManager.set(cacheKey, result.data, { ttl: (FORMAT.MS.ONE_DAY * 7) / 1000 });
        res = result.data;
      }
    }
    return ctx.getResponseInstance(ctx).setResponseData(res).sendSuccess();
  }

  @Get("/selectAddress/:address")
  // 异步选择地址
  async selectAddress(@Param('address') address: string) {
    // 获取上下文对象
    const { ctx } = this;
    // 调用areaService的getAreaInfo方法获取地址信息
    const result = await this.areaService.getAreaInfo(address);
    if (result.statusCode === 200) {
      // 返回响应实例，设置响应数据，发送成功响应
      return ctx.getResponseInstance(ctx).setResponseData(result.data).send();
    } else {
      // 返回响应实例，设置响应数据，发送失败响应
      return ctx.getResponseInstance(ctx).setResponseData(result.data).send();
    }
  }
}
