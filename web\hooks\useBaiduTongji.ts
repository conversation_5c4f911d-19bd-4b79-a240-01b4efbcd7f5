import { useEffect } from "react";

const useBaiduTongji = () => {
  useEffect(() => {
    initTongjiScript();
  }, []);
  const initTongjiScript = () => {
    const hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?d8def2366520c7752dcc5f48c31811a7";
    const s = document.getElementsByTagName("script")[0];
    s?.parentNode?.insertBefore(hm, s);
  };
};
export default useBaiduTongji;
