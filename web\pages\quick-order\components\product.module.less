.productCard {
  :global {
    display: flex;
    .productImage {
      margin-right: 15px;
      img {
        width: 96px;
        height: 96px;
        object-fit: cover;
        border-radius: 5px;
      }
    }
    .productInfo {
      text-align: left;
      .productInfoHeader {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .productTag {
          margin-right: 4px;
          padding: 0 4px;
          line-height: 1.5;
        }
      }
      .productName {
        font-size: 16px;
        font-weight: normal;
        line-height: 24px;
        padding: 0;
        color: #e62129;
      }
      .productDesc {
        .ant-descriptions-item-label {
          color: rgba(0, 0, 0, 0.45);
        }
        .ant-descriptions-item-content {
          color: #333;
        }
      }
    }
  }
}
.quantityInput {
  :global {
    position: relative;
    .quantityInputText {
      position: absolute;
      left: 0;
      top: 35px;
      width: 100%;
      font-size: 12px;
      color: #888;
    }
  }
}
.priceInfo {
  :global {
    padding-top: 5px;
    .item {
      padding-bottom: 8px;
      &:last-child {
        font-size: 15px;
        font-weight: bold;
        padding-bottom: 0;
        label {
          color: #333;
        }
        span {
          font-size: 16px;
        }
      }
      label {
        color: rgba(0, 0, 0, 0.45);
      }
      span {
        color: #333;
        font-size: 14px;
      }
    }
  }
}
.controlArea {
  :global {
    display: flex;
    margin: 20px 0;
    .control {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      color: #e62129;
      box-shadow: 0 2px 0 rgba(42, 46, 54, 0.02);
      border: 1px dashed #e62129;
      font-size: 14px;
      height: 36px;
      border-radius: 6px;
      cursor: pointer;
      margin: 0 10px;
      p {
        margin-left: 5px;
      }
    }
    .control-delete {
      border-color: #ccc;
      color: #333;
    }
  }
}
.addProductTable {
  :global {
    display: flex;
    padding: 20px;
    background-color: #fafafa;
    .tableItem {
      margin-right: 20px;
      &:nth-child(1) {
        width: 520px;
      }
      &:nth-child(2) {
      }
      &:last-child {
        margin-right: 0;
      }
      .tableHeader {
        height: 22px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .tableHeaderTitle {
          font-size: 14px;
          font-weight: bold;
          color: hsl(0, 0%, 20%);
          span {
            margin-left: 6px;
            color: #e62129;
          }
        }
      }
      .tableBody {
        margin-top: 10px;
        button {
          margin: 0 5px;
        }
        .ant-input-group-addon {
          button {
            margin: 0;
          }
        }
      }
    }
  }
}
.productTable {
  :global {
    .productName {
      position: relative;
      span {
        position: absolute;
        left: 0;
        top: 0;
        font-size: 12px;
        padding: 0 5px;
      }
      .productName__buy {
        padding-left: 52px;
      }
    }
  }
}
