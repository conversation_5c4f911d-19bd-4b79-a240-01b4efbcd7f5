import { Controller, Get } from "@midwayjs/decorator";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { BaseController } from "@/controller/base.controller";

@Controller("/brand")
export class BrandController extends BaseController {
  @Get()
  async index(): Promise<void> {
    const ctx = this.ctx;
    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
