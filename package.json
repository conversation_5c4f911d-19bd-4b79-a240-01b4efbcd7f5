{"name": "node-ghmall-portal", "version": "1.0.0", "author": "tmtc", "email": "<EMAIL>", "description": "基于midway-react-ssr的商城前台，使用midwayjs作为中间层！", "private": true, "scripts": {"start": "ssr start", "start:vite": "ssr start --vite", "prod": "ssr build && pm2 start pm2.config.js", "prod:vite": "ssr build --vite && pm2 start pm2.config.js", "stop": "pm2 stop pm2.config.js", "build": "ssr build", "build:vite": "ssr build --vite", "deploy": "ssr build && ssr deploy", "lint": "eslint . --ext .js,.tsx,.ts --cache", "lint:fix": "eslint . --ext .js,.tsx,.ts --cache --fix", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:stylelint": "stylelint --cache --fix \"web/**/*.{less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:eslint-fix": "eslint --cache --max-warnings 0  \"{src,web}/**/*.{tsx,ts,js,jsx}\" --fix", "lint:prettier": "prettier --write  \"{web,src}/**/*.{js,jsx,tsx,css,less,scss,ts}\""}, "dependencies": {"@midwayjs/axios": "3", "@midwayjs/bootstrap": "^3.0.0", "@midwayjs/cache": "^3.0.0", "@midwayjs/core": "^3.0.0", "@midwayjs/cross-domain": "^3.3.6", "@midwayjs/decorator": "^3.0.0", "@midwayjs/http-proxy": "3.8.0", "@midwayjs/i18n": "3", "@midwayjs/jwt": "^3.3.6", "@midwayjs/koa": "^3.0.0", "@midwayjs/passport": "^3.0.0", "@midwayjs/redis": "^3.0.0", "@midwayjs/security": "^3.8.0", "@midwayjs/upload": "3", "@midwayjs/validate": "^3.0.0", "@midwayjs/view-ejs": "3", "antd": "^4.24.4", "antd-img-crop": "^4.5.2", "antd-mobile": "5.34.0", "cache-manager": "^3.4.1", "cache-manager-ioredis": "^2.1.0", "core-js": "3.27.1", "dotenv": "^16.0.1", "file-saver": "^2.0.5", "global": "^4.4.0", "js-cookie": "^2.2.0", "jsencrypt": "^3.3.1", "koa-static-cache": "^5.1.4", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "passport": "^0.5.2", "passport-jwt": "^4.0.0", "pm2": "^5.2.2", "qrcode.react": "^3.1.0", "react": "^17.0.0", "react-device-detect": "^2.2.3", "react-dom": "^17.0.0", "react-draggable": "^4.4.5", "react-helmet": "^6.1.0", "react-image-zoom": "^1.3.1", "react-intl": "^6.2.1", "react-lazyload": "^3.2.0", "react-router-dom": "^5.1.2", "react-transition-group": "^2.5.0", "ssr-core-react": "^6.0.0", "ssr-hoc-react": "^6.2.10", "ssr-server-utils": "^6.0.0", "style-resources-loader": "^1.5.0", "svg-captcha": "^1.4.0", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-require-transform": "^1.0.4", "weixin-js-sdk": "^1.6.5", "read-excel-file": "^5.8.6"}, "devDependencies": {"@babel/cli": "^7.20.7", "@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@commitlint/cli": "^17.2.0", "@commitlint/config-conventional": "^17.2.0", "@midwayjs/mock": "^3.0.0", "@types/cache-manager": "^3.4.0", "@types/file-saver": "^2.0.5", "@types/ioredis": "^4.28.7", "@types/lodash": "^4.14.182", "@types/passport": "^1.0.7", "@types/passport-jwt": "^3.0.6", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-router-dom": "^5.1.3", "eslint-config-standard-react-ts": "^1.0.5", "husky": "^8.0.2", "jssha": "^3.3.1", "lint-staged": "^13.0.3", "postcss": "^8.4.33", "postcss-html": "^1.5.0", "postcss-mobile-forever": "^4.1.1", "prettier": "^2.7.1", "rc-util": "^5.38.1", "ssr": "^6.0.0", "ssr-plugin-midway": "^6.0.0", "ssr-plugin-react": "^6.0.0", "ssr-types-react": "^6.0.0", "stylelint": "^14.15.0", "stylelint-config-prettier": "^9.0.4", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "svg-sprite-loader": "^6.0.11", "typescript": "^4.0.0"}, "engines": {"node": ">=16", "npm": ">= 8"}}