.wrapper {
  /*菜单导航栏变量*/
  @ucenterNavWidth: 200px;
  @ucenterNavContentMarginLeft: 15px;
  @navBackgroundDefaultColor: #fff;
  @navBackgroundActivateColor: #fff3f0;
  @navSubBackgroundColor: rgba(250, 250, 250, 0.65);
  @navFontColor: #434343;
  @navArrowColor: #595959;
  @navFontActivateColor: #ff5c58;
  @navFontSelectedColor: #ff5c58;
  @navFontSelectedBorderColor: #ff5c58;
  :global {
    .tm-menu-box {
      // 边框
      .ant-card-body {
        padding: 12px !important;
      }
      // 设置菜单样式
      .ant-menu,
      .ant-menu-sub,
      .ant-menu-inline {
        color: @navFontColor;
        background-color: @navBackgroundDefaultColor !important;
      }
      // 设置子菜单展开样式
      .ant-menu-submenu > .ant-menu {
        background-color: @navSubBackgroundColor !important;
      }
      .ant-menu-submenu-title {
        color: @navFontColor !important;
      }
      // 去掉右边框
      .ant-menu-inline {
        border: none;
        margin: 0;
      }
      // 设置 a 链接样式
      .ant-menu-item a {
        color: @navFontColor !important;
      }
      .ant-menu-item a:hover {
        color: @navFontActivateColor !important;
      }
      .ant-menu-item a:active:before {
        color: @navFontActivateColor !important;
      }
      .ant-menu-item a:active:after {
        color: @navFontActivateColor !important;
      }
      // 下拉箭头样式
      .ant-menu-submenu-arrow {
        color: @navArrowColor !important;
      }
      // 菜单收缩样式
      .ant-menu.ant-menu-inline-collapsed {
        width: 60px;
      }
      // 设置收缩后的右边框
      .ant-menu-inline,
      .ant-menu-vertical,
      .ant-menu-vertical-left {
        border-right: none;
      }
      // 气泡框样式
      .ant-tooltip-inner,
      .ant-tooltip-arrow-content {
        background-color: @navBackgroundActivateColor !important;
      }
      .ant-tooltip-inner a {
        color: @navFontColor !important;
      }
      // 收缩的样式
      .ant-layout-sider-trigger {
        background-color: @navBackgroundActivateColor !important;
      }
      // sider 的背景色
      .ant-layout-sider {
        background-color: @navBackgroundActivateColor !important;
      }

      // 选中菜单状态
      .ant-menu-item-selected {
        color: @navFontActivateColor !important;
        a {
          color: @navFontActivateColor !important;
        }
        &:after {
          border-right: 3px solid @navFontSelectedBorderColor;
        }
        background-color: @navBackgroundActivateColor !important;
      }
      // 设置未选中菜单项鼠标滑过样式
      .ant-menu-item-active {
        background-color: transparent;
        color: @navFontActivateColor !important;
      }
    }
  }
}
