import { Inject, Middleware } from "@midwayjs/decorator";
import { PassportMiddleware } from "@midwayjs/passport";
import * as passport from "passport";
import { JwtStrategy } from "@/strategy/jwt.strategy";
import { Context, NextFunction } from "@midwayjs/koa";
import { httpError } from "@midwayjs/core";
import { IAuthService } from "@/service/auth.service";

/**
 * @desc jwt中间件
 *
 * <AUTHOR>
 * @date 22/5/15
 */
@Middleware()
export class JwtPassportMiddleware extends PassportMiddleware(JwtStrategy) {
  @Inject("AuthService")
  authService: IAuthService;

  resolve(): any {
    return async (ctx: Context, next: NextFunction) => {
      const jwtToken = ctx.get("Authorization");
      const jwtCheck = jwtToken?.startsWith("Bearer");
      if (!jwtCheck) {
        throw new httpError.BadRequestError("token获取失败！");
      }
      const token = jwtToken.replace(/(^Bearer*)/g, "").trim();
      let payload: any;
      try {
        payload = await this.authService.parseJwtTokenToPayload(token);
      } catch (e) {
        throw new httpError.ForbiddenError("token不合法!");
      }
      if (!payload) {
        throw new httpError.ForbiddenError("token不合法!");
      }
      const redisToken = await this.authService.getRedisJwtToken(payload?.member_id);
      if (token !== redisToken) {
        throw new httpError.UnauthorizedError("token已失效了，请重新登录！");
      }
      if (!redisToken) {
        throw new httpError.UnauthorizedError("登录已失效，请重新登录！");
      }
      ctx.state.user = payload;
      return next();
    };
  }

  // 设置 AuthenticateOptions
  getAuthenticateOptions(): Promise<passport.AuthenticateOptions> | passport.AuthenticateOptions {
    return {};
  }
}
