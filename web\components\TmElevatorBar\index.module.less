.wrapper {
  :global {
    .tm-elevator-bar {
      position: fixed;
      z-index: 999;
      &-item {
        .flex-col(center, center);
        width: 64px;
        height: 64px;
        cursor: pointer;
        background: #fff;
        border: 1px solid #dedede;
        gap: 5px;
        margin-bottom: 4px;
        color: #6d7278;
        .svg-icon {
          font-size: 1.8em;
        }
        &:hover {
          background: #e02020;
          border: 1px solid #e02020;
          color: #fff;
          .svg-icon {
            color: #fff;
          }
        }
      }
      /*修改回到顶部样式*/
      .ant-back-top {
        .flex-col(flex-start, center);
        width: 64px;
        height: 64px;
        bottom: 125px;
        .back2top {
          .ant-btn {
            color: #6d7278;
            &:hover {
              color: #434343;
              border: 1px solid #8c8c8c;
            }
          }
        }
      }
    }
  }
}
