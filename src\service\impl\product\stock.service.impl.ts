import { Provide, Scope, <PERSON>opeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IStockService } from "@/service/product/stock.service";
import { productMaterialStockDto } from "~/typings/data/product/product";

@Provide("StockService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class StockServiceImpl extends BaseService implements IStockService {
  /**
   * 根据skuId查规格产品库存
   * @param criteria
   */
  async getSkuStockBySkuId(criteria: { skuId: number; inputVal: number }): Promise<any> {
    const result = await this.easyHttp.get(`/api/products/query/ncRealTimeStock`, criteria);
    // 库存返回值控制
    if (result.data.realStock >= Number(criteria.inputVal)) {
      result.data = {
        [criteria.skuId]: `库存充足`,
      };
    } else if (result.data.stock > 0) {
      result.data = {
        [criteria.skuId]: `请咨询客服`,
      };
    } else {
      // 没库存的情况
      result.data = {
        [criteria.skuId]: `请咨询客服`,
      };
    }
    return this.easyResponse(result);
  }

  /**
   * 个人中心-查库存列表-模糊搜索
   * @param criteria
   */
  async getMaterialStocks(criteria: productMaterialStockDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/products/material/stocks`, criteria));
  }
}
