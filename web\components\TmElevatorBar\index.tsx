import React, { memo, useContext, useEffect, useState } from "react";
import style from "./index.module.less";
import { BackTop, Badge, Button, Popover } from "antd";
import { VerticalAlignTopOutlined } from "@ant-design/icons";
import SvgIcon from "@/components/SvgIcon";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import GhWechatQrcode from "@/components/GhWechatQrcode";
import GhCustomerServiceQrcodes from "@/components/GhCustomerServiceQrcodes";

interface floatBarProps {
  right: string;
  bottom: string;
  initVisibilityHeight: number;
}

interface elevatorBarNavProps {
  icon: React.ReactNode | string | undefined;
  label: React.ReactNode | string;
  href?: string;
  isLink: boolean;
  isBlank?: boolean;
  onclick?: any;
  popup?: React.ReactNode | string | undefined;
}

const TmElevatorBar = (props: floatBarProps) => {
  const { bottom, right, initVisibilityHeight } = props;
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const shoppingCartHook = useShoppingCart();
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    shoppingCartHook.init();
    // 解决BackTop隐藏后仍可被点击
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // 自定义滚动条监听
  const handleScroll = () => {
    const currentScrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    setVisible(currentScrollTop > initVisibilityHeight);
  };

  const elevatorBarNav: elevatorBarNavProps[] = [
    {
      icon: (
        <Badge count={state.shoppingCartNum < 100 ? state.shoppingCartNum : 99}>
          <SvgIcon iconClass="home-cart" />
        </Badge>
      ),
      label: <span>购物车</span>,
      isLink: true,
      onclick: () => {
        shoppingCartHook.toggle();
      },
    },
    {
      icon: <SvgIcon iconClass="home-ucenter" />,
      label: "个人中心",
      href: "/ucenter",
      isLink: true,
    },
    {
      icon: <SvgIcon iconClass="home-customer" />,
      label: "在线客服",
      isLink: false,
      popup: (
          <GhCustomerServiceQrcodes />
      )
      // isBlank: true,
      // href: "https://p.qiao.baidu.com/cps/chat?siteId=11303754&userId=2127853",
    },
    {
      icon: <SvgIcon iconClass="home-phone" />,
      label: "电话热线",
      isLink: false,
      popup: <span className="tip">020-84382888转8836</span>,
    },
    {
      icon: <SvgIcon iconClass="home-wechat" />,
      label: "官方微信",
      isLink: false,
      popup: (
        <GhWechatQrcode />
      ),
    },
  ];
  return (
    <div className={style.wrapper}>
      <div
        className="tm-elevator-bar"
        style={{
          bottom: `${bottom}`,
          right: `${right}`,
        }}
      >
        {elevatorBarNav.map((item, key) => {
          return item.isLink ? (
            item?.onclick ? (
              <div className="tm-elevator-bar-item" key={key} onMouseEnter={() => shoppingCartHook.init()} onClick={item.onclick}>
                {item.icon}
                <span>{item.label}</span>
              </div>
            ) : (
              <a href={item.href} target={item?.isBlank ? "_blank" : ""} key={key} className="tm-elevator-bar-item">
                {item.icon}
                <span>{item.label}</span>
              </a>
            )
          ) : (
            <Popover overlayClassName="gh-wechat-popover" key={key} placement="left" content={item.popup} trigger="hover">
              <div className="tm-elevator-bar-item">
                {item.icon}
                <span>{item.label}</span>
              </div>
            </Popover>
          );
        })}
        {visible && (
          <BackTop visibilityHeight={initVisibilityHeight} style={{ right: right }}>
            <div className="back2top">
              <Button shape="circle" size="large" icon={<VerticalAlignTopOutlined />} />
            </div>
          </BackTop>
        )}
      </div>
    </div>
  );
};

TmElevatorBar.defaultProps = {
  bottom: "214px",
  right: "80px",
  initVisibilityHeight: 500,
};

export default memo(TmElevatorBar);
