import { Controller, Get, Inject, Param } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IProductService } from "@/service/product/product.service";

@Controller("/product")
export class ProductController extends BaseController {
  @Inject("ProductService")
  productService: IProductService;

  @Get("/:id")
  async detail(@Param("id") productNo: number | string): Promise<void> {
    const { ctx } = this;
    const res = await this.productService.detail(productNo);
    ctx.product = res.data;
    if (!res.data) {
      await this.renderNotfoundPage("抱歉，产品信息不存在或已下架！");
    } else {
      ctx.body = await render<Readable>(ctx, {
        stream: true,
      });
    }
  }
}
