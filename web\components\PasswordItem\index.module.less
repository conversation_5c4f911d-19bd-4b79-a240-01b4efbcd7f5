// prefix.
@ghmall-prefix: ghmall-;
@ghmall-lang-en: ~"@{ghmall-prefix}lang-en-us";
// color.
@ghmall-white: #fff;
@ghmall-black: #000;

//theme
@ghmall-theme: #52c41a;
@ghmall-theme-default: #bfbfbf;
@ghmall-theme-selection: #1890ff;
@ghmall-error: #ff4d4f;
@ghmall-font: @ghmall-black;
@ghmall-selection: fade(@ghmall-theme-selection, 80%);
// password
@ghmall-passport-strength: #999;
// gap.
@ghmall-gap: 8px;

// font-size.
@ghmall-font-size: 1rem;
@ghmall-font-size-sm: 12px;
@ghmall-font-size-normal: 14px;
@ghmall-font-size-base: 16px;

// properties
.properties(@key, @value: 8) {
  @{key}: (@value / @ghmall-font-size-base) * @ghmall-font-size;
}
// flex.
.flex-fix(@align: center, @justify: center, @direction: row) {
  display: flex;
  align-items: @align;
  justify-content: @justify;
  flex-direction: @direction;
}

@ghmall-password: ~"@{ghmall-prefix}password";
:global {
  .@{ghmall-password} {
    &-tips {
      .flex-fix(flex-start, center, column);
      color: var(--ghmall-font, @ghmall-font);
    }

    &-strength {
      &-item {
        .flex-fix(center, flex-start);
        .properties(line-height, 24);
        width: auto;

        &:first-child {
          justify-content: flex-start;
        }

        .anticon {
          .properties(font-size, 24);

          &.failed,
          &.success {
            .properties(font-size, 14);
            color: var(--ghmall-error, @ghmall-error);
            .properties(margin-right);
          }

          &.success {
            color: var(--ghmall-theme, @ghmall-theme);
          }
        }

        .theme-color {
          color: var(--ghmall-theme, @ghmall-theme);
        }
      }

      &-group {
        .flex-fix();
        .properties(margin-left);
        .properties(margin-right);

        .@{ghmall-password} {
          &-strength {
            .properties(width, 40);
            .properties(height, 12);
            background: var(--ghmall-passport-strength, @ghmall-passport-strength);
            display: inline-block;
            .properties(margin-right);

            &:last-child {
              margin-right: 0;
            }

            &.active {
              background: var(--ghmall-theme, @ghmall-theme);
            }
          }
        }
      }
    }

    &-input {
      border-color: var(--ghmall-theme, @ghmall-theme-default);
      outline: none;

      &:not(.ant-input-affix-wrapper-disabled) {
        &:hover,
        &:focus {
          border-color: var(--ghmall-theme, @ghmall-theme-selection);
          box-shadow: 0 0 2px fade(@ghmall-theme-selection, 20%);
        }
      }

      input.ant-input {
        background: transparent !important;
      }
    }
  }
  .@{ghmall-lang-en} {
    .@{ghmall-password} {
      &-strength {
        &-item {
          .properties(width, 520);
          .properties(margin-bottom);
          .properties(line-height, 20);

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
