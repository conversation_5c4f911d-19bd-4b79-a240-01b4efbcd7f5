@product-width: 168px;

.wrapper {
  :global {
    .hot-reagent {
      margin-top: @plate-margin-top;
      background-color: @main-bg-color-white;
      padding: 30px 30px 10px 30px;
      box-sizing: border-box;
      position: relative;
      // 头部栏
      .hot-reagent-header {
        .flex-row(space-between, center);
        width: 100%;
        height: 40px;
        .header-title {
          font-size: 32px;
          font-weight: 500;
          line-height: 1;
          color: @main-text-color;
        }
        .reagent-cates {
          list-style: none;
          .flex-row(flex-end, center);
          gap: 32px;
          li {
            _:-ms-fullscreen,
            & {
              margin-right: 32px;
              &:last-child {
                margin-right: 0;
              }
            }
          }
          .reagent-cate-item {
            width: 100%;
            font-size: 18px;
            font-weight: 400;
            line-height: 1;
            color: #6d7278;
            .flex-center(row);
            cursor: pointer;
            padding-bottom: 6px;
            &.active {
              font-weight: 700;
              color: #e02020;
              position: relative;
              left: 0;
              &::after {
                content: "";
                width: 100%;
                height: 2px;
                background-color: #e02020;
                position: absolute;
                bottom: 0%;
                left: 0%;
              }
            }
            &:hover {
              color: #e02020;
            }
          }
        }
      }
      .hot-reagent-container {
        width: 100%;
        display: flex;
        gap: 16px;
        margin-top: 16px;
        .reagent-products {
          width: 100%;
          _:-ms-fullscreen,
          & {
            margin: 0 8px;
          }
          margin-right: -16px;
          .theme-render-logo {
            img {
              width: 100%;
            }
          }
          & > * {
            display: inline-block;
            float: left;
            margin: 0 16px 16px 0;
          }
          li {
            _:-ms-fullscreen,
            & {
              margin: 0 8px;
            }
            width: @product-width;
          }
        }

        // 无产品
        .not-products {
          width: 100%;
          height: 300px !important;
          border: 1px dotted #f5f5f5;
          .flex-center(column) !important;
        }
      }
    }
  }
}
