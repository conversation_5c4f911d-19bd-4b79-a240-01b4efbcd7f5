/* 订单状态:-1-已取消;0-建单;10-收款;20-业务审核;30-财务确认;40-备货;50-发货;51-部分发货;60-评价;70-已完成 */
export const ORDER_STATE_MAPPING: object = {
  CANCEL: {
    value: -1,
    desc: "已取消",
  },
  BUILD: {
    value: 0,
    desc: "待审核",
  },
  PAID: {
    value: 10,
    desc: "已付款",
  },
  BUSINESS_AUDIT: {
    value: 20,
    desc: "待付款", // 业务审核
  },
  FINANCE_AUDIT: {
    value: 30,
    desc: "已确认", // 财务确认
  },
  DELIVER: {
    value: 40,
    desc: "发货中", // 备货中
  },
  IN_TRANSIT: {
    value: 50,
    desc: "运输中",
  },
  COMMENT: {
    value: 60,
    desc: "待评价",
  },
  FINISH: {
    value: 70,
    desc: "已完成",
  },
};

export function findOrderDescByValue(value: number) {
  return Object.values(ORDER_STATE_MAPPING).find(item => item.value === value)?.desc;
}

export function findOrderTagColorByValue(value: number | undefined) {
  if (!value) {
    return "error";
  }

  return value < 0 ? "error" : value >= 10 ? "success" : "processing";
}

/** 通用需开发票类型,0-13%增值税专用发票;1-普通发票;2-稍候填写 */
export const RECEIPT_TYPE_OPTIONS = {
  0: "13%增值税专用发票",
  1: "普通发票",
  2: "不开发票",
};

/**
 * 付款方式,0-100%预付;1-货到付款;2-预付款
 * @type {{"0": string, "1": string, "2": string}}
 */
export const PAYMENT_METHOD_OPTIONS = {
  0: "100%预付",
  1: "货到付款",
  2: "预付款",
};

/** 订单是否超时过期:0-否;1-是 */
export const ORDER_IS_EXPIRED = {
  0: "否",
  1: "是",
};

/** 付款状态:0=>未收款;1=>部分付款;2=>全部收款;3=>部分退款;-1=>全部退款 */
export const PAYMENT_STATE_OPTIONS = {
  0: "未收款",
  1: "部分付款",
  2: "全部收款",
  3: "部分退款",
  "-1": "全部退款"
}

/** 收款方式:11-线上支付;12-信用支付;13-货到付款;14-汇款;15-其它 */
export const COLLECTION_TYPE_OPTIONS = {
  11: '线上支付',
  12: '信用支付',
  13: '货到付款',
  14: '汇款',
  15: '其它'
}

export default {
  ORDER_STATE_MAPPING,
  findOrderDescByValue,
  RECEIPT_TYPE_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  findOrderTagColorByValue,
  ORDER_IS_EXPIRED,
  PAYMENT_STATE_OPTIONS,
  COLLECTION_TYPE_OPTIONS
};
