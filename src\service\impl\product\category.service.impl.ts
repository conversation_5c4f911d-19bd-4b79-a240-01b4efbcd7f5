import { Inject, Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IProductCategoryService } from "@/service/product/category.service";
import { CacheManager } from "@midwayjs/cache";
import { FORMAT } from "@midwayjs/core";
import cacheKeyConstant from "@/common/constants/cache.constant";

@Provide("ProductCategoryService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class ProductCategoryServiceImpl extends BaseService implements IProductCategoryService {
  @Inject()
  cacheManager: CacheManager;

  async cascade(): Promise<any> {
    // 缓存键
    const cacheKey = cacheKeyConstant.BUSINESS.PLATFORM_PRODUCT_CATEGORY_CASCADE;
    let res: any = await this.cacheManager.get(cacheKey);
    if (!res) {
      const result = await this.easyResponse(await this.easyHttp.get(`/api/product/categories/cascade`));
      if (result.data?.length) {
        await this.cacheManager.set(cacheKey, result.data, { ttl: FORMAT.MS.ONE_DAY / 1000 });
        res = result.data;
      }
    }
    return res;
  }
}
