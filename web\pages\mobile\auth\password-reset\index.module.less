.wrapper {
  :global {
    .auth-page {
      .flex-col();
      min-height: 100vh;

      .auth-container {
        margin: auto auto;
        width: 88%;
        //表单内容
        .auth-content {
          .flex-col();
          padding: 30px 10px 30px 10px;
          background-color: #fff;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
          opacity: 0.9;
          .navbar-box {
            display: flex;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            &-item {
              color: #999999;
              border-bottom: 2px solid #fff;
              padding-bottom: 2px;
              & ~ {
                margin-left: 16px;
              }
            }

            &-on {
              color: #282828;
              border-bottom-color: #f35749;
            }
          }

          .auth-form-box {
            .adm-form-footer {
              padding: 10px;
            }
            .item {
              .flex-row();
              font-size: 14px;
              padding: 2px 1px 2px 8px;
              label {
                width: 70px;
              }
              input {
                display: flex;
                flex: 1;
                font-size: 14px;
              }
            }

            .auth-submit-btn {
              width: 100%;
              font-size: 16px;
              color: #fff;
              font-weight: bold;
              border-radius: 10px;
              padding: 10px;
              background: linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
              background: -webkit-linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
              background: -moz-linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
              text-align: center;
            }
          }

          .auth-tip {
            color: #cccccc;
            font-size: 14px;
            text-align: center;
            &-forget {
              margin-right: 10px;
              text-decoration: underline;
            }
            &-login {
              color: #fc4141 !important;
              text-decoration: underline;
            }
          }
        }
        .auth-bottom {
          background-image: url("@@img/m/registerB.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          height: 19px;
          margin: 0 auto;
        }
      }
    }
  }
}
