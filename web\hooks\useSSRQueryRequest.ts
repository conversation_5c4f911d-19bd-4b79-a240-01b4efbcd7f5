/* 适用于SSR分页条件请求-hook */
import { useContext, useMemo, useRef, useState } from "react";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import qs from "qs";
import searchCrud from "@/apis/search/search";

interface queryProps {
  current: number; // 页码
  pageSize: number; // 每页数据条数
  total: number; // 总数据条数
  queryParams: any; // 请求参数
  url: string; // 目标url,默认为当前URL
  showSizeChanger: boolean; // 是否展示分页大小选择框
  showTotalText: boolean; // 是否展示总数文本
}

export const useSSRQueryRequest = (props: Partial<queryProps>) => {
  const defaultProps = {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotalText: true,
    url: __isBrowser__ && location.pathname,
  };
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const [ssrRequest, setSsrRequest] = useState<any>(Object.assign({}, defaultProps, props));
  const [tableData, setTableData] = useState<any>(state?.mallData ?? null);
  const ssrRequestRef = useRef(ssrRequest);
  // 分页改变操作
  const pageChangeHandler = async (page: number, pageSize: number) => {
    setSsrRequest(data => {
      data.current = page;
      data.pageSize = pageSize;
      return { ...data };
    });
    await searchEvent();
  };
  // 查询事件
  const searchEvent = async (params?: object) => {
    const requestParams = {
      ...getUrlParams(),
      ...ssrRequest.queryParams,
      ...params,
      page: params ? 1 : ssrRequest.current,
      size: ssrRequest.pageSize,
    };
    window.location.href = `${ssrRequest.url}?${qs.stringify(requestParams, { indices: false })}`;
    return false;
  };
  // 异步========>分页改变操作
  const pageChangeAsyncHandler = async (page: number, pageSize: number) => {
    setSsrRequest(data => {
      data.current = page;
      data.pageSize = pageSize;
      return { ...data };
    });
    ssrRequestRef.current.current = page;
    ssrRequestRef.current.pageSize = pageSize;
    await searchAsyncEvent();
  };
  // 异步========>异步搜索
  const searchAsyncEvent = async (params?: object) => {
    const requestParams = {
      ...getUrlParams(),
      ...ssrRequestRef.current.queryParams,
      ...params,
      page: params ? 1 : ssrRequestRef.current.current,
      size: ssrRequestRef.current.pageSize,
    };
    if (params) {
      ssrRequestRef.current.queryParams = { ...ssrRequestRef.current.queryParams, ...params };
    }
    // 保存 params
    const [err, res] = await searchCrud
      .fetchSearchList(requestParams)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return [];
    }
    setTableData(res.data);
  };
  // 获取表单数据
  const fetchTableDataFunc = () => {
    if (!tableData) {
      return {};
    }
    setSsrRequest(data => {
      data.current = tableData.currentPage;
      data.total = tableData.total;
      data.pageSize = tableData.pageSize;
      return { ...data };
    });
    return tableData;
  };
  const getTableData = useMemo(() => {
    return fetchTableDataFunc();
  }, [tableData]);
  // 获取分页信息
  const getPaginationOptions = () => {
    return {
      total: ssrRequest.total,
      current: ssrRequest.current,
      pageSize: ssrRequest.pageSize,
      showQuickJumper: true,
      showSizeChanger: ssrRequest.showSizeChanger,
      showTotal: _total => (ssrRequest?.showTotalText ? `共 ${_total} 条数据` : ""),
    };
  };
  // 获取当前 url params
  const getUrlParams = (): object => {
    return state?.urlCurrentParams ?? {};
  };
  return {
    getTableData,
    searchEvent,
    getUrlParams,
    pageChangeHandler,
    getPaginationOptions,
    searchAsyncEvent,
    pageChangeAsyncHandler,
  };
};
