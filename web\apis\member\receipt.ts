import request from "@/utils/request.util";
import { IReceiptType } from "@/typings/member.interface";
import { $tools } from "@/utils/tools.util";

export function getList(params?: object) {
  return request({
    url: "/api/ucenter/receipt",
    method: "get",
    params,
  });
}
export function show(receiptId: number | undefined) {
  return request({
    url: `/api/ucenter/receipt/${receiptId}/detail`,
    method: "get",
  });
}

/** 更新发票信息 */
export function add(receiptInfo: Partial<IReceiptType>) {
  $tools.removeFormFields(receiptInfo, ["area"]);

  return request({
    url: `/api/ucenter/receipt`,
    method: "POST",
    data: receiptInfo,
  });
}

/** 更新地址信息 */
export function edit(receiptId: number, receiptInfo: IReceiptType) {
  $tools.removeFormFields(receiptInfo, ["area"]);

  return request({
    url: `/api/ucenter/receipt/${receiptId}`,
    method: "PUT",
    data: receiptInfo,
  });
}

/** 设置默认收货地 */
export function setDefaultReceipt(receiptId: number) {
  return request({
    url: `/api/ucenter/receipt/${receiptId}/default`,
    method: "PUT",
  });
}

/** 主动删除某个收货地 */
export function remove(receiptId: number) {
  return request({
    url: `/api/ucenter/receipt/${receiptId}`,
    method: "DELETE",
  });
}

export default {
  getList,
  remove,
  setDefaultReceipt,
  edit,
  add,
  show,
};
