/* eslint-disable */
const zh_CN = {
  language: "简体中文",
  start: "开始",
  switch: "切换",
  say: "{name} 说:你好！",
  test: "测试 | 测试 | 测试 | 测试",
  "mall.success": "成功！",
  "lang.changeSuccess": "语言切换成功",
  "lang.changeFail": "语言切换成功",
  searchText: "搜索",
  /*======== 配置密码组件国际化-start ========*/
  "password.lv1": "弱不禁风",
  "password.lv2": "平淡无奇",
  "password.lv3": "出神入化",
  "password.lv4": "登峰造极",
  "password.tip": "需包含字母、数字及特殊字符两种或以上组合",
  "password.placeholder": "请输入密码",
  "password.repeat": "请再次输入密码",
  "password.strong": "密码强度",
  "password.size": "{min}-{max}个字符，区分大小写，前后无空格",
  "password.format": "字母、数字、英文、下划线等其他特殊字符",
  "password.different": "两次密码输入不一致",
  "password.setting": "请设置密码",
  "password.least": "密码长度至少为{min}个字符",
  /*======== 配置密码组件国际化-end ========*/
};
export default zh_CN;
