import React, { useState, useEffect, useContext } from "react";
import { Card, Table, Button, Tag, Descriptions, message, Modal, Form, InputNumber, Input, Row, Col } from "antd";
import { EditOutlined, ShoppingCartOutlined, FileTextOutlined, DeleteOutlined } from "@ant-design/icons";
import { IContext, SProps } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import { useLoading } from "@/hooks/useLoading";
import quoteApi from "@/apis/quote/quote";
import { ghmallGuidePrice2Show } from "@/utils/price-format.util";
import { QuoteStatus, ProductMatchStatus } from "../../../../typings/data/quote";
import style from "./index.module.less";

const { TextArea } = Input;

export default function QuoteDetail(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const { quoteDetail } = props.fetchData;
  const useLoadingHook = useLoading();
  
  const [detail, setDetail] = useState(quoteDetail);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [quoteModalVisible, setQuoteModalVisible] = useState(false);
  const [orderModalVisible, setOrderModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 获取状态标签
  const getStatusTag = (status: QuoteStatus) => {
    const statusConfig = {
      [QuoteStatus.DRAFT]: { color: "default", text: "草稿" },
      [QuoteStatus.PENDING]: { color: "processing", text: "待报价" },
      [QuoteStatus.QUOTED]: { color: "success", text: "已报价" },
      [QuoteStatus.ACCEPTED]: { color: "success", text: "已接受" },
      [QuoteStatus.REJECTED]: { color: "error", text: "已拒绝" },
      [QuoteStatus.EXPIRED]: { color: "default", text: "已过期" },
    };
    
    const config = statusConfig[status] || { color: "default", text: "未知" };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取产品匹配状态标签
  const getMatchStatusTag = (status: ProductMatchStatus) => {
    switch (status) {
      case ProductMatchStatus.MATCHED:
        return <Tag color="success">已匹配</Tag>;
      case ProductMatchStatus.PARTIAL_MATCH:
        return <Tag color="warning">部分匹配</Tag>;
      case ProductMatchStatus.NOT_MATCHED:
        return <Tag color="error">未匹配</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "产品信息",
      key: "productInfo",
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.productName}
          </div>
          <div style={{ fontSize: 12, color: "#666" }}>
            SKU: {record.sku}
          </div>
          {record.brandName && (
            <div style={{ fontSize: 12, color: "#666" }}>
              品牌: {record.brandName}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "规格",
      dataIndex: "specification",
      key: "specification",
      width: 120,
      render: (spec: string, record: any) => (
        <div>
          {spec || (record.packingRatio ? `${record.packingRatio}${record.unit || ""}` : "-")}
        </div>
      ),
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      width: 100,
    },
    {
      title: "预估价格",
      dataIndex: "estimatedPrice",
      key: "estimatedPrice",
      width: 120,
      render: (price: number) => (
        <span style={{ color: "#f50" }}>
          {price ? ghmallGuidePrice2Show(price) : "询价"}
        </span>
      ),
    },
    {
      title: "报价",
      dataIndex: "quotedPrice",
      key: "quotedPrice",
      width: 120,
      render: (price: number) => (
        <span style={{ color: "#52c41a", fontWeight: 500 }}>
          {price ? ghmallGuidePrice2Show(price) : "-"}
        </span>
      ),
    },
    {
      title: "匹配状态",
      dataIndex: "matchStatus",
      key: "matchStatus",
      width: 100,
      render: (status: ProductMatchStatus) => getMatchStatusTag(status),
    },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
      width: 150,
      render: (remark: string) => remark || "-",
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.matchStatus === ProductMatchStatus.NOT_MATCHED,
    }),
  };

  // 生成报价单
  const handleGenerateQuote = async (values: any) => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要报价的产品！");
      return;
    }

    try {
      useLoadingHook.showLoading("生成报价单中...");
      
      const selectedProducts = detail.products.filter(p => 
        selectedRowKeys.includes(p.id)
      );

      const [err, res] = await quoteApi
        .generateQuote(detail.id, selectedProducts, values.validDays, values.remark)
        .then(res => [null, res])
        .catch(err => [err, null]);

      useLoadingHook.hideLoading();

      if (err) {
        message.error(err?.data?.message || "生成报价单失败！");
        return;
      }

      message.success("报价单生成成功！");
      setQuoteModalVisible(false);
      
      // 刷新详情
      window.location.reload();
    } catch (error) {
      useLoadingHook.hideLoading();
      message.error("生成报价单失败！");
    }
  };

  // 创建快速订单
  const handleCreateQuickOrder = async (values: any) => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要下单的产品！");
      return;
    }

    try {
      useLoadingHook.showLoading("创建订单中...");
      
      const [err, res] = await quoteApi
        .createQuickOrderFromQuote(detail.id, selectedRowKeys as number[], values)
        .then(res => [null, res])
        .catch(err => [err, null]);

      useLoadingHook.hideLoading();

      if (err) {
        message.error(err?.data?.message || "创建订单失败！");
        return;
      }

      message.success("订单创建成功！");
      setOrderModalVisible(false);
      
      // 跳转到订单页面
      window.location.href = `/ucenter/trade/order?status=0`;
    } catch (error) {
      useLoadingHook.hideLoading();
      message.error("创建订单失败！");
    }
  };

  if (!detail) {
    return (
      <div className={style.wrapper}>
        <div className="container">
          <Card title="询报价详情">
            <div style={{ textAlign: "center", padding: "50px 0" }}>
              询报价单不存在或已删除
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={style.wrapper}>
      <div className="container">
        <Card 
          title={`询报价详情 - ${detail.title}`}
          extra={getStatusTag(detail.status)}
        >
          {/* 基本信息 */}
          <Descriptions title="基本信息" bordered column={2} style={{ marginBottom: 24 }}>
            <Descriptions.Item label="询报价单号">{detail.quoteNo || detail.id}</Descriptions.Item>
            <Descriptions.Item label="状态">{getStatusTag(detail.status)}</Descriptions.Item>
            <Descriptions.Item label="联系人">{detail.contactName}</Descriptions.Item>
            <Descriptions.Item label="联系电话">{detail.contactPhone}</Descriptions.Item>
            <Descriptions.Item label="联系邮箱">{detail.contactEmail || "-"}</Descriptions.Item>
            <Descriptions.Item label="公司名称">{detail.companyName || "-"}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{detail.createdAt}</Descriptions.Item>
            <Descriptions.Item label="有效期">{detail.expiresAt || "-"}</Descriptions.Item>
            <Descriptions.Item label="描述" span={2}>
              {detail.description || "-"}
            </Descriptions.Item>
          </Descriptions>

          {/* 产品列表 */}
          <div style={{ marginBottom: 24 }}>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 16 }}>
              <h3>产品列表</h3>
              <div>
                <Button
                  type="primary"
                  icon={<FileTextOutlined />}
                  onClick={() => setQuoteModalVisible(true)}
                  disabled={selectedRowKeys.length === 0}
                  style={{ marginRight: 8 }}
                >
                  生成报价单 ({selectedRowKeys.length})
                </Button>
                <Button
                  type="default"
                  icon={<ShoppingCartOutlined />}
                  onClick={() => setOrderModalVisible(true)}
                  disabled={selectedRowKeys.length === 0}
                >
                  快速下单 ({selectedRowKeys.length})
                </Button>
              </div>
            </div>
            
            <Table
              columns={columns}
              dataSource={detail.products || []}
              rowSelection={rowSelection}
              rowKey="id"
              pagination={false}
              size="small"
              scroll={{ x: 800 }}
            />
          </div>
        </Card>

        {/* 生成报价单弹窗 */}
        <Modal
          title="生成报价单"
          open={quoteModalVisible}
          onCancel={() => setQuoteModalVisible(false)}
          footer={null}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleGenerateQuote}
            initialValues={{ validDays: 30 }}
          >
            <Form.Item
              label="报价有效天数"
              name="validDays"
              rules={[{ required: true, message: "请输入有效天数" }]}
            >
              <InputNumber min={1} max={365} style={{ width: "100%" }} />
            </Form.Item>
            
            <Form.Item
              label="报价备注"
              name="remark"
            >
              <TextArea rows={4} placeholder="请输入报价备注（选填）" />
            </Form.Item>
            
            <Form.Item>
              <Button type="primary" htmlType="submit" block>
                生成报价单
              </Button>
            </Form.Item>
          </Form>
        </Modal>

        {/* 创建订单弹窗 */}
        <Modal
          title="创建快速订单"
          open={orderModalVisible}
          onCancel={() => setOrderModalVisible(false)}
          footer={null}
        >
          <Form
            layout="vertical"
            onFinish={handleCreateQuickOrder}
          >
            <Form.Item
              label="收货地址ID"
              name="receivingAddressId"
              rules={[{ required: true, message: "请选择收货地址" }]}
            >
              <InputNumber min={1} style={{ width: "100%" }} placeholder="请输入收货地址ID" />
            </Form.Item>
            
            <Form.Item
              label="发票ID"
              name="receiptId"
            >
              <InputNumber min={1} style={{ width: "100%" }} placeholder="请输入发票ID（选填）" />
            </Form.Item>
            
            <Form.Item
              label="配送方式"
              name="sendType"
            >
              <Input placeholder="请输入配送方式（选填）" />
            </Form.Item>
            
            <Form.Item
              label="订单备注"
              name="remark"
            >
              <TextArea rows={4} placeholder="请输入订单备注（选填）" />
            </Form.Item>
            
            <Form.Item>
              <Button type="primary" htmlType="submit" block>
                创建订单
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
}
