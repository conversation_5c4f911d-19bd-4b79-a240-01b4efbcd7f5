import { AL<PERSON>, Body, Controller, Del, FORMAT, Get, HttpCode, Inject, Post, Query, SetHeader } from "@midwayjs/decorator";
import { httpError } from "@midwayjs/core";
import { BaseController } from "@/controller/base.controller";
import { RedisService } from "@midwayjs/redis";
import { IToolService } from "@/service/tool.service";
import { Validate } from "@midwayjs/validate";
import { AuthRegisterDto, AuthRegisterEmailDto } from "@/dto/auth-register.dto";
import { AuthLoginDto } from "@/dto/auth-login.dto";
import { IAuthService } from "@/service/auth.service";
import authConstant from "@/common/constants/auth.constant";
import { AuthSendSmsDto } from "@/dto/auth-send-sms.dto";
import { AuthLoginSmsDto } from "@/dto/auth-login-sms.dto";
import { AuthEmailPasswordResetDto, AuthPasswordResetDto } from "@/dto/auth-password-reset.dto";
import { ISmsService } from "@/service/platform/sms.service";
import { PlatformSmsSendDto } from "@/dto/platform-sms-send.dto";
import { PlatformSmsVerifyDto } from "@/dto/platform-sms-verify.dto";
import { bsLoginDto } from "@/dto/auth.dto";
import { IEmailService } from "@/service/platform/email.service";
import { PlatformSendEmailDto } from "@/dto/platform-send-email.dto";

@Controller("/api/auth")
export class AuthController extends BaseController {
  @Inject("ToolService")
  toolService: IToolService;

  @Inject()
  redisService: RedisService;

  @Inject("AuthService")
  authService: IAuthService;

  @Inject("PlatformSmsService")
  smsService: ISmsService;

  @Inject("PlatformEmailService")
  emailService: IEmailService;

  /**
   * <p>通过账号密码登录-session</p>
   *
   * @param loginDto 登录dto
   */
  @Post("/login")
  @Validate()
  @HttpCode(200)
  async loginByPassword(@Body() loginDto: AuthLoginDto) {
    const { ctx } = this;
    const loginData = {
      account: loginDto.phone,
      password: loginDto.password,
      requestIp: ctx.ip,
      companyName: loginDto.companyName,
    };

    // 1-请求java接口校验是否正确登录
    await this.authService.loginByPassword(loginData);
    // 2-获取用户信息
    // const memberResult = await this.authService.getMemberInfo(loginDto.phone);
    const memberResult = await this.authService.getAccountInfo(loginDto.phone, loginDto.companyName);
    const member = memberResult.data ?? [];
    // 写入session
    await this.authService.saveOrUpdateSession(ctx, member);
    return ctx
      .getResponseInstance(ctx)
      .setResponseData({ ...member })
      .sendSuccess("登录成功");
  }

  /**
   * <p>通过短信验证码登录</p>
   *
   * @param loginSmsDto 登录dto
   */
  @Post("/sms-login")
  @Validate()
  @HttpCode(200)
  async loginBySmsCode(@Body() loginSmsDto: AuthLoginSmsDto) {
    const { ctx } = this;
    // 1、处理短信验证码是否正确
    await this.validAccountSmsVerifyCode(ctx, loginSmsDto.phone, loginSmsDto.smsCode);
    // 2、调用短信登录接口
    const loginData = {
      account: loginSmsDto.phone,
      smsCode: loginSmsDto.smsCode,
      requestIp: ctx.ip,
      companyName: loginSmsDto.companyName ? loginSmsDto.companyName : "",
    };
    const memberResult = await this.authService.loginBySms(loginData);
    const member = memberResult.data ?? [];
    // 写入session
    await this.authService.saveOrUpdateSession(ctx, member);
    return ctx.getResponseInstance(ctx).sendSuccess("登录成功");
  }

  /**
   * <p>通过手机号注册:api</p>
   *
   * @param registerDto 注册dto
   */
  @Post("/register")
  @Validate()
  async register(@Body() registerDto: AuthRegisterDto) {
    AuthController.validAccountPasswordEqual(registerDto.password, registerDto.confirmPassword);
    const { ctx } = this;
    // 1、处理短信验证码是否正确
    await this.validAccountSmsVerifyCode(ctx, registerDto.phone, registerDto.smsCode);
    // 2、调用注册接口
    registerDto.requestIp = ctx.ip;
    const result = await this.authService.register(registerDto);
    // 3、是否需要登录，注册成功自动登录
    // const memberResult = await this.authService.getMemberInfo(registerDto.phone);
    const memberResult = await this.authService.getAccountInfo(registerDto.phone, registerDto.companyName);
    const member = memberResult.data ?? [];
    // 写入session
    await this.authService.saveOrUpdateSession(ctx, member);
    return ctx.getResponseInstance(ctx).setResponseData(result?.data).sendSuccess("注册成功！", 201);
  }

  /**
   * <p>通过邮箱注册</p>
   *
   * @param registerDto 注册dto
   */
  @Post("/email-register")
  @Validate()
  async registerByEmail(@Body() registerDto: AuthRegisterEmailDto) {
    const { ctx } = this;
    AuthController.validAccountPasswordEqual(registerDto.password, registerDto.confirmPassword);
    // 2、调用注册接口
    registerDto.requestIp = ctx.ip;
    const result = await this.authService.registerByEmail(registerDto);
    // 3、是否需要登录，注册成功自动登录
    // const memberResult = await this.authService.getMemberInfo(registerDto.email);
    const memberResult = await this.authService.getAccountInfo(registerDto.email, registerDto.companyName);
    const member = memberResult.data ?? [];
    // 写入session
    await this.authService.saveOrUpdateSession(ctx, member);
    return ctx.getResponseInstance(ctx).setResponseData(result?.data).sendSuccess("注册成功！", 201);
  }

  /**
   * <p>登出</p>
   */
  @Del("/logout")
  async logout() {
    const { ctx } = this;
    const session = ctx.session;
    if (session) {
      session.userLoginState = null;
      session.userData = null;
    }
    return ctx.getResponseInstance(ctx).sendSuccess("退出成功！", 200);
  }

  /**
   * <p>通过手机号重置密码</p>
   *
   * @param passwordResetDto /
   */
  @Post("/password-reset")
  @Validate()
  async passwordReset(@Body() passwordResetDto: AuthPasswordResetDto) {
    const { ctx } = this;
    AuthController.validAccountPasswordEqual(passwordResetDto.password, passwordResetDto.confirmPassword);
    // 1、处理短信验证码是否正确
    await this.validAccountSmsVerifyCode(ctx, passwordResetDto.phone, passwordResetDto.smsCode);
    // 2、调用重置接口
    await this.authService.passwordReset(passwordResetDto);
    return ctx.getResponseInstance(ctx).sendSuccess("重置密码成功！");
  }

  /**
   * <p>通过邮箱重置密码</p>
   *
   * @param passwordResetDto /
   */
  @Post("/email-password-reset")
  @Validate()
  async passwordResetByEmail(@Body() passwordResetDto: AuthEmailPasswordResetDto) {
    const { ctx } = this;
    AuthController.validAccountPasswordEqual(passwordResetDto.password, passwordResetDto.confirmPassword);
    await this.authService.passwordResetByEmail(passwordResetDto);
    return ctx.getResponseInstance(ctx).sendSuccess("重置密码成功！");
  }

  /**
   * <p>获取图形验证码</p>
   */
  @Get("/captcha-code")
  @SetHeader("Content-Type", "image/svg+xml")
  async getCaptchaCode() {
    const captcha = await this.toolService.generateCaptchaCode(); /* 获取服务里面返回的生成的验证码信息 */
    this.ctx.session.authCaptchaCode = captcha.text; /* 验证码上面的信息,文字内容存放到session里面 */
    this.ctx.session.maxAge = FORMAT.MS.ONE_MINUTE * authConstant.AUTH_CAPTCHA_CODE_EXPIRE;
    this.ctx.body = captcha.data; /* 给页面返回一张图片 */
  }

  /**
   * <p>发送短信验证码</p>
   *
   * @param smsDto 短信验证码dto
   */
  @Post("/send-sms")
  @Validate()
  async sendSmsCode(@Body() smsDto: AuthSendSmsDto) {
    const { ctx } = this;
    const validCodeFail = smsDto.captchaCode.toLowerCase() !== ctx.session?.authCaptchaCode?.toLowerCase();
    if (validCodeFail) {
      throw new httpError.BadRequestError("图形验证码不正确！");
    }
    // 图形验证码失效
    ctx.session.authCaptchaCode = null;
    // 减少无效短信发送-判断手机是否已注册
    if (smsDto.type === "register") {
      // const memberResult = await this.authService.getMemberInfo(smsDto.phone);
      const memberResult = await this.authService.getAccountInfo(smsDto.phone, smsDto.companyName);
      if (memberResult?.data?.memberId) {
        throw new httpError.BadRequestError(`${smsDto.phone}手机号已被客户${memberResult?.data?.companyName}(${memberResult?.data?.memberId})注册，请直接登录！`);
      }
    }
    // 请求接口发送短信验证码
    const smsSendDto: PlatformSmsSendDto = new PlatformSmsSendDto();
    smsSendDto.phone = smsDto.phone;
    smsSendDto.ip = ctx.ip;
    const result = await this.smsService.sendVerifyCode(smsSendDto);
    return ctx.getResponseInstance(ctx).setResponseData(result.data).sendSuccess(`${smsDto.phone} 短信验证码发送成功！`);
  }

  /**
   * <p>一键登录</p>
   *
   * @param dto /
   */
  @Get("/bslogin")
  @Validate()
  async handleOneClickLogin(@Query(ALL) dto: bsLoginDto): Promise<any> {
    const { ctx } = this;
    dto.requestIp = ctx.ip ?? "";
    const memberResult = await this.authService.loginByAuthCode(dto);
    if (!memberResult?.data) {
      return ctx.redirect("/ucenter/trade/order");
    }
    const member = memberResult.data ?? [];
    // 写入session
    await this.authService.saveOrUpdateSession(ctx, member);
    return ctx.redirect("/ucenter/member");
  }

  /**
   * <p>发送邮件验证码</p>
   *
   * @param sendEmailCodeDto 邮件验证码dto
   */
  @Post("/send-email-code")
  @Validate()
  async sendEmailCode(@Body() sendEmailCodeDto: PlatformSendEmailDto) {
    const { ctx } = this;
    // 请求接口发送邮件验证码
    const result = await this.emailService.sendVerifyCode(sendEmailCodeDto);
    return ctx.getResponseInstance(ctx).setResponseData(result.data).sendSuccess(`${sendEmailCodeDto.email} 邮件验证码发送成功！`);
  }

  private static validAccountPasswordEqual(password: string, confirmPassword: string) {
    if (password !== confirmPassword) {
      throw new httpError.BadRequestError({ message: "两次输入的密码不一致，请核对后重试！" });
    }
  }

  /**
   *
   * @param ctx 必须携带ctx避免  Cannot read properties of undefined (reading 'url')错误
   * @param phone /
   * @param smsCode /
   * @private /
   */
  private async validAccountSmsVerifyCode(ctx: any, phone: string, smsCode: string) {
    const smsVerifyDto: PlatformSmsVerifyDto = new PlatformSmsVerifyDto();
    smsVerifyDto.phone = phone;
    smsVerifyDto.smsCode = smsCode;
    const result1 = await this.smsService.checkVerifyCode(smsVerifyDto);
    if (!result1.data.verify) {
      throw new httpError.BadRequestError({ message: "短信验证码不正确！" });
    }
  }

  @Get("/allAccountInfoByPhone")
  async getAllAccountInfoByPhone(@Query("phone") phone: string): Promise<any> {
    const { ctx } = this;
    const res = await this.authService.getAccountInfoByPhone(phone);
    return ctx.getResponseInstance(ctx).setResponseData(res).send();
  }
}
