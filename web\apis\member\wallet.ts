import request from "@/utils/request.util";

/** 获取账户余额 */
export function getWallet(params) {
  return request({
    url: "/api/ucenter/my-wallet/wallet",
    method: "get",
    data: params,
  });
}

/** 获取交易流水记录 */
export function getWalletBillFlow(params) {
  return request({
    url: "/api/ucenter/my-wallet/bill-flow",
    method: "get",
    params,
  });
}

/** 获取充值订单记录 */
export function getWalletRecharge(params) {
  return request({
    url: "/api/ucenter/my-wallet/recharge",
    method: "get",
    params,
  });
}

/** 充值 */
export function tradeRecharge(form: { price: number }) {
  return request({
    url: "/api/ucenter/my-wallet/trade-recharge",
    method: "post",
    data: form,
  });
}

/** 取消充值订单 */
export function cancelRechargeOrder(rechargeSn) {
  return request({
    url: "/api/ucenter/my-wallet/recharge-cancel",
    method: "delete",
    data: {
      rechargeSn,
    },
  });
}

export default {
  getWallet,
  getWalletBillFlow,
  getWalletRecharge,
  tradeRecharge,
  cancelRechargeOrder,
};
