import React, { useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import style from "./index.module.less";
import { Button, Table, Space, Checkbox, Empty, Modal, message, notification } from "antd";
import { ColumnsType } from "antd/es/table";
import { ShoppingCartOutlined, ReloadOutlined } from "@ant-design/icons";
import * as shoppingCartCrud from "@/apis/trade/shopping-cart";
import { price2Thousand, ghmallGuidePrice2Show } from "@/utils/price-format.util";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import { tradeCartDataType } from "@/typings/shopping-cart.interface";
import { useProductCollection } from "@/hooks/useProductCollection";
import { debounce } from "lodash";
import Add2CartCnpt from "./parts/Add2CartCnpt";
import commonConstant from "@/constants/common";
import SalesHistoryDialog from "@/components/SalesHistoryDialog";

export default function TradeShoppingCartIndex(props: SProps) {
  const tableColumns: ColumnsType<tradeCartDataType> = [
    {
      title: "行号",
      width: 44,
      align: "center",
      key: "idx",
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "SKU",
      dataIndex: "productSku",
      key: "productSku",
      align: "center",
      render: (text, record, index) => (
        <a className="product-detail-link" href={`/product/${record.product.productNo}?sku=${record.product.sku}`} target="_blank">
          {record.product.sku}
        </a>
      ),
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      key: "productName",
      align: "center",
      render: (text, record, index) => <span className="product-name">{record.product.skuName ? record.product.skuName : record.product.productNameZh}</span>,
    },
    {
      title: "品牌",
      dataIndex: "brandName",
      key: "brandName",
      align: "center",
      render: (text, record) => <span>{record.product.brandName}</span>,
    },
    {
      title: "包装",
      dataIndex: "productPacking",
      key: "productPacking",
      align: "center",
      render: (text, record, index) => <span>{!record.product?.packing || record.product.packing === "1" ? "-" : record.product.packing}</span>,
    },
    {
      title: "起订量",
      dataIndex: "packingRatio",
      key: "packingRatio",
      align: "center",
      width: 80,
      render: (text, record, index) => record.product.packingRatio || 1,
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      align: "center",
      width: 140,
      render: (_, record) => <Add2CartCnpt record={record} callbackFunc={async () => await getShoppingCartList()} />,
    },
    {
      title: "单位",
      dataIndex: "unit",
      key: "unit",
      align: "center",
      render: (text, record, index) => record.product.unit || "-",
    },
    {
      title: "件数",
      dataIndex: "itemsQuantity",
      key: "itemsQuantity",
      align: "center",
      render: (text, record, index) => record.product.itemsQuantity || "-",
    },
    {
      title: "单价",
      dataIndex: "productTaxPrice",
      key: "productTaxPrice",
      align: "center",
      render: (text, record, index) => (
        <>
          <span>{ghmallGuidePrice2Show(record.product.discountPrice)}</span>
          <Button style={{ fontSize: "12px" }} type="link" onClick={async () => await handleViewHistoryPrice(record)} size="small">
            历史价
          </Button>
        </>
      ),
    },
    {
      title: "小计",
      dataIndex: "totalPrice",
      key: "totalPrice",
      align: "center",
      render: (text, record, index) => `${record.product.discountPrice === 0 ? "询价" : price2Thousand(record.product.discountPrice * record.quantity)}`,
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record) => (
        <>
          <Space direction="horizontal" size="small">
            <Button ghost type="primary" size="small" onClick={async () => await delCurrentEvent(record.id)}>
              移除
            </Button>
            <Button ghost type="primary" size="small" onClick={async () => await collectProductEvent(record.productSkuId)}>
              收藏
            </Button>
          </Space>
        </>
      ),
    },
  ];
  /* ======================================= use state start======================================= */
  const [tableDataSource, setTableDataSource] = useState<tradeCartDataType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [currentProduct, setCurrentProduct] = useState<any>("");
  const [historyPriceVisible, setHistoryPriceVisible] = useState<boolean>(false);
  // 选中数量
  const [selectedNum, setSelectedNum] = useState<number>(0);
  // 选中产品金额
  const [selectedFlowPrice, setSelectedFlowPrice] = useState<number>(0);
  const shoppingCartHook = useShoppingCart();
  const collectionHooks = useProductCollection();
  /* ======================================= use state end======================================= */

  /* ======================================= method start======================================= */
  const handleTableCheckboxChange = async (selectedRowKeys: React.Key[], selectedRows: tradeCartDataType[]) => {
    const keys: number[] | any = selectedRows.map(item => item.id);
    setSelectedRowKeys(keys);
    setSelectedRows([...selectedRows]);
  };
  const rowSelectionEvent = {
    hideSelectAll: true,
    onChange: handleTableCheckboxChange,
    getCheckboxProps: (record: tradeCartDataType) => ({}),
    onSelect: async function (record, selected, selectedRows, nativeEvent) {
      // 调用选中接口
      await shoppingCartCrud.selectedOrNot({
        productSkuId: record.productSkuId,
        selected: selected ? 1 : 0,
      });
    },
    // 选中的值
    selectedRowKeys: selectedRowKeys,
  };
  // 前往结算页
  const go2payEvent = async () => {
    if (selectedNum === 0) {
      notification.warning({ message: "请至少选择一件产品" });
    } else {
      // 前往结算页
      window.location.href = "/pay/confirm";
    }
  };
  // 手动全选|不全选
  const onCheckAllEvent = async (e: CheckboxChangeEvent) => {
    // 若已经是全选状态，当再次点击全选按钮的时候，则就执行取消全选操作；
    // 若不是全选状态，点击全选按钮的时候，就执行全选操作
    if (e.target.checked) {
      const keys: number[] | any = tableDataSource.map(item => item.id);
      setSelectedRowKeys(keys);
      await handleTableCheckboxChange([], [...tableDataSource]);
    } else {
      await handleTableCheckboxChange([], []);
    }
    // 调用全选接口
    await shoppingCartCrud.selectedAll({ selected: e.target.checked ? 1 : 0 });
  };
  // 删除当前行产品
  const delCurrentEvent = async (id: any) => {
    Modal.confirm({
      content: "您确定删除此条购物车产品吗？",
      type: "warning",
      onOk: async () => {
        const [err, res] = await shoppingCartCrud
          .del({ ids: id })
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (!err) {
          await getShoppingCartList();
          message.success(res.message || "删除成功");
        }
      },
    });
  };
  // 清空所选
  const delSelectedEvent = () => {
    Modal.confirm({
      content: `您确定删除所选的 ${selectedRowKeys.length} 条购物车产品吗？`,
      type: "warning",
      centered: true,
      onOk: async () => {
        const [err, res] = await shoppingCartCrud
          .del({ ids: selectedRowKeys.join(",") })
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (!err) {
          await getShoppingCartList();
          message.success(res.message || "删除成功");
        }
      },
    });
  };
  // 清空全部购物车
  const removeAllEvent = () => {
    Modal.confirm({
      content: `您确定清空购物车产品吗？`,
      type: "warning",
      centered: true,
      onOk: async () => {
        const ids = tableDataSource.map(item => item.id).join(",");
        const [err, res] = await shoppingCartCrud
          .del({ ids })
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (!err) {
          await getShoppingCartList();
          message.success("清空成功" || res.message);
        }
      },
    });
  };
  // 收藏事件
  const collectProductEvent = debounce(async (skuId: any) => {
    const hasCollection = await collectionHooks.isCollection(skuId);
    if (!hasCollection) {
      await collectionHooks.create({ skuId });
    } else {
      message.warning("该产品已收藏~");
    }
  }, 500);
  // 获取购物车列表数据
  const getShoppingCartList = async () => {
    setLoading(true);
    const [err, res] = await shoppingCartCrud
      .getList()
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoading(false);
    if (!err) {
      const data: tradeCartDataType[] = res.data;
      setTableDataSource(data);
      const selectedData: any = data.filter(item => item.selected);
      setSelectedRows(selectedData);
      setSelectedRowKeys(selectedData.map(item => item.id));
      await shoppingCartHook.init();
    }
  };
  const handleViewHistoryPrice = async (record: any) => {
    setCurrentProduct(record.product);
    setHistoryPriceVisible(true);
  };

  /* ======================================= method end======================================= */
  useEffect(() => {
    getShoppingCartList();
  }, []);
  // 监听选中的rows
  useEffect(() => {
    let sum = 0;
    let flowPrice = 0;
    selectedRows.forEach(item => {
      sum += Number(item.quantity);
      flowPrice += Number(item.product.discountPrice * item.quantity);
    });
    setSelectedNum(sum);
    setSelectedFlowPrice(flowPrice);
  }, [selectedRows]);

  /* ======================================= 渲染组件 ======================================= */
  const renderTitle = () => (
    <div className="title-wrapper">
      <span>我的购物车</span>
      {tableDataSource?.length && (
        <div className="btn-wrapper">
          <Button className="btn-clear" danger size="small" icon={<ShoppingCartOutlined />}>
            <a href={"/search"}>继续购物</a>
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <>
      <div className={style.wrapper}>
        <UCenterCard title={renderTitle()} />
        <div className="shopping-cart">
          <div className="shopping-cart-top-tool">
            <div className="reminder">温馨提示：产品是否购买成功，以最终下单为准哦，请尽快结算！</div>
            <div className="refresh">
              （更新商品后若数量不变更，请刷新购物车，以便金额核算）
              <Button icon={<ReloadOutlined />} type="link" loading={loading} onClick={getShoppingCartList}>
                刷新
              </Button>
            </div>
          </div>
          {tableDataSource.length ? (
            <>
              <div className="shopping-cart-table">
                <Table
                  rowSelection={{
                    type: "checkbox",
                    ...rowSelectionEvent,
                  }}
                  rowKey={"id"}
                  size="middle"
                  dataSource={tableDataSource}
                  columns={tableColumns}
                  pagination={false}
                />
              </div>
              {/* 工具操作栏 */}
              <div className="shopping-cart-tool">
                <div className="check-all">
                  <Checkbox onChange={onCheckAllEvent}>全选</Checkbox>
                </div>
                <div className="action-btn">
                  <Button onClick={delSelectedEvent} type="link" disabled={selectedRowKeys.length === 0}>
                    删除选中商品
                  </Button>
                  <Button onClick={removeAllEvent} type="link">
                    清空购物车
                  </Button>
                </div>
                <div className="statistics">
                  <div className="selected-count">
                    数量总计：<span>{selectedNum}</span>
                  </div>
                  <div className="total-price">
                    总计(不含运费)：
                    <span className="total-price-amount">￥{tableDataSource.findIndex(item => item.product.discountPrice === 0) === -1 ? price2Thousand(selectedFlowPrice) : "询价"}</span>
                  </div>
                </div>
                <div className="to-pay" onClick={async () => await go2payEvent()}>
                  去结算
                </div>
              </div>
            </>
          ) : (
            <Empty
              image={commonConstant.COMMON_IMAGE_PATHS.EMPTY_DEFAULT}
              imageStyle={{
                height: "100%",
              }}
              description={<span className="empty-tip">购物车还是空的</span>}
            >
              <a className="go2purchase" href={"/"}>
                继续购物
              </a>
            </Empty>
          )}
          <SalesHistoryDialog
            visible={historyPriceVisible}
            currentProduct={currentProduct}
            onClose={() => {
              setHistoryPriceVisible(false);
            }}
          />
        </div>
      </div>
    </>
  );
}
