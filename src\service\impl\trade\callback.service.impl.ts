import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ICallbackService } from "@/service/trade/callback.service";

@Provide("TradeCallbackService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class callbackServiceImpl extends BaseService implements ICallbackService {
  /**
   * 支付同步回调转发
   *
   * @param paymentMethod /
   * @param req /
   */
  async syncCallbackRedirect(paymentMethod: string, req: any): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/payment/cashier/callback/${paymentMethod}`, req));
  }
}
