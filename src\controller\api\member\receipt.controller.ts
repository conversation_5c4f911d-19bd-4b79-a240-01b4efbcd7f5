import { Body, Controller, Del, Get, Inject, Param, Post, Put } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { IReceiptService } from "@/service/member/receipt.service";
import { receiptUpdateDto, receiptCreateDto } from "~/typings/data/member/receipt";
import { Validate } from "@midwayjs/validate";

@Controller("/api/ucenter/receipt", { middleware: [AuthenticationMiddleware] })
export class ReceiptController extends BaseController {
  @Inject("ReceiptService")
  receiptService: IReceiptService;

  /**
   * @desc 发票列表
   */
  @Get()
  async getLists() {
    const res = await this.receiptService.getPageList(this.getMemberId());
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 详情
   */
  @Get("/:id/detail")
  async show(@Param("id") addressId: number) {
    const res = await this.receiptService.show(this.getMemberId(), addressId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 新增
   */
  @Post()
  @Validate()
  async create(@Body() resource: receiptCreateDto) {
    const res = await this.receiptService.create(this.getMemberId(), resource);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 更新
   */
  @Put("/:id")
  @Validate()
  async update(@Param("id") receiptId: number, @Body() resource: receiptUpdateDto) {
    resource.receiptId = receiptId;
    const res = await this.receiptService.update(this.getMemberId(), resource);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 默认
   */
  @Put("/:id/default")
  async setDefault(@Param("id") addressId: number) {
    const res = await this.receiptService.setDefault(this.getMemberId(), addressId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 删除
   */
  @Del("/:id")
  async delete(@Param("id") addressId: number) {
    const res = await this.receiptService.delete(this.getMemberId(), addressId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }
}
