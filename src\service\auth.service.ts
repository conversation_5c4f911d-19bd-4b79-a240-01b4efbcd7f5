import { ILoginPasswordParams, ILoginSmsParams } from "~/typings/data/login";
import { Context } from "@midwayjs/koa";
import { bsLoginDto } from "@/dto/auth.dto";

export interface IAuthService {
  loginByPassword: (data: ILoginPasswordParams) => Promise<any>;
  loginBySms: (data: ILoginSmsParams) => Promise<any>;
  register: (data: object) => Promise<any>;
  registerByEmail: (data: object) => Promise<any>;
  passwordReset: (data: object) => Promise<any>;
  passwordResetByEmail: (data: object) => Promise<any>;
  getMemberInfo: (phone: string) => Promise<any>;
  getAccountInfo: (phone: string, companyName: string) => Promise<any>;
  genJwtToken: (payload: any) => Promise<any>;
  logout: (jwtToken: string) => Promise<any>;
  getRedisJwtToken: (memberId: string) => Promise<any>;
  parseJwtTokenToPayload: (jwtToken: string) => Promise<any>;
  getAccountInfoByPhone: (phone: string) => Promise<any>;
  /**
   * 保存或更新登录信息
   *
   * @param ctx /
   * @param member 会员信息
   */
  saveOrUpdateSession: (ctx: Context, member: any) => Promise<any>;

  /**
   * <p>通过授权code登录</p>
   *
   * @param dto /
   */
  loginByAuthCode: (dto: bsLoginDto) => Promise<any>;
}
