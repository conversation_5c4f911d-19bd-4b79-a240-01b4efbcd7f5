import { Rule, RuleType } from "@midwayjs/validate";
import { CustomHelpers } from "joi";

class AuthRegisterBaseDto {
  @Rule(RuleType.string().required())
  password: string;

  @Rule(RuleType.string().required())
  confirmPassword: string;

  @Rule(RuleType.string().required())
  companyName: string;

  @Rule(RuleType.string().required())
  customerCategoryCode: string;

  @Rule(RuleType.string().allow(""))
  address: string;

  @Rule(RuleType.array().items(RuleType.number()).required())
  areas: number[];

  @Rule(RuleType.string().allow(""))
  requestIp: string;

  @Rule(RuleType.number().allow(null))
  principalMainUserid: number;

  @Rule(RuleType.string().allow(null))
  contactName: string;
}

export class AuthRegisterDto extends AuthRegisterBaseDto {
  @Rule(
    RuleType.string()
      .required()
      .length(11)
      .pattern(/^1([3-9][0-9])\d{8}$/)
  )
  phone: string;

  @Rule(RuleType.string().required())
  smsCode: string;
}

export class AuthRegisterEmailDto extends AuthRegisterBaseDto {
  @Rule(
    RuleType.string()
      .required()
      .custom((value: string, helpers: CustomHelpers) => {
        const mailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
        if (!mailReg.test(value)) {
          return helpers.error("any.invalid");
        }
        return value;
      })
      .error(new Error("`email`格式不正确，请输入正确的邮箱！"))
  )
  email: string;

  @Rule(RuleType.string().required())
  contactPhone: string;

  @Rule(RuleType.string().required())
  verifyCode: string;
}
