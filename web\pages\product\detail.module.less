@content-bg-color: #f5f5f5;
body {
  background-color: #fff;
}
.wrapper {
  #labelBeforeFlag(@width:4px,@marginRight:10px) {
    position: relative;
    padding-left: 16px;
    padding-bottom: 0;
    &:before {
      position: absolute;
      left: 0;
      top: 50%;
      margin-top: -11px;
      content: "";
      width: @width;
      height: 22px;
      background-color: #e62129;
      border-radius: 5px;
    }
  }
  #labelBeforeDiamond(@width:8px,@marginRight:8px) {
    &:before {
      content: "";
      display: inline-block;
      width: @width;
      height: @width;
      background-color: @mallThemeColor;
      transform: rotate(45deg);
      -ms-transform: rotate(45deg); /* Internet Explorer */
      -moz-transform: rotate(45deg); /* Firefox */
      -webkit-transform: rotate(45deg); /* Safari 和 Chrome */
      -o-transform: rotate(45deg); /* Opera */
      margin-right: @marginRight;
    }
  }
  :global {
    .product {
      // background-color: @main-bg-color-white;
      .product-top {
        // padding: 44px 39px;
        .flex-row();
        /*产品图*/
        .product-banner-box {
          width: 402px;
        }
        .product-menu-box {
          margin: 50px 50px 0 50px;
          border: 1px solid #e6e6e6;
          border-radius: 50px;
          display: flex;
          overflow: hidden;
          .product-menu-item {
            position: relative;
            flex: 1;
            font-size: 16px;
            text-align: center;
            padding: 10px 0;
            cursor: pointer;
            &:hover {
              color: #e62129;
            }
            .product-menu-icon {
              font-size: 18px;
              margin-right: 5px;
            }
            &::before {
              content: "";
              position: absolute;
              left: 0;
              top: 0;
              width: 1px;
              height: 100%;
              background-color: #e6e6e6;
            }
            &:first-child {
              &::before {
                display: none;
              }
            }
          }
        }
        /*产品*/
        .product-item-info {
          margin-left: 42px;
          width: calc(100% - 402px);
          .title-box {
            position: relative;
            .main-title {
              font-weight: 600;
              font-size: 32px;
              display: inline-block;
              padding-right: 50px;
              color: #1d1819;
            }
            .sub-title {
              font-size: 14px;
              color: #5f5d5d;
              span {
                position: relative;
                padding-left: 10px;
                padding-right: 10px;
                &.danger {
                  color: #e62129;
                }
                &::before {
                  position: absolute;
                  left: 0;
                  top: 50%;
                  margin-top: -5px;
                  content: "";
                  width: 1px;
                  height: 10px;
                  background-color: #ccc;
                }
                &:first-child {
                  padding-left: 0;
                  &::before {
                    display: none;
                  }
                }
              }
            }
            .collection {
              cursor: pointer;
              position: absolute;
              top: 10px;
              right: 10px;
              color: #e62129;
            }
            font-size: 16px;
          }
          > div:not(:first-child) {
            margin-top: 16px;
          }
          /*卖点*/
          .sell-point-box {
            &-label {
              padding: 5px 10px;
              font-size: 12px;
              font-weight: 400;
              color: #b6b6b7;
              background: #fff;
              border-radius: 2px;
              border: 1px solid #e6e6e6;
              margin-right: 12px;
              &.active {
                border-color: #e62129;
                color: #e62129;
              }
            }
          }
          /*售价*/
          .sell-price-box {
            padding: 20px 20px 10px 20px;
            background: #f3f3f3;
            font-size: 30px;
            background-image: url("data:image/png;base64,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");
            background-repeat: no-repeat;
            background-position: right bottom;
            .item-price {
              font-weight: 400;
              color: #333333;
              span {
                font-size: 14px;
              }
            }
            .isHasDiscount {
              text-decoration: line-through;
              font-size: 14px;
              color: #5f5d5d;
            }
            .item-price-discount {
              color: #e62129;
              // color: @mallThemeColor;
              span {
                font-size: 14px;
              }
            }
          }
          /*温馨提示*/
          .reminder-box {
            position: relative;
            margin-top: 0 !important;
            color: #6d7278;
            font-size: 13px;
            padding: 8px 20px;
            border: 1px solid #f3f3f3;
            &::before {
              position: absolute;
              top: -16px;
              left: 20px;
              content: "";
              border: 8px solid transparent;
              border-bottom-color: #fff;
            }
            a {
              color: #e62129;
              padding: 0 5px;
            }
            // span:first-child {
            //   color: #333333;
            // }
          }
          /*产品规格*/
          .product-spec-box {
            font-weight: 400;
            font-size: 14px;
            .flex-row();
            .spec-select-attr {
              padding-bottom: 15px;
              margin-bottom: 10px;
              border-bottom: 1px solid #f1f1f1;
              ul {
                display: flex;
                flex-wrap: wrap;
                flex: 1;
              }
              .spec-attr-item {
                display: flex;
                padding: 10px 0;
                width: 50%;
                .spec-attr-item-label {
                  width: 85px;
                  font-weight: bold;
                  position: relative;
                  color: #6d7278;
                  margin-right: 7px;
                }
                .spec-attr-item-value {
                  color: #1d1819;
                }
                .spec-attr-item-link {
                  padding: 0;
                  color: #e62129;
                  cursor: pointer;
                }
                .stock-search-btn {
                  padding: 0;
                  height: 22px;
                  color: #e62129;
                }
              }
            }
            .spec-select-label {
              color: #333333;
              padding-top: 5px;
            }
            .spec-select-column {
              display: inline-flex;
              flex-wrap: wrap;
              flex: 1;
              gap: 10px;
              /*兼容IE11写法*/
              _:-ms-fullscreen,
              & {
                justify-content: flex-end;
              }
              .spec-select-row {
                /*兼容IE11写法*/
                _:-ms-fullscreen,
                & {
                  margin: 0 0 10px 10px;
                  height: 32px;
                  overflow-y: hidden;
                }
                .spec-select-item {
                  .ellipsis(1);
                  cursor: pointer;
                  padding: 5px 10px;
                  border: 1px solid #e6e6e6;
                  background-color: #fff;
                  border-radius: 2px;
                  &:hover {
                    background-color: #fff;
                    border-color: #e62129;
                    color: #e62129;
                  }
                }
                .spec-select-item-active {
                  background-color: #e62129;
                  color: #fff;
                  border-color: #e62129;
                }
              }
            }
          }
          /*加到购物车*/
          .add2cart-box {
            position: relative;
            display: flex;
            .label {
              font-weight: 400;
              color: #333333;
              font-size: 14px;
              line-height: 38px;
            }
            .add2cart-panel {
              display: flex;
              align-items: end;
              .btn-add2cart,
              .customer-service-btn {
                display: block !important;
                background-color: #fff !important;
                font-size: 16px !important;
                padding: 0 25px !important;
                height: 48px !important;
                line-height: 48px !important;
                border: 1px solid #e62129 !important;
                background-color: #fff;
                color: #e62129;
                outline: none !important;
                box-shadow: rgba(230, 33, 41, 0.2) 0px 4px 6px 0px;
              }
              .btn-add2cart {
                margin-top: 20px;
                &:hover {
                  opacity: 0.8;
                  box-shadow: none;
                }
                &:focus {
                  opacity: 1;
                }
              }
              .customer-service-btn {
                margin-left: 20px;
                border: 1px solid #e62129 !important;
                background-color: #e62129 !important;
                color: #fff;
                border-radius: 2px;
                &:hover {
                  opacity: 0.8;
                  box-shadow: none;
                }
                &:focus {
                  opacity: 1;
                  border: 1px solid #db161e !important;
                  background-color: #db161e !important;
                }
              }
              /** 库存情况查询按钮 */
              .stock-search-btn {
                cursor: pointer;
                margin-left: 13px;
              }
            }
            /** 库存情况 */
            .stock-result {
              position: absolute;
              left: 195px;
              top: 10px;
              color: #6d7278;
            }
            .purchase-shop-cart {
              display: block;
              font-size: 0;
              .btn {
                width: 36px;
                height: 36px;
                display: inline-block;
                border-color: #e6e6e6;
                font-size: 14px;
                &:hover {
                  position: relative;
                  border: 1px solid #e62129 !important;
                  background-color: #fff !important;
                  color: #e62129;
                  z-index: 9;
                }
                &.btn-add2cart {
                  display: block;
                  width: 100%;
                }
              }
              .input-box {
                position: relative;
                top: 1px;
                height: 36px;
                margin: 0 -1px;
                border-radius: 0;
                .ant-input-number-input {
                  height: 36px;
                  border-color: #e6e6e6;
                  margin: 0 !important;
                }
              }
            }
          }
        }
      }
      /*产品详情描述*/
      .product-content {
        margin-top: 50px;
        padding-bottom: 80px;
        /** 详情tab */
        .ant-tabs-nav {
          .ant-tabs-nav-wrap {
            .ant-tabs-tab {
              padding: 12px 30px;
              margin-left: 15px;
              &.ant-tabs-tab-active {
                .ant-tabs-tab-btn {
                  color: #e62129;
                }
              }
            }
            // transform: translate(24px, 0);
          }
        }
        /** 详情信息 */
        .product-detail-box {
          margin-top: 15px;
          .desc-title {
            color: #1d1819;
            font-size: 22px;
            font-weight: normal;
            #labelBeforeFlag();
          }
          .danger {
            font-size: 18px;
            font-weight: 400;
            color: #e62129;
            cursor: pointer;
            margin-left: 25px;
            line-height: 24px;
            &:before {
              content: "";
              display: inline-block;
              margin-right: 5px;
              width: 12px;
              height: 12px;
              border: 1px solid #e62129;
              background-color: #e62129;
            }
          }
          .dangerText {
            color: #e62129;
          }
          .ant-descriptions-view {
            // padding: 16px 20px;
            // background: @content-bg-color;
          }
          .main-content {
            padding: 16px 20px;
            background: @content-bg-color;
          }
        }
        .intro-box {
          margin: 30px 0;
          .intro-label {
            color: #1d1819;
            font-size: 22px;
            font-weight: normal;
            #labelBeforeFlag();
          }
          .intro-content {
            margin-top: 15px;
          }
          .attr-box {
            padding: 16px 20px;
            background: @content-bg-color;
            .attr-label {
              font-size: 14px;
              margin: 5px 0;
              padding-bottom: 5px;
              #labelBeforeDiamond();
            }
            .attr-value {
              p {
                line-height: 35px;
              }
            }
          }
          .main-content {
            padding: 16px 20px;
            background: @content-bg-color;
          }
        }
        /*coa-box*/
        .coa-box {
          margin-top: 15px;
          padding-bottom: 15px;
          // padding: 0 30px 16px 30px;
          .main-label {
            color: #1d1819;
            font-size: 22px;
            font-weight: normal;
            #labelBeforeFlag();
          }
          .search-box {
            margin-top: 16px;
          }
          .main-content {
            margin-top: 4px;
            padding: 16px 20px;
            background: @content-bg-color;
            .tip {
              margin-bottom: 10px;
              color: #888888;
              a {
                margin-left: 0;
              }
            }
            a {
              margin-left: 16px;
              &:hover {
                color: #ff1b1b;
                text-decoration: underline;
              }
            }
          }
        }
        /*价格说明*/
        .price-intro-box {
          margin-top: 15px;
          // padding: 0 30px 16px 30px;
          .main-label {
            color: #1d1819;
            font-size: 22px;
            font-weight: normal;
            #labelBeforeFlag();
          }
          .main-content {
            margin-top: 15px;
            // padding: 16px 20px;
            // background: @content-bg-color;
            .sub-label {
              font-size: 15px;
              color: #1d1819;
              font-weight: 600;
              margin-top: 15px;
              line-height: 2;
            }
            p {
              font-weight: 400;
              color: #5f5d5d;
              font-size: 15px;
              line-height: 2;
            }
          }
        }
      }
    }
  }
}

:global {
  .ant-popover {
    img {
      width: 640px;
    }
  }
}
