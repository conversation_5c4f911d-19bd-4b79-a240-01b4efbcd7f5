import React, { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Card } from "antd";
import style from "./index.module.less";
import { CardTabListType } from "antd/lib/card/Card";

interface UCenterCardProps {
  title: React.ReactNode;
  extra?: any;
  tabs?: CardTabListType[];
  actDefaultTab?: string;
  onMonitorTabChange?: any;
  setActTabKey?: any;
}

const UCenterCard = forwardRef((props: UCenterCardProps, ref?: any) => {
  const titleRender = () => {
    return (
      <>
        <span className="title">{props.title}</span>
      </>
    );
  };
  const extraRender = () => {
    return <>{props.extra}</>;
  };
  // 用useImperativeHandle暴露一些外部ref能访问的属性
  useImperativeHandle(ref, () => {
    // 需要将暴露的接口返回出去
    return {
      getActiveTabKey,
      activeTabKey,
      setActiveTabKey,
    };
  });
  const [activeTabKey, setActiveTabKey] = useState<string>(props.actDefaultTab ?? "all");
  const onTabChangeEvent = (key: string) => {
    setActiveTabKey(key);
    props?.onMonitorTabChange && props.onMonitorTabChange(key);
  };
  const getActiveTabKey = () => {
    return activeTabKey;
  };
  const headStyle = {};
  if (props.tabs) {
    headStyle["borderBottom"] = "none";
  }
  useEffect(() => {
    props?.setActTabKey && props.setActTabKey(activeTabKey);
  }, [activeTabKey]);
  return (
    <>
      <div className={style.ucenterCardWrapper}>
        <Card
          className="ucenter-content-head"
          bodyStyle={{ padding: "0" }}
          headStyle={headStyle}
          bordered={false}
          tabList={props.tabs}
          activeTabKey={activeTabKey}
          onTabChange={key => {
            onTabChangeEvent(key);
          }}
          title={titleRender()}
          extra={extraRender()}
        />
      </div>
    </>
  );
});
export default UCenterCard;
