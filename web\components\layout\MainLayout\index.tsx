import React, { useContext, useEffect, useState } from "react";
import { IContext, LayoutProps } from "ssr-types-react";
import style from "./index.module.less";
import Header from "./parts/Header";
import Footer from "./parts/Footer";
import HoverSearch from "./parts/HoverSearch";
import TmElevatorBar from "@/components/TmElevatorBar";
import TmQuickDrawer from "@/components/TmQuickDrawer";
import { useStoreContext } from "ssr-common-utils";

const MainLayout = (props: LayoutProps) => {
  // 注：Layout 只会在服务端被渲染，不要在此运行客户端有关逻辑
  const { state } = useContext<IContext>(useStoreContext());
  const [navigationActive, setNavigationActive] = useState(state?.navSelectedKeys?.length ? state?.navSelectedKeys[0] : "index");

  useEffect(() => {
    !navigationActive && setNavigationActive("index");
  }, []);
  return (
    <>
      <div className={style.pageWrapper}>
        {/* 固定头部 */}
        {navigationActive === "index" && <HoverSearch />}
        {/* 头部组件 */}
        <Header />
        {/* 主内容 */}
        <div id="app">{props.children}</div>
        {/* 右侧电梯导航栏 */}
        <TmElevatorBar />
        {/* 右侧购物车栏 */}
        <TmQuickDrawer />
        {/* 尾部组件 */}
        <Footer />
      </div>
    </>
  );
};

export default MainLayout;
