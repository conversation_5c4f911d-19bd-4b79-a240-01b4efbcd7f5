import { Provide } from "@midwayjs/decorator";

@Provide()
export class IpUtil {
  static getClientIP(req) {
    let ip =
      req.headers["X-Forwarded-For"] ||
      req.headers["x-forwarded-for"] || // 判断是否有反向代理 IP
      req.ip ||
      req.connection.remoteAddress || // 判断 connection 的远程 IP
      req.socket.remoteAddress || // 判断后端的 socket 的 IP
      req.connection.socket.remoteAddress ||
      "";
    if (ip) {
      ip = ip.replace("::ffff:", "");
    }
    return ip;
  }
}
