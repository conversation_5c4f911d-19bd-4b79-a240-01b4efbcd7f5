import { Config, Inject, Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ILayoutService } from "@/service/layout.service";
import { ISearchService } from "@/service/search/search.service";
import { IProductCategoryService } from "@/service/product/category.service";
import seoJsonData from "@/data/seo.json";
import { IHomepageService } from "@/service/homepage.service";

@Provide("LayoutService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class LayoutServiceImpl extends BaseService implements ILayoutService {
  @Config("systemConfig")
  systemConfig;

  @Config("systemModalOpenConfig")
  systemModalOpenConfig;

  @Inject("SearchService")
  searchService: ISearchService;

  @Inject("ProductCategoryService")
  productCategoryService: IProductCategoryService;

  @Inject("HomepageService")
  homepageService: IHomepageService;


  /**
   * 获取seo配置信息
   *
   * @private /
   */
  private static getSeoData () {
    return seoJsonData;
  }

  // 初始化站点layout数据,如：seo
  async initSiteLayoutData (): Promise<any> {
    const ossCdnDomain = this.systemConfig.ossCdnDomain;
    // 产品分类数据
    let [cascadeErr, cascade] = await this.productCategoryService
      .cascade()
      .then(res => [null, res])
      .catch(err => [err, null]);
    cascadeErr && (cascade = []);
    // 推荐热词数据
    let [hotWordResultErr, hotWordResult] = await this.searchService
      .getHotWords()
      .then(res => [null, res])
      .catch(err => [err, null]);
    hotWordResultErr && (hotWordResult = []);

    // 获取热门品牌介绍数据, []
    const hotBrandIntroduce = await this.homepageService.getHotBrandIntroduce();
    // 品牌介绍数据
    return {
      message: "layout-init-data",
      ossCdnDomain,
      cascade,
      seoData: LayoutServiceImpl.getSeoData(),
      hotWords: hotWordResult.data,
      hotBrandIntroduce: hotBrandIntroduce || [],
      ccOldMemberLoginOpen: this.systemModalOpenConfig.ccOldMemberLoginOpen,
      ccFrontendDomain: this.systemConfig.ccMallFrontendDomain ?? "http://www2.guanghuayigou.com",
      checkMemberIsDefaultPwdModalControl: this.systemModalOpenConfig.checkMemberIsDefaultPwdModalControl,
    };
  }
}
