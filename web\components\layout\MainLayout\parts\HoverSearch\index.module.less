.wrapper {
  width: 100%;
  height: 60px;
  transform: translateY(0);
  background-color: #fff;
  position: fixed;
  top: 0;
  z-index: 998;
  box-shadow: 0 0 10px 2px rgb(90 90 90 / 60%);
  transition: 0.35s;
  :global {
    .scroll-hover-search-box {
      background-color: @main-bg-color-white;
      border-bottom: 1px solid #e72129;
      .content {
        width: @main-width;
        margin: 10px auto;
        position: relative;
        height: 40px;
        display: flex;
        .category-nav {
          display: inline-block;
          height: 100%;
        }
        .search-box {
          position: relative;
          width: 800px;
          .search-main {
            display: flex;
            .ant-input-affix-wrapper {
              input {
                height: 28px;
                font-size: 16px;
                padding-left: 4px;
                &:active,
                &:focus {
                  border-color: #e6212a !important;
                }
              }
            }
            .search-btn {
              width: 125px;
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
              margin-left: -1px;
            }
          }
          .search-suggest {
            position: absolute;
            left: 0;
            top: 36px;
            width: calc(800px - 110px);
            height: auto;
            background-color: #fff;
            border: 1px solid #e6212a;
            border-radius: 0 0 5px 5px;
            border-top: none;
            z-index: 9;
            ul {
              margin: 7px 14px 0;
              padding: 8px 0 7px;
              background: 0 0;
              border-top: 2px solid #f5f5f6;
              li {
                display: block;
                position: relative;
                width: auto;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding-left: 14px;
                margin-left: -14px;
                margin-right: -14px;
                color: #626675;
                font-size: 14px;
                line-height: 38px;
                z-index: 10;
                &:hover {
                  cursor: pointer;
                  background-color: rgba(245, 34, 45, 0.05);
                  color: #e6212a;
                }
              }
            }
          }
        }
        .shopping-cart {
          display: inline-flex;
          position: absolute;
          height: 100%;
          right: 50px;
          justify-content: space-around;
          align-items: center;
          color: #e62129;
          cursor: pointer;
          .svg-icon {
            font-size: 2em;
          }
        }
      }
    }
  }
}
