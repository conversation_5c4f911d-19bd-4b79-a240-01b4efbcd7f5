@newActiveTextLink: rgba(194, 12, 12, 0.62);
@imgHoverBgColor: rgba(0, 0, 0, 0.2);
@contentTextColor: #6d7278;

.wrapper {
  :global {
    .industry-news {
      margin-top: @plate-margin-top;
      &-container {
        .flex-row(normal, center);
        width: 100%;
        gap: 18px;
        .news-recommend {
          flex: 1;
          height: 480px;
          box-sizing: border-box;
          padding: 18px;
          background: @main-bg-color-white;
          .recommend-head {
            width: 100%;
            height: 314px;
            overflow: hidden;
            margin-bottom: 14px;
            position: relative;
            cursor: pointer;
            display: inline-block;
            border: 1px solid #f5f5f5;
            .recommend-title {
              position: absolute;
              left: 26px;
              bottom: 12px;
              font-size: 28px;
              z-index: 2;
              color: @main-bg-color-white;
              .ellipsis(1);
            }
            .ant-image {
              width: 100%;
            }
            &::after {
              content: "";
              width: 100%;
              height: 78px;
              position: absolute;
              left: 0;
              bottom: 0;
              background: rgba(114, 114, 114, 0.3);
            }
          }
          .recommend-images {
            .flex-row(normal, center);
            gap: 12px;
            &-item {
              _:-ms-fullscreen, & {
                background: red;
                padding: 6px;
                margin-right: 24px;
                &:last-child {
                  margin-right: 0;
                }
              }
              display: inline-block;
              width: 178px;
              height: 116px;
              cursor: pointer;
              position: relative;
              border: 1px solid #f5f5f5;
              padding: 2px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            // focus 效果
            &-item::after {
              content: "";
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              z-index: 1;
              background-color: @imgHoverBgColor;
              display: none;
              transition: all 0.3s ease-in-out;
            }
            &-item:hover {
              &::after {
                display: inline-block;
              }
            }
          }
          // 推荐图组样式
          .recommend-article {
            width: 100%;
            height: 314px;
            position: relative;
            margin-bottom: 12px;
            overflow: hidden;
            .article-masker {
              width: 100%;
              min-height: 72px;
              padding: 4px 24px;
              position: absolute;
              left: 0;
              bottom: 0;
              z-index: 1;
              background: rgba(0, 0, 0, 0.5);
              opacity: 0.9;
              color: #fff;
              font-size: 16px;
              cursor: pointer;
              .article-title {
                font-size: 24px;
                font-weight: 600;
                .ellipsis(1);
              }
              .article-description {
                .ellipsis(1);
              }
              &:hover {
                opacity: 1;
              }
            }
          }
        }
        .news-list {
          flex: 1;
          height: 480px;
          box-sizing: border-box;
          padding: 18px;
          background: @main-bg-color-white;
          display: flex;
          flex-direction: column;
          gap: 48px;
          position: relative;
          .news-item {
            height: 84px;
            .flex();
            &:hover {
              .news-item-content {
                color: @newActiveTextLink;
              }
            }
            &:active {
              .news-item-content {
                color: @imgHoverBgColor;
              }
            }
            // 新闻项-a标签
            .news-item-content {
              flex: 1;
              display: inline-block;
              color: @main-text-color;
              .new-title {
                font-size: 21px;
                font-weight: 700;
                .ellipsis(1);
              }
              .new-sub-title {
                font-size: 18px;
                .ellipsis(2);
              }
              .new-publish-datetime {
                color: #8c8c8c;
                font-size: 14px;
                margin-top: 5px;
              }
              &:hover {
                .new-title {
                  text-decoration: underline;
                }
              }
            }
          }
          .news-btn-more {
            display: inline-flex;
            font-size: 16px;
            background: @main-bg-color-white;
            color: @contentTextColor;
            flex-direction: row-reverse;
            align-items: center;
            &:hover {
              color: @newActiveTextLink;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
