import React, { memo } from "react";
import style from "./index.module.less";

/* 公司服务宗旨 */
export default memo(function CompanyPurpose() {
  const purposeArray = ["解放采购", "稳定供应", "及时交付", "全程追踪"];
  return (
    <div className={style.wrapper}>
      <div className="purpose">
        {purposeArray.map((item, index) => (
          <span key={index} className="purpose-item">
            {item}
          </span>
        ))}
      </div>
    </div>
  );
});
