import { Body, Controller, Inject, Post } from "@midwayjs/decorator";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { BaseController } from "@/controller/base.controller";
import { IOrderFreightService } from "@/service/order/freight.service";
import { orderFreightQueryDto } from "~/typings/data/order";

@Controller("/api/orders/freight", { middleware: [AuthenticationMiddleware] })
export class OrderFreightController extends BaseController {
  @Inject("OrderFreightService")
  orderFreightService: IOrderFreightService;

  @Post()
  async calculateOrderFreight(@Body() criteria: orderFreightQueryDto) {
    const { ctx } = this;
    const res = await this.orderFreightService.calculateFreight(criteria);
    return ctx.getResponseInstance(ctx).setResponseData(res).send();
  }
}
