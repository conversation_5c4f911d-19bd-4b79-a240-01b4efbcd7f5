import React from "react";

export interface IProductItem {
  id: number;
  productId: number | string;
  sku: string;
  skuName: string;
  guidePrice: number;
  discountPrice: number;
  aptDiscount: number;
  spec: string;
  packing: string;
  packingRatio: string;
  productNameZh: string;
  productNameEn: string;
  brandName: string;
  brandId: number;
  productNo: string;
  isDanger?: number;
  isPoison?: number;
  isExplode?: number;
  unit?: string;
  itemsQuantity?: number;
}
export interface tradeCartDataType {
  id: React.Key;
  memberId: string;
  productSkuId: number;
  quantity: number;
  selected: number;
  createdDate: string;
  modifiedDate: string;
  remark: string;
  product: IProductItem;
  isDanger?: boolean;
}
