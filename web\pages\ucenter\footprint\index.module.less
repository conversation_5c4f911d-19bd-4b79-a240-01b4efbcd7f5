@contentColor: #333333;
@linkHoverColor: #ff1b1b;
@linkTextColor: #e02020;
@contentPadding: 16px;
@listHoverBgColor: #fafafa;

.wrapper {
  :global {
    .title-wrapper {
      width: 100%;
      display: inline-flex;
      justify-content: space-between;
      .btn-wrapper {
        margin-right: 14px;
        .flex();
        gap: 16px;
        .btn-clear {
          border-radius: 2px;
        }
      }
    }
    .foot-print-list {
      /** 重置 antd-list 样式 */
      .ant-list-item {
        padding: @contentPadding 0;
      }
      /** list 自定义头 */
      .product-list-header {
        width: 100%;
        height: 40px;
        line-height: 40px;
        background-color: #f5f5f5;
        .flex-row(space-between, cneter);
        padding: 0 @contentPadding;
        border-bottom: 1px solid @listHoverBgColor;
        div {
          width: 120px;
          .ellipsis();
          text-align: center;
        }
        .index {
          width: 60px;
        }
        .action {
          min-width: 120px;
        }
      }
      .ant-list-item {
        padding: 0;
        .product-item {
          padding: 6px @contentPadding;
          .flex-row(space-between, center);
          &:hover {
            background-color: @listHoverBgColor;
          }
          .item-child {
            width: 120px;
            .ellipsis();
            text-align: center;
          }
          .index {
            width: 60px;
          }
          .name,
          .brand {
            &:hover {
              color: @linkHoverColor;
              text-decoration: underline;
            }
          }
          .sku {
            color: @linkTextColor;
            text-decoration: underline;
          }
          .price {
            color: @linkTextColor;
          }
        }
      }
      // 分页
      .ant-list-pagination {
        margin-top: 12px;
      }
    }
  }
}
