import { Inject, Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { INewsService } from "@/service/ghtech/news.service";
import { FORMAT } from "@midwayjs/core";
import { CacheManager } from "@midwayjs/cache";
import { INewsParams } from "~/typings/data/ghtech/ghtech";
import cacheKeyConstant from "@/common/constants/cache.constant";

@Provide("NewService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class NewsServiceImpl extends BaseService implements INewsService {
  cacheKey = cacheKeyConstant.BUSINESS.PLATFORM_NEWS;

  @Inject()
  cacheManager: CacheManager;

  /** 获取新闻资讯列表 */
  async getNewList(params: INewsParams): Promise<any> {
    // 新闻资讯缓存处理 - 1小时
    let res = await this.cacheManager.get(this.cacheKey);
    if (!res) {
      try {
        const result = await this.easyHttp.get("/api/ghtech/news", params);
        if (result.data?.content?.length) {
          await this.cacheManager.set(this.cacheKey, result.data?.content, { ttl: FORMAT.MS.ONE_HOUR / 1000 });
          res = result.data?.content;
        }
      } catch (e) {
        res = []
      }
    }
    return res;
  }

  /**
   * 获取首页图组新闻推荐数据
   */
  async getArticleRecommends(): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/website/homepage/article-recommends`));
  }
}
