@contentPadding: 16px;

.wrapper {
  :global {
    // 修改自定义card的样式
    .ucenter-content-head {
      margin-bottom: 0 !important;
      .ant-tabs-nav {
        margin-bottom: 0 !important;
      }
    }

    .receipt-container-empty {
      width: 160px;
      padding: @contentPadding;
      .flex-col(center, center);
      gap: 16px;
    }

    .receipt-container {
      padding: @contentPadding;

      .ant-descriptions-item {
        padding-bottom: @contentPadding;
      }

      .btn-edit {
        margin-top: 4px;
      }
    }
  }
}
