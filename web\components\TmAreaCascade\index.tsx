import React, { forwardRef, useEffect, useImperativeHandle, useState, memo, useRef, useMemo, useContext } from "react";
import { Cascader } from "antd";
import type { DefaultOptionType } from "antd/es/cascader";
import areaCrud from "@/apis/platform/area";
import style from "./index.module.less";

interface Props {
  placeholder?: string;
  disabled?: boolean; // 是否禁用操作
  initialValues?: string[] | number[]; // 默认值
  onMonitorCascadeChange?: any;
  onClearEvent?: any;
  formItemProps?: Record<string, any>; // formItem配置
  bordered?: boolean;
}
interface Option {
  id?: number;
  name?: string;
  children?: Option[];
}

const TmAreaCascade = forwardRef((prop: Props, ref?: any) => {
  const [options, setOptions] = useState<Option[]>([]);
  const [targetVal, setTargetVal] = useState<string[] | number[]>(prop?.initialValues ?? []);

  const onChange = (value: string[], selectedOptions: Option[]) => {
    if (!value) {
      return;
    }
    setTargetVal(value);
    prop?.onMonitorCascadeChange && prop.onMonitorCascadeChange(value, selectedOptions);
  };

  /** 清空省市区选择 */
  const onClear = () => {
    setTargetVal([]);
    prop?.onClearEvent && prop.onClearEvent(null);
  };

  const getTargetValFunc = () => {
    return targetVal;
  };

  const setTargetValFunc = async (value: string[] | number[]) => {
    await setTargetVal(value);
  };

  useImperativeHandle(ref, () => {
    return { getTargetValFunc, setTargetValFunc };
  });

  const filter = (inputValue: string, path: DefaultOptionType[]) => {
    return path.some(option => (option.name as string).toLowerCase().includes(inputValue.toLowerCase()));
  };

  const fetchAreaCascade = async (): Promise<void> => {
    const [err, res] = await areaCrud
      .getAreaCascade()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err) {
      setOptions(res.data);
    }
  };

  useEffect(() => {
    fetchAreaCascade();
  }, []);

  return (
    <>
      <div className={style.wrapper}>
        <Cascader
          bordered={prop.bordered}
          fieldNames={{ label: "name", value: "id", children: "children" }}
          options={options}
          onChange={onChange}
          onClear={onClear}
          placeholder={prop.placeholder}
          showSearch={{ filter }}
          disabled={prop.disabled}
          defaultValue={prop.initialValues}
          value={targetVal}
          notFoundContent="暂无数据，请刷新重试！"
        />
      </div>
    </>
  );
});

TmAreaCascade.defaultProps = {
  placeholder: "请选择省市区",
  disabled: false,
  initialValues: [],
};

export default memo(TmAreaCascade);
