import React, { useState, useContext } from "react";
import { Card, Steps, Button, Form, Input, Upload, message, Row, Col, Divider } from "antd";
import { UploadOutlined, InboxOutlined, FileExcelOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import { IContext, SProps } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import { useLoading } from "@/hooks/useLoading";
import { downloadFromOssFilepath } from "@/utils/download.util";
import quoteApi from "@/apis/quote/quote";
import ProductMatchResult from "../components/ProductMatchResult";
import QuoteBasicInfo from "../components/QuoteBasicInfo";
import style from "./index.module.less";

const { Dragger } = Upload;
const { TextArea } = Input;

export default function QuoteRequest(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const useLoadingHook = useLoading();
  const [form] = Form.useForm();
  
  const [current, setCurrent] = useState(0);
  const [quoteRequestId, setQuoteRequestId] = useState<number | null>(null);
  const [matchResult, setMatchResult] = useState<any>(null);
  const [fileList, setFileList] = useState<any[]>([]);

  // 模板文件路径
  const templateFilePath = "/file/询报价产品批量导入模板.xlsx";

  const steps = [
    {
      title: "基本信息",
      description: "填写询报价基本信息",
    },
    {
      title: "上传文件",
      description: "上传产品清单文件",
    },
    {
      title: "匹配结果",
      description: "查看产品匹配结果",
    },
    {
      title: "生成报价",
      description: "生成报价单或订单",
    },
  ];

  // 下载模板
  const handleDownloadTemplate = () => {
    downloadFromOssFilepath(templateFilePath, "询报价产品批量导入模板.xlsx");
  };

  // 文件上传配置
  const uploadProps: UploadProps = {
    multiple: false,
    maxCount: 1,
    accept: ".xls,.xlsx",
    fileList,
    beforeUpload: (file) => {
      // 验证文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'application/vnd.ms-excel' ||
                     file.name.endsWith('.xlsx') || 
                     file.name.endsWith('.xls');
      
      if (!isExcel) {
        message.error('只能上传 Excel 文件！');
        return false;
      }

      // 验证文件大小（5MB）
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('文件大小不能超过 5MB！');
        return false;
      }

      return false; // 阻止自动上传
    },
    onChange: (info) => {
      setFileList(info.fileList);
    },
    onRemove: () => {
      setFileList([]);
    },
  };

  // 提交基本信息
  const handleSubmitBasicInfo = async (values: any) => {
    try {
      useLoadingHook.showLoading("保存基本信息中...");
      
      const [err, res] = await quoteApi
        .createQuoteRequest(values)
        .then(res => [null, res])
        .catch(err => [err, null]);

      useLoadingHook.hideLoading();

      if (err) {
        message.error(err?.data?.message || "保存失败，请稍后再试！");
        return;
      }

      setQuoteRequestId(res.data.id);
      setCurrent(1);
      message.success("基本信息保存成功！");
    } catch (error) {
      useLoadingHook.hideLoading();
      message.error("保存失败，请稍后再试！");
    }
  };

  // 上传文件并匹配产品
  const handleUploadAndMatch = async () => {
    if (fileList.length === 0) {
      message.error("请选择要上传的文件！");
      return;
    }

    const file = fileList[0].originFileObj;
    const formData = new FormData();
    formData.append("file", file);
    
    // 添加基本信息
    const basicInfo = form.getFieldsValue();
    Object.keys(basicInfo).forEach(key => {
      if (basicInfo[key]) {
        formData.append(key, basicInfo[key]);
      }
    });

    try {
      useLoadingHook.showLoading("文件上传中，正在匹配产品...");
      
      const [err, res] = await quoteApi
        .uploadFileAndMatchProducts(formData)
        .then(res => [null, res])
        .catch(err => [err, null]);

      useLoadingHook.hideLoading();

      if (err) {
        message.error(err?.data?.message || "文件上传失败，请稍后再试！");
        return;
      }

      setMatchResult(res.data);
      setQuoteRequestId(res.data.quoteRequestId);
      setCurrent(2);
      
      const { matchedCount, unmatchedCount, totalRows } = res.data;
      message.success(`文件解析完成！共 ${totalRows} 行数据，匹配成功 ${matchedCount} 个产品，未匹配 ${unmatchedCount} 个产品。`);
    } catch (error) {
      useLoadingHook.hideLoading();
      message.error("文件上传失败，请稍后再试！");
    }
  };

  // 上一步
  const handlePrev = () => {
    setCurrent(current - 1);
  };

  // 下一步
  const handleNext = () => {
    if (current === 0) {
      form.submit();
    } else if (current === 1) {
      handleUploadAndMatch();
    } else {
      setCurrent(current + 1);
    }
  };

  return (
    <div className={style.wrapper}>
      <div className="container">
        <Card title="询报价" className="quote-card">
          <Steps current={current} items={steps} className="quote-steps" />
          
          <div className="step-content">
            {current === 0 && (
              <QuoteBasicInfo 
                form={form}
                onSubmit={handleSubmitBasicInfo}
              />
            )}

            {current === 1 && (
              <div className="upload-step">
                <div className="template-section">
                  <h3>1. 下载模板文件</h3>
                  <p>请先下载模板文件，按照格式填写产品信息</p>
                  <Button 
                    type="primary" 
                    icon={<FileExcelOutlined />} 
                    onClick={handleDownloadTemplate}
                  >
                    下载询报价模板
                  </Button>
                </div>

                <Divider />

                <div className="upload-section">
                  <h3>2. 上传产品清单文件</h3>
                  <Dragger {...uploadProps} className="upload-dragger">
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">单击或拖动文件到此区域进行上传</p>
                    <p className="ant-upload-hint">
                      只支持上传 Excel 文件（.xlsx, .xls），文件大小不超过 5MB
                    </p>
                  </Dragger>
                </div>
              </div>
            )}

            {current === 2 && matchResult && (
              <ProductMatchResult 
                matchResult={matchResult}
                quoteRequestId={quoteRequestId}
                onNext={() => setCurrent(3)}
              />
            )}

            {current === 3 && (
              <div className="final-step">
                <h3>询报价单创建完成</h3>
                <p>您可以选择以下操作：</p>
                <Row gutter={16}>
                  <Col span={8}>
                    <Button type="primary" size="large" block>
                      生成报价单
                    </Button>
                  </Col>
                  <Col span={8}>
                    <Button type="default" size="large" block>
                      生成快速订单
                    </Button>
                  </Col>
                  <Col span={8}>
                    <Button type="link" size="large" block href={`/quote/detail/${quoteRequestId}`}>
                      查看详情
                    </Button>
                  </Col>
                </Row>
              </div>
            )}
          </div>

          <div className="step-actions">
            {current > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {current < steps.length - 1 && (
              <Button type="primary" onClick={handleNext}>
                {current === 0 ? "保存并继续" : current === 1 ? "上传文件" : "下一步"}
              </Button>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}
