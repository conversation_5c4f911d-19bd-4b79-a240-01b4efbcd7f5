import { Popover, Divider, Button, Descriptions, Empty, Form, Input, Space, Spin, Table, TableColumnsType } from "antd";
import React, { useContext, useState } from "react";
import style from "./index.module.less";
import { FileSearchOutlined, QuestionCircleOutlined, UndoOutlined } from "@ant-design/icons";
import crudOrderLogistics from "@/apis/order/logistics";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";

export default function ReportOrderDelivery() {
  /* ======================================= useSate start======================================= */
  const [loadingState, setLoadingState] = useState(false);
  const [orderDelivery, setOrderDelivery] = useState<any>(null);
  const [searchForm] = Form.useForm();
  const rules = {
    memberId: [{ required: true, message: "客户编号不能为空！" }],
    orderNo: [{ required: true, message: "订单编号不能为空！" }],
  };
  const { state } = useContext<IContext>(useStoreContext());
  const defaultForm = {
    memberId: state?.userData?.memberId ?? "",
    orderNo: "",
  };
  const [resultText, setResultText] = useState("暂无数据");

  /* ======================================= useSate end======================================= */
  const logisticsTableColumns: TableColumnsType<any> = [
    {
      title: "出库日期",
      align: "center",
      dataIndex: "outboundDate",
      width: 90,
    },
    {
      title: "出库单号",
      align: "center",
      dataIndex: "outboundSn",
      width: 150,
      ellipsis: true,
      render: text => (
        <>
          <Popover content={text}>{text}</Popover>
        </>
      ),
    },
    {
      title: "托运站",
      align: "center",
      dataIndex: "shipper",
      width: 75,
    },
    {
      title: (
        <>
          <Popover content={"汕头区号:0754"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          货运电话
        </>
      ),
      align: "center",
      dataIndex: "shipperPhone",
      width: 105,
    },
    {
      title: "业务员",
      align: "center",
      dataIndex: "salesman",
      width: 75,
    },
    {
      title: "公司名称",
      align: "center",
      dataIndex: "company",
      width: 210,
      ellipsis: true,
    },
    {
      title: "件数",
      align: "center",
      dataIndex: "shipperQuantity",
      width: 50,
    },
    {
      title: "重量",
      align: "center",
      dataIndex: "shipperWeight",
      width: 50,
    },
    {
      title: (
        <>
          <Popover content={"部份中转地区无法提供到站电话,请打发货电话查询"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          物流联系方式
        </>
      ),
      align: "center",
      dataIndex: "shipperArrivalPhoneRemark",
      width: 125,
    },
    {
      title: (
        <>
          <Popover content={"注意：货运到货不含当天发货的时间"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          预计到货天数
        </>
      ),
      align: "center",
      dataIndex: "shipperArrivalDays",
      width: 120,
    },
    {
      title: "货运路线",
      align: "center",
      dataIndex: "shipperRoute",
    },
  ];
  /* ======================================= 方法 start======================================= */
  /** 执行搜索 */
  const executeOrderDeliverySearch = async () => {
    setLoadingState(true);
    const [err, res] = await crudOrderLogistics
      .queryDelivery(searchForm.getFieldsValue())
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoadingState(false);
    if (!err) {
      setOrderDelivery(res.data);
      !res?.data && setResultText("暂未找到对应订单发货信息");
    }
  };

  const handleResetForm = () => {
    searchForm.resetFields();
    setOrderDelivery(null);
    setResultText("暂无数据");
  };
  /* ======================================= 方法 end======================================= */

  return (
    <div className={style.wrapper}>
      <div className="order-delivery">
        <div className="search-bar">
          <Form form={searchForm} layout="inline" initialValues={defaultForm} onFinish={executeOrderDeliverySearch}>
            <Form.Item label="客户编号" name={"memberId"} rules={rules.memberId}>
              <Input size="large" allowClear placeholder="请输入客户编号" />
            </Form.Item>
            <Form.Item label="订单编号" name={"orderNo"} rules={rules.orderNo}>
              <Input size="large" allowClear placeholder="请输入订单编号" />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button htmlType="submit" type={"primary"} size={"large"} icon={<FileSearchOutlined />}>
                  查询
                </Button>
                <Button danger size={"large"} icon={<UndoOutlined />} onClick={handleResetForm}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
        <Spin spinning={loadingState}>
          <div className="search-result" style={orderDelivery ? { border: "solid 1px #f8f8f8", background: "rgba(248,248,248,0.45)" } : { background: "#f8f8f8" }}>
            {orderDelivery ? (
              <>
                <Descriptions title="" column={3} bordered>
                  <Descriptions.Item span={1} label="订单号">
                    <span>{orderDelivery?.orderNo || "-"}</span>
                  </Descriptions.Item>
                  <Descriptions.Item span={1} label="客户ID">
                    <span>{orderDelivery?.memberId || "-"}</span>
                  </Descriptions.Item>
                  <Descriptions.Item span={1} label="下单时间">
                    <span>{orderDelivery?.createdDate || "-"}</span>
                  </Descriptions.Item>
                </Descriptions>
                <Divider orientation="left">物流明细</Divider>
                <Table rowKey="id" size="small" bordered={false} className="logistics-table" columns={logisticsTableColumns} dataSource={orderDelivery.logistics} pagination={false} />
              </>
            ) : (
              <Empty description={resultText} />
            )}
          </div>
        </Spin>
      </div>
    </div>
  );
}
