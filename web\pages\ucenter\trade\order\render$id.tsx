import React, { useContext, useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import { Card, Descriptions, Empty, Modal, Table, Tag, Tooltip, Popover, Button, message } from "antd";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import style from "./detail$id.module.less";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import * as orderCrud from "@/apis/order/order";
import { ColumnsType } from "antd/es/table";
import { IOrderDetailParams, IOrderProductColumnType, IOrderLogistic } from "@/typings/order.interface";
import { price2Thousand, price2ThousandFixed4, ghmallGuidePrice2Show } from "@/utils/price-format.util";
import { $tools } from "@/utils/tools.util";
import { IReceiptUnderlineType } from "@/typings/member.interface";
import { useLoading } from "@/hooks/useLoading";
import orderConstants from "@/constants/order";
import OrderLogisticsModal from "./parts/OrderLogisticsModal";
import { getFlagDate2Date } from "@/utils/date.utils";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { Helmet } from "react-helmet";
import { PRODUCT_QUANTITY_DECIMAL_DIGITS } from "@/constants/common";
import { useShoppingCart } from "@/hooks/useShoppingCart";

/** 收货信息 */
interface IReceivingAddressType {
  nickname?: string;
  phone?: string;
  telephone?: string;
  address?: string;
  company_name?: string;
}

export default function OrderDetail(props: SProps) {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const orderNo = state.orderNo ?? props?.match?.params["id"];
  const [orderDetail, setOrderDetail] = useState<IOrderDetailParams>();
  /* 展示物流 */
  const [logisticsModalVisible, setLogisticsModalVisible] = useState<boolean>(false);
  /* ======================================= hook start ======================================= */
  const useLoadingHook = useLoading();
  const shoppingCartHook = useShoppingCart();
  /* ======================================= hook end ======================================= */

  // 组装描述信息数据
  const [receivingAddressDescriptions, setReceivingAddressDescriptions] = useState<IReceivingAddressType>();
  const [receiptDescriptions, setReceiptDescriptions] = useState<Partial<IReceiptUnderlineType>>();
  // 订单追踪信息
  const [orderTrackingLogs, setOrderTrackingLogs] = useState<any[]>([]);
  /** 接口请求-获取订单详情 */
  const fetchDetail = async (): Promise<void> => {
    useLoadingHook.showLoading("订单加载中...", "ucenterApp");
    const [err, res] = await orderCrud
      .show(orderNo)
      .then(res => [null, res])
      .catch(err => [err, null]);
    useLoadingHook.hideLoading("ucenterApp");
    if (err) {
      Modal.error({
        content: "网络异常，获取订单详情失败！",
        type: "error",
        centered: true,
        closable: false,
        maskClosable: false,
        keyboard: false,
        okText: "回到订单列表",
        onOk: async () => {
          window.location.href = "/ucenter/trade/order";
        },
      });
      return;
    }
    /** 订单支付信息 */
    setOrderDetail({ ...orderDetail, ...res.data });
    setReceivingAddressDescriptions(JSON.parse(res.data?.receivingAddressInfo));
    setReceiptDescriptions($tools.generateOrderReceiptObj(res.data?.receiptInfo));

    await initOrderTrackingLogs();
  };

  /**
   * 初始化订单对应的追踪信息
   */
  const initOrderTrackingLogs = async () => {
    // 加载订单追踪信息
    const [err, res] = await orderCrud
      .getOrderTracking(orderNo)
      .then(res => [null, res])
      .catch(err => [err, null]);

    if (err) {
      return;
    }

    // @ts-expect-error
    res.data.sort((prev, next) => new Date(next.createdDate) - new Date(prev.createdDate));
    setOrderTrackingLogs(res.data);
  };

  /** 获取产品的包装 */
  const getSkuPacking = record => {
    if (!record?.productPacking || record.productPacking === "1") {
      return "1EA";
    }
    return record.productPacking;
  };

  /** 获取产品预计到货日期 = 订单创建时间 + 产品货期 */
  const getSkuExpectedDeliveryDate = record => {
    if (!orderDetail?.createdDate) {
      return "-";
    }
    const afterDate = Number(record.productDeliveryDay ?? 0);
    return getFlagDate2Date(afterDate, orderDetail.createdDate);
  };

  useEffect(() => {
    fetchDetail();
    return () => {
      useLoadingHook.hideLoading("ucenterApp");
    };
  }, []);

  /** 渲染表格表头文字-带说明 */
  const renderOtherQuantityTitle = () => {
    return (
      <div className="quantity-header-title">
        <span>
          <Tooltip title="产品取消数量">
            <QuestionCircleOutlined />
          </Tooltip>
          &nbsp;取消
        </span>
        <span>
          <Tooltip title="产品发货数量">
            <QuestionCircleOutlined />
          </Tooltip>
          &nbsp;发货
        </span>
        <span>
          <Tooltip title="产品开票数量">
            <QuestionCircleOutlined />
          </Tooltip>
          &nbsp;开票
        </span>
      </div>
    );
  };

  // 产品列配置
  const productColumns: ColumnsType<IOrderProductColumnType> = [
    {
      title: "行号",
      dataIndex: "rowNum",
      width: 55,
      align: "center",
      render: (text, record, index) => <span>{text}</span>,
    },
    {
      title: "SKU",
      dataIndex: "productSku",
      width: 80,
      align: "center",
      ellipsis: false,
      render: (text, record, index) => (
        <a className="product-detail-link" href={`/product/${record.productNo}?sku=${record.productSku}`} target="_blank">
          {record.productSku}
        </a>
      ),
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      width: 100,
      align: "center",
      ellipsis: false,
      render: (text, record, index) => <span className="product-packing-unit">{record.productName}</span>,
    },
    {
      title: "包装",
      width: 72,
      align: "center",
      ellipsis: false,
      render: (text, record, index) => <span className="product-packing-unit">{getSkuPacking(record)}</span>,
    },
    {
      title: "价格单位",
      dataIndex: "productUnit",
      width: 70,
      align: "center",
      render: text => <span className="product-packing-unit">{text}</span>,
    },
    {
      title: "单价",
      width: 90,
      dataIndex: "productPrice",
      align: "center",
      render: productPrice => <span className="text-red-highlight">{ghmallGuidePrice2Show(productPrice)}</span>,
    },
    {
      title: "数量",
      width: 60,
      dataIndex: "productQuantity",
      align: "center",
    },
    {
      title: "小计",
      width: 90,
      align: "center",
      render: (text, record, index) => (
        <span className="text-red-highlight">{record.productPrice === 0 ? "询价" : price2Thousand(record.productPrice * (record.productQuantity - (record.cancelQuantity || 0)))}</span>
      ),
    },
    // {
    //   title: renderOtherQuantityTitle,
    //   width: 150,
    //   dataIndex: "cancelQuantity",
    //   key: "cancelQuantity",
    //   align: "center",
    //   className: "quantity-row",
    //   render: (text, record, index) => (
    //     <span className="quantity-body-title">
    //       <span>{record?.cancelQuantity || 0}</span>
    //       <span>{record?.deliverQuantity || 0}</span>
    //       <span>{record?.receiptQuantity || 0}</span>
    //     </span>
    //   ),
    // },
    {
      title: "预计到货",
      width: 90,
      align: "center",
      render: (text, record, index) => <span className="">{getSkuExpectedDeliveryDate(record)}</span>,
    },
    {
      title: "备注",
      dataIndex: "remark",
      align: "center",
      ellipsis: false,
      render: (text, record, index) => (
        <Tooltip placement="topLeft" title={record.remark}>
          <div className="text-ellipsis">{record.remark}</div>
        </Tooltip>
      ),
    },
    {
      title: "操作",
      width: 80,
      align: "center",
      ellipsis: false,
      render: (text, record, index) => (
        <Button
          type="link"
          onClick={() => {
            handleBuyAgain(record);
          }}
        >
          再次购买
        </Button>
      ),
    },
  ];
  // 物流信息列配置
  const logisticsColumns: ColumnsType<IOrderLogistic> = [
    {
      title: "日期",
      dataIndex: "shipperDeliveryDate",
      align: "center",
      render: (text, record, index) => <span style={{ fontSize: "13px" }}>{text}</span>,
    },
    {
      title: "件数",
      dataIndex: "shipperQuantity",
      align: "center",
      render: (text, record, index) => <span style={{ fontSize: "13px" }}>{text}</span>,
    },
    {
      title: "重量",
      dataIndex: "shipperWeight",
      align: "center",
      render: (text, record, index) => <span style={{ fontSize: "13px" }}>{text}</span>,
    },
    {
      title: "出库单号",
      dataIndex: "erpOutboundSn",
      align: "center",
      render: (text, record, index) => <span style={{ fontSize: "13px" }}>{text}</span>,
    },
    {
      title: (
        <>
          <Popover content={"部份中转地区无法提供到站电话,请打发货电话查询"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          物流联系方式
        </>
      ),
      dataIndex: "shipperArrivalPhoneRemark",
      align: "center",
      render: (text, record, index) => <span style={{ fontSize: "13px" }}>{text}</span>,
    },
    {
      title: (
        <>
          <Popover content={"注意：货运到货不含当天发货的时间"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          预计到货天数
        </>
      ),
      dataIndex: "shipperArrivalDays",
      align: "center",
      render: (text, record, index) => <span style={{ fontSize: "13px" }}>{text}</span>,
    },
    {
      title: "发运路线",
      dataIndex: "shipperRoute",
      align: "center",
      render: (text, record, index) => <Tag color="gold">{text}</Tag>,
    },
  ];

  /** 订单详情-sku列表根据行号排序 */
  const sortOrderProductSkuByRowNum = (skus: any) => {
    if (!skus || !skus.length) {
      return [];
    }
    // 执行排序
    skus?.sort((a, b) => a.rowNum - b.rowNum);
    return skus;
  };

  /** 操作人-显示控制 */
  const calcOperator = (operator: string) => {
    return isNaN(Number(operator)) ? operator : "工作人员";
  };

  // 查询物流信息
  const handleQueryLogisticsEvent = async () => {
    setLogisticsModalVisible(true);
  };

  /** 解析异常联系信息 */
  const getExceptionContactInfo = () => {
    if (!orderDetail || (!orderDetail?.exceptionContact && !orderDetail?.exceptionContactPhone)) {
      return "-";
    }
    return `${orderDetail?.exceptionContact ?? ""}${orderDetail?.exceptionContactPhone ? "(" + orderDetail.exceptionContactPhone + ")" : ""}`;
  };

  /** 渲染支付信息 */
  function renderPayDescriptions() {
    // 未支付，啥也不渲染
    if (!orderDetail?.isPaid && !orderDetail?.paymentState) {
      return null;
    }
    // 支付情况
    return (
      <>
        <Descriptions.Item label="付款方式" span={1}>
          <span>{orderConstants.COLLECTION_TYPE_OPTIONS[orderDetail.collectionType]}</span>
        </Descriptions.Item>
        <Descriptions.Item label="付款状态" span={1}>
          <span className="blue-tip">{orderConstants.PAYMENT_STATE_OPTIONS[orderDetail.paymentState]}</span>
        </Descriptions.Item>
        <Descriptions.Item label="付款金额" span={1}>
          <span className="red-tip">{orderDetail?.paymentReceivedAmount ? `￥${price2Thousand(orderDetail?.paymentReceivedAmount)}` : "-"}</span>
        </Descriptions.Item>
        {orderDetail?.paidTime && (
          <Descriptions.Item label="付款时间" span={1}>
            <span>{orderDetail?.paidTime}</span>
          </Descriptions.Item>
        )}
        {orderDetail?.transaction_id && (
          <Descriptions.Item label="第三方支付流水" span={1}>
            <span>{orderDetail?.transaction_id}</span>
          </Descriptions.Item>
        )}
      </>
    );
  }

  // 再次购买商品
  const handleBuyAgain = async item => {
    // 重新加入购物车
    const shopCartProducts = item?.productSkuId
      ? {
          selected: 1,
          quantity: item.productQuantity,
          productSkuId: item.productSkuId,
        }
      : null;
    if (!shopCartProducts) {
      message.warn("该产品不允许重新加购，可能产品已下架！");
      return false;
    }
    const res = await shoppingCartHook.insert(shopCartProducts, false);
    if (res) {
      message.success("加购成功，请前往我的购物车页进行下单操作！");
    }
    return false;
  };

  return (
    <div className={style.wrapper}>
      <Helmet encodeSpecialCharacters={false}>
        <title>订单详情-{orderNo}</title>
      </Helmet>
      <UCenterCard
        title={`订单详情「${orderNo}」`}
        extra={
          <>
            {orderDetail?.orderState === 20 && orderDetail.isPaid === 0 ? (
              <a className="return2list" target="_blank" href={`/pay/payment?sn=${orderNo}`}>
                前往支付
              </a>
            ) : null}
            {orderDetail?.hasLogistics ? (
              <a className="return2list" onClick={handleQueryLogisticsEvent}>
                查看物流
              </a>
            ) : null}
            <a className="return2list" onClick={async () => await fetchDetail()}>
              刷新订单
            </a>
            <a className="return2list" href={"/ucenter/trade/order"}>
              返回订单
            </a>
          </>
        }
      />
      <Card className="order-container" bodyStyle={{ padding: "10px 20px" }} bordered>
        {/* 订单信息 */}
        <div className="order-info">
          <Descriptions column={3} title="订单信息">
            {/* 第一行 */}
            <Descriptions.Item label="下单时间" span={1}>
              <span className="create-date">{orderDetail?.createdDate}</span>
            </Descriptions.Item>
            <Descriptions.Item label="合同单号" span={1}>
              <span className="order-no">{orderDetail?.orderNo}</span>
            </Descriptions.Item>
            <Descriptions.Item label="订单状态" span={1}>
              {orderDetail && <Tag color={orderConstants.findOrderTagColorByValue(orderDetail?.orderState)}>{orderConstants.findOrderDescByValue(orderDetail.orderState)}</Tag>}
            </Descriptions.Item>
            {/* 第二行 */}
            <Descriptions.Item label="公司名称" span={1}>
              {orderDetail?.customerCompany || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="运输方式" span={1}>
              <span>{orderDetail?.transportType || "-"}</span>
            </Descriptions.Item>
            <Descriptions.Item label="异常联系人" span={1}>
              {getExceptionContactInfo()}
            </Descriptions.Item>
            {/* 第三行 */}
            {renderPayDescriptions()}
            {/* 第四行 */}
            <Descriptions.Item label="客户PO号" span={1}>
              {orderDetail?.customerPo || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="订单备注" span={1}>
              {orderDetail?.remark || "-"}
            </Descriptions.Item>
          </Descriptions>
        </div>

        {/* 订单产品列表 */}
        <div className="products-card">
          <div className="products-card-title">购买产品</div>
          <Table
            rowKey="id"
            className="order-products"
            columns={productColumns}
            dataSource={sortOrderProductSkuByRowNum(orderDetail?.products)}
            bordered={false}
            pagination={false}
            size={"large"}
            summary={tableData => {
              let totalQuantity = 0;
              let totalAmount = 0;
              tableData.forEach(({ productQuantity, productPrice }) => {
                totalQuantity += productQuantity;
                totalAmount += productPrice * productQuantity;
              });
              return (
                <>
                  <Table.Summary.Row>
                    <Table.Summary.Cell colSpan={8} index={0} />
                    <Table.Summary.Cell colSpan={3} index={1}>
                      <div className="summary-cell-row">
                        <span className="title">产品数量:</span>
                        <span>{$tools.keepDigitDecimal(totalQuantity, PRODUCT_QUANTITY_DECIMAL_DIGITS)}</span>
                      </div>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell colSpan={8} index={0} />
                    <Table.Summary.Cell colSpan={3} index={1}>
                      <div className="summary-cell-row">
                        <span className="title">产品金额:</span>
                        <span>￥{tableData.findIndex(item => item.productPrice === 0) === -1 ? price2Thousand(totalAmount) : "询价"}</span>
                      </div>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell colSpan={8} index={0} />
                    <Table.Summary.Cell colSpan={3} index={1}>
                      <div className="summary-cell-row">
                        <span className="title">运费:</span>
                        <span>+ ￥{price2Thousand(orderDetail?.freightFee)}</span>
                      </div>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell colSpan={8} index={0} />
                    <Table.Summary.Cell colSpan={3} index={1}>
                      <div className="summary-cell-row">
                        <span className="title">订单总金额:</span>
                        <span className="price-main">￥{tableData.findIndex(item => item.productPrice === 0) === -1 ? price2Thousand(orderDetail?.totalMoney) : "询价"}</span>
                      </div>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </>
              );
            }}
          />
        </div>

        {/* 物流信息 */}
        {orderDetail?.outboundLogisticsDtos?.length ? (
          <div className="products-card">
            <div className="products-card-title">物流信息</div>
            <Table
              rowKey="id"
              className="order-products"
              columns={logisticsColumns}
              dataSource={sortOrderProductSkuByRowNum(orderDetail?.outboundLogisticsDtos)}
              bordered={false}
              pagination={false}
              size={"large"}
            />
          </div>
        ) : null}

        {/* 收货信息 */}
        <div className="receiving-address">
          <Descriptions column={3} title="收货信息">
            {/* 第一行 */}
            <Descriptions.Item label="收货人" span={1}>
              {receivingAddressDescriptions?.nickname}
            </Descriptions.Item>
            <Descriptions.Item label="联系号码" span={1}>
              {receivingAddressDescriptions?.phone || receivingAddressDescriptions?.telephone}
            </Descriptions.Item>
            <Descriptions.Item label="单位/公司" span={1}>
              {receivingAddressDescriptions?.company_name || "-"}
            </Descriptions.Item>
            {/* 第二行 */}
            <Descriptions.Item label="收货地址" span={1}>
              {receivingAddressDescriptions?.address}
            </Descriptions.Item>
          </Descriptions>
        </div>

        {/* 发票信息 */}
        <div className="receipt">
          <Descriptions title="发票信息" column={3}>
            {receiptDescriptions && Object.keys(receiptDescriptions).length ? (
              <>
                <Descriptions.Item span={1} label="发票类型">
                  <span className="receipt-type">{receiptDescriptions?.receipt_type_name || "-"}</span>
                </Descriptions.Item>
                {receiptDescriptions?.vat_name ? (
                  <Descriptions.Item span={1} label="发票抬头">
                    {receiptDescriptions.vat_name || "-"}
                  </Descriptions.Item>
                ) : null}
                {receiptDescriptions?.vat_id ? (
                  <Descriptions.Item span={1} label="信用代码/税号" labelStyle={{ width: "110px" }}>
                    {receiptDescriptions.vat_id || "-"}
                  </Descriptions.Item>
                ) : null}

                {receiptDescriptions?.receipt_people ? (
                  <Descriptions.Item span={1} label="收票人姓名">
                    {receiptDescriptions.receipt_people || "-"}
                  </Descriptions.Item>
                ) : null}
                {receiptDescriptions?.receipt_people_telephone ? (
                  <Descriptions.Item span={1} label="收票人手机">
                    {receiptDescriptions.receipt_people_telephone || "-"}
                  </Descriptions.Item>
                ) : null}
                {receiptDescriptions?.receipt_people_telephone ? (
                  <Descriptions.Item span={1} label="开户银行">
                    {receiptDescriptions.receipt_people_telephone || "-"}
                  </Descriptions.Item>
                ) : null}

                {receiptDescriptions?.receipt_bank_account ? (
                  <Descriptions.Item span={1} label="开户账号">
                    {receiptDescriptions.receipt_bank_account || "-"}
                  </Descriptions.Item>
                ) : null}
                {receiptDescriptions?.receipt_people_phone ? (
                  <Descriptions.Item span={1} label="注册号码">
                    {receiptDescriptions.receipt_people_phone || "-"}
                  </Descriptions.Item>
                ) : null}
                {receiptDescriptions?.receipt_register_address ? (
                  <Descriptions.Item span={1} label="注册地址">
                    {receiptDescriptions.receipt_register_address || "-"}
                  </Descriptions.Item>
                ) : null}

                {receiptDescriptions?.receipt_address ? (
                  <Descriptions.Item span={1} label="收票地址">
                    {receiptDescriptions.receipt_address || "-"}
                  </Descriptions.Item>
                ) : null}
              </>
            ) : (
              <Descriptions.Item span={1}>
                <Tag color="error">不开发票</Tag>
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>

        {/* 订单追踪 */}
        <div className="order-tracking">
          <div className="products-card-title">订单追踪</div>
          {orderTrackingLogs.length ? (
            <ul className="tracking-logs">
              {orderTrackingLogs.map(log => (
                <li key={log.id} className="tracking-log-item">
                  <div className="create-date">{log.createdDate}</div>
                  <div className="operator">{calcOperator(log.operator)}</div>
                  <div className="action">{log.action}</div>
                </li>
              ))}
            </ul>
          ) : (
            <Empty description="暂无记录" imageStyle={{ height: 60 }} />
          )}
        </div>
      </Card>
      {orderDetail?.hasLogistics ? (
        <OrderLogisticsModal
          modalVisible={logisticsModalVisible}
          changeModalVisible={setLogisticsModalVisible}
          memberId={(orderDetail?.memberId ?? "").toString()}
          orderNo={orderDetail?.orderNo ?? ""}
        />
      ) : null}
    </div>
  );
}
