import { IMiddleware } from "@midwayjs/core";
import { Inject, Middleware } from "@midwayjs/decorator";
import { Context, NextFunction } from "@midwayjs/koa";
import { v4 as uuidv4 } from "uuid";
import { IpUtil } from "@/utils/ip.util";
import { ILayoutService } from "@/service/layout.service";

@Middleware()
export class RequestMiddleware implements IMiddleware<Context, NextFunction> {
  @Inject("LayoutService")
  layoutService: ILayoutService;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      ctx.logger_unq_id = uuidv4().replace(/-/g, "");
      ctx.userId = "visitor";
      ctx.request_ip = IpUtil.getClientIP(ctx);
      ctx.layoutService = this.layoutService;
      ctx.websiteLanguage =
        ctx.cookies.get("WEBSITE_LANGUAGE", {
          signed: false,
        }) || "zh";
      return next();
    };
  }

  static getName(): string {
    return "request";
  }
}
