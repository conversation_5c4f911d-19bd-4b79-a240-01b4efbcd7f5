import React, { useContext, useEffect, useRef, useState } from "react";
import { IContext, SProps } from "ssr-types-react";
import { Button, Descriptions, Empty, message, notification, Spin, Tabs, Tooltip } from "antd";
import style from "./detail.module.less";
import { useStoreContext } from "ssr-common-utils";
import TmBreadcrumb from "@/components/TmBreadcrumb";
import GoodsImageZoom from "@/pages/product/components/GoodsImageZoom";
import Add2ShopCart from "@/pages/search/parts/Add2ShopCart";
import SvgIcon from "@/components/SvgIcon";
import { HeartOutlined, HeartFilled, UndoOutlined, DownloadOutlined, FilePdfOutlined, QuestionCircleOutlined, FileDoneOutlined, CustomerServiceOutlined } from "@ant-design/icons";
import { ghmallDiscountPrice2Show, ghmallGuidePrice2Show } from "@/utils/price-format.util";
import { useProductCollection } from "@/hooks/useProductCollection";
import { useFootprint } from "@/hooks/useFootprint";
import { useLoginModal } from "@/hooks/useLoginModal";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import { useQueryStock } from "@/hooks/useQueryStock";
import { useOss } from "@/hooks/useOss";
import { useAgreementPrice } from "@/hooks/useAgreementPrice";
import crudGHTech from "@/apis/ghtech";
import { productRelationMSDSType } from "@/typings/ghtech.interface";
import { GHTECH_COA_PREFIX, COMMON_IMAGE_PATHS, BRAND_HUADA_ID } from "@/constants/common";
import { downloadFromOssFilepath } from "@/utils/download.util";
import { debounce } from "lodash";
import ReportCOA from "@/pages/report/parts/ReportCOA";
import { getProductDangerText } from "@/constants/product";

export default function ProductDetail(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  // 产品详情数据
  const product = state.product;
  /* ======================================= state start======================================= */
  // 当前选中的sku 规格
  const [currentSkuSelected, setCurrentSkuSelected] = useState<any>(null);
  /* 指导价 */
  const [guidePrice, setGuidePrice] = useState<string>("");
  /* 产品属性 */
  const [productAttributesItems, setProductAttributesItems] = useState<any[]>();
  /* 折扣价 */
  const [discountPrice, setDiscountPrice] = useState<string>("");
  const [isCollection, setIsCollection] = useState<boolean>(false);
  /* 库存查询 */
  const [stockLoading, setStockLoading] = useState<boolean>(false);
  const ossService = useOss();
  // 协议价格查询hook
  const agreementPriceHook = useAgreementPrice();
  /** msds */
  const [productRelationMSDS, setProductRelationMSDS] = useState<productRelationMSDSType>();
  const [msdsLoading, setMsdsLoading] = useState(false);
  // 初始化产品详情页的图片
  const [productImages, setProductImages] = useState<string[]>([]);
  // 自营品牌
  const [ownerBrand, setOwnerBrand] = useState<string[]>([]);
  /* ======================================= hook end======================================= */

  const add2ShopCartRef = useRef<any>();
  const collectionHooks = useProductCollection();
  const footprintHooks = useFootprint();
  const loginFormModal = useLoginModal();
  const shoppingCartHook = useShoppingCart();
  const useQueryStockHook = useQueryStock();
  /* ======================================= state end======================================= */

  /* ======================================= 初始化 ========================================== */
  useEffect(() => {
    initProductImages();
    getDefaultPrice();
    productAttrHandle();
    setOwnerBrand(state.layoutInitData.hotBrandIntroduce.filter(item => item?.brand_type === "owner")?.map(item => item.brand_id));
  }, []);
  /* ======================================= 初始化 ========================================== */

  /* ======================================= 方法 start======================================= */
  const getProductDangerTip = (record: any) => {
    return `${getProductDangerText(product?.isDanger && "DANGER")} ${getProductDangerText(product?.isExplode && "EXPLODE")} ${getProductDangerText(product?.isPoison && "POISON")}`.trim();
  };
  const initProductImages = () => {
    const productHasImage = product.productImages || product.headImage;
    const temp = productHasImage ? productHasImage.split(",").map(item => ossService.generateOssFullFilepath(item)) : [COMMON_IMAGE_PATHS.DEFAULT_PRODUCT];
    setProductImages(temp);
  };

  /** 设置coa链接地址 */
  const setCoaLinkPath = (filepath: string | undefined) => {
    if (!filepath) {
      return "";
    }
    return `${/[\u4e00-\u9fa5]/.test(filepath) ? GHTECH_COA_PREFIX.NEW : GHTECH_COA_PREFIX.OLD}${filepath}`;
  };

  /** 规格选择切换 */
  const selectSkuEvent = async sku => {
    setCurrentSkuSelected(sku);
    setGuidePrice(ghmallGuidePrice2Show(sku.guidePrice));
    if (state.userLoginState) {
      setDiscountPrice(ghmallGuidePrice2Show(sku.discountPrice));
    }

    // 检查选中sku的收藏状态
    await checkSkuCollectionState(sku);
  };

  /** sku查询库存 */
  const refreshSkuStockHandle = debounce(async () => {
    setStockLoading(true);
    await useQueryStockHook.getStockRemarkBySkuId(currentSkuSelected.id, add2ShopCartRef?.current?.getOrderQuantity() || 1);
    setStockLoading(false);
  }, 300);

  /** 获取默认价格 */
  const getDefaultPrice = () => {
    const params = new URLSearchParams(props.location.search);
    const skus = product?.skus;
    if (!skus || Array.from(skus).length === 0) {
      return;
    }
    let filterSku = null;
    if (params.get("sku")) {
      filterSku = skus.filter(item => item.sku === params.get("sku"))[0];
      filterSku && selectSkuEvent(filterSku);
    }
    if (!guidePrice && !filterSku) {
      const maxPrice = Math.max.apply(
        null,
        skus.map(item => item.guidePrice)
      );
      const minPrice = Math.min.apply(
        null,
        skus.map(item => item.guidePrice)
      );
      setGuidePrice(maxPrice === minPrice ? ghmallGuidePrice2Show(minPrice) : `${ghmallGuidePrice2Show(minPrice)} - ${ghmallGuidePrice2Show(maxPrice)}`);
      const discountPriceRange = "登录可见";
      if (state.userLoginState) {
        const aptDiscountRange = skus.map(item => item.aptDiscount);
        // 计算折扣区间
        const discountMaxPrice = ghmallDiscountPrice2Show(maxPrice, Math.max.apply(null, aptDiscountRange));
        const discountMinPrice = ghmallDiscountPrice2Show(minPrice, Math.min.apply(null, aptDiscountRange));
        setDiscountPrice(maxPrice === minPrice ? discountMinPrice : `${discountMinPrice} - ${discountMaxPrice}`);
      } else {
        setDiscountPrice(discountPriceRange);
      }
    }
  };

  /** 取消或添加-收藏事件处理 */
  const collectSkuEvent = async () => {
    // 判断是否已登录
    if (!state.userLoginState) {
      message.warning("您还未登录，请登录！");
      loginFormModal.open();
      return;
    }

    if (!currentSkuSelected) {
      notification.warning({ message: "请选择需要收藏的规格！" });
      return;
    }

    if (!isCollection) {
      // 添加收藏
      const success = await collectionHooks.create({ skuId: currentSkuSelected.id });
      success && setIsCollection(true);
    } else {
      // 取消收藏
      const success = await collectionHooks.deleteBySkuId(currentSkuSelected.id);
      success && setIsCollection(false);
    }
  };

  /** 检查切换的sku的收藏状态 */
  const checkSkuCollectionState = async sku => {
    if (currentSkuSelected?.id === sku.id) {
      return;
    }
    // 用户登录的情况下，才去获取当前切换的sku去状态
    if (state.userLoginState) {
      const result = await collectionHooks.isCollection(sku.id);
      setIsCollection(result);
      // 登录后才会保存浏览记录-添加sku到足迹-浏览记录
      await footprintHooks.addHistory(product, sku);
    }
  };

  /** 处理数量变化时的协议价格查询 */
  const handleQuantityChange = async (quantity: number) => {
    // 只有在用户登录且有当前选中的SKU时才查询协议价格
    if (state?.userLoginState && currentSkuSelected) {
      try {
        const result = await agreementPriceHook.queryMemberVolumeDiscount(
          quantity,
          product?.brandId?.toString(),
          currentSkuSelected.id
        );
        // console.log('协议价格查询结果:', result)
        // 如果查询成功且有协议价格配置，更新折扣价显示
        if (result && result.orderQuantityConfiguration && result.productSkuDto?.discountPrice) {
          // console.log('协议价格查询结果:', result.productSkuDto.discountPrice)
          setDiscountPrice(ghmallGuidePrice2Show(result.productSkuDto.discountPrice));
        }
      } catch (error) {
        console.error("查询协议价格失败:", error);
      }
    }
  };

  /* 处理属性tab */
  const productAttrHandle = () => {
    if (product?.attributes && product?.attributes?.length) {
      setProductAttributesItems(product.attributes.filter(item => item.value));
      // const tabs = product.attributes.map((attr, idx) => {
      //   if (attr?.value) {
      //     return {
      //       label: attr.label,
      //       key: idx,
      //       children: (
      //         <div className="attr-box" key={idx}>
      //           <div className="attr-value" dangerouslySetInnerHTML={{ __html: attr.value || "-" }} />
      //         </div>
      //       ),
      //     };
      //   }
      // });
      // setProductAttributesItems(tabs);
    }
  };

  /** 下载msds */
  const downloadMsdsFile = (filepath: any) => {
    const filepathFull = ossService.generateOssFullFilepath(filepath);
    const filename = `${product.brandName}-${product.productName}-msds.pdf`;
    downloadFromOssFilepath(filepathFull, filename);
  };

  /** 立刻购买 */
  const handleQuickBuy = debounce(async () => {
    if (!state.userLoginState) {
      message.warning("请先登录！");
      loginFormModal.open();
      return;
    }
    if (!currentSkuSelected) {
      notification.warning({ message: "请先选择产品规格！" });
      return;
    }

    const productQuantity = add2ShopCartRef?.current?.getOrderQuantity() || 1;

    if (state.isOpenProductMinPackingRatioControl && currentSkuSelected?.packingRatio > 1 && productQuantity % Number(currentSkuSelected?.packingRatio) !== 0) {
      notification.warning({ message: `产品购买数量必须为 ${currentSkuSelected?.packingRatio} 的倍数` });
      return;
    }

    const result = await shoppingCartHook.insert(
      {
        quantity: productQuantity,
        productSkuId: currentSkuSelected.id,
        cartType: "BUY_NOW",
      },
      false
    );
    if (result) {
      // 前往结算页
      window.location.href = "/pay/confirm?way=BUY_NOW";
    } else {
      notification.error({ message: "网站开小差了，请稍后再试或联系客服处理！" });
    }
  }, 300);

  // 联系客服
  const handleToContactus = () => {
    window.open("/ucenter/contactus");
  };

  // 产品标题
  const generateProductTitle = (): string => {
    let packing = "";
    let sku = "";
    if (currentSkuSelected?.packing && /^[A-Za-z]+/.test(currentSkuSelected?.packing)) {
      packing = currentSkuSelected?.packing;
    }
    if (currentSkuSelected?.sku) {
      sku = currentSkuSelected?.sku;
    }
    return `${product.productName} ${packing}`;
  };

  /** 切换到coa/msds tab栏时 */
  const handleChangeContentTab = async (tab: string) => {
    if (tab === "coa") {
      !productRelationMSDS && (await getProductMSDSByRelation());
    }
  };

  /**
   * 产品msds单个获取
   */
  const getProductMSDSByRelation = async () => {
    setMsdsLoading(true);
    const [err, res] = await crudGHTech
      .getProductMSDSByRelation({
        productName: product.productName,
        // cas: product.cas
      })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return;
    }
    setMsdsLoading(false);
    if (res?.data) {
      setProductRelationMSDS(res.data);
    }
  };

  const handleDownloadMSDS = async () => {
    if (productRelationMSDS?.filepath) {
      downloadMsdsFile(productRelationMSDS?.filepath);
      return;
    }
    const [err, res] = await crudGHTech
      .getProductMSDSByRelation({
        productName: product.productName,
      })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return;
    }
    setMsdsLoading(false);
    if (res?.data) {
      setProductRelationMSDS(res.data);
      downloadMsdsFile(res.data?.filepath);
    } else {
      message.warning("暂无该产品MSDS资料");
    }
  };

  const renderDangerText = () => {
    const { brandId, isDanger, isExplode, isExplosion, isPoison } = product;
    const hasDangerTag = isExplode || isExplosion || isPoison;
    if (isDanger && hasDangerTag) {
      return `危险化学品（${getProductDangerText(isExplode && "EXPLODE")} ${getProductDangerText(isPoison && "POISON")} ${getProductDangerText(isExplosion && "EXPLOSION")}）`;
    } else if (!isDanger && hasDangerTag) {
      return `非危险化学品（${getProductDangerText(isExplode && "EXPLODE")} ${getProductDangerText(isPoison && "POISON")} ${getProductDangerText(isExplosion && "EXPLOSION")}）`;
    } else if (isDanger && !hasDangerTag) {
      return `危险化学品`;
    } else {
      return ownerBrand.includes(brandId) ? `非危险化学品（普通货物）` : `暂无`;
    }
  };

  /* ======================================= 方法 end======================================= */

  /** 渲染-产品选项 */
  const tabItem = [
    {
      label: "商品详情",
      key: "detail",
      children: (
        <>
          <div className="product-detail-box">
            <Descriptions
              title={<span className="desc-title">产品信息</span>}
              column={2}
              bordered
              contentStyle={{
                fontWeight: 400,
                color: "#1d1819",
                fontSize: "15px",
              }}
              labelStyle={{
                fontWeight: 400,
                color: "#6d7278",
                width: "180px",
                fontSize: "15px",
              }}
            >
              <Descriptions.Item label="产品编号">{product.productNo}</Descriptions.Item>
              <Descriptions.Item label="产品货号">{product.itemNo}</Descriptions.Item>
              <Descriptions.Item label="CAS号">{product.cas}</Descriptions.Item>
              <Descriptions.Item label="品牌">{product.brandName}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{product.productName}</Descriptions.Item>
              <Descriptions.Item label="产品英文名称">{product.productNameEn || "-"}</Descriptions.Item>
              <Descriptions.Item label="产品别名">{product.alias_name || "-"}</Descriptions.Item>
              <Descriptions.Item label="存储方式">{product.storage_type || "-"}</Descriptions.Item>
              <Descriptions.Item label="产品危险性" span={2}>
                <span className="dangerText">{renderDangerText()}</span>
              </Descriptions.Item>
            </Descriptions>
          </div>
          {/* 产品属性 */}
          {productAttributesItems?.length > 0 ? (
            <div className="intro-box">
              <div className="intro-label">产品属性</div>
              <div className="intro-content">
                <Descriptions
                  column={1}
                  bordered
                  contentStyle={{
                    fontWeight: 400,
                    color: "#1d1819",
                    fontSize: "15px",
                    lineHeight: "28px",
                  }}
                  labelStyle={{
                    fontWeight: 400,
                    color: "#6d7278",
                    fontSize: "15px",
                    width: "180px",
                  }}
                >
                  {productAttributesItems?.some(item => item) ? (
                    productAttributesItems?.map((item, index) => {
                      return (
                        <Descriptions.Item label={item?.label} key={index}>
                          <div dangerouslySetInnerHTML={{ __html: item?.value }}></div>
                        </Descriptions.Item>
                      );
                    })
                  ) : (
                    <Empty />
                  )}
                </Descriptions>
              </div>
            </div>
          ) : null}

          {/* 产品详情 */}
          {product?.content ? (
            <div className="intro-box">
              <div className="intro-label">产品详情</div>
              <div className="intro-content">{product?.content ? <div dangerouslySetInnerHTML={{ __html: product?.content }} /> : <Empty />}</div>
            </div>
          ) : null}
        </>
      ),
    },
    {
      label: "MSDS与COA",
      key: "coa",
      children: (
        <div>
          <Spin spinning={msdsLoading}>
            <div className="coa-box">
              <div className="main-label">MSDS</div>
              <div className="main-content">
                msds化学品安全说明书:{" "}
                {productRelationMSDS?.filepath ? (
                  <a title="点击下载" onClick={() => downloadMsdsFile(productRelationMSDS.filepath)}>
                    <FilePdfOutlined />
                    &nbsp;{productRelationMSDS.productName}&nbsp;
                    <DownloadOutlined />
                  </a>
                ) : (
                  <span>暂无资料</span>
                )}
              </div>
            </div>
          </Spin>
          <div className="coa-box">
            <div className="main-label">COA</div>
            {/* 华大品牌才显示COA搜索 */}
            {product.brandId === BRAND_HUADA_ID ? <ReportCOA initValue={product?.productName} /> : <div className="main-content">暂无资料</div>}
          </div>
        </div>
      ),
    },
    {
      label: "价格说明",
      key: "price",
      children: (
        <div className="price-intro-box">
          <div className="main-label">价格说明</div>
          <div className="main-content">
            <p>商品在采购的展示标价，具体的成交价格可能因商品参加活动等情况发生变化，也可能随着购买数量不同或所选规格不同而发生变化，如用户与业务员线下达成协议，以线下协议的结算价格为准</p>
            <div className="sub-label">会员价/优惠价：</div>
            <p>商品参与营销活动的活动价格，也可能随着购买数量不同或所选规格不同而发生变化，最终以订单结算页价格为准。</p>
            <div className="sub-label">特别提示：</div>
            <p>
              商品详情页中（含主图）以文字或者图片形式标注的抢购价等价格可能是在特定活动时段下的价格，商品的具体价格以订单结算页价格为准或者是您与业务员或客服联系后协商达成的实际成交价格为准；如您发现活动商品价格或活动信息有异常，购买前联系客服。
            </p>
          </div>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className={style.wrapper}>
        <TmBreadcrumb breadcrumbItem={[{ label: "产品", href: `/search?keyword=${product.productName}&brandId=${product.brandId}` }, { label: "产品详情" }]} />
        <div className="product">
          {product?.skus ? (
            <>
              <div className="product-top">
                <div className="product-banner-box">
                  <GoodsImageZoom imageLists={productImages} />

                  <div className="product-menu-box">
                    <div className="product-menu-item" onClick={handleDownloadMSDS}>
                      <FileDoneOutlined className="product-menu-icon" />
                      <span className="product-menu-label">MSDS</span>
                    </div>
                    <div className="product-menu-item" onClick={handleToContactus}>
                      <CustomerServiceOutlined className="product-menu-icon" />
                      <span className="product-menu-label">联系客服</span>
                    </div>
                  </div>
                </div>
                <div className="product-item-info">
                  <div className="title-box">
                    <span className="main-title">{generateProductTitle()}</span>
                    <div className="sub-title">
                      <span>{product.productNameEn || product.productName}</span>
                      {renderDangerText() !== "暂无" && <span className="danger">{renderDangerText()}</span>}
                      <span>货号:{`${product.itemNo}`} </span>
                      <span>CAS:{`${product.cas || "暂无"}`}</span>
                    </div>
                    <div className="collection" onClick={async () => await collectSkuEvent()}>
                      {!isCollection ? (
                        <>
                          <HeartOutlined />
                          &nbsp;收藏
                        </>
                      ) : (
                        <>
                          <HeartFilled />
                          &nbsp;已收藏
                        </>
                      )}
                    </div>
                  </div>
                  <div className="sell-point-box">
                    {ownerBrand.includes(product.brandId) && <span className="sell-point-box-label active">光华科技旗下品牌</span>}
                    <span className="sell-point-box-label">{product.brandName}品牌</span>
                    <span className="sell-point-box-label">官方自营</span>
                    <span className="sell-point-box-label">正品保证</span>
                    <span className="sell-point-box-label">价格实惠</span>
                  </div>
                  <div className="sell-price-box">
                    <div className={`item-price ${state.userLoginState && "isHasDiscount"}`}>
                      <span>市场价：￥</span>
                      {guidePrice}{" "}
                    </div>
                    {state.userLoginState ? (
                      <div className="item-price-discount">
                        <span>优惠价：￥</span>
                        {discountPrice}{" "}
                      </div>
                    ) : null}
                  </div>
                  <div className="reminder-box">
                    <div>
                      温馨提示：商品有客户专属优惠价，价格更实惠，请
                      {!state.userLoginState && (
                        <span>
                          <a
                            onClick={() => {
                              loginFormModal.open();
                            }}
                          >
                            登录
                          </a>
                          查看或
                        </span>
                      )}
                      <a onClick={handleToContactus}>联系客服</a>
                      咨询。
                    </div>
                  </div>
                  {currentSkuSelected ? (
                    <div className="product-spec-box">
                      {/* <div className="spec-select-attr-label">规格属性：</div> */}
                      <div className="spec-select-attr">
                        <ul>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">品牌</div>
                            <div className="spec-attr-item-value">
                              {product.brandName ? (
                                <a className="spec-attr-item-link" href={`/search?keyword=&brandId=${product.brandId}`}>
                                  {product.brandName}{" "}
                                </a>
                              ) : (
                                "-"
                              )}
                            </div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">产品代码</div>
                            <div className="spec-attr-item-value">{currentSkuSelected?.sku ?? "-"}</div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">产品等级</div>
                            <div className="spec-attr-item-value">{currentSkuSelected?.spec ?? "-"}</div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">包装规格</div>
                            <div className="spec-attr-item-value">{currentSkuSelected?.packing + (currentSkuSelected?.attributeModel ? "（" + currentSkuSelected?.attributeModel + "）" : "")}</div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">最小包装量</div>
                            <div className="spec-attr-item-value">{currentSkuSelected?.packingRatio ?? "-"}</div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">包装单位</div>
                            <div className="spec-attr-item-value">{currentSkuSelected?.unit ?? "-"}</div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">{state.isOpenProductMinPackingRatioControl ? "起订数量" : "建议起订量"}</div>
                            <div className="spec-attr-item-value">
                              {currentSkuSelected?.packingRatio ?? "-"}
                              {/* 大包装-有包装系数限制-提示 */}
                              {currentSkuSelected?.sku && Number(currentSkuSelected?.packingRatio) > 1 && (
                                <Tooltip
                                  placement="right"
                                  title={`温馨提示：该规格产品${state.isOpenProductMinPackingRatioControl ? "最小起订量" : "建议起订量"}：${currentSkuSelected?.packingRatio}`}
                                >
                                  &nbsp;&nbsp;
                                  <QuestionCircleOutlined />
                                </Tooltip>
                              )}
                            </div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">产品名称</div>
                            <div className="spec-attr-item-value">{currentSkuSelected?.skuName ?? "-"}</div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">产品英文</div>
                            <div className="spec-attr-item-value">{currentSkuSelected?.skuNameEn || "-"}</div>
                          </li>
                          <li className="spec-attr-item">
                            <div className="spec-attr-item-label">库存</div>
                            <div className="spec-attr-item-value">
                              {currentSkuSelected?.id ? (
                                useQueryStockHook.skuStockResults[currentSkuSelected?.id] ? (
                                  useQueryStockHook.skuStockResults[currentSkuSelected?.id] === "请咨询客服" ? (
                                    <span className="spec-attr-item-link" onClick={handleToContactus}>
                                      联系客服
                                    </span>
                                  ) : (
                                    <span>{useQueryStockHook.skuStockResults[currentSkuSelected?.id]}</span>
                                  )
                                ) : (
                                  <Button className="stock-search-btn" disabled={!currentSkuSelected} loading={stockLoading} size="small" onClick={refreshSkuStockHandle} type="link">
                                    点击查询
                                  </Button>
                                )
                              ) : (
                                "请选择规格查询"
                              )}
                            </div>
                          </li>
                          {currentSkuSelected?.weight ? (
                            <li className="spec-attr-item">
                              <div className="spec-attr-item-label">包装重量</div>
                              <div className="spec-attr-item-value">{currentSkuSelected?.weight ?? "-"}</div>
                            </li>
                          ) : null}
                        </ul>
                      </div>
                    </div>
                  ) : null}
                  <div className="product-spec-box">
                    <div className="spec-select-label">规格：</div>
                    <div className="spec-select-column">
                      {product?.skus.map((sku, idx) => {
                        // const skuDesc = `${sku.sku} ${sku.packing ? " | " + sku.packing : ""}`;
                        const skuDesc = `${sku.packing ? sku.packing : ""}`;
                        return (
                          <div className="spec-select-row" key={idx}>
                            <div
                              title={skuDesc + " | 指导价:￥" + ghmallGuidePrice2Show(sku.guidePrice)}
                              onClick={async () => await selectSkuEvent(sku)}
                              className={`spec-select-item ${sku === currentSkuSelected ? "spec-select-item-active" : undefined}`}
                            >
                              {skuDesc}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                  <div className="add2cart-box">
                    <span className="label">数量：</span>
                    <div className="add2cart-panel">
                      <Add2ShopCart
                        ref={add2ShopCartRef}
                        item={currentSkuSelected}
                        showHeaderExpand={false}
                        showAdd2CartDesc="加入购物车"
                        onQuantityChange={handleQuantityChange}
                      />
                      <button className="customer-service-btn" onClick={handleQuickBuy}>
                        <SvgIcon iconClass="buy-now" />
                        &nbsp;&nbsp;立即购买
                      </button>
                      {/* <button className="customer-service-btn" onClick={handleToContactus}>
                        <SvgIcon iconClass="home-customer" />
                        &nbsp;&nbsp;咨询客服
                      </button> */}
                    </div>
                  </div>
                </div>
              </div>
              <div className="product-content">
                <Tabs defaultActiveKey="1" type="card" size="large" items={tabItem} onChange={handleChangeContentTab} />
              </div>
            </>
          ) : (
            <>
              <Empty description={"暂无当前产品数据或产品已下架"} />
            </>
          )}
        </div>
        {/* 登录弹窗 */}
        <loginFormModal.FormModal />
      </div>
    </>
  );
}
