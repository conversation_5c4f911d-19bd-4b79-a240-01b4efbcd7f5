import React, { useEffect, useState } from "react";
import { CascadePicker, CascadePickerOption, Input, Space } from "antd-mobile";
import areaCrud from "@/apis/platform/area";
import { PickerValue } from "antd-mobile/es/components/picker-view";

interface Props {
  placeholder?: string;
  disabled?: boolean; // 是否禁用操作
  defaultValue?: PickerValue[]; // 默认值
  onMonitorCascadeChange?: any;
}

const TmMobileAreaCascade = (prop: Props) => {
  const [options, setOptions] = useState<CascadePickerOption[]>([]);
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const fetchAreaCascade = async (): Promise<void> => {
    setLoading(true);
    const [err, res] = await areaCrud
      .getAreaCascade()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err) {
      const generateAreas = (data: any) => {
        return data.map(({ children, id, name }) => {
          if (children?.length) {
            children = generateAreas(children);
          }
          return {
            label: name,
            value: id,
            children: children ?? null,
          };
        });
      };
      setOptions(generateAreas(Array.from(res.data)));
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchAreaCascade();
  }, []);
  return (
    <>
      <CascadePicker
        defaultValue={prop?.defaultValue ?? []}
        loading={loading}
        loadingContent={"暂无数据，请刷新重试"}
        options={options}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        onConfirm={async (val, extend) => {
          prop?.onMonitorCascadeChange && prop.onMonitorCascadeChange(val);
        }}
      >
        {items => {
          return (
            <Space align="center" onClick={() => setVisible(true)}>
              <Input readOnly className="input" value={items.every(item => item === null) ? prop?.placeholder : items.map(item => item?.label).join(" ")} />
            </Space>
          );
        }}
      </CascadePicker>
    </>
  );
};
TmMobileAreaCascade.defaultProps = {
  placeholder: "请选择省市区",
  disabled: false,
  defaultValue: [],
};
export default TmMobileAreaCascade;
