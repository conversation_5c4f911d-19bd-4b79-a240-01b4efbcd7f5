import { Modal } from "antd";
import { ReactNode } from "react";

interface TmConfirmModalType {
  content: ReactNode | string;
  title?: ReactNode | string;
  confirmText?: string;
  cancelText?: string;
}

/** 二次封装Modal.confirm 支持 async/await, 返回字符串: confirm 确定, cancel 取消 */
const TmConfirmModal = async (props: TmConfirmModalType) =>
  await new Promise(resolve => {
    const { content = "", title = "提示", confirmText = "确认", cancelText = "取消" } = props;

    Modal.confirm({
      title,
      content,
      okText: confirmText,
      getContainer: document.getElementById('app') || document.body,
      cancelText,
      centered: true,
      onOk: () => {
        resolve("confirm");
      },
      onCancel: () => {
        resolve("cancel");
      }
    });
  });

export default TmConfirmModal;
