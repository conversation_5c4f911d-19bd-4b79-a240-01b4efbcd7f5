import { StorageUtil, WEBSITE_LANGUAGE } from "@/utils/storage.util";

const state = {
  locale: "zh",
};
const CHANGE_LOCALE = "CHANGE_LOCALE";

function reducer(state: any, action: any) {
  switch (action.type) {
    case CHANGE_LOCALE:
      StorageUtil.getInstance().set(WEBSITE_LANGUAGE, action.payload?.locale || "zh");
      return { ...state, ...(action.payload || "zh") };
    case "CHANGE_LOCALE_INIT":
      return { ...state, ...(action.payload || "zh") };
  }
}
export { state, reducer };
