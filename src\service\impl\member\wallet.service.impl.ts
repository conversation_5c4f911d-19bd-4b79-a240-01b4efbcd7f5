import { Provide, Scope, <PERSON>opeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IWalletService } from "@/service/member/wallet.service";
import { CommonUtil } from "@/utils/common.util";

@Provide("WalletService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class WalletServiceImpl extends BaseService implements IWalletService {
  /** 账户余额 */
  async getWallet(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/wallet/${memberId}/wallet`));
  }

  /** 账户流水 */
  async getWalletBillFlow(memberId: string, params: any): Promise<any> {
    params = CommonUtil.convertSearchCreatDate(params)
    const result = await this.easyHttp.get(`/api/members/wallet/${memberId}/bill-flow`, params);
    return this.easyResponse(result);
  }

  /** 充值记录 */
  async getWalletRecharge(memberId: string, params: any): Promise<any> {
    params = CommonUtil.convertSearchCreatDate(params)
    const result = await this.easyHttp.get(`/api/members/wallet/${memberId}/recharge`, params);
    return this.easyResponse(result);
  }

  /** 创建会员余额充值交易 */
  async addTradeRecharge(data: { memberId: string; price: number }): Promise<any> {
    return this.easyResponse(await this.easyHttp.post(`/api/trade/recharge`, data));
  }

  /** 取消充值订单 */
  async cancelRecharge(params: { rechargeSn: string; memberId: string }): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete(`/api/members/wallet/${params.memberId}/recharge-cancel`, params));
  }
}
