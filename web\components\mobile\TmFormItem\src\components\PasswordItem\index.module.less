// prefix.
@ghmall-prefix: ghmall-m-;
@ghmall-lang-en: ~"@{ghmall-prefix}lang-en-us";
// color.
@ghmall-white: #fff;
@ghmall-black: #000;

//theme
@ghmall-theme: #52c41a;
@ghmall-theme-default: #bfbfbf;
@ghmall-theme-selection: #1890ff;
@ghmall-error: #ff4d4f;
@ghmall-font: @ghmall-black;
@ghmall-selection: fade(@ghmall-theme-selection, 80%);
// password
@ghmall-passport-strength: #999;
// gap.
@ghmall-gap: 8px;

// font-size.
@ghmall-font-size: 10px;
@ghmall-font-size-sm: 12px;
@ghmall-font-size-normal: 14px;
@ghmall-font-size-base: 16px;

// properties
.properties(@key, @value: 8) {
  @{key}: (@value / @ghmall-font-size-base) * @ghmall-font-size;
}
// flex.
.flex-fix(@align: center, @justify: center, @direction: row) {
  display: flex;
  align-items: @align;
  justify-content: @justify;
  flex-direction: @direction;
}

@ghmall-password: ~"@{ghmall-prefix}password";
:global {
  .@{ghmall-password} {
    &-tips {
      .flex-fix(flex-start, center, column);
      color: var(--ghmall-font, @ghmall-font);
    }

    &-strength {
      &-item {
        .flex-fix(center, flex-start);
        line-height: 24px;
        width: auto;
        height: 25px;
        font-size: 12px;

        &:first-child {
          justify-content: flex-start;
        }

        .anticon {
          &.failed,
          &.success {
            font-size: 14px;
            color: var(--ghmall-error, @ghmall-error);
            margin-right: 5px;
          }

          &.success {
            color: var(--ghmall-theme, @ghmall-theme);
          }
        }

        .theme-color {
          color: var(--ghmall-theme, @ghmall-theme);
          margin-left: 5px;
        }
      }
      /*显示强度进度条*/
      &-group {
        .flex-fix();
        .@{ghmall-password} {
          &-strength {
            width: 38px;
            height: 12px;
            background: var(--ghmall-passport-strength, @ghmall-passport-strength);
            display: inline-block;
            margin-right: 5px;

            &:first-child {
              margin-left: 5px;
            }

            &:last-child {
              margin-right: 0;
            }

            &.active {
              background: var(--ghmall-theme, @ghmall-theme);
            }
          }
        }
      }
    }

    &-input {
      border-color: var(--ghmall-theme, @ghmall-theme-default);
      outline: none;

      &:not(.ant-input-affix-wrapper-disabled) {
        &:hover,
        &:focus {
          border-color: var(--ghmall-theme, @ghmall-theme-selection);
          box-shadow: 0 0 2px fade(@ghmall-theme-selection, 20%);
        }
      }

      input.adm-input-element {
        background: transparent !important;
      }
    }
  }
  .@{ghmall-lang-en} {
    .@{ghmall-password} {
      &-strength {
        &-item {
          width: 300px;
          .properties(margin-bottom);
          line-height: 20px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
