import { Body, Controller, Del, Get, Inject, Post, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { IWalletService } from "@/service/member/wallet.service";
import { walletQueryDto } from "~/typings/data/member/wallet";

@Controller("/api/ucenter/my-wallet", { middleware: [AuthenticationMiddleware] })
export class WalletController extends BaseController {
  @Inject("WalletService")
  walletService: IWalletService;

  @Get("/wallet")
  async getMemberWalletBalance() {
    const res = await this.walletService.getWallet(this.getMemberId());
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 流水
   */
  @Get("/bill-flow")
  async getMemberWalletBillFlow(@Query() params: Partial<walletQueryDto>) {
    const res = await this.walletService.getWalletBillFlow(this.getMemberId(), params);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 充值订单
   */
  @Get("/recharge")
  async getMemberWalletRecharge(@Query() params: Partial<walletQueryDto>) {
    const res = await this.walletService.getWalletRecharge(this.getMemberId(), params);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * 充值-创建会员余额充值交易
   * @param data
   */
  @Post("/trade-recharge")
  async addTradeRecharge(@Body() data: { price: number }) {
    const res = await this.walletService.addTradeRecharge({
      ...data,
      memberId: this.getMemberId(),
    });
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * 取消充值
   * @param data
   */
  @Del("/recharge-cancel")
  async cancelRecharge(@Body() data: { rechargeSn: string }) {
    const res = await this.walletService.cancelRecharge({ rechargeSn: data.rechargeSn, memberId: this.getMemberId() });
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }
}
