import { useState, useContext } from "react";
import { getMemberVolumeDiscount } from "@/apis/agreement-price";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import { AgreementPriceResult } from "~/typings/data/agreement-price/agreement-price";

export const useAgreementPrice = () => {
  const { state } = useContext<IContext>(useStoreContext());
  const [loading, setLoading] = useState<boolean>(false);
  const [agreementPriceData, setAgreementPriceData] = useState<AgreementPriceResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * 查询会员产品区间折扣
   * @param orderQuantity 下单数量
   * @param brandId 品牌ID
   * @param productSkuId 规格产品ID
   */
  const queryMemberVolumeDiscount = async (
    orderQuantity: number,
    brandId?: string,
    productSkuId?: number
  ) => {
    // 检查用户是否登录
    if (!state?.userLoginState) {
      setError("用户未登录");
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const params = {
        orderQuantity,
        brandId,
        productSkuId,
      };

      const [err, res] = await getMemberVolumeDiscount(params)
        .then(res => [null, res])
        .catch(err => [err, null]);

      if (err) {
        setError(err.message || "查询协议价格失败");
        return null;
      }

      // 中间层返回的数据结构：{ success: boolean, data: any, message: string }
      console.log('协议价格查询结果:', res)
      if (res?.data?.statusCode === 200 && res?.data?.data) {
        setAgreementPriceData(res.data.data);
        return res.data.data;
      } else {
        setError(res?.data?.message || "查询协议价格失败");
        return null;
      }
    } catch (error: any) {
      setError(error.message || "查询协议价格失败");
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 清除数据
   */
  const clearData = () => {
    setAgreementPriceData(null);
    setError(null);
  };

  return {
    loading,
    agreementPriceData,
    error,
    queryMemberVolumeDiscount,
    clearData,
  };
};
