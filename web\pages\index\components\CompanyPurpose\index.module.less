.itemBeforeImgLoop(@index) when (@index <= 4) {
  &:nth-child(@{index})::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 56px;
    height: 56px;
    background: url("@@img/purpose-sprites.png") -10px-68px * (@index - 1) -10px;
  }
  .itemBeforeImgLoop(@index+1);
}

.wrapper {
  :global {
    .purpose {
      .flex-row(space-between, center);
      margin-top: @plate-margin-top;
      padding: 27px 46px;
      background: @main-bg-color-white;
      .purpose-item {
        width: 144px;
        height: 56px;
        font-size: 20px;
        font-weight: 600;
        .flex-row(flex-end, center);
        cursor: pointer;
        color: @main-text-color;
        position: relative;
        .itemBeforeImgLoop(0);
      }
    }
  }
}
