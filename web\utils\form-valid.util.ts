// 验证手机号码
export const validateMobile = async (obj: any, value: any) => {
  if (!value) {
    return await Promise.resolve();
  }
  const phoneReg = /^1[3456789]\d{9}$/;
  if (!phoneReg.test(value)) {
    throw "手机号码格式不正确，请重新输入";
  }
  return await Promise.resolve();
};
// 验证邮箱
export const validateEmail = async (obj: any, value: any) => {
  if (!value) {
    return await Promise.resolve();
  }
  const mailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
  if (!mailReg.test(value)) {
    throw "邮箱格式不正确，请重新输入";
  }
  return await Promise.resolve();
};
// 验证电话号码
export const validatePhone = async (obj: any, value: any) => {
  if (!value) {
    return await Promise.resolve();
  }
  if (value.length > 13) {
    throw "电话号码格式不正确，请重新输入";
  }
  if (/^\d+$/.test(value)) {
    return await Promise.resolve();
  }
  const phoneReg = /^[0-9]+([-]{1}[0-9]+){0,1}$/;
  if (!phoneReg.test(value)) {
    throw "电话号码格式不正确，请重新输入";
  }
  return await Promise.resolve();
};
// 验证邮编
export const validatePostNo = async (obj: any, value: any) => {
  if (!value) {
    return await Promise.resolve();
  }
  const phoneReg = /^[0-9]{6}$/;
  if (!phoneReg.test(value)) {
    throw "邮编格式不正确，请重新输入";
  }
  return await Promise.resolve();
};

// 验证手机号码与邮箱
export const validateGhmallLoginAccount = async (obj: any, value: any) => {
  if (!value) {
    return await Promise.resolve();
  }
  const phoneReg = /^1[3456789]\d{9}$/;
  const mailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
  if (!mailReg.test(value) && !phoneReg.test(value)) {
    throw "邮箱格式或手机号码格式不正确";
  }
  return await Promise.resolve();
};
export default {
  validateMobile,
  validateEmail,
  validatePhone,
  validatePostNo,
  validateGhmallLoginAccount,
};
