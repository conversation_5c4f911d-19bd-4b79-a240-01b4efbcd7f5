/**
 * 光华精选-产品类型
 */
export interface GHSelectionType {
  id: number;
  url: string;
  title: string;
  create_date: string;
  content: string;
  creator: string;
  sub_title: string;
  head_icon: string;
  images: string[];
}

/** 板块模块-类型 */
export interface IHomepageDataType {
  flagCode: string;
  title: string;
  headImage: string;
  subTitle?: string;
  description?: string;
  galleryImages?: string;
  moreLink?: string;
  optionalJson?: string;
  sortOrder: number;
  products: IHomePagePlateProductType[]
}

/** 首页-展示产品的类型 */
export interface IHomePagePlateProductType {
  cas: string;
  classification: string;
  description: string;
  discountPrice: number;
  filed1: string;
  filed2: string;
  flagCode: string;
  guidePrice: number;
  productName: string;
  productNo: string;
  remark: string;
  showImage: string;
  sku: string;
  skuGuidePrice: number;
  skuId: string;
  packing: string;
}
