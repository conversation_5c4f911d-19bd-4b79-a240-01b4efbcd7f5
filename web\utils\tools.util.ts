import { RECEIPT_TYPE_OPTIONS } from "@/constants/order";
import { IReceiptUnderlineType } from "@/typings/member.interface";

/**
 * 工具类集合
 *
 */
class TmTools {
  /**
   * Whether the string content is empty.
   * @param str
   * @param format
   */
  isEmpty(str: any, format = false): boolean | string {
    let result: any = str === null || str === "" || typeof str === "undefined";
    if (format) result = this.formatEmpty(str);
    return result;
  }

  /**
   * Format string content.
   * @param str
   * @param formatter
   */
  formatEmpty(str?: string, formatter?: string): string | undefined {
    if (this.isEmpty(str)) return formatter ?? "-";
    return str;
  }

  /**
   * Check Password by rules.
   * @param password
   */
  checkPassword(password: string): boolean {
    const regExp = /^[A-Za-z0-9~!@#$%^&*()_+=\-.,]{6,32}$/;
    return regExp.test(password);
  }

  /**
   * Get the password strength.
   * return a number level ( 1 - 4 ).
   * @param password
   */
  getPasswordStrength(password: string): number {
    const reg = {
      lower: /[a-z]/,
      upper: /[A-Z]/,
      number: /[\d]/,
      character: /[~!@#$%^&*()_+=\-.,]/,
    };
    let strength = 0;
    if (reg.lower.test(password)) {
      strength++;
    }
    if (reg.upper.test(password)) {
      strength++;
    }
    if (reg.number.test(password)) {
      strength++;
    }
    if (reg.character.test(password)) {
      strength++;
    }
    return strength;
  }

  /**
   * Whether it is a number.
   * @param number
   */
  isNumber(number: any): boolean {
    return typeof number === "number" && isFinite(number);
  }

  /**
   * 转成rem.
   * @param num
   * @returns
   */
  convert2Rem(num: any): any {
    return $tools.isNumber(num) ? `${this.px2Rem(parseInt(num.toString()))}rem` : num ? (/%|px|rem|em|rpx/g.test(num.toString()) ? num : `${this.px2Rem(parseInt(num.toString()))}rem`) : null;
  }

  /**
   * Unit conversion.
   * @param value
   * @param base
   */
  px2Rem(value: number | undefined, base = 16): number | undefined {
    return value ? Math.round((value / base) * 100) / 100 : value;
  }

  /**
   * Generate unique string.
   * @param upper
   * @param prefix
   * @returns {string}
   */
  uid(upper = false, prefix?: string): string {
    let str = (this.random() + this.random() + this.random() + this.random() + this.random() + this.random() + this.random() + this.random()).toLocaleUpperCase();
    if (prefix) str = prefix + str;
    return upper ? str.toUpperCase() : str.toLowerCase();
  }

  /**
   * random.
   * @returns {string}
   */
  random(): string {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }

  /**
   * 移除form中不必要的字段
   * @param {*} form form对象
   * @param {*} fields 不需要的字段的数组, exp: ['creator', 'modifier', 'created_date', 'modified_date']
   */
  removeFormFields = (form, fields: string[] = []) => {
    if (!form && fields.length <= 0) {
      return;
    }

    fields.forEach(field => {
      delete form[field];
    });
  };

  /** 获取发票省市区地址详情 */
  getAddressByReceiptData(receipt: any) {
    // 过滤掉4个直辖市
    const addressWhites = ["北京市", "上海市", "天津市", "重庆市"];
    if (addressWhites.includes(receipt?.cityInfo?.name)) {
      return `${receipt?.cityInfo?.name || ""}${receipt?.districtInfo?.name || ""}${(receipt?.receiptAddress || "")}`;
    }

    return `${receipt?.provinceInfo?.name || ""}${receipt?.cityInfo?.name || ""}${(receipt?.districtInfo?.name || "")}${(receipt?.receiptAddress || "")}`;
  }

  /** 获取订单的发票类型字符串 */
  generateOrderReceiptObj = (receiptInfoStr): IReceiptUnderlineType => {
    const receiptInfo = JSON.parse(receiptInfoStr);
    receiptInfo.receipt_type_name = receiptInfo?.receipt_type !== undefined ? RECEIPT_TYPE_OPTIONS[receiptInfo.receipt_type] : "不开发票";
    return receiptInfo;
  };

  /** 自定义-字符串驼峰转下划线方法，lodash的snakeCase会将: field2 => field_2, 不满足业务需求 */
  camelToSnake = (str: string) => {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /** 数值-保留n位小数，四舍五入 */
  keepDigitDecimal = (num: number, digit = 1) => {
    const target = parseFloat(String(num))
    if (isNaN(target)) { return false }
    return Math.round(num * Math.pow(10, digit)) / Math.pow(10, digit)
  }
}
export const $tools: TmTools = new TmTools();
