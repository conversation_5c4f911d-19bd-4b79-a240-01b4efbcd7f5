import { BaseService } from "@/common/base/base.service";
import { ICcMallService } from "@/service/member/cc-mall.service";
import { Config, Inject, Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { FORMAT, httpError } from "@midwayjs/core";
import { CacheManager } from "@midwayjs/cache";
import cacheKeyConstant from "@/common/constants/cache.constant";

@Provide("CcMallService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class CcMallServiceImpl extends BaseService implements ICcMallService {
  cacheKeyPrefix = cacheKeyConstant.BUSINESS.CC_MALL_LOGIN;

  @Config("systemModalOpenConfig")
  systemModalOpenConfig;

  @Config("systemConfig")
  systemConfig;

  @Inject()
  cacheManager: CacheManager;

  private readonly crypto = require("crypto");

  async generateQuickLoginUrl(cmid: string): Promise<any> {
    const ccOldMemberLoginOpen = this.systemModalOpenConfig.ccOldMemberLoginOpen;
    if (!ccOldMemberLoginOpen) {
      throw new httpError.BadRequestError({ message: "请求被禁止，该功能暂未支持！" });
    }
    const ccMallFrontendDomain = this.systemConfig.ccMallFrontendDomain;
    // 生成临时code，存放在redis中，有效期‘2’分钟
    const mid = this.getMemberId();
    const code = await this.generateSignName(cmid, mid);
    return `${ccMallFrontendDomain}account/quicklogin?code=${code}`;
  }

  async findCodeValue(code: string): Promise<any> {
    const cacheKey = `${this.cacheKeyPrefix}:${code}`;
    const cmid = await this.cacheManager.get(cacheKey);
    if (!cmid) {
      throw new httpError.BadRequestError({ message: "请求被禁止，code非法或已过期！" });
    }
    await this.cacheManager.del(cacheKey);
    return cmid;
  }

  private async generateSignName(cmid: string, mid: string): Promise<string> {
    const tmp: string = `${mid}_${cmid}`;
    const md5 = this.crypto.createHash("md5");
    const md5str = `${md5.update(tmp).digest("hex")}${new Date().getTime()}`;
    const code = this.crypto.createHash("md5").update(md5str).digest("hex");
    // 存入redis,有效期2分钟
    await this.cacheManager.set(`${this.cacheKeyPrefix}:${code}`, cmid, { ttl: (FORMAT.MS.ONE_MINUTE * 2) / 1000 });
    return code;
  }
}
