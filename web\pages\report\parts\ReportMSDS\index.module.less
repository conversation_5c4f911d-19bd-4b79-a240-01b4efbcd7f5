.wrapper {
  :global {
    .msds {
      padding: 10px;
      background-color: #ffffff;
      .search-bar {
        width: 100%;
        padding-bottom: 12px;
        display: flex;
        flex-direction: column;
        .ant-input-search-button {
          height: 39px !important;
        }
        .description {
          margin: 8px 0;
          color: #999;
          font-size: 13px;
        }
      }

      .search-result {
        .result-tip {
          font-size: 14px;
          margin-bottom: 10px;
        }
        ul {
          width: 100%;
          flex-wrap: wrap;
          border: 1px solid #eeeeee;
          min-height: 200px;
          li {
            .flex-row(space-between, center);
            height: 40px;
            margin: 16px;
            border-bottom: 1px solid #dbdbdb;
            color: #545557;
            &:hover {
              color: red;
            }
          }
        }
      }

      // antd-empty
      .ant-empty {
        padding: 36px;
      }
    }
  }
}
