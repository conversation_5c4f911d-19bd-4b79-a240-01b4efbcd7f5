// 密钥对生成 http://web.chacuo.net/netrsakeypair
const publicKey =
  "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCiSteSS1kZyz6e23h1r38buDi+ZhpH47YUeGNIrDf4np2Oqww84/W4X6CCIq3B9tU6Ze3MRdd1Ci8I3a33RgBe9vs78TpTHQjQTKT0OLZfCZS6eQ9vJMPLSWGp5pRUE0R6wmkued7FK56tsrBdXy0YpvEgzVnUqT03fMu0YGekEwIDAQAB";
const privateKey = ""; // 不建议此处放私钥

// 加密
export function encrypt(txt) {
  const JSEncrypt = require("jsencrypt/bin/jsencrypt.min");
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey); // 设置公钥
  return encryptor.encrypt(txt); // 对需要加密的数据进行加密
}

// 解密
export function decrypt(txt) {
  const JSEncrypt = require("jsencrypt/bin/jsencrypt.min");
  const encryptor = new JSEncrypt();
  encryptor.setPrivateKey(privateKey); // 设置私钥
  return encryptor.decrypt(txt); // 对数据进行解密
}
