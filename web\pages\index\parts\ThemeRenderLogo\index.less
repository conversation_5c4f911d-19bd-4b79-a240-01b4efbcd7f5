@theme-default-width: 214px;
@theme-default-height: 300px;
@theme-default-padding: 24px;
@theme-title-font-size: @font-size-24;
@theme-title-font-weight: 600;
@theme-content-font-size: 16px;
@theme-content-top: 8px;
@theme-tag-width: 96px;

.theme-render-logo {
  .flex-col(flex-start, center);
  width: @theme-default-width;
  height: @theme-default-height;
  padding: @theme-default-padding;
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
  transition: all 0.1s ease-in-out;
  display: inline-block;
  overflow-y: hidden;
  .title {
    .flex-row(center, center);
    color: @main-text-color;
    font-size: @theme-title-font-size;
    font-weight: @theme-title-font-weight;
    line-height: 1;
  }
  .sub-title {
    width: 100%;
    line-height: 1;
    display: inline-block;
    text-align: center;
    margin-top: @theme-content-top;
    font-size: @theme-content-font-size;
  }
  .theme-tags {
    margin-top: @theme-content-top;
    list-style: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .flex(column, center, center);
    gap: 12px;
    .theme-tag {
      width: @theme-tag-width;
      border: 1px solid #fff;
      border-radius: 4px;
      color: #fff;
      .flex-center(row);
    }
  }
}
