import { useContext, useMemo } from "react";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";
import { SYSTEM_OSS_CDN } from "@/constants/system";
import commonConstant from "@/constants/common";

export const useOss = () => {
  const { state } = useContext<IContext>(useStoreContext());

  const getOssCdnDomain = useMemo(() => {
    return () => {
      const domain = state.layoutInitData?.ossCdnDomain || SYSTEM_OSS_CDN;
      return domain.endsWith("/") ? domain.slice(0, domain.length - 1) : domain;
    };
  }, []);
  const generateOssFullFilepath = (ossPath: string, errorPath: string = commonConstant.COMMON_IMAGE_PATHS.AVATAR_DEFAULT): string | undefined => {
    if (!ossPath) {
      return errorPath;
    }
    ossPath = ossPath.startsWith("/") ? ossPath.substring(1) : ossPath;
    return `${getOssCdnDomain()}/${ossPath}`;
  };
  return {
    getOssCdnDomain,
    generateOssFullFilepath,
  };
};
