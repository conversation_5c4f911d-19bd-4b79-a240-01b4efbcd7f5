import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from "react";
import { Button, InputNumber, notification } from "antd";
import { UndoOutlined } from "@ant-design/icons";
import SvgIcon from "@/components/SvgIcon";
import style from "./index.module.less";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import { useQueryStock } from "@/hooks/useQueryStock";
import { useAdd2ShopCartInput } from "@/hooks/useAdd2ShopCart";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";

interface IProps {
  item?: any;
  showAdd2CartDesc?: string;
  showHeaderExpand?: boolean;
  showHeaderExpandClass?: string;
  parentWatchInputChange?: () => void;
  onQuantityChange?: (quantity: number) => void; // 新增：数量变化回调
}

export const Add2ShopCart = forwardRef((prop: IProps, ref?: any) => {
  const { state } = useContext<IContext>(useStoreContext());
  const [stockLoading, setStockLoading] = useState<boolean>(false);
  const shoppingCartHook = useShoppingCart();

  // 库存查询hook
  const useQueryStockHook = useQueryStock();

  // 处理数量变化时的回调
  const handleQuantityChange = (quantity: number) => {
    if (prop.onQuantityChange) {
      prop.onQuantityChange(quantity);
    }
  };

  // 使用修改后的 Hook，传入数量变化回调
  const useAdd2ShopCartInputHook = useAdd2ShopCartInput(handleQuantityChange);
  /* 初始化 */
  useEffect(() => {
    if (prop?.item) {
      const stepValue = Number(prop.item?.packingRatio || 1);
      useAdd2ShopCartInputHook.setCartQuantityStep(stepValue);
      useAdd2ShopCartInputHook.setPurchaseQuantity(stepValue);
    }
  }, [prop.item]);
  useImperativeHandle(ref, () => {
    // 需要将暴露的接口返回出去
    return {
      purchaseQuantity: useAdd2ShopCartInputHook.purchaseQuantity,
      getOrderQuantity,
      handleQueryProductStockEvent,
      getProductStockVal,
    };
  });

  /** ========================= 直接修改 end ========================= */
    // 加到购物车
  const add2ShopCartActionEvent = async () => {
      if (!prop.item) {
        return notification.warning({ message: "请先选择规格！" });
      }
      // 是否限制库存下单，默认关闭
      const openStockLimit = false;
      if (openStockLimit && prop.item.stock && useAdd2ShopCartInputHook.add2CartRef.current.value > prop.item.stock) {
        return notification.warning({ message: "超出可下单库存，请咨询客服！" });
      }

      const productQuantity = Number(useAdd2ShopCartInputHook.add2CartRef.current.value);
      if (state.isOpenProductMinPackingRatioControl && useAdd2ShopCartInputHook.cartStep > 1 && productQuantity % useAdd2ShopCartInputHook.cartStep !== 0) {
        notification.warning({ message: `产品购买数量必须为 ${useAdd2ShopCartInputHook.cartStep} 的倍数` });
        return;
      }

      await shoppingCartHook.insert({
        selected: 1,
        quantity: Number(useAdd2ShopCartInputHook.add2CartRef.current.value),
        productSkuId: prop.item.id,
      });
    };

  // 获取选择的下单数量
  const getOrderQuantity = (): number => {
    return Number(useAdd2ShopCartInputHook.add2CartRef?.current?.value ?? 1);
  };

  /** 触发-查询sku库存 */
  const handleQueryProductStockEvent = async () => {
    setStockLoading(true);
    await useQueryStockHook.getStockRemarkBySkuId(prop.item.id, getOrderQuantity());
    setTimeout(() => {
      setStockLoading(false);
    }, 100);
  };

  /** 获取查询结果 */
  const getProductStockVal = async () => {
    return useQueryStockHook.skuStockResults[prop.item.id];
  };

  return (
    <div className={style.wrapper}>
      {/* purchase-shop-cart */}
      <div className="purchase-shop-cart">
        <button onClick={() => useAdd2ShopCartInputHook.add2CartReduceEvent()} className="btn btn-reduce">
          -
        </button>
        <InputNumber onBlur={e => useAdd2ShopCartInputHook.add2CartBlurEvent(e)} onPressEnter={e => useAdd2ShopCartInputHook.add2CartBlurEvent(e)} className="input-box" ref={useAdd2ShopCartInputHook.add2CartRef} precision={0} min={1} value={useAdd2ShopCartInputHook.purchaseQuantity} defaultValue={1} controls={false} />
        <button onClick={() => useAdd2ShopCartInputHook.add2CartPlusEvent()} className="btn btn-add">
          +
        </button>
        <button title="加入到购物车" onClick={async () => await add2ShopCartActionEvent()} className="btn btn-add2cart">
          <SvgIcon iconClass="home-cart" />
          {prop.showAdd2CartDesc ? (
            <>
              <span>&nbsp;&nbsp;{prop.showAdd2CartDesc}</span>
            </>
          ) : null}
        </button>
        {prop.showHeaderExpand && (
          <div className={`${prop.showHeaderExpandClass}`}>
            <Button title="查询库存" icon={<UndoOutlined />} loading={stockLoading} size="small" danger onClick={handleQueryProductStockEvent}>
              查库存
            </Button>
            <span className="result-text">{useQueryStockHook.skuStockResults[prop.item?.id] ? useQueryStockHook.skuStockResults[prop.item.id] : ""}</span>
          </div>
        )}
      </div>
      {/* 底部拓展 */}
    </div>
  );
});
export default Add2ShopCart;
