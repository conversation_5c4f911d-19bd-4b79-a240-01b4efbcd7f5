@headCardBgColor: #ff4401; // #5d5d5d;
@contentTop: 32px;
@simplyInfoTextColor: #ffffff;
@simplyInfoBigFontSize: 18px;
@simplyInfoTitleFontSize: 16px;
@simplyInfoTextFontSize: 14px;
@simplyBtnFontSize: 10px;
@borderColor: #fad2ce;
@contentColor: #333333;
@contentLabelColor: #6d7278;
@contentPaddingOrMargin: 16px;
@balanceBgColor: #f8f8f8;
@simplyBigFontSize: 24px;
@contentTipColor: #ffffff;
@redTip: #e02020;
@blueTip: #0091ff;
@paymentTipColor: #f7b500;

.wrapper {
  :global {
    .balance-container {
      .balance-head {
        margin: 8px 0;
        display: flex;
        gap: @contentPaddingOrMargin;
        .balance-head-card {
          _:-ms-fullscreen,
          & {
            margin-right: @contentPaddingOrMargin;
          }
          width: 100%;
          padding: 6px 16px;
          background: @headCardBgColor;
          color: @simplyInfoTextColor;
          border-radius: 4px;
          .account {
            height: 90px;
            .flex-row(flex-start, center);
            .user {
              font-size: @simplyInfoTextFontSize;
            }
            .balance {
              width: 100%;
              font-size: @simplyBigFontSize;
              color: @simplyInfoTextColor;
            }
            .btn-add {
              color: @simplyInfoTextColor;
              &:hover,
              &:active {
                background: @simplyInfoTextColor;
                color: @redTip;
              }
            }
            .ant-btn.ant-btn-background-ghost:focus {
              border-color: @simplyInfoTextColor;
            }
          }
          .tip {
            .flex-col(center, flex-start);
            padding: 4px;
            color: @contentTipColor;
            border-radius: 4px;
            a,
            strong {
              font-weight: bolder;
              cursor: pointer;
              text-decoration: underline;
              color: #fff;
            }
          }
        }
      }

      .balance-information-box {
      }

      .red-tip {
        color: @redTip;
      }
      .blue-tip {
        color: @blueTip;
      }
    }
  }
}

// 弹窗-新增充值订单
:global {
  .trade-modal {
    .ant-modal-body {
      padding: 0px !important;
    }
    // 文字提示
    .ant-alert {
      padding: 6px 24px;
      .payment-tips {
        .flex-col();
        color: #6d7278;
        font-size: 12px;
        .payment-tip-bill-flow {
          cursor: pointer;
          text-decoration: underline;
          color: @headCardBgColor;
        }
      }
    }
    // 表单内容
    .payment-form {
      padding: 16px;
      .ant-form-item {
        margin-bottom: 8px;
        .ant-form-item-label {
          width: 80px;
        }
        &:nth-last-child(2) {
          margin-bottom: 16px;
        }
      }
      .btn-wrapper {
        width: 100%;
        .flex-center();
        gap: 16px;
        button {
          _:-ms-fullscreen,
          & {
            margin-right: 16px;
          }
        }
      }
    }
  }
}
