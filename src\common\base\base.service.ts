import { Destroy, Init, Inject, Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { Context } from "@midwayjs/koa";
import { EasyHttp } from "@/library/easyhttp/easy.http";
import { RedisService } from "@midwayjs/redis";
import { httpError } from "@midwayjs/core";

/**
 * 服务层基类
 */
@Provide("BaseService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export abstract class BaseService {
  @Inject()
  easyHttp: EasyHttp;

  @Inject()
  ctx: Context;

  @Inject()
  redisService: RedisService;

  @Init()
  async init() {}

  @Destroy()
  async destroy() {}

  /**
   * 快速响应封装
   *
   * @param result  响应结果
   * @param message 错误时的提示message
   * @protected
   */
  protected async easyResponse(result: any, message = "获取数据出错了，请稍后再试！"): Promise<any> {
    if (!result?.statusCode) {
      throw new httpError.BadRequestError({ message: result?.message ?? message });
    } else {
      const responseMessage = result?.message ?? message;
      switch (result?.statusCode) {
        case 400:
          throw new httpError.BadRequestError({ message: responseMessage });
        case 401:
          throw new httpError.UnauthorizedError({ message: responseMessage });
        case 403:
          throw new httpError.ForbiddenError({ message: responseMessage });
        case 422:
          throw new httpError.NotAcceptableError({ message: responseMessage });
        case 500:
          throw new httpError.InternalServerErrorError({ message: responseMessage });
        case 502:
          throw new httpError.GatewayTimeoutError({ message: responseMessage });
        case 504:
          throw new httpError.BadGatewayError({ message: responseMessage });
        default:
          break;
      }
      if (result?.statusCode >= 400) {
        throw new httpError.BadRequestError({ message: responseMessage });
      }
    }
    return result;
  }

  protected getDefaultHeaders(): object {
    return { "ghmall-member-id": this.getMemberId() };
  }

  protected setHeaders(headers: object): object {
    return {
      ...this.getDefaultHeaders(),
      ...headers,
    };
  }

  protected getMemberId() {
    return this.ctx.session?.userData?.memberId ?? null;
  }
}
