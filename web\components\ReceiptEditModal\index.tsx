import React, { memo, useEffect, useRef, useState } from "react";
import TmModal from "@/components/TmModal";
import style from "./index.module.less";
import { Form, Input, Button, Radio, Spin, message, FormInstance } from "antd";
import TmAreaCascade from "@/components/TmAreaCascade";
import { IReceiptType } from "@/typings/member.interface";
import crudReceipt from "@/apis/member/receipt";
import { validateEmail } from "@/utils/form-valid.util";

interface IEditModalProps {
  modalVisible: boolean;
  changeModalVisible: (bool: boolean) => void;
  mode: "edit" | "add";
  callbackFunc?: any;
  receiptId: number | undefined;
  receiptType: number;
}

const defaultForm: Partial<IReceiptType> & { area: number[] } = {
  receiptAddress: "",
  isDefault: 0,
  receiptBank: "",
  receiptBankAccount: "",
  receiptProvinceId: undefined,
  receiptCityId: undefined,
  receiptDistrictId: undefined,
  receiptPeople: "",
  receiptPeoplePhone: "",
  receiptPeopleTelephone: "",
  receiptRegisterAddress: "",
  receiptRegisterPhone: "",
  receiptType: 1,
  vatId: "",
  vatName: "",
  id: undefined,
  area: [],
};
const rules = {
  vatId: [
    { required: true, message: "信用代码/税号不能为空" },
    { pattern: /^[^\s]*$/, message: "禁止输入空格" },
    { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: "请输入正确有效的信用代码/税号" },
  ],
  vatName: [{ required: true, message: "企事业单位请填写单位全称,个人请填写全名" }],
  receiptBank: [{ required: true, message: "开户银行不能为空" }],
  receiptBankAccount: [{ required: true, message: "银行账号不能为空" }],
  email: [{ validator: validateEmail, message: "请输入正确的电子邮箱!" }],
  // receiptAddress: [{ required: true, message: "寄送详细地址不能为空" }],
  receiptRegisterAddress: [{ required: true, message: "注册地址不能为空" }],
  // receiptRegisterPhone: [{ required: true, message: "发票人电话不能为空" }],
  // // receiptPeople: [{ required: true, message: "发票人姓名不能为空" }],
  // receiptPeoplePhone: [
  //   { required: true, message: "发票人手机不能为空" },
  //   { size: 11, message: "请填入11位手机号" },
  //   { pattern: /^1([3-9][0-9])\d{8}$/, message: "请输入正确格式的手机号" },
  // ],
  // area: [{ required: true, message: "请选择发票寄送省市区" }],
};

export default memo(function ReceiptEditModal(props: IEditModalProps) {
  const { mode, receiptId, modalVisible, changeModalVisible, receiptType } = props;

  const isEditMode = mode === "edit";
  const receiptTypeStr = receiptType === 0 ? "13%增值税专用发票" : "普通发票";
  const title = isEditMode ? `编辑: ${receiptTypeStr}` : `新增: ${receiptTypeStr}`;
  /* ======================================= use state start======================================= */
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const currentFormRef = useRef<FormInstance>(null);
  const tmAreaCascadeRef = useRef<any>(null);
  /* ======================================= use state end======================================= */

  /* ======================================= 监听|mounted start======================================= */
  useEffect(() => {
    if (modalVisible) {
      form.setFieldsValue({
        ...defaultForm,
      });
      mode === "edit" && fetchReceiptDetailEvent();
    } else {
      currentFormRef?.current?.resetFields();
    }
  }, [modalVisible]);
  /* ======================================= 监听|mounted end======================================= */

  /* ======================================= method start======================================= */
  /** 发票-详情 */
  const fetchReceiptDetailEvent = async () => {
    const [err1, res1] = await crudReceipt
      .show(receiptId)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err1) {
      const receipt = res1.data;
      receipt.area = [receipt?.receiptProvinceId, receipt?.receiptCityId, receipt?.receiptDistrictId];
      form.setFieldsValue({
        ...receipt,
      });
      tmAreaCascadeRef?.current?.setTargetValFunc(receipt?.area);
    }
  };
  /** 取消操作 */
  const handleCancel = () => {
    changeModalVisible(false);
    currentFormRef?.current?.resetFields();
  };

  /** 提交-表单校验通过 */
  const handleFinish = async form => {
    setLoading(true);
    form.area[0] && (form.receiptProvinceId = form.area[0]);
    form.area[1] && (form.receiptCityId = form.area[1]);
    form.area[2] && (form.receiptDistrictId = form.area[2]);
    form.receiptType = receiptType;

    console.log("地址表单填写完毕：", form);

    let response;
    if (!isEditMode) {
      // 新增操作
      console.log("新增操作-接口调用");
      response = await crudReceipt
        .add(form)
        .then(res => [null, res])
        .catch(err => [err, null]);
    } else {
      if (!receiptId) {
        setLoading(false);
        return;
      }
      console.log("编辑操作-接口调用");
      response = await crudReceipt
        .edit(receiptId, form)
        .then(res => [null, res])
        .catch(err => [err, null]);
    }

    setLoading(false);
    const [err, res] = response;
    if (err) {
      message.success(err.data.message || "操作失败");
    } else {
      props?.callbackFunc && props.callbackFunc();
      handleCancel();
      message.success(res.message || "操作成功");
    }
  };

  /** 省市区选择回调 */
  const handleMonitorAreaChange = area => {
    if (!area) {
      form.setFieldValue("area", "");
      return;
    }
    form.setFieldValue("area", area);
    form.validateFields(["area"]);
  };

  /* ======================================= method end======================================= */
  const renderFormContent = () => (
    <>
      <Spin className={style.wrapper} spinning={loading}>
        <Form ref={currentFormRef} form={form} scrollToFirstError className="receipt-form" onFinish={handleFinish} autoComplete="off" size="middle">
          <Form.Item name="vatName" label="发票抬头" rules={rules.vatName}>
            <Input placeholder="请填写开票公司名称" />
          </Form.Item>
          <Form.Item name="vatId" label="信用代码/税号" rules={rules.vatId} validateFirst>
            <Input placeholder="请填写国税号码" />
          </Form.Item>
          {/* 增值税专项 */}
          {receiptType === 0 ? (
            <>
              <Form.Item name="receiptBank" label="开户银行" rules={rules.receiptBank}>
                <Input placeholder="请填写开户银行" />
              </Form.Item>
              <Form.Item name="receiptBankAccount" label="银行账户" rules={rules.receiptBankAccount}>
                <Input placeholder="请填写开户银行账户" />
              </Form.Item>

              <Form.Item name="receiptRegisterAddress" label="注册地址" rules={rules.receiptRegisterAddress}>
                <Input placeholder="请选择注册地址" />
              </Form.Item>
              <Form.Item name="receiptRegisterPhone" label="注册电话" rules={rules.receiptRegisterPhone}>
                <Input placeholder="请填写注册电话" />
              </Form.Item>
            </>
          ) : null}
          <Form.Item name="email" label="接收电子邮箱" rules={rules.email}>
            <Input placeholder="请输入接收电子邮箱" />
          </Form.Item>
          {/* 省市区 + 详细地址 --- start */}
          <Form.Item name="area" label="发票寄送地址" rules={rules.area}>
            <TmAreaCascade ref={tmAreaCascadeRef} onMonitorCascadeChange={area => handleMonitorAreaChange(area)} />
          </Form.Item>
          <Form.Item name="receiptAddress" label="详细寄送地址" rules={rules.receiptAddress}>
            <Input placeholder="详细地址, 如: xxx街道xx号" />
          </Form.Item>
          {/* 省市区 + 详细地址 --- end */}

          <Form.Item name="receiptPeople" label="发票人姓名" rules={rules.receiptPeople}>
            <Input placeholder="请填写发票人姓名" />
          </Form.Item>
          <Form.Item name="receiptPeoplePhone" label="发票人手机" rules={rules.receiptPeoplePhone}>
            <Input placeholder="请填写发票人手机" />
          </Form.Item>
          <Form.Item name="receiptPeopleTelephone" label="发票人电话">
            <Input placeholder="请填写发票人电话" />
          </Form.Item>
          <Form.Item name="isDefault" label="设为默认开票">
            <Radio.Group className="tm-radios" buttonStyle="solid">
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item>
            <div className="receipt-btn-box">
              <Button size={"middle"} onClick={handleCancel}>
                取消
              </Button>
              <Button size={"middle"} danger={isEditMode} type="primary" htmlType="submit">
                {isEditMode ? "修改" : "保存"}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Spin>
    </>
  );
  return modalVisible ? (
    <TmModal
      maskClosable={false}
      title={title}
      width={540}
      centered={true}
      open={modalVisible}
      keyboard={!isEditMode}
      content={renderFormContent()}
      footer={null}
      onOk={() => handleCancel()}
      onCancel={() => handleCancel()}
    />
  ) : null;
});
