import request from "@/utils/request.util";
import { ILoginPasswordParams, ILoginSmsParams, IRegisterEmailParams, IRegisterParams, IRestEmailPasswordParams, IRestPasswordParams } from "@/typings/auth.interface";

/** 密码登录 */
export function loginByPassword(loginData: ILoginPasswordParams) {
  return request({
    url: "/api/auth/login",
    method: "POST",
    data: loginData,
  });
}

/** 短信登录 */
export function loginBySms(smsData: ILoginSmsParams) {
  return request({
    url: "/api/auth/sms-login",
    method: "POST",
    data: smsData,
  });
}

/** 注册 */
export async function registerByPassword(registerData: IRegisterParams) {
  return request({
    url: "/api/auth/register",
    method: "POST",
    data: registerData,
  });
}

/** 邮箱方式注册 */
export async function registerByEmailPassword(registerData: IRegisterEmailParams) {
  return request({
    url: "/api/auth/email-register",
    method: "POST",
    data: registerData,
  });
}

/** 获取图像验证码 */
export async function getCaptchaCode() {
  return request({
    url: "/api/auth/captcha-code",
    method: "GET",
  });
}

/** 主动登出 */
export async function logout() {
  return request({
    url: "/api/auth/logout",
    method: "DELETE",
  });
}

/** 获取登录信息 */
export async function getAuthData() {
  return request({
    url: "/api/ucenter/auth-info",
    method: "GET",
  });
}

/** 找回密码 */
export async function findAuthPwd(data: IRestPasswordParams) {
  return request({
    url: "/api/auth/password-reset",
    method: "POST",
    data,
  });
}

/** 通过邮箱找回密码 */
export async function findAuthPasswordByEmail(data: IRestEmailPasswordParams) {
  return request({
    url: "/api/auth/email-password-reset",
    method: "POST",
    data,
  });
}
export async function getAccountInfoByPhone(phone) {
  return request({
    url: `/api/auth/allAccountInfoByPhone?phone=${phone}`,
    method: "GET",
  });
}

export default {
  logout,
};
