NODE_ENV="production"
#=============================================
#===============  基础  配置  ==================
#=============================================
#启动端口
APP_PORT=3001
# 日志目录
MIDWAYJS_LOG_DIR=./logs
MIDWAYJS_LOG_LEVEL='info'
# axios服务端超时时间，单位秒
AXIOS_REQUEST_TIMEOUT=300

# 跨域配置
MIDWAYJS_CORS_ORIGIN=*
MIDWAYJS_CORS_CREDENTIALS=0

# 调用API
JAVA_BASE_API_URL=http://java-api.guanghuayigou.com:8080

#=============================================
#===============  阿里OSS配置  =================
#=============================================
# 使用方式 process.env.ALI_OSS_ACCESSKEY
ALI_OSS_SECRET=ALI_12345654321_ALI_OSS_SECRET
ALI_OSS_ACCESSKEY=ALI_12345654321_ALI_OSS_ACCESSKEY
#oss地址
ALI_OSS_CDN="//upload.guanghuayigou.com/"

#=============================================
#===============  redis配置  ==================
#=============================================
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_USERNAME=''
REDIS_PASSWORD=''
REDIS_DATABASE=6

#=============================================
#===============  cache配置  ==================
#=============================================
# 默认2小时过期
CACHE_TTL=7200

#=============================================
#===============  jwt  配置  ==================
#=============================================
#刷新token过期时间
JWT_REFRESH_TOKEN_EXPIRES_IN=3600
JWT_ONLINE_KEY=portal-online-token:
JWT_COOKIE_TOKEN_KEY=GHMALL-ADMIN-PORTAL

#=============================================
#===============  docker配置  =================
#=============================================
DOCKER_CONTAINER_NAME=node-ghmall-portal
# 映射日志
DOCKER_NODE_LOG_MAPPING=/var/logs/node-ghmall-portal
# PM2启动参数
NODEJS_PM2_OPTS=pm2.prod.config.js

#=============================================
#===============  其它配置  ===================
#=============================================
#是否开启百度统计，正式环境才开启
BAIDU_TJ_ENABLED='1'

#旧cc商城前台域名
CC_MALL_FRONTEND_DOMIAN=http://www2.guanghuayigou.com/

#=============================================
#===============  系统功能开关  =================
#=============================================
# 是否开启ERP客户身份校验，true:开启，false:关闭
OPEN_CHECK_MEMBER_IS_ERP_CONTROL=false
# 是否开启产品最小起订量控制，true:开启，false:关闭
OPEN_CHECK_PRODUCT_PACKING_RATIO_CONTROL=false
# 是否开启客户默认初始密码检测前往修改的控制，true: 开启，false:关闭
OPEN_CHECK_MEMBER_IS_DEFAULT_PWD_MODAL_CONTROL=true
# 是否开启一键登录旧CC入口，true: 开启，false:关闭
CC_OLD_MEMBER_LOGIN_OPEN=true
