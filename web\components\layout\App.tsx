// 此文件将会在服务端/客户端都将会用到
// 可通过 __isBrowser__ 或者 useEffect 判断当前在 浏览器环境做一些初始化操作
// 初始化默认样式
import { LayoutProps } from "ssr-types-react";
import MainLayout from "./MainLayout";
import MobileAuthLayout from "./MobileAuthLayout";
import UcenterLayout from "./UcenterLayout";
import React, { useContext, useEffect } from "react";
import zhCN from "antd/lib/locale/zh_CN";
import { ConfigProvider } from "antd";
import Inter from "@/intl";
import { debounce } from "lodash";
import { StorageUtil, WEBSITE_LANGUAGE } from "@/utils/storage.util";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";
import "@/assets/icons";
import useBaiduTongji from "@/hooks/useBaiduTongji";
import "antd-mobile/es/global";

export default (props: LayoutProps) => {
  // 国际化监测已配置的设置
  const { state, dispatch } = useContext<IContext>(useStoreContext());
  useEffect(() => {
    webSiteDefaultLangFunc(dispatch, state);
  }, [dispatch, state]);
  // 百度统计
  if (state?.isEnabledBDTJ) {
    useBaiduTongji();
  }
  // 此处可根据条件判断使用哪个布局
  const path = __isBrowser__ ? location.pathname : props.ctx?.request.path;
  let tpl;
  switch (true) {
    case /^\/m\/auth/.test(path):
      // 移动端处理
      tpl = <MobileAuthLayout>{props.children!}</MobileAuthLayout>;
      break;
    case /^\/auth/.test(path):
      // 登录鉴权
      tpl = <>{props.children!}</>;
      break;
    case /^\/ucenter/.test(path):
      // 个人中心
      tpl = <UcenterLayout>{props.children!}</UcenterLayout>;
      break;
    case /^\/error/.test(path):
      tpl = props.children!;
      break;
    case /^\/message/.test(path):
      // 移动端处理
      tpl = <>{props.children!}</>;
      break;
    default:
      tpl = <MainLayout>{props.children!}</MainLayout>;
      break;
  }

  return (
    <ConfigProvider locale={zhCN} getPopupContainer={() => document.getElementById("app") ?? document.body} componentSize="middle">
      <Inter>{tpl}</Inter>
    </ConfigProvider>
  );
};

const webSiteDefaultLangFunc = debounce(async (dispatch, state) => {
  if (__isBrowser__) {
    const localeCache = StorageUtil.getInstance().get(WEBSITE_LANGUAGE) || "zh";
    if (state.locale !== localeCache) {
      dispatch?.({
        type: "CHANGE_LOCALE_INIT",
        payload: {
          locale: localeCache,
        },
      });
    }
  }
});
