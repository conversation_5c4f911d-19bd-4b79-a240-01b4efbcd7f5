import { Controller, Get, Inject, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { SearchQueryDto } from "@/dto/search-query.dto";
import { ISearchService } from "@/service/search/search.service";
import { IHomepageService } from "@/service/homepage.service";
import { COMMON_CONSTANTS } from "@/common/constants/common.constant";

@Controller("/api/product/search")
export class SearchController extends BaseController {
  @Inject("SearchService")
  searchService: ISearchService;

  // 用于品牌filter,替换对应的品牌logo
  @Inject("HomepageService")
  homepageService: IHomepageService;

  @Get()
  async search(@Query() criteria: Partial<SearchQueryDto>) {
    const { ctx } = this;
    criteria.memberId = this.getMemberId();
    criteria.customerId = this.getCustomerIdId();
    if (criteria?.keyword) {
      criteria.keyword = criteria.keyword.trim();
    }
    const res = await this.searchService.searchFromSql(criteria);
    // 产品搜索-优先展示华大产品
    res?.data?.content?.sort((a, _) => a.brandId === COMMON_CONSTANTS.BRAND_HUADA_ID ? -1 : 1)
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Get("/es")
  async esSearch(@Query() criteria: Partial<SearchQueryDto>) {
    const { ctx } = this;
    criteria.memberId = this.getMemberId();
    const res = await this.searchService.searchFromEs(criteria);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /** 关键字联想 */
  @Get("/keyword-remind")
  async keywordRemindSearch(@Query() params: { keyword: string }) {
    const { ctx } = this;
    const res = await this.searchService.keywordRemindSearch(params);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Get("/related")
  async related(@Query() criteria: Partial<SearchQueryDto>) {
    const { ctx } = this;
    const res = await this.searchService.getGoodsRelated(criteria);
    const brandIntroduces = (await this.homepageService.getHotBrandIntroduce()) || [];

    // 替换对应oss brand_logo
    if (res.data?.brands) {
      res.data.brands = res.data.brands.map(item => {
        item.url = brandIntroduces?.find(brand => item.value === String(brand.brand_id))?.logo_path || "";
        return item;
      });
    }

    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /** 获取热词 */
  @Get("/hot-words")
  async getHotWords() {
    const { ctx } = this;
    const res = await this.searchService.getHotWords();
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }
}
