/** 登录基础类型 */
export interface ILoginBaseParams {
  phone: string;
  password: string;
  smsCode: string;
  loginType?: string;
  companyName?: string;
}
/** 密码登录 */
export type ILoginPasswordParams = Omit<ILoginBaseParams, "smsCode">;
/** 验证码登录 */
export type ILoginSmsParams = Omit<ILoginBaseParams, "password">;

/** 重置密码-数据类型 */
export interface IRestPasswordParams {
  phone: string;
  smsCode: string;
  password: string;
  confirmPassword?: string;
}

/** 重置密码-数据类型 */
export interface IRestEmailPasswordParams {
  email: string;
  verifyCode: string;
  password: string;
  confirmPassword?: string;
  companyName?: string;
}

/** 注册账号-数据类型 */
export type IRegisterParams = IRestPasswordParams & {
  companyName: string;
  customerCategoryCode: string | null;
  agreement?: boolean;
  repeat?: string;
  account?: string;
  areas?: number[];
  address?: string;
  principalMainUserid?: number | null;
  principalMainUsername?: string | null;
  contactName: string | null;
};

/** 邮箱注册账号-数据类型 */
export type IRegisterEmailParams = {
  email: string;
  verifyCode: string;
  password: string;
  contactPhone: string;
  confirmPassword?: string;
  companyName: string;
  customerCategoryCode: string | null;
  agreement?: boolean;
  repeat?: string;
  account?: string;
  areas?: number[];
  address?: string;
  principalMainUserid?: number | null;
  principalMainUsername?: string | null;
  contactName: string | null;
};
