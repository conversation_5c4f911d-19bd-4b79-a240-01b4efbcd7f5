import React, { memo, useEffect, useRef, useState } from "react";
import TmModal from "@/components/TmModal";
import style from "./index.module.less";
import { Button, Table, Form, FormInstance, Input, message, Spin, InputNumber, Space } from "antd";
import crudAddress from "@/apis/member/address";
import { useSSRQueryRequest } from "@/hooks/useSSRQueryRequest";
import { price2Thousand, ghmallGuidePrice2Show } from "@/utils/price-format.util";
import Add2CartCnpt from "@/pages/ucenter/trade/shopping-cart/parts/Add2CartCnpt";

interface IProductSelectModalProps {
  modalVisible: boolean;
  changeModalVisible: (bool: boolean) => void;
  callbackFunc?: any;
  onAddProduct?: any;
}

export default memo(function ProductSelectModal(props: IProductSelectModalProps) {
  const { modalVisible, changeModalVisible, onAddProduct } = props;
  const useSSRQueryRequestHook = useSSRQueryRequest({
    queryParams: {},
    pageSize: 10,
    showSizeChanger: false,
    showTotalText: false,
  });
  // 产品列配置
  const tableColumns = [
    {
      title: "SKU",
      dataIndex: "productSku",
      key: "productSku",
      align: "center",
      render: (text, record, index) => (
        <a className="product-detail-link" href={`/product/${record.productNo}?sku=${record.productSku}`} target="_blank">
          {record.productSku}
        </a>
      ),
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      key: "productName",
      align: "center",
      render: (text, record, index) => <span className="product-name">{record.productName}</span>,
    },
    {
      title: "品牌",
      dataIndex: "productBrand",
      key: "productBrand",
      align: "center",
      render: (text, record) => <span>{record.productBrand}</span>,
    },
    {
      title: "包装",
      dataIndex: "productPacking",
      key: "productPacking",
      align: "center",
      render: (text, record, index) => <span>{!record.productPacking || record.productPacking === "1" ? "-" : record.productPacking}</span>,
    },
    {
      title: "起订量",
      dataIndex: "packingRatio",
      key: "packingRatio",
      align: "center",
      render: (text, record, index) => <span>{record.packingRatio || "1"}</span>,
    },
    {
      title: "数量",
      dataIndex: "productQuantity",
      key: "productQuantity",
      align: "center",
      width: 140,
      render: (_, record, index) => (
        <>
          <InputNumber min={1} defaultValue={record.productQuantity} onChange={val => updateQuantityChange(val, record, index)} />
        </>
      ),
    },
    {
      title: "单位",
      dataIndex: "productUnit",
      key: "productUnit",
      align: "center",
      render: (text, record, index) => record.productUnit || "-",
    },
    {
      title: "单价",
      dataIndex: "productTaxPrice",
      key: "productTaxPrice",
      align: "center",
      render: (text, record, index) => <span>{ghmallGuidePrice2Show(record.productTaxPrice)}</span>,
    },
    {
      title: "小计",
      dataIndex: "totalPrice",
      key: "totalPrice",
      align: "center",
      render: (text, record, index) => `${record.productTaxPrice === 0 ? "询价" : price2Thousand(record.productTaxPrice * record.productQuantity)}`,
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record, index) => (
        <>
          <Space direction="horizontal" size="small">
            <Button ghost type="primary" size="small" onClick={() => handleAddProduct(record)}>
              添加
            </Button>
            <Button type="default" size="small" href={`/product/${record.productNo}?sku=${record.sku}`} target="_blank">
              查看产品
            </Button>
          </Space>
        </>
      ),
    },
  ];

  /* ======================================= use state start======================================= */
  const [keyword, setKeyword] = useState("");
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  /* ======================================= use state end======================================= */

  /* ======================================= 监听|mounted start======================================= */
  useEffect(() => {
    if (modalVisible && useSSRQueryRequestHook.getTableData.content?.length > 0) {
      fetchTableData(useSSRQueryRequestHook.getTableData.content);
    }
  }, [modalVisible, useSSRQueryRequestHook.getTableData.content]);

  /* ======================================= 监听|mounted end======================================= */

  /* ======================================= method start======================================= */
  // 关键词赋值
  const handleChange = e => {
    setKeyword(e.target.value?.trim());
    if (!e.target.value?.trim()) {
    }
  };
  // 执行搜索
  const handleSearch = async () => {
    setLoading(true);
    const params = {
      keyword,
    };
    setTableData([]);
    await useSSRQueryRequestHook.searchAsyncEvent(params);
    setLoading(false);
  };
  // 表格数据宣传
  const fetchTableData = async data => {
    let _tableData = [] as any;
    data?.map(item => {
      item?.skus.map(sku => {
        _tableData.push({
          productBrand: item.brandName,
          productId: item.id,
          productNo: item.productNo,
          productName: item.productName,
          productPacking: sku.packing,
          packingRatio: sku.packingRatio,
          productQuantity: sku.packingRatio ? Number(sku.packingRatio) : 1,
          productUnit: sku.unit,
          productSku: sku.sku,
          productSkuId: sku.id,
          productTaxPrice: sku.discountPrice,
          isDanger: item.isDanger,
        });
      });
    });
    setTableData(_tableData);
  };

  // 更新计算
  const updateQuantityChange = (val, record, index) => {
    setTableData(prevItems => prevItems.map(item => (item.productSkuId === record.productSkuId ? { ...item, productQuantity: val } : item)));
  };
  // 取消操作
  const handleCancel = () => {
    changeModalVisible(false);
  };
  // 添加操作
  const handleAddProduct = record => {
    onAddProduct(record);
  };

  /* ======================================= method end======================================= */

  const renderContent = () => (
    <>
      <Spin size="default" tip="全力搜索中" spinning={loading}>
        <div className={style.wrapper}>
          <div className={style.search}>
            <Input className={style.searchInput} value={keyword} onChange={handleChange} allowClear size={"large"} placeholder="产品名/产品货号/产品代码/SKU/CAS" onPressEnter={handleSearch} />
            <Button className={style.searchBtn} onClick={handleSearch} size={"large"} type={"primary"}>
              搜索
            </Button>
          </div>
          <div className={style.table}>
            <Table rowKey="id" size="middle" dataSource={tableData} columns={tableColumns} pagination={false} />
          </div>
        </div>
      </Spin>
    </>
  );
  return modalVisible ? (
    <TmModal centered maskClosable={false} title="选择产品" width={1000} open={modalVisible} content={renderContent()} footer={null} onOk={() => handleCancel()} onCancel={() => handleCancel()} />
  ) : null;
});
