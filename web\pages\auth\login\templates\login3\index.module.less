@import "web/assets/css/variables";

.loginPage {
  width: 100%;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  :global {
    .login-header {
      width: 100%;
      height: 80px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
      box-sizing: border-box;
      .login-header-content {
        max-width: @main-width;
        width: 80%;
        margin: 0 auto;
        .logo-box {
          display: inline-block;
          width: 110px;
          height: 46px;
          background: url("@{img-base-path}/logo.svg") 100% no-repeat;
        }
      }
    }
    .login-container {
      width: 100%;
      height: 550px;
      position: relative;
      .login-carousel {
        width: 100%;
        height: 550px;
        .login-carousel-item {
          margin: 0 auto;
          img {
            width: 100%;
          }
        }
      }
      .form-box {
        width: 350px;
        box-sizing: border-box;
        position: absolute;
        top: 80px;
        right: 12%;
        padding: 20px;
        box-sizing: border-box;
        background: rgba(255, 255, 255, 0.9);
      }
    }
    .footer {
      position: fixed;
      bottom: 24px;
      left: 50%;
      transform: translate(-50%);
      width: 400px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
      .a-links {
        width: 240px;
        margin: 0 auto 10px;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }
    }
  }
}
