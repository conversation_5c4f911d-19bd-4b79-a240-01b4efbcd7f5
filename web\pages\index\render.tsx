import React, { useContext } from "react";
import { SProps } from "ssr-types-react";
import style from "./index.module.less";
import TopBanner from "./components/TopBanner";
import GeneralPlate from "./components/GeneralPlate";
import BeltClassificationPlate from "./components/BeltClassificationPlate";
import IndustryNews from "./components/IndustryNews";
import CompanyPurpose from "./components/CompanyPurpose";
import Adsense from "./components/Adsense";
import UcenterDasboard from "@/pages/index/components/UcenterDasboard";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";
import { PLATE_GH_SELECTION_FLAG } from "@/constants/common";
import GHSelection from "@/pages/index/components/GHSelection";
import AdsenseFlow from "@/pages/index/components/AdsenseFlow";

export default function Index(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  // 解析首页板块数据
  const homePageDataArray = state?.indexData?.homePageDataArray || [];
  homePageDataArray?.sort((a, b) => a?.sortOrder - b?.sortOrder)

  return (
    <>
      <div className={style.wrapper}>
        <div className="top-dashboard-part">
          <div className="nav-side" />
          <div className="nav-content">
            {/* 1 首页BANNER */}
            <TopBanner />
          </div>
          <div className="nav-right">
            <UcenterDasboard />
          </div>
        </div>
        {/* 公司愿景广告 */}
        <AdsenseFlow />
        {/* 广告位 */}
        <Adsense />
        {/* 首页板块渲染 - 根据板块数组信息渲染 */}
        <>
          {
            homePageDataArray.map((plateData, index) => {
              if (plateData.flagCode === PLATE_GH_SELECTION_FLAG) {
                // 特定-光华精选标识的模板展示
                return <GHSelection data={plateData} key={index} />
              } else {
                const optionalJsonArr = JSON.parse(plateData.optionalJson || '[]');
                if (optionalJsonArr.length) {
                  // 有子分类的情况
                  return <BeltClassificationPlate data={plateData} key={index} />
                } else {
                  // 普通展示方式
                  return <GeneralPlate data={plateData} key={index} />
                }
              }
            })
          }
        </>
        {/* 行业动态 */}
        <IndustryNews />
        {/* 宗旨 */}
        <CompanyPurpose />
      </div>
    </>
  );
}
