import { CustomStrategy, PassportStrategy } from "@midwayjs/passport";
import { Strategy, ExtractJwt } from "passport-jwt";
import { Config } from "@midwayjs/decorator";

@CustomStrategy()
export class JwtStrategy extends PassportStrategy(Strategy, "jwt") {
  @Config("jwt")
  jwtConfig;

  // 策略的验证
  async validate(payload) {
    return payload;
  }

  // 当前策略的参数
  getStrategyOptions(): any {
    return {
      secretOrKey: this.jwtConfig.secret,
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    };
  }

  serializeUser(user, done) {
    done(null, user);
  }
}
