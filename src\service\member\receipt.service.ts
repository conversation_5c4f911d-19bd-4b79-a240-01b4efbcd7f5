import { receiptCreateDto, receiptUpdateDto } from "~/typings/data/member/receipt";

export interface IReceiptService {
  /**
   * 获取分页列表
   *
   * @param memberId 会员ID
   * @param criteria 条件
   */
  getPageList: (memberId: string) => Promise<any>;

  /**
   * 新增
   *
   * @param resource /
   */
  create: (memberId: string, resource: receiptCreateDto) => Promise<any>;

  /**
   *详情
   *
   * @param memberId /
   * @param receiptId /
   */
  show: (memberId: string, receiptId: number) => Promise<any>;

  /**
   * 删除
   *
   * @param memberId /
   * @param receiptId /
   */
  delete: (memberId: string, receiptId: number) => Promise<any>;

  /**
   * 更新
   *
   * @param resource /
   */
  update: (memberId: string, resource: receiptUpdateDto) => Promise<any>;

  /**
   * 默认
   *
   * @param memberId /
   * @param receiptId /
   */
  setDefault: (memberId: string, receiptId: number) => Promise<any>;
}
