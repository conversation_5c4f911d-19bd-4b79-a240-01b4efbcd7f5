import React, { memo, useRef, useState } from "react";
import TmModal from "@/components/TmModal";
import { <PERSON>ert, Button, Col, Form, Input, message, notification, Row, Spin } from "antd";
import { FormInstance } from "antd/es/form";
import { validateEmail } from "@/utils/form-valid.util";
import style from "./index.module.less";
import { MailOutlined } from "@ant-design/icons";
import EmailSendCodeCnpt from "@/components/EmailSendCodeCnpt";
import { EmailSendTypeEnum } from "@/enums/EmailSendTypeEnum";
import { rebindEmail } from "@/apis/member/member";

interface IEmailBindModalProps {
  modalVisible: boolean;
  changeModalVisible: (bool: boolean) => void;
  callbackFunc?: any;
  title?: string;
  originData?: string;
}

export default memo(function EmailBindModal(props: IEmailBindModalProps) {
  const { modalVisible, title, changeModalVisible, callbackFunc, originData } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const currentFormRef = useRef<FormInstance>(null);
  const validateEmailRepeat = async (obj: any, value: any) => {
    if (value && originData === value) {
      throw "请不要输入当前已绑定的邮箱";
    }
    return await Promise.resolve();
  };
  let sendEmailType = EmailSendTypeEnum.BIND;
  if (originData) {
    sendEmailType = EmailSendTypeEnum.REBIND;
  }
  const formRules = {
    email: [{ required: true, whitespace: true, message: "邮箱不能为空" }, { validator: validateEmail }, { validator: validateEmailRepeat }],
  };
  /** 取消操作 */
  const handleCancel = () => {
    changeModalVisible(false);
    form.resetFields();
  };
  const onFinish = async (form: any) => {
    setLoading(true);
    const [err, res] = await rebindEmail(form)
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoading(false);
    if (err) {
      return notification.error({ message: err?.data?.message ?? "请求出错了" });
    }
    handleCancel();
    message.success("处理成功，账号可使用新邮箱进行登录！" || res?.message);
    if (callbackFunc) {
      setTimeout(() => {
        callbackFunc();
      }, 1000);
    }
  };
  const renderFormContent = () => (
    <>
      <Spin className={style.wrapper} spinning={loading}>
        <div className="member-edit">
          {originData ? <Alert style={{ marginBottom: "10px" }} message={`当前已绑定邮箱为：${originData}`} type="warning" /> : null}
          <Form
            ref={currentFormRef}
            className="member-form"
            name={"member-edit-form"}
            form={form}
            scrollToFirstError
            onFinish={onFinish}
            autoComplete="off"
            size="large"
            labelAlign="right"
            labelCol={{ style: { width: 100, whiteSpace: "normal" } }}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="邮箱" name="email" rules={formRules.email} tooltip="请填入需要绑定的邮箱">
                  <Input allowClear prefix={<MailOutlined />} placeholder="请输入邮箱地址" />
                </Form.Item>
              </Col>
              <Col span={24}>
                {/* ========== 邮件验证码发送组件 ========== */}
                <EmailSendCodeCnpt uuid={"bindMemberEmail"} sendType={sendEmailType} countdownSeconds={30} />
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item colon={false} className="email-form-submit">
                  <Button type="default" size="middle" onClick={() => handleCancel()}>
                    取消
                  </Button>
                  <Button type="primary" danger htmlType="submit" size="middle">
                    确认
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Spin>
    </>
  );
  return modalVisible ? (
    <TmModal
      maskClosable={false}
      keyboard={false}
      title={title}
      width={580}
      centered={true}
      open={modalVisible}
      content={renderFormContent()}
      footer={null}
      onOk={handleCancel}
      onCancel={() => handleCancel()}
    />
  ) : null;
});
