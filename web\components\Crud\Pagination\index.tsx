import React from "react";
import { Row, Pagination } from "antd";
import "@/assets/css/myPagination.less";
import type { PaginationProps } from "antd";

const pageSizeOptions = ["5", "10", "20", "30", "50"];

export interface PageInfo {
  page?: number;
  pagesize?: number;
  crudInstance: any;
  showSizeChanger?: boolean;
  pageChangeCallback?: () => void;
}

// 上一页、下一页
const itemRender: PaginationProps["itemRender"] = (_, type, originalElement) => {
  if (type === "prev") {
    return <a className="pagination-btn-prev">上一页</a>;
  }
  if (type === "next") {
    return <a className="pagination-btn-next">下一页</a>;
  }
  return originalElement;
};

// 分页
export default function MyCrudPagination(props: PageInfo) {
  const crud = props.crudInstance;
  const { pagination } = crud;
  const pageChangeHandler = async (page: number, pageSize: number) => {
    await crud.pageChangeHandler(page, pageSize);
    if (props?.pageChangeCallback) {
      props.pageChangeCallback();
    }
  };
  return (
    <Row justify="end" className="my-pagination-wrapper">
      <Pagination
        pageSize={pagination.size}
        showSizeChanger={props?.showSizeChanger ?? false}
        showQuickJumper
        onChange={pageChangeHandler}
        current={pagination.page}
        total={pagination.total}
        pageSizeOptions={pageSizeOptions}
        showTotal={total => `共 ${total} 条`}
        itemRender={itemRender}
      />
    </Row>
  );
}
