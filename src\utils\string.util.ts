import { Provide } from "@midwayjs/decorator";

@Provide()
export abstract class StringUtil {
  /**
   * 随机生成字符串
   * @param len 指定生成字符串长度
   */
  static getRandomString(len: number) {
    const _charStr = "abacdefghjklmnopqrstuvwxyzABCDEFGHJKLMNOPQRSTUVWXYZ0123456789";
    const min = 0;
    const max = _charStr.length - 1;
    let _str = ""; // 定义随机字符串 变量
    // 判断是否指定长度，否则默认长度为15
    len = len || 15;
    // 循环生成字符串
    for (let i = 0, index; i < len; i++) {
      index = (function (randomIndexFunc, i) {
        return randomIndexFunc(min, max, i, randomIndexFunc);
      })(function (min: number, max: number, i: number, _self) {
        let indexTemp = Math.floor(Math.random() * (max - min + 1) + min);
        const numStart = _charStr.length - 10;
        if (i === 0 && indexTemp >= numStart) {
          indexTemp = _self(min, max, i, _self);
        }
        return indexTemp;
      }, i);
      _str += _charStr[index];
    }
    return _str;
  }

  /**
   * <p>用于签名生成：参考微信</p>
   *
   * <p>https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html</p>
   * <p>对所有待签名参数按照字段名的ASCII 码从小到大排序（字典序）后，使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串string1。
   * 这里需要注意的是所有参数名均为小写字符。对string1作sha1加密，字段名和字段值都采用原始值，不进行URL 转义</p>
   * @param args /
   */
  static rawSign(args: object) {
    let keys = Object.keys(args);
    // eslint-disable-next-line @typescript-eslint/require-array-sort-compare
    keys = keys.sort();
    const newArgs = {};
    keys.forEach(function (key) {
      newArgs[key.toLowerCase()] = args[key];
    });

    let string = "";
    for (const k in newArgs) {
      string += "&" + k + "=" + newArgs[k];
    }
    string = string.substr(1);
    return string;
  }
}
