import React, { useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import { Result } from "antd";

export default function ForbiddenPage(props: SProps) {
  const [params, setParams] = useState<any>();
  const getUrlParams = () => {
    const params = new URLSearchParams(props.location.search);
    setParams(params);
  };
  const renderSubTitle = () => {
    return (
      <>
        <div style={{ fontWeight: 600 }}>{params?.get("message") ?? "抱歉，您当前请求不被允许！"}</div>
        {params?.get("origin") ? <div>{`来源地址：${params?.get("origin")}`}</div> : null}
      </>
    );
  };
  useEffect(() => {
    getUrlParams();
  }, []);
  return (
    <>
      <Result
        status="403"
        title="不合法的请求"
        subTitle={renderSubTitle()}
        extra={
          <a style={{ textDecoration: "underline" }} href="/">
            回到首页
          </a>
        }
      />
    </>
  );
}
