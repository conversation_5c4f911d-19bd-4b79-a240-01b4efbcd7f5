.wrapper {
  :global {
    .title-wrapper {
      width: 100%;
      display: inline-flex;
      justify-content: space-between;
      .btn-wrapper {
        margin-right: 14px;
        a {
          padding-left: 4px;
          color: #ee5454;
        }
      }
    }
    .shopping-cart {
      &-top-tool {
        padding-left: 8px;
        border: 1px solid rgba(0, 0, 0, 0.08);
        height: 40px;
        .flex-row(flex-start,center);
        font-size: 14px;
        .reminder {
          color: #ee5454;
        }
        .refresh {
          flex: 1;
          text-align: right;
          color: #6d7278;
        }
      }
      &-table {
        margin-top: 16px;
        .product-detail-link {
          &:hover {
            color: #e02020;
            text-decoration: underline;
          }
        }
        .product-quantity {
          .flex-center(row);
          .input-box {
            width: 64px;
            height: 24px;
            border-radius: 2px;
            border: 1px solid rgb(221, 221, 221);
            .ant-input-number-input {
              height: 100%;
              text-align: center;
            }
          }
          .btn {
            height: 24px;
            width: 24px;
            background: #fff;
            border: 1px solid rgb(221, 221, 221);
            border-radius: 2px;
            cursor: pointer;
            &:hover {
              background-color: #ff1b1b !important;
              border: 1px #ff1b1b !important;
              border-radius: 2px !important;
              color: #fff;
            }
            &:active {
              background-color: #ff7474 !important;
            }
          }
        }
      }
      &-tool {
        margin-top: 16px;
        height: 44px;
        background-color: #fff;
        padding-left: 8px;
        border: 1px solid rgba(0, 0, 0, 0.08);
        .flex-row(flex-start,center);
        .check-all {
          width: 60px;
          span {
            color: #6d7278;
          }
        }
        .action-btn {
          .ant-btn {
            color: #40a9ff;
            width: 85px;
            margin-right: 10px;
          }
        }
        .statistics {
          flex: 1;
          text-align: right;
          color: #8c8c8c;
          margin-left: 33px;
          font-size: 14px;
          > div {
            display: inline-block;
            &:first-child {
              margin-right: 25px;
            }
            > span {
              color: #e02020;
            }
            .total-price-amount {
              font-size: 16px;
            }
          }
        }
        .to-pay {
          width: 150px;
          font-size: 20px;
          line-height: 45px;
          margin-left: 20px;
          text-align: center;
          height: 100%;
          color: #fff;
          background-color: #ee5454;
          cursor: pointer;
        }
      }
      .empty-tip {
        color: #6d7278;
        font-size: 14px;
      }
      .go2purchase {
        border-radius: 14px;
        border: 1px solid #e02020;
        font-weight: 400;
        color: #e02020;
        font-size: 14px;
        padding: 5px 15px;
        &:hover {
          color: #fff;
          border: 1px solid #fff;
          background-color: #e02020;
        }
      }
    }
  }
}
