import request from "@/utils/request.util";

export function getRelatedFilterList(params: object) {
  return request({
    url: "/api/product/search/related",
    method: "get",
    params,
  });
}
export function fetchSearchList(params: object) {
  return request({
    url: "/api/product/search",
    method: "get",
    params,
  });
}

/**
 * 关键字联想
 * @param keyword
 */
export function keywordRemindSearch(keyword: string) {
  return request({
    url: `/api/product/search/keyword-remind`,
    method: "get",
    params: {
      keyword,
    },
  });
}

/**
 * 获取搜索词汇
 */
export function getHotWords() {
  return request({
    url: "/api/product/search/hot-words",
    method: "get",
  });
}

export default {
  getRelatedFilterList,
  fetchSearchList,
  getHotWords,
  keywordRemindSearch,
};
