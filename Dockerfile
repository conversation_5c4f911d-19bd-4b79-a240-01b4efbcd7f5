FROM node:lts-alpine3.15

MAINTAINER tmtc

ENV TZ=Asia/Shanghai \
    APP_PATH=/app

COPY ./sources.list.stretch /etc/apt/sources.list

#设置当前路径，也就是下面所有命令的执行都是在这个路径
WORKDIR $APP_PATH

COPY package.json ./
COPY yarn.lock ./

# 安装依赖包

RUN npm config -g set registry "https://registry.npmmirror.com/"  && \
    npm install -g npm@9.1.2 && \
    npm install -g yarn --force && npm i pm2 -g && \
    yarn config set registry "https://registry.npmmirror.com/" && \
    yarn install --network-timeout 1000000

#拷贝当前目录下面的指定文件到目标目录下
COPY . .

# 构建

RUN yarn build

# can use

#RUN npm config set registry https://registry.npm.taobao.org && \
#    npm install && npm i pm2 -g && npm run build

# custom log
RUN echo "docker-compose ghmall portal container build success!"

ENV NODEJS_PM2_OPTS=${NODEJS_PM2_OPTS}

# 查看pm2
CMD ["sh", "-c", "pm2-runtime start ${NODEJS_PM2_OPTS} && pm2 list"]
