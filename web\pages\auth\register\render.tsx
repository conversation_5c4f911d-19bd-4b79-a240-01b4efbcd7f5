import React from "react";
import style from "./index.module.less";
import { Tabs } from "antd";
import { SProps } from "ssr";
import SimpleLayout from "@/components/layout/SimpleLayout";
import { useRegisterForm } from "@/hooks/useRegisterForm";

export default function Register(props: SProps) {
  const useRegisterFormHook = useRegisterForm(null);
  /** ======================================== method end ======================================= */
  return (
    <SimpleLayout title="欢迎注册" hideAgreement={true}>
      <div className={style.wrapper}>
        <div className="register">
          <h3 className="register-title">注册账号</h3>
          <Tabs items={useRegisterFormHook.renderTabItems} className="register-tabs" onChange={useRegisterFormHook.handleChangeTab} />
        </div>
        {/* 协议弹窗 */}
        <useRegisterFormHook.AgreementModal />
      </div>
    </SimpleLayout>
  );
}
