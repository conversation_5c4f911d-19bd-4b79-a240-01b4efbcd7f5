import { Middleware } from "@midwayjs/decorator";
import { httpError, IMiddleware } from "@midwayjs/core";
import { Context, NextFunction } from "@midwayjs/koa";
import authConstant from "@/common/constants/auth.constant";

/**
 * @desc 登录中间件
 *
 * <AUTHOR>
 * @date 22/5/14
 * @since 22/11/23 完善逻辑
 */
@Middleware()
export class AuthenticationMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 处理登录认证
      if (AuthenticationMiddleware.authIgnoreHandle(ctx.request.path)) {
        if (!ctx.session.userLoginState) {
          // 判断是否 XMLHttpRequest
          if (ctx.get("X-Requested-With") === "XMLHttpRequest" || ctx.path.startsWith("/api")) {
            throw new httpError.UnauthorizedError("未登录，不允许操作！");
          } else {
            return ctx.redirect(`/auth/login?redirect=${encodeURIComponent(ctx.url)}`);
          }
        }
      }
      return next();
    };
  }

  static getName(): string {
    return "authentication";
  }

  private static authIgnoreHandle(targetUrl: string): boolean {
    const ignoreUrls = authConstant.AUTH_LOGIN_IGNORE_URL; // 允许直接访问的接口
    return ignoreUrls.some(url => {
      return !targetUrl.startsWith(url.replace(/(\\*$)/g, ""));
    });
  }
}
