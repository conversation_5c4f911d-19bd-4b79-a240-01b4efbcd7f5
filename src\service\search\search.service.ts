import { SearchQueryDto } from "@/dto/search-query.dto";

export interface ISearchService {
  /**
   * 普通搜索
   * @param criteria 请求params
   */
  searchFromSql: (criteria: Partial<SearchQueryDto>) => Promise<any>;

  /**
   * es搜索
   * @param criteria 请求params
   */
  searchFromEs: (criteria: Partial<SearchQueryDto>) => Promise<any>;

  /**
   * 关键字联想
   * @param keyword string
   */
  keywordRemindSearch: (params: { keyword: string }) => Promise<any>;

  /** 获取关联筛选条件
   *
   * @param criteria /
   */
  getGoodsRelated: (criteria: Partial<SearchQueryDto>) => Promise<any>;

  /**
   * 获取搜索热词
   */
  getHotWords: () => Promise<any>;
}
