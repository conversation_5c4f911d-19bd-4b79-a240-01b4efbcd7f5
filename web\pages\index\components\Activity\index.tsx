import React, { useContext } from "react";
import style from "./index.module.less";
import { useStoreContext } from "ssr-common-utils";
import { IContext } from "ssr-types";

export default function Activity() {
  const { state } = useContext<IContext>(useStoreContext());
  const activities = state?.indexData?.homeActivityData || [];
  return (
    <>
      {!!activities.length && (
        <div className={style.wrapper}>
          <ul className="activity">
            {activities.map(activity => (
              <li key={activity.id} className="activity-item">
                <a href={`${activity.url}`}>
                  <div className="activity-info">
                    <span className="activity-title">{activity.title}</span>
                    <span className="activity-remark">{activity.remark}</span>
                  </div>
                  {activity?.img ? <img src={activity.img} alt="activity" /> : null}
                </a>
              </li>
            ))}
          </ul>
        </div>
      )}
    </>
  );
}
