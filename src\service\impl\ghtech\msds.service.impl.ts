import { Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IProductMsds } from "@/service/ghtech/msds.service";
import { IMSDSParams, IMSDSRelationParams } from "~/typings/data/ghtech/ghtech";

@Provide("MsdsService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class MsdsServiceImpl extends BaseService implements IProductMsds {
  /** 产品名称获取COA资源 */
  async getMsdsByProductName(params: IMSDSParams): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/ghtech/msds`, params), "获取数据出错了，请稍后再试！");
  }

  /** msds单个获取-cas + productName */
  async getProductMSDSByRelation (params: IMSDSRelationParams): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/ghtech/msds/relation`, params), "获取数据出错了，请稍后再试！");
  }
}
