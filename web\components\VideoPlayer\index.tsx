import React, { useRef, useEffect, useState } from "react";
import { PlayCircleOutlined, PauseCircleOutlined } from "@ant-design/icons";
import styles from "./index.module.less";

interface VideoPlayerProps {
  src: string; // 视频源URL
  poster?: string; // 视频封面图
  width?: number | string; // 视频宽度
  height?: number | string; // 视频高度
  autoPlay?: boolean; // 是否自动播放
  muted?: boolean; // 是否静音
  controls?: boolean; // 是否显示控制栏
  loop?: boolean; // 是否循环播放
  className?: string; // 自定义类名
  onPlay?: () => void; // 播放回调
  onPause?: () => void; // 暂停回调
  onEnded?: () => void; // 结束回调
  onError?: (error: any) => void; // 错误回调
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  width = "100%",
  height = "auto",
  autoPlay = false,
  muted = false,
  controls = true,
  loop = false,
  className = "",
  onPlay,
  onPause,
  onEnded,
  onError,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showCustomControls, setShowCustomControls] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => {
      setIsPlaying(true);
      onPlay?.();
    };

    const handlePause = () => {
      setIsPlaying(false);
      onPause?.();
    };

    const handleEnded = () => {
      setIsPlaying(false);
      onEnded?.();
    };

    const handleError = (error: any) => {
      onError?.(error);
    };

    video.addEventListener("play", handlePlay);
    video.addEventListener("pause", handlePause);
    video.addEventListener("ended", handleEnded);
    video.addEventListener("error", handleError);

    return () => {
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("pause", handlePause);
      video.removeEventListener("ended", handleEnded);
      video.removeEventListener("error", handleError);
    };
  }, [onPlay, onPause, onEnded, onError]);

  const togglePlay = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
  };

  return (
    <div className={`${styles.videoContainer} ${className}`} style={{ width, height }} onMouseEnter={() => setShowCustomControls(true)} onMouseLeave={() => setShowCustomControls(false)}>
      <video
        ref={videoRef}
        className={styles.video}
        src={src}
        poster={poster}
        width={width}
        height={height}
        autoPlay={autoPlay}
        muted={muted}
        controls={controls}
        loop={loop}
        playsInline
        webkit-playsinline="true"
      />
      {!controls && showCustomControls && (
        <div className={styles.customControls}>
          <button onClick={togglePlay} className={styles.playButton}>
            {isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          </button>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
