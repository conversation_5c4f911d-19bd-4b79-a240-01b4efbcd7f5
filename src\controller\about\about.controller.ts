import { Controller, Get, HttpCode, Inject } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IHonorService } from "@/service/honor.service";

@Controller("/about")
export class AboutController extends BaseController {
  @Inject("HonorService")
  honorService: IHonorService;

  /** 关于我们 */
  @Get("/")
  @HttpCode(200)
  async index() {
    try {
      this.ctx.body = await render<Readable>(this.ctx, {
        stream: true,
        mode: "ssr",
      });
    } catch (error) {
      this.ctx.body = error;
    }
  }

  /** 荣誉资质 */
  @Get("/honor")
  @HttpCode(200)
  async honor() {
    const result = await this.honorService.getHonorData();
    this.ctx.honorData = result.data;

    try {
      this.ctx.body = await render<Readable>(this.ctx, {
        stream: true,
        mode: "ssr",
      });
    } catch (error) {
      this.ctx.body = error;
    }
  }

  /** 联系我们 */
  @Get("/contactus")
  @HttpCode(200)
  async contactus() {
    try {
      this.ctx.body = await render<Readable>(this.ctx, {
        stream: true,
        mode: "ssr",
      });
    } catch (error) {
      this.ctx.body = error;
    }
  }
}
