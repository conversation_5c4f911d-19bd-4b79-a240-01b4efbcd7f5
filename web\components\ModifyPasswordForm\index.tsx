import React, { useContext } from "react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import { Button, Form, Input, message, notification } from "antd";
import PasswordItem from "@/components/PasswordItem";
import { modifyPassword } from "@/apis/member/member";
import style from "./index.module.less";
import { UnlockOutlined } from "@ant-design/icons";
import { useLoading } from "@/hooks/useLoading";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { IContext } from "ssr-types-react";
import { STORE_CONTEXT } from "_build/create-context";
import authCrud from "@/apis/auth";

interface IModifyPasswordProps {
  title: React.ReactNode | string;
  success: () => void;
}

/** 修改密码组件 */
const ModifyPasswordForm = (props: IModifyPasswordProps) => {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const loadingHooks = useLoading();
  const rules = {
    oldPass: [{ required: true, message: "请输入旧密码" }],
  };

  const handleOnFinish = async (vals: { oldPass: string; password: string }) => {
    loadingHooks.showLoading();
    const [err, res] = await modifyPassword({ oldPass: encrypt(vals.oldPass), newPass: encrypt(vals.password) })
      .then(res => [null, res])
      .catch(err => [err, null]);
    loadingHooks.hideLoading();
    if (err) {
      notification.error({ message: err.data.message });
      return;
    }
    // 发起退出请求
    await authCrud.logout();
    message.success("密码修改成功，即将重新登录！");
    setTimeout(() => {
      location.href = `/auth/login?redirect=${encodeURIComponent(state.redirect)}`;
    }, 600);
  };

  return (
    <div className={style.wrapper}>
      <UCenterCard title={props.title} />
      <Form className="mdf-pass-form" onFinish={async val => await handleOnFinish(val)}>
        <Form.Item name="oldPass" label="旧的密码" rules={rules.oldPass}>
          <Input.Password className="old-pass-input" prefix={<UnlockOutlined />} placeholder="请输入旧密码" />
        </Form.Item>
        <PasswordItem prefixCls={"ghmall"} repeatLabel={"确认密码"} passwordLabel="新的密码" repeat />
        <Form.Item>
          <Button danger size={"large"} htmlType="submit">
            修改密码
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

ModifyPasswordForm.defaultProps = {
  title: "修改密码",
  titleStyle: null,
  success: () => null,
};

export default ModifyPasswordForm;
