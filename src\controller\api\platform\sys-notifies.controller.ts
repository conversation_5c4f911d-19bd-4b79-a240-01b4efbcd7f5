import { Controller, Get, Inject, Param, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { INotifyService } from "@/service/platform/notifies.service";
import { INotifiesQuery } from "~/typings/data/spread";

@Controller("/api/platform/sys-notifies")
export class SysNotifiesController extends BaseController {
  @Inject("NotifiesService")
  notifiesService: INotifyService;

  @Get("/")
  async getNotifies(@Query() params: INotifiesQuery) {
    const { ctx } = this;
    const res = await this.notifiesService.getNotifies(params);
    return ctx.getResponseInstance(ctx).setResponseData(res?.data).sendSuccess();
  }

  @Get("/:code")
  async getNotifyDetailByCode(@Param("code") code: string) {
    const { ctx } = this;
    const res = await this.notifiesService.getNotifyDetailByCode(code);
    return ctx.getResponseInstance(ctx).setResponseData(res).sendSuccess();
  }
}
