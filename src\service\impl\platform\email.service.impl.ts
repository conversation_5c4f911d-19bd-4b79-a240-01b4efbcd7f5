import { Provide, <PERSON>ope, <PERSON><PERSON><PERSON><PERSON> } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IEmailService } from "@/service/platform/email.service";
import { PlatformSendEmailDto } from "@/dto/platform-send-email.dto";

@Provide("PlatformEmailService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class EmailServiceImpl extends BaseService implements IEmailService {
  async sendVerifyCode(dto: PlatformSendEmailDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/api/platform/email/verify-code", dto));
  }
}
