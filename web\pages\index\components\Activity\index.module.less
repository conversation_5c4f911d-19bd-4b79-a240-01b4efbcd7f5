/** 生成item背景色 */
@activeBgColorArr: #ffffff, #eee9e1, #e3e2e9, #d0e1e1;
@activeSubTextColorArr: #000000, #5470c6, #9085d1, #355e76;
@len: 4;
.loopActivityColor(@index) when (@index <= @len) {
  // nth-child 如何从 2开始
  // @{index}: 如何从 2开始
  &:nth-child(@{index}) {
    background-color: extract(@activeBgColorArr, @index);
    .activity-remark {
      color: extract(@activeSubTextColorArr, @index);
    }
  }
  // 循环增加
  .loopActivityColor(@index+1);
}

.wrapper {
  :global {
    .activity {
      .flex-row(space-between, center);
      margin-top: @plate-margin-top;
      .activity-title {
        color: @main-text-color;
        font-size: 24px;
        font-weight: 700;
      }
      .remark {
        font-size: 20px;
        font-weight: 350;
      }
      // 活动项
      .activity-item {
        width: 317px;
        height: 150px;
        a {
          width: 100%;
          height: 100%;
          .flex-row(space-around, center);
          padding: 0 12px;
          .activity-info {
            .flex-col(center, flex-start);
            gap: 8px;
            .activity-title {
              font-size: 24px;
              font-weight: 700;
              line-height: 1;
              .ellipsis();
            }
            .activity-remark {
              font-size: 20px;
              font-weight: 350;
              line-height: 1;
            }
          }
          // 适当控制图片最大显示宽度
          img {
            max-width: 120px;
            transition: all 0.1s ease-in-out;
          }
        }
        .loopActivityColor(0);
        &:first-child {
          width: 200px;
          height: 151px;
          background: #ffffff;
          .activity-title {
            color: #f60909;
          }
          .activity-remark {
            color: #f06f6f;
          }
          // 首项隐藏图片
          img {
            display: none;
          }
        }
        &:hover {
          box-shadow: 2px 0 6px #ccc;
          img {
            transform: scale(1.01);
          }
        }
      }
    }
  }
}
