import { Provide, Scope, <PERSON><PERSON><PERSON><PERSON> } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ISpreadTopicService } from "@/service/spread/topic.service";

@Provide("SpreadTopicService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class TopicServiceImpl extends BaseService implements ISpreadTopicService {
  async getCategories(): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/spread/topic/categories"));
  }

  async getTopicList(categoryCode: string, params: { orderBy?: string; page?: number; size?: string }): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/spread/topic/categories/${categoryCode}/topics`, params));
  }

  async getTopicDetail(categoryCode: string, topicCode: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/spread/topic/categories/${categoryCode}/topics/${topicCode}`));
  }
}
