import React, { useEffect, useState } from "react";
import { $tools } from "@/utils/tools.util";
import { useCountDownCache } from "@/hooks/useCountDown";
import { $localStorage } from "@/utils/storage.util";
import { Form, Input, Toast } from "antd-mobile";
import { sendEmailVerifyCode } from "@/apis/email";
import { onlyCsr } from "ssr-hoc-react";
import { EmailSendTypeEnum } from "@/enums/EmailSendTypeEnum";
import style from "./index.module.less";
import { useForm } from "rc-field-form";

interface IEmailSendCodeProps {
  countdownSeconds: number; // 倒计时单位秒,默认60秒
  uuid: string;
  sendType: EmailSendTypeEnum;
  label: string;
  rules: object; // 校验规则
}
type EmailSendPartialProps = Partial<IEmailSendCodeProps> & { formRef: typeof useForm };

const EmailSendCode = (props: EmailSendPartialProps) => {
  const formRef: any = props.formRef;
  // 是否已触发发送
  const [isTrigger, setIsTrigger] = useState(false);
  /* 是否倒计时处理中 */
  const [isCountDownIng, setIsCountDownIng] = useState(false);
  const [formParams, setFormParams] = useState({
    validate: {
      verifyCode: null,
    },
    rules: {
      verifyCode: [{ required: true, whitespace: true, message: "邮件验证码不能为空" }],
    },
  });
  const rules = Object.assign({}, formParams.rules, props.rules);
  const cnptUuid = props.uuid ?? $tools.uid();
  const smsCountDownHook = useCountDownCache(props.countdownSeconds ?? 120, cnptUuid, () => {
    setIsCountDownIng(false);
  });
  useEffect(() => {
    if ($localStorage.get(`timerStartTime-${cnptUuid}`)) {
      setIsTrigger(true);
      setIsCountDownIng(true);
      smsCountDownHook.start();
    }
  }, []);
  // 发送邮件验证码
  const renderSendVerifyCode = () => {
    return (
      <span className={`sms-send-btn${isCountDownIng ? " sms-send-btn-ing" : ""}`} onClick={handleSendEmailVerifyCodeEvent}>
        {!isTrigger ? "获取验证码" : isCountDownIng ? `倒计时 ${smsCountDownHook.count} 秒` : "重新获取"}
      </span>
    );
  };
  const handleSendEmailVerifyCodeEvent = async () => {
    const errMap = await formRef
      .validateFields(["email"])
      .then(() => null)
      .catch(err => err);
    if (errMap) {
      Toast.show({
        content: "请先填入正确的邮箱",
        maskClickable: false,
        duration: 1500,
      });
      return false;
    }
    if (isCountDownIng) {
      return false;
    }
    const sendType = props.sendType ?? EmailSendTypeEnum.REGISTER;
    const email = formRef.getFieldValue("email");
    const [err, res] = await sendEmailVerifyCode({ email, sendType })
      .then(data => [null, data])
      .catch(err => [err, null]);
    if (err) {
      Toast.show({
        content: err.data?.message,
        maskClickable: false,
        duration: 1500,
      });
      return false;
    }
    setIsTrigger(true);
    setIsCountDownIng(true);
    smsCountDownHook.start();
    Toast.show({
      content: "已发送验证码到邮箱，请注意查收" || res?.message,
      maskClickable: false,
      duration: 1500,
      icon: "success",
    });
  };
  return (
    <>
      <div className={style.wrapper}>
        <Form.Item name="verifyCode" className="item" label={props?.label} rules={rules.verifyCode} extra={renderSendVerifyCode()}>
          <Input className="input" autoComplete="off" placeholder="请输入邮件验证码" />
        </Form.Item>
      </div>
    </>
  );
};

EmailSendCode.defaultProps = {
  countdownSeconds: 60,
  label: "邮箱验证码",
};
export default onlyCsr(EmailSendCode) as any;
