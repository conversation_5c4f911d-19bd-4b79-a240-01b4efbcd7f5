interface IProduct {
  productId: number;
  productQuantity:number;
  productSku: string;
  remark?: string;
  rowNum?: number;
  skuId?: number;
  isDanger?: boolean;
  price?: boolean;
}

export interface ICreateVo {
  id?: number;
  receiptId: number;//发票地址
  receivingAddressId: number;//会员地址
  remark: string;
  memberId?: string;
  orderTitle?: string;
  exceptionContact?: string;
  exceptionContactPhone?: string;
  deliveryRequirements?: string;
  sourceChannel?: number;
  invoiceWithGoods?: number;
  freightType?: number;
  sendType?: number;
  transportType?: string;//选择的物流方式
  freightFee?: number;
  deliveryOrderOptions?: number;
  customerPo?: string;
  tradeType?: string;
  products: IProduct[] | any
}

export interface IPayGatewayVo {
  sn: string;
  orderType?: string;
  paymentClient?: string;
}

