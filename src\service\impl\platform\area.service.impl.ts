import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IAreaService } from "@/service/platform/area.service";

@Provide("AreaService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class AreaServiceImpl extends BaseService implements IAreaService {
  async getAreaCascade(): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/platform/provinces/cascade"), "获取数据出错了，请稍后再试！");
  }

  async getAreaInfo(address: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/platform/provinces/selectAddress?address=${address}`), "获取数据出错了，请稍后再试！");
  }
}
