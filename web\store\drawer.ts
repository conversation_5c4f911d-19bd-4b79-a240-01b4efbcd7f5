const state = {
  shoppingCartVisible: false, // 购物车
  shoppingCartNum: 0, // 购物车商品数
  shoppingCartData: [], // 购物车列表
};

function reducer(state: any, action: any) {
  switch (action.type) {
    case "DRAWER_SHOPPING_CART_TOGGLE":
      return { ...state, ...action.payload };
    case "SHOPPING_CART_NUM_UPDATE":
      return { ...state, ...action.payload };
    case "SHOPPING_CART_INIT":
      return { ...state, ...action.payload };
  }
}
export { state, reducer };
