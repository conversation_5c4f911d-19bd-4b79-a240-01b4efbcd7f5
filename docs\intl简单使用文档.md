[TOC]

# react-intl

参考用法：

```javascript
import React, { useContext } from 'react'
import { IContext, SProps } from 'ssr-types-react'

import { FormattedMessage, useIntl } from "react-intl";
import { Button } from "antd";
import { useStoreContext } from "ssr-common-utils";

export default function Index (props: SProps) {
  const { dispatch, state } = useContext<IContext>(useStoreContext())
  const { locale } = state
  const changeLang = () => {
    // 改变状态里的 语言 进行切换
    console.log("当前语言为：", locale)
    dispatch?.({
      type: 'CHANGE_LOCALE',
      payload: {
        locale: locale === 'zh' ? 'en' : 'zh'
      }
    })
  }
  // 为什么不要直接使用 formatMessage 这个语法糖？
  // 虽然 formatMessage 使用起来会非常方便，但是它脱离了 react 的生命周期，最严重的问题就是切换语言时无法触发 dom 重新渲染。为了解决这个问题，我们切换语言时会刷新一下浏览器，用户体验很差，所以推荐大家使用 useIntl 或者 injectIntl，可以实现同样的功能。
  const intl = useIntl();
  const t = (id) => intl.formatMessage({ id }) // 写成传参方式
  const {formatMessage: f} = useIntl()

  return (
    <>
      {/* 测试语言包 */}
      <div>
        <FormattedMessage id="start" />
        {f({id: 'start'})}
      </div>
      <div>
        {/* 写法二 */}
        {t('start')}
      </div>
      <div>
        <Button onClick={changeLang} > <FormattedMessage id="switch" /></Button>
      </div>
      <div>
        {/* 加变量也可以 */}
        <div> <FormattedMessage id="say" values={{ name: 'tom' }} /></div>
        <div><FormattedMessage id="mall.success"/></div>
        <div>{intl.formatMessage({ id: 'say', }, { name: 'tom', },)}</div>
      </div>
      主页
    </>
  )
}

```
