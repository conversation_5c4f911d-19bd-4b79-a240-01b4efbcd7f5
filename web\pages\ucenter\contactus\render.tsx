import React, { useContext } from "react";
import { IContext, SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import { Card, Descriptions } from "antd";
import style from "./index.module.less";
import SvgIcon from "@/components/SvgIcon";
import { useStoreContext } from "ssr-common-utils";

export default function UcenterContactus(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  // 专属业务顾问
  const memberContact = state?.memberContact;

  return (
    <>
      <div className={style.wrapper}>
        <UCenterCard title={"联系我们"} />
        <Card className="sub-container" bodyStyle={{ padding: "0" }} bordered={false}>
          <Card
            className="sub-container-item"
            type="inner"
            title={
              <span className="title">
                <SvgIcon iconClass="customer-service" /> {memberContact ? "您的商务经理" : "易购客服"}
              </span>
            }
          >
            {memberContact ? (
              <Descriptions bordered={false}>
                <Descriptions.Item label="姓名" span={3}>
                  {memberContact.nickname}
                </Descriptions.Item>
                <Descriptions.Item label="电话" span={3}>
                  {memberContact.telephone || "未设置"}
                </Descriptions.Item>
                <Descriptions.Item label="手机" span={3}>
                  {memberContact.phone}
                </Descriptions.Item>
                <Descriptions.Item label="邮箱" span={3}>
                  {memberContact.email || "未设置"}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <span>电话：020-84382888转8836</span>
            )}
          </Card>
        </Card>
      </div>
    </>
  );
}
