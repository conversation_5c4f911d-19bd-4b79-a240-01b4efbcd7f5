import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { IOrderFreightService } from "@/service/order/freight.service";
import { orderFreightQueryDto } from "~/typings/data/order";
import FreightCalculate from "./freight.calculate";

@Provide("OrderFreightService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class OrderFreightServiceImpl implements IOrderFreightService {
  /**
   * 运费计算
   *
   * @param criteria /
   */
  async calculateFreight(criteria: orderFreightQueryDto): Promise<any> {
    return FreightCalculate.executeLogic(criteria);
  }
}
