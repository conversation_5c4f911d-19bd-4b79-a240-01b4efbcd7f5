import React, { useEffect, useState } from "react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import { useFootprint } from "@/hooks/useFootprint";
import { Button, message, Modal } from "antd";
import { ClearOutlined, RedoOutlined } from "@ant-design/icons";
import style from "./index.module.less";
import { useLoading } from "@/hooks/useLoading";
import FootPrintList from "./parts/FootList";
import FootPrintSpan from "./parts/FootPrintSpan";

export default function FootprintIndex() {
  const { history, refresh, clearHistory, deleteHistory, limit } = useFootprint();
  const [footPrintHistory, setFootPrintHistory] = useState(history);
  const pageSize = 15;
  const loadingHooks = useLoading();
  const [currentRecord, setCurrentRecord] = useState(0);

  const handleRemoveItem = async item => {
    const success = await deleteHistory(item);
    success && setFootPrintHistory(refresh());
    if (currentRecord > 0) {
      setCurrentRecord(currentRecord - 1);
    }
  };

  useEffect(() => {
    setCurrentRecord(footPrintHistory.length);
  }, []);

  const handleRefresh = () => {
    loadingHooks.showLoading();
    setTimeout(() => {
      loadingHooks.hideLoading();
      setFootPrintHistory(refresh());
    }, 200);
    setTimeout(async () => {
      await setCurrentRecord(footPrintHistory.length);
    }, 200);
  };

  const handleClearAll = () => {
    Modal.confirm({
      content: "是否清空足迹记录？",
      title: "警告",
      type: "warning",
      centered: true,
      onOk: () => {
        clearHistory();
        setFootPrintHistory(refresh());
        message.success("清空成功！");
      },
    });
  };

  const renderTitle = () => {
    return (
      <div className="title-wrapper">
        <FootPrintSpan count={history.length} limit={limit} />
        <div className="btn-wrapper">
          <Button type="link" size="small" icon={<RedoOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
          <Button className="btn-clear" danger size="small" icon={<ClearOutlined />} disabled={!currentRecord} onClick={handleClearAll}>
            清空足迹记录
          </Button>
        </div>
      </div>
    );
  };

  const renderListHeader = () => {
    return (
      <>
        <div className="index">序号</div>
        <div>产品名称</div>
        <div>品牌</div>
        <div>SKU</div>
        <div>CAS</div>
        <div>包装规格</div>
        <div>官方指导价</div>
        <div className="action">操作</div>
      </>
    );
  };

  return (
    <div className={style.wrapper}>
      <UCenterCard title={renderTitle()} />
      <div className="foot-print-list">
        <div className="product-list-header">{renderListHeader()}</div>
        <FootPrintList pageSize={pageSize} footPrintHistory={footPrintHistory} handleRemoveItem={handleRemoveItem} />
      </div>
    </div>
  );
}
