/** 公告数据类型 */
export interface INotifiesArrayType {
  content?: Array<INotifiesDataType>;
  currentPage?:number;
  total?: number;
  pageSize?: number;
  totalPages?: number;
}

export interface INotifiesDataType {
  attachFiles?: string;
  author?: string;
  code?: string;
  content?: string;
  createdDate?: string;
  description?: string;
  id?: number;
  isTop?: number;
  remark?: string;
  sendTime?: string;
  title?: string;
}
