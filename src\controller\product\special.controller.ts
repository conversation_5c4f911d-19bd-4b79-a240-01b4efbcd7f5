import { Controller, Get, Inject, Param } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { ISpreadTopicService } from "@/service/spread/topic.service";
import { FORMAT } from "@midwayjs/core";
import { CacheManager } from "@midwayjs/cache";
import cacheKeyConstant from "@/common/constants/cache.constant";

@Controller("/product-special")
export class SpecialController extends BaseController {
  @Inject("SpreadTopicService")
  specialService: ISpreadTopicService;

  @Inject()
  cacheManager: CacheManager;

  topicCategoryCacheKey = cacheKeyConstant.BUSINESS.PLATFORM_TOPIC_CATEGORY;

  @Get("/:code")
  async topic(@Param("code") specialCode: string): Promise<void> {
    const { ctx } = this;
    ctx.topicCategories = await this.getTopicCategoriesData();

    // 根据code获取对应专题列表数据
    const topicList = await this.specialService.getTopicList(specialCode);
    ctx.topicListData = topicList.data;

    ctx.body = await render<Readable>(ctx, {
      stream: true,
    });
  }

  @Get("/:code/:detailId")
  async topicDetail(@Param("code") specialCode: string, @Param("detailId") id: string): Promise<void> {
    const { ctx } = this;
    ctx.topicCategories = await this.getTopicCategoriesData();

    const result = await this.specialService.getTopicDetail(specialCode, id);
    ctx.topicDetail = result.data;

    ctx.body = await render<Readable>(ctx, {
      stream: true,
    });
  }

  /** 取专题分类数据 */
  protected async getTopicCategoriesData() {
    // 专题分类缓存，1分钟
    let topicCategoriesData: any = await this.cacheManager.get(this.topicCategoryCacheKey);
    if (!topicCategoriesData) {
      const result = await this.specialService.getCategories();
      if (result.data?.length) {
        await this.cacheManager.set(this.topicCategoryCacheKey, result.data, { ttl: FORMAT.MS.ONE_SECOND / 1000 });
        topicCategoriesData = result.data;
      }
    }
    return topicCategoriesData;
  }
}
