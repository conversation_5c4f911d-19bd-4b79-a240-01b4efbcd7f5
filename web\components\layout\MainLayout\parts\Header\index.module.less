@top-bar-nav-link-font-size: @font-size-14; // 顶部栏-按钮链接字体 14px
@top-bar-nav-link-font-color: @main-text-color; // 顶部栏-字体颜色

@stock-code-font-size: @font-size-16; // 股票代码字体大小 16px
@stock-code-font-color: #e62129; // 股票代码字体颜色
@stock-code-font-weight: 600; // 股票代码字体粗细
@stock-code-font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; // 股票代码字体

@header-search-wrapper-height: 118px; // 顶部搜索框整体-高度 108px

.mainHeader {
  :global {
    background-color: #fff;
    min-width: @main-width;
    .header-wrapper {
      /*===== 1、登录信息 ===== */
      .header-top-wrapper {
        background-color: #f4f4f4;
        .flex-row(center,center);
        // 顶部栏
        .header-top-box {
          width: @main-width;
          .flex-row(space-between, center);
          box-sizing: border-box;
          height: 30px;
          font-size: @auxiliary-text-size;
          color: @regular-text-color;
          .header-left-welcome {
            .welcome-company {
              color: #ff3939;
            }
          }
          // 顶部导航条
          .header-right-nav {
            .nav-link {
              margin-right: 15px;
              height: 20px;
              cursor: pointer;
              font-size: @top-bar-nav-link-font-size;
              color: @top-bar-nav-link-font-color;
              &:last-child {
                margin-right: 0;
              }
              .icon {
                margin-right: 4px;
                display: inline-block;
                color: inherit;
                line-height: 0;
                text-align: center;
                text-transform: none;
                vertical-align: -0.125em;
                text-rendering: optimizeLegibility;
                -webkit-font-smoothing: antialiased;
                &.contract-us {
                  background: url("@@img/main-layout-sprites.png") -10px -10px;
                  width: 12px;
                  height: 12px;
                }
                &.help-center {
                  width: 12px;
                  height: 12px;
                  background: url("@@img/main-layout-sprites.png") -74px -10px;
                }
                &.honor {
                  width: 12px;
                  height: 12px;
                  background: url("@@img/main-layout-sprites.png") -340px 0px;
                }
              }
              &:hover,
              &:active {
                color: @main-link-hover-color;
                text-decoration: underline;
              }
              &-contract:hover {
                .contract-us {
                  background: url("@@img/main-layout-sprites.png") -42px -10px;
                }
              }
              &-help:hover {
                .help-center {
                  background: url("@@img/main-layout-sprites.png") -106px -10px;
                }
              }
              &-honor:hover {
                .honor {
                  background: url("@@img/main-layout-sprites.png") -352px 0;
                }
              }
            }
          }
        }
      }
      //===== 2、搜索栏 =====
      .header-search-wrapper {
        width: @main-width;
        height: @header-search-wrapper-height;
        margin: 0 auto;
        .flex-row(left, center);
        .header-logo-wrapper {
          margin-left: 10px;
          .flex-col(center, center);
          .app-logo {
            width: 180px;
            height: 64px;
            cursor: pointer;
            display: inline-block;
            background: url("@@img/logo.svg") 50% 50% no-repeat;
            background-size: 76%;
          }
          .stock-code {
            padding-left: 2px;
            line-height: 1;
            font-size: @stock-code-font-size;
            color: @stock-code-font-color;
            font-weight: @stock-code-font-weight;
            font-family: @stock-code-font-family;
          }
        }
        .header-mascot {
          width: 200px;
          cursor: pointer;
          display: flex;
          justify-content: center;
        }
      }
      //===== 3、导航栏 =====
      .header-nav-wrapper {
        .flex-row(center,center);
        border-bottom: 4px solid #e72129;
        .header-nav-box {
          width: @main-width;
          box-sizing: border-box;
          height: 38px;
          display: flex;
          // 导航
          .nav-box {
            width: 100%;
            height: 38px;
            // antd
            .ant-menu-item,
            .ant-menu-submenu-title {
              .ant-menu-title-content {
                font-size: 16px;
                font-weight: 600;
              }
            }
            .ant-menu-item-active,
            .ant-menu-item-only-child,
            .ant-menu-submenu-selected,
            .ant-menu-item-selected,
            .ant-menu-submenu {
              &::after {
                border-bottom: 0;
              }
            }
            // 促销活动
            .promotional {
              position: relative;
              &::before {
                content: "";
                position: absolute;
                left: -24px;
                top: -4px;
                width: 24px;
                height: 24px;
                background-image: url("https://oss.qa114.com/pictures/enTw48brGpBbi224mD9kBLNk3piAn57SO8khM6SW.gif");
                background-size: 100%;
                background-repeat: no-repeat;
              }
            }
          }
        }
      }
    }
  }
}
