import React, { useState } from "react";
import { SProps } from "ssr-types-react";
import { loginByPassword, loginBySms } from "@/apis/auth";
import { message } from "antd";
import LoginTpl from "./templates/login4/login.tpl";
import { ILoginPasswordParams, ILoginSmsParams } from "@/typings/auth.interface";
import { SYSTEM_LOGIN_TYPES } from "@/constants/system";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { debounce } from "lodash";

interface IProps extends SProps {
  getCheckPass: () => void;
}

export default function Login(props: IProps) {
  // 表单默认值
  const isProduction = process.env.NODE_ENV === "production";
  const defaultForm: ILoginPasswordParams | ILoginSmsParams = {
    phone: isProduction ? "" : "13800138000",
    password: isProduction ? "" : "abc123456@",
    loginType: SYSTEM_LOGIN_TYPES.PASSWORD.value,
  };
  const [loading, setLoading] = useState(false);

  // 触发提交登录
  const onFinish = debounce(async (values: ILoginPasswordParams | ILoginSmsParams) => {
    setLoading(true);
    let response;
    if (values.loginType === SYSTEM_LOGIN_TYPES.PASSWORD.value) {
      values = values as ILoginPasswordParams;
      response = await loginByPassword({ phone: values.phone, password: encrypt(values.password), companyName: values.companyName ? values.companyName : undefined })
        .then(res => [null, res])
        .catch(err => [err, null]);
    } else if (values.loginType === SYSTEM_LOGIN_TYPES.SMS.value) {
      values = values as ILoginSmsParams;
      response = await loginBySms({ phone: values.phone, smsCode: values.smsCode, companyName: values.companyName ? values.companyName : undefined })
        .then(res => [null, res])
        .catch(err => [err, null]);
    } else {
      setLoading(false);
      message.error("未处理的登录类型");
      return;
    }

    const [err, res] = response;
    if (err) {
      setLoading(false);
      message.error(err?.data?.message ?? "手机号或密码错误，登录失败！");
      return;
    }
    message.success(res.message || "登录成功");
    setTimeout(() => {
      const params = new URLSearchParams(props.location.search);
      window.location.href = params.get("redirect") || "/";
    }, 3000);
  }, 300);

  return <LoginTpl defaultForm={defaultForm} onFinish={onFinish} loginLoading={loading} />;
}
