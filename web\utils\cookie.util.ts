import Cookies from "js-cookie";

export class <PERSON>ieUtil {
  private static instance: <PERSON><PERSON>Util;

  static getInstance() {
    if (!this.instance) {
      this.instance = new CookieUtil();
    }
    return this.instance;
  }

  public get(name: string) {
    return Cookies.get(name);
  }

  /**
   *
   * @param name
   * @param data
   * @param options like:{ expires: expires }
   */
  public set(name: string, data: any, options?: Object) {
    if (options !== undefined) {
      Cookies.set(name, data, options);
    } else {
      Cookies.set(name, data);
    }
  }

  public remove(name: string) {
    Cookies.remove(name);
  }
}
