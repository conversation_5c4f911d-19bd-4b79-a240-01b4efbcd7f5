import React, { useContext, useEffect, useState } from "react";
import { IContext, SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import CRUD from "@/components/Crud/crud";
import { ColumnsType } from "antd/es/table";
import UDOperation from "@/components/Crud/UDOperation";
import style from "./index.module.less";
import MyCrudPagination from "@/components/Crud/Pagination";
import { Table, Spin, Button, Card, message, Modal, Space } from "antd";
import { PlusOutlined, ReloadOutlined } from "@ant-design/icons";
import crudAddress from "@/apis/member/address";
import { IReceivingAddressType } from "@/typings/member.interface";
import AddressEditModal from "@/components/AddressEditModal";
import { useStoreContext } from "ssr-common-utils";

interface dataType extends IReceivingAddressType {
  key: React.Key;
  provinceInfo: any;
  cityInfo: any;
  districtInfo: any;
  address: string;
  isDefault: number;
  sortOrder: number;
}

export default function MemberAddressIndex(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  /* ======================================= use state start======================================= */
  const [mode, setMode] = useState<"add" | "edit">("add");
  const [currentRow, setCurrentRow] = useState<IReceivingAddressType>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  /* ======================================= use state end======================================= */
  const crud = CRUD({ url: "/api/ucenter/addresses", pageSize: 10 });
  // 地址明细
  const tableDataSource: IReceivingAddressType[] = crud.tableData;
  const tableColumns: ColumnsType<dataType> = [
    {
      title: "序号",
      width: 50,
      align: "center",
      key: "idx",
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "收货人",
      dataIndex: "nickname",
      width: 90,
      key: "nickname",
      align: "center",
    },
    {
      title: "公司名称",
      width: 120,
      dataIndex: "companyName",
      key: "companyName",
      align: "center",
    },
    {
      title: "收货地址",
      dataIndex: "fullAddress",
      key: "fullAddress",
      align: "center",
      render: (text, record, index) => (
        <>
          <div className="address-box">
            <div className="address-area">
              {record.isDefault === 1 ? <span className="default-flag">默认</span> : null}
              <span className="area">{`${record.provinceInfo?.name}${record.cityInfo?.name}${record.districtInfo?.name}`}</span>
            </div>
            <div className="address-detail">{record.address}</div>
          </div>
        </>
      ),
    },
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
      align: "center",
    },
    {
      title: "固话",
      dataIndex: "telephone",
      key: "telephone",
      align: "center",
      render: text => `${text || "-"}`,
    },
    {
      title: "邮编",
      dataIndex: "zipCode",
      key: "zipCode",
      align: "center",
      render: text => `${text || "-"}`,
    },
    {
      title: "操作",
      key: "operation",
      fixed: "right",
      align: "center",
      className: "operation",
      width: 175,
      render: (text, record, index) => (
        <UDOperation
          row={record}
          showEvent={row => handleOpenEditAddressPanel(row)}
          delEvent={async row => await handleDeleteAddress(row)}
          opt={
            <Button type="primary" ghost size="small" disabled={!!record.isDefault} onClick={async () => await handleSetDefaultAddress(record["id"])}>
              {record.isDefault ? "默认地址" : "设为默认"}
            </Button>
          }
        />
      ),
    },
  ];
  useEffect(() => {
    crud.refresh();
  }, []);

  /* ======================================= method start======================================= */
  /** 设为默认 */
  const handleSetDefaultAddress = async (id: number) => {
    Modal.confirm({
      content: "确定将该条地址设为默认？",
      title: "提醒",
      type: "warning",
      centered: true,
      closable: true,
      onOk: async () => {
        const [err, res] = await crudAddress
          .setDefaultAddress(id)
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (err) {
          message.error(err.data.message || "请求异常，设置失败！");
          return;
        }
        message.success(res.meesage || "设置成功");
        crud.refresh();
      },
    });
  };

  /** 删除地址 */
  const handleDeleteAddress = async (row: IReceivingAddressType) => {
    Modal.confirm({
      content: "确定删除此条地址信息吗？",
      title: "警告",
      type: "warning",
      centered: true,
      closable: true,
      onOk: async () => {
        if (!row.id) {
          return;
        }
        const [err, res] = await crudAddress
          .remove(row.id)
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (err) {
          message.error(err.data.message || "请求异常，删除失败！");
          return;
        }
        message.success("删除成功" || res.message);
        crud.refresh();
      },
    });
  };

  /** 激活-新增地址面板 */
  const handleOpenAddAddressPanel = () => {
    setMode("add");
    setModalVisible(true);
  };

  /** 激活-地址编辑面板 */
  const handleOpenEditAddressPanel = (row: IReceivingAddressType) => {
    setCurrentRow(row);
    setMode("edit");
    setModalVisible(true);
  };
  /* ======================================= method end======================================= */

  return (
    <>
      <div className={style.addressWrapper}>
        <UCenterCard
          title={"收货地址"}
          extra={
            <div className="opt-action">
              <Space>
                <Button size="middle" loading={crud.loading} icon={<ReloadOutlined />} onClick={() => crud.refresh()}>
                  刷新
                </Button>
                <Button size="middle" icon={<PlusOutlined />} onClick={handleOpenAddAddressPanel}>
                  新增收货地址
                </Button>
              </Space>
            </div>
          }
        />
        <Spin size="default" spinning={crud.loading}>
          <Card loading={crud.loading} type="inner" bodyStyle={{ padding: 0 }}>
            <div className="content-container">
              <Table rowKey={"id"} size="small" dataSource={tableDataSource} columns={tableColumns} pagination={false} />
              <div className="content-container-pagination">
                <MyCrudPagination crudInstance={crud} showSizeChanger={true} />
              </div>
            </div>
          </Card>
        </Spin>
        {/* 地址操作面板 */}
        <AddressEditModal callbackFunc={() => crud.refresh()} modalVisible={modalVisible} changeModalVisible={setModalVisible} mode={mode} addressId={currentRow?.id} companyName={state?.userData?.companyName} />
      </div>
    </>
  );
}
