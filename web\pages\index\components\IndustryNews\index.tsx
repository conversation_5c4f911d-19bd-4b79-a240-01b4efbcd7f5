import React, { useContext, useEffect, useRef, useState } from "react";
import style from "./index.module.less";
import { parseTime } from "@/utils/date.utils";
import { Button, Image } from "antd";
import { RightOutlined } from "@ant-design/icons";
import IndustryOrSelectionHeader from "@/pages/index/parts/IndustryOrSelectionHeader";
import { useStoreContext } from "ssr-common-utils";
import { IContext } from "ssr-types";
import { useOss } from "@/hooks/useOss";

interface IndustryNewType {
  id: number;
  url: string;
  title: string;
  create_date: string;
  content: string;
  creator: string;
  sub_title: string;
  head_icon: string;
  images: string[];
  recommend?: boolean;
}

interface ArticleRecommendsDataType {
  title: string;
  link: string;
  image_path: string;
  description: string;
  filed: string;
  is_main_image: boolean;
}

export default function IndustryNews() {
  const { state } = useContext<IContext>(useStoreContext());
  // 推荐图组
  const articleRecommendsData = state?.indexData?.articleRecommendsData || [];
  const [mainRecommend, setMainRecommend] = useState<ArticleRecommendsDataType>();
  const [articleImages, setArticleImages] = useState<string[]>();
  // 推荐新闻
  const moreLink = "https://www.ghtech.com/Gnews/gnews.html";
  const linkPrefix = `https://www.ghtech.com/GNews/gnews_`;
  const myRef = useRef(null);
  const [industryNews, setIndustryNews] = useState<IndustryNewType[] | any>([]);
  // const [industryNews, setIndustryNews] = useState<IndustryNewType[] | any>(state?.indexData?.homeNewsData || []);
  const useOssHook = useOss();
  // 默认
  const [headerImg, setHeaderImg] = useState("");
  const defaultRecommend = {
    author: "光华科技",
    title: "光华科技",
    description: "",
    url: "",
    head_icon: "/images/news/company-1.jpg",
    images: ["/images/news/company-1.jpg", "/images/news/company-2.jpg", "/images/news/company-3.jpg"],
    recommend: true,
  };

  useEffect(() => {
    initArticleRecommendsData()
  }, []);

  /** 初始化 */
  const initArticleRecommendsData = () => {
    if (!articleRecommendsData.length) { return }
    const tempRecommends = articleRecommendsData.sort(item => item.is_main_image).slice(0, 3)
    let mainRecommendIndex = tempRecommends.findIndex(item => item.is_main_image)
    if (mainRecommendIndex === -1) { mainRecommendIndex = 0 }

    setMainRecommend(tempRecommends[mainRecommendIndex])
    setArticleImages(tempRecommends.map((item) => {
      return item.image_path;
    }))

    // 如果官网新闻列表为空，则使用默认推荐图组
    if (!industryNews?.length) {
      setIndustryNews(articleRecommendsData.map(item => {
        return {
          url: item.link,
          title: item.title,
          content: item?.description || '',
          recommend: item?.is_main_image || false,
          creator: '',
          sub_title: '',
          create_date: ''
        }
      }));
    }
  }

  /** 默认-更换推荐图片显示 */
  const changeHeaderImgDefaultEvent = img => {
    if (!img || img === headerImg) {
      return;
    }
    setHeaderImg(img);
  };

  /** 有效推荐新闻图组-更换推荐图片显示 */
  const changeHeaderImgEvent = (index) => {
    setMainRecommend(articleRecommendsData[index])
  }

  /** 查看更多新闻 */
  const handleShowMoreNews = () => {
    window.open(moreLink);
  };

  /** 渲染-默认-占位推荐图 */
  const renderDefaultRecommend = () => {
    return <>
      {/* 推荐新闻必须传1张主图 */}
      <a className="recommend-head">
        <Image src={headerImg || defaultRecommend?.head_icon} alt="head-img" />
        <span className="recommend-title">{defaultRecommend?.title}</span>
        {defaultRecommend?.description && <span className="recommend-description">{defaultRecommend.description}</span>}
      </a>
      {/* 推荐新闻必须传3张子图 */}
      <div className="recommend-images">
        {defaultRecommend?.images.map(img => (
          <span key={img} className="recommend-images-item" onMouseEnter={() => changeHeaderImgDefaultEvent(img)}>
              <img src={img} alt="img" />
            </span>
        ))}
      </div>
    </>
  }

  /** 渲染-有效推荐新闻图组首项 */
  const renderMainRecommend = () => {
    return mainRecommend && <div className="recommend-article">
      <Image src={useOssHook.generateOssFullFilepath(mainRecommend.image_path)} alt="article-img" />
      <a href={mainRecommend.link} target="_blank">
        <div className="article-masker">
          <div className="article-title">{ mainRecommend.title }</div>
          { mainRecommend.description && <div className="article-description">{ mainRecommend.description }</div> }
        </div>
      </a>
    </div>
  }

  /** 渲染-有效推荐新闻图组 */
  const renderRecommendImages = () => {
    return articleImages?.length && <div className="recommend-images">
      {articleImages.map((img, index) => (
        <span key={index} className="recommend-images-item" onMouseEnter={() => changeHeaderImgEvent(index)}>
          <img src={useOssHook.generateOssFullFilepath(img)} alt="img" />
        </span>
      ))}
    </div>
  }

  /** 渲染有效推荐图组 */
  const renderArticleRecommend = () => {
    return <div>
      {/* 头图推荐 */}
      { renderMainRecommend() }
      {/* 图组选择器 */}
      { renderRecommendImages() }
    </div>
  }

  return (
    <div className={style.wrapper} ref={myRef}>
      <div className="industry-news">
        <IndustryOrSelectionHeader title="光华动态" />
        <div className="industry-news-container">
          {/* 推荐新闻 */}
          <div className="news-recommend">
            {
              !articleRecommendsData.length ? renderDefaultRecommend() : renderArticleRecommend()
            }
          </div>
          {/* 新闻列表-首页显示3条新闻列 */}
          <ul className="news-list">
            {
              industryNews?.slice(0, 3).map((item, index) => (
                <li key={index} className="news-item">
                  <a href={(item?.id ? `${linkPrefix}${item.id}.html` : item.url) || moreLink} className="news-item-content" target="_blank">
                    <span className="new-title" dangerouslySetInnerHTML={{ __html: item?.title || '' }} />
                    <span className="new-sub-title" dangerouslySetInnerHTML={{ __html: item?.content || item?.seodescription || '' }} />
                    <div className="new-publish-datetime">{parseTime(item?.addDate, "{y}-{m}-{d}")}</div>
                  </a>
                </li>
              ))
            }
            {/* 更多 */}
            <Button type="text" size="large" className="news-btn-more" icon={<RightOutlined />} onClick={handleShowMoreNews}>
              查看更多
            </Button>
          </ul>
        </div>
      </div>
    </div>
  );
}
