import React, { useContext, useRef, useState } from "react";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import { Button, Checkbox, Form, FormInstance, Input, message, notification, Select } from "antd";
import { validateEmail, validateMobile } from "@/utils/form-valid.util";
import { ICustomerCategoryType, ISalesmanSpreadInfoType } from "@/typings/member.interface";
import { IRegisterEmailParams, IRegisterParams } from "@/typings/auth.interface";
import { useAgreementModal } from "@/hooks/useAgreementModal";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { registerByEmailPassword, registerByPassword } from "@/apis/auth";
import { InsertRowLeftOutlined, MailOutlined, MobileOutlined, UserOutlined } from "@ant-design/icons";
import Sms<PERSON>aptcha from "@/components/SmsCaptcha";
import EmailSendCodeCnpt from "@/components/EmailSendCodeCnpt";
import { EmailSendTypeEnum } from "@/enums/EmailSendTypeEnum";
import PasswordItem from "@/components/PasswordItem";
import TmAreaCascade from "@/components/TmAreaCascade";
import { AGREEMENT_CONFIG } from "@/constants/common";
import { SProps } from "ssr";

export const useRegisterForm = (salesmanSpreadInfo: ISalesmanSpreadInfoType | null, props?: SProps) => {
  const { state } = useContext<IContext>(useStoreContext());
  const [form] = Form.useForm();
  const registerRef = useRef<FormInstance>(null) as any;
  const [currentRegisterTab, setCurrentRegisterTab] = useState<string>("phoneRegister");
  // 注册校验规则
  const rules = {
    /* 手机注册验证-s */
    phone: [{ required: true, whitespace: true, message: "手机号不能为空!" }, { validator: validateMobile }],
    smsCode: [
      { required: true, whitespace: true, message: "短信验证码不能为空!" },
      { pattern: /^[0-9]{1,}$/, message: "请输入短信验证码!" },
    ],
    /* 手机注册验证-e */
    /* 邮件注册验证-s */
    email: [{ required: true, whitespace: true, message: "邮箱不能为空!" }, { validator: validateEmail }],
    verifyCode: [{ required: true, whitespace: true, message: "邮件验证码不能为空!" }],
    /* 邮件注册验证-e */
    captcha: [{ required: false, whitespace: true, message: "图像验证码不能为空!" }],
    agreement: [
      {
        validator: async (_, value) => {
          if (!value) {
            throw new Error("勾选-同意光华易购平台协议");
          }
          return await Promise.resolve();
        },
      },
    ],
    companyName: [
      { required: true, whitespace: true, message: "请输入公司名称或单位全称!" },
      { max: 50, message: "您输入公司/单位名称超过50个字，请简写~" },
    ],
    customerCategory: [{ required: true, message: "请选择公司或单位所属类别" }],
    address: [{ max: 100, message: "详细地址超过100个字了，请简写~" }],
    areas: [{ required: true, message: "请选择省市区" }],
    principalMainUserid: [],
    principalMainUsername: [{ required: true, message: "请选择专属业务员" }],
    contactName: [{ required: true, message: "请输入联系人姓名" }],
  };
  /** 加载客户类型数据 */
  const customerCategories: ICustomerCategoryType[] = state.customerCategory;
  /** 注册表单数据 */
  const [defaultRegisterForm] = useState<IRegisterParams | IRegisterEmailParams>({
    companyName: "",
    phone: "",
    smsCode: "",
    email: "",
    verifyCode: "",
    password: "",
    repeat: "",
    agreement: false,
    customerCategoryCode: null,
    areas: [],
    address: "",
    principalMainUserid: salesmanSpreadInfo?.userid ?? null,
    principalMainUsername: salesmanSpreadInfo?.nickname ?? null,
    contactName: null,
  });
  const [loading, setLoading] = useState(false);
  // 协议弹窗
  const useAgreementModalHook = useAgreementModal();
  // 省市区ref
  const areaCascadeRef = useRef<any>(null);

  /** ======================================== method start ======================================= */
  /** 事件-点击-立刻注册 */
  const handleRegisterEvent = async params => {
    if (loading) {
      return;
    }
    const encryptPassword = encrypt(params.password);
    const registerData: any = {
      companyName: params.companyName,
      password: encryptPassword,
      confirmPassword: encryptPassword,
      address: params.address,
      areas: params.areas,
      customerCategoryCode: params.customerCategoryCode,
      contactName: params.contactName,
    };
    if (salesmanSpreadInfo?.userid) {
      registerData.principalMainUserid = salesmanSpreadInfo?.userid;
    }
    let err, res;
    if (currentRegisterTab === "phoneRegister") {
      setLoading(true);
      registerData.phone = params.phone;
      registerData.smsCode = params.smsCode;
      const [error1, res1] = await registerByPassword(registerData)
        .then(response => [null, response])
        .catch(error => [error, null]);
      setLoading(false);
      err = error1;
      res = res1;
    } else if (currentRegisterTab === "emailRegister") {
      setLoading(true);
      registerData.email = params.email;
      registerData.verifyCode = params.verifyCode;
      registerData.contactPhone = params.contactPhone;
      const [error2, res2] = await registerByEmailPassword(registerData)
        .then(response => [null, response])
        .catch(error => [error, null]);
      setLoading(false);
      err = error2;
      res = res2;
    } else {
      return notification.error({ message: "非法注册请求类型，请重试！" });
    }
    if (err) {
      notification.error({ message: err.data?.message || "注册失败" });
      return;
    }
    setTimeout(() => {
      message.success("注册成功！" || res?.message);
      window.location.href = "/";
    }, 800);
  };

  /** 激活-平台协议弹窗 */
  const handleOpenAgreement = (type: string) => {
    useAgreementModalHook.open(type);
  };

  /** 省市区选择回调 */
  const handleMonitorAreaChange = areas => {
    if (!areas) {
      form.setFieldValue("areas", "");
      return;
    }
    form.setFieldValue("areas", areas);
    form.validateFields(["areas"]);
  };
  const renderFormCnpt = (registerType: string) => {
    const registerCnpt = {
      phoneRegister: (
        <>
          <Form.Item className="phone" name="phone" label="手机号码" rules={rules.phone}>
            <Input prefix={<MobileOutlined />} placeholder="请输入手机号" autoComplete="off" />
          </Form.Item>
          <SmsCaptcha uuid={"registerPassword"} border />
        </>
      ),
      emailRegister: (
        <>
          <Form.Item className="email" name="email" label="邮箱地址" rules={rules.email}>
            <Input prefix={<MailOutlined />} placeholder="请输入邮箱地址" autoComplete="off" />
          </Form.Item>
          {/* ========== 邮件验证码发送组件 ========== */}
          <EmailSendCodeCnpt uuid={"registerMemberEmail"} sendType={EmailSendTypeEnum.REGISTER} countdownSeconds={60} />
          {/* 联系人手机号码 */}
          <Form.Item className="phone" name="contactPhone" label="联系手机" rules={rules.phone}>
            <Input prefix={<MobileOutlined />} placeholder="请输入联系手机号" autoComplete="off" />
          </Form.Item>
        </>
      ),
    };
    return (
      <Form ref={registerRef} className="register-form" form={form} size="large" initialValues={defaultRegisterForm} onFinish={handleRegisterEvent} labelAlign="right">
        <Form.Item name="companyName" label="公司名称" rules={rules.companyName} validateFirst>
          <Input prefix={<InsertRowLeftOutlined />} placeholder="请输入公司名称或单位全称" autoComplete="off" />
        </Form.Item>
        {registerCnpt[registerType]}
        <PasswordItem prefixCls={"ghmall"} radius={2} />
        {/* 推荐业务员 */}
        {salesmanSpreadInfo ? (
          <Form.Item name="principalMainUsername" className="customer-principal" label="专属业务员" rules={rules.principalMainUsername}>
            <Input prefix={<UserOutlined />} readOnly placeholder="请填入推荐业务员" autoComplete="off" />
          </Form.Item>
        ) : null}
        {/* 公司所属类别 */}
        <Form.Item name="contactName" className="customer-principal" label="联系人" rules={rules.contactName}>
          <Input placeholder="请输入联系人姓名" autoComplete="off" />
        </Form.Item>
        <Form.Item name="customerCategoryCode" rules={rules.customerCategory} label="公司所属类别">
          <Select allowClear showSearch optionFilterProp="class" placeholder="公司或单位所属类别" optionLabelProp="label">
            {customerCategories.map((item, index) => (
              <Select.Option key={index} value={item.code} label={item.label}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={<span className="company-address-label">公司所在地</span>}>
          <Input.Group compact>
            <Form.Item name="areas" rules={rules.areas}>
              <TmAreaCascade onMonitorCascadeChange={handleMonitorAreaChange} onClearEvent={handleMonitorAreaChange} ref={areaCascadeRef} />
            </Form.Item>
            <Form.Item name="address" rules={rules.address}>
              <Input maxLength={100} placeholder="详细街道门牌号,如:创启路63号光华科技大厦A座" autoComplete="off" />
            </Form.Item>
          </Input.Group>
        </Form.Item>
        {/* 协议确认 */}
        <Form.Item className="register-agreement">
          <Form.Item name="agreement" valuePropName="checked" rules={rules.agreement} noStyle>
            <Checkbox checked={defaultRegisterForm.agreement}>阅读并同意:</Checkbox>
          </Form.Item>
          <span className="agreement-teams">
            <span onClick={() => handleOpenAgreement(AGREEMENT_CONFIG.PRIVACY.value)}>《注册协议》和 《隐私政策》</span>
          </span>
        </Form.Item>
        {/* 提交按钮 */}
        <Form.Item className="register-submit">
          <Button loading={loading} type="primary" htmlType="submit" className="register-submit-btn">
            同意协议，立即注册
          </Button>
          <div className="tool-wrap">
            <a href={"/auth/login"}>前往登录</a>
          </div>
        </Form.Item>
      </Form>
    );
  };
  const renderTabItems = [
    {
      label: "手机注册",
      key: "phoneRegister",
      children: currentRegisterTab === "phoneRegister" ? renderFormCnpt("phoneRegister") : null,
    },
    {
      label: "邮箱注册",
      key: "emailRegister",
      children: currentRegisterTab === "emailRegister" ? renderFormCnpt("emailRegister") : null,
    },
  ];
  const handleChangeTab = (tab: string) => {
    setCurrentRegisterTab(tab);
    form?.resetFields();
  };
  /** ======================================== method end ======================================= */

  return {
    renderTabItems,
    handleChangeTab,
    AgreementModal: useAgreementModalHook.AgreementModal,
  };
};
