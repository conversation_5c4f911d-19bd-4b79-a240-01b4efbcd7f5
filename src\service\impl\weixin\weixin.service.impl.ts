import { Inject, Provide, Scope, Scope<PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IWeixinService } from "@/service/weixin/weixin.service";
import { HttpService } from "@midwayjs/axios";
import simpleRequest from "@/utils/simple.request.util";
import { CacheManager } from "@midwayjs/cache";
import cacheKeyConstant from "@/common/constants/cache.constant";
import { FORMAT } from "@midwayjs/core";
import { StringUtil } from "@/utils/string.util";
import jsSHA from "jssha";

@Provide("WeixinService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class WeixinServiceImpl extends BaseService implements IWeixinService {
  @Inject()
  cacheManager: CacheManager;

  @Inject()
  httpService: HttpService;

  /**
   * <p>获取微信accessToken</p>
   */
  async getAccessToken(): Promise<any> {
    return simpleRequest({
      url: `http://oa.ghtech.com:9000/api/GH_OA/accessToken/get`,
      method: "POST",
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });
  }

  /**
   * <p>获取jsapi_ticket</p>
   *
   * @param accessToken /
   */
  async getTicket(accessToken?: string): Promise<any> {
    const cacheKey = cacheKeyConstant.BUSINESS.WX_JS_TICKET;
    const res = await this.cacheManager.get(cacheKey);
    if (!res) {
      if (!accessToken) {
        const accessTokenResult = await this.getAccessToken();
        accessToken = accessTokenResult?.data;
      }
      console.log("wx，request js ticket！");
      const result = await this.httpService.request({
        url: `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`,
        method: "GET",
      });
      // 有效期7200秒
      if (result?.data && result?.data?.errcode === 0) {
        await this.cacheManager.set(cacheKey, result.data, { ttl: (FORMAT.MS.ONE_SECOND * 7150) / 1000 });
        return result?.data;
      }
    }
    return res;
  }

  /**
   * <p>签名</p>
   *
   * @param url
   */
  async getWxSignature(url: string): Promise<any> {
    const ticketResult = await this.getTicket();
    const jsTicket = ticketResult?.ticket;
    // 生成签名数据
    const signatureData: any = {
      noncestr: StringUtil.getRandomString(12),
      jsapi_ticket: jsTicket,
      timestamp: parseInt(String(Date.now() / 1000)),
      url: decodeURIComponent(url).split("#")[0],
    };

    console.log("wx origin url is：", signatureData.url);
    const rawString = StringUtil.rawSign(signatureData);
    // eslint-disable-next-line new-cap
    const shaObj = new jsSHA("SHA-1", "TEXT", { encoding: "UTF8" });
    shaObj.update(rawString);
    const signature = shaObj.getHash("HEX");
    return {
      timestamp: signatureData.timestamp,
      nonceStr: signatureData.noncestr,
      signature,
      url: signatureData.url,
      appId: "wx32b385a58a660f7b",
    };
  }
}
