import { Controller, Get, Inject, Param, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { SearchQueryDto } from "@/dto/search-query.dto";
import { ISearchService } from "@/service/search/search.service";

@Controller("/search")
export class SearchController extends BaseController {
  @Inject("SearchService")
  searchService: ISearchService;

  @Get("/")
  async index(@Query() criteria: SearchQueryDto): Promise<void> {
    const { ctx } = this;
    const res = await this.searchService.searchFromSql(criteria);
    ctx.mallData = res.data;
    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  /** 旧商城书签访问查询-识别跳转(如:/search/keyword/次氯酸钠) */
  @Get("/keyword/:keyword")
  async keyword(@Param('keyword') keyword: string): Promise<void> {
    const { ctx } = this;
    return ctx.redirect(`/search?keyword=${encodeURIComponent(keyword)}`);
  }
}
