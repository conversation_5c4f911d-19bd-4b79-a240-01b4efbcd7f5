import React, { memo, useContext } from "react";
import style from "./index.module.less";
import { Avatar, Popover, Input } from "antd";
import { SearchOutlined, UserOutlined } from "@ant-design/icons";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import SvgIcon from "@/components/SvgIcon";
import { useOss } from "@/hooks/useOss";

export default memo(function Header() {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const navItem = [
    {
      name: "商城主页",
      href: "/",
      isSelected: true,
    },
    {
      name: "我的订单",
      href: "/ucenter/trade/order",
      isSelected: false,
    },
    {
      name: "我的足迹",
      href: "/ucenter/footprint",
      isSelected: false,
    },
    {
      name: "我的消息",
      href: "/ucenter/message",
      isSelected: false,
    },
    {
      name: (
        <span>
          <SvgIcon iconClass="shopping-cart" /> 购物车
        </span>
      ),
      href: "/ucenter/trade/shopping-cart",
      isSelected: false,
    },
  ];
  const user = state?.userData;
  const logoutUrl = `/auth/logout?redirect=${encodeURIComponent("/auth/login?redirect=/ucenter")}`;

  const onSearch = (e) => {
    const word = e?.target?.value?.trim()
    if (!word.trim()) {
      return
    }
    setTimeout(() => {
      window.open(`/search?keyword=${encodeURIComponent(word)}`)
    }, 100)
  }

  return (
    <>
      <div className={style.ucenterHeaderWrapper}>
        <div className="ucenter-header-wrapper">
          <div className="ucenter-header">
            <span className="ucenter-header-logo">
              <a href={"/"}>我的易购商城</a>
            </span>
            {/* 搜索框 */}
            <div className="ucenter-header-search">
              <Input className="search" placeholder="关键字/产品货号/CAS" prefix={<SearchOutlined />} onPressEnter={(e) => onSearch(e)} />
            </div>
            {/* 快速导航栏 */}
            <ul className="ucenter-header-nav">
              <li className="link-item">
                <Popover
                  overlayClassName="ucenterHeaderNavMorePopover"
                  content={
                    <ul className="site-nav-menu-list">
                      <li className="site-nav-menu-list-item">
                        <a href={"/ucenter/member"}>我的主页</a>
                      </li>
                      <li className="site-nav-menu-list-item">
                        <a href={"/ucenter/favorites"}>我的收藏</a>
                      </li>
                      <li className="site-nav-menu-list-item">
                        <a href={"/ucenter/member/mdf-password"}>修改密码</a>
                      </li>
                      <li className="site-nav-menu-list-item">
                        <a href={logoutUrl}>退出登录</a>
                      </li>
                    </ul>
                  }
                >
                  <div className="user-info">
                    <Avatar src={useOss().generateOssFullFilepath(user?.avatar)} icon={<UserOutlined />} />
                    <span>&nbsp;{user?.nickname}</span>
                  </div>
                </Popover>
              </li>
              {navItem.map((item, key) => {
                return (
                  <li key={key} className={`link-item ${item.isSelected ? "selected" : ""}`}>
                    <span className="link-item-name">
                      <a href={item.href}>{item.name}</a>
                    </span>
                    <i className="link-item-arrow" />
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
    </>
  );
});
