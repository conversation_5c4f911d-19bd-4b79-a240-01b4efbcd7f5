import React, { useContext, useState } from "react";
import { IContext, SProps } from "ssr-types-react";
import style from "./detail.module.less";
import TmBreadcrumb from "@/components/TmBreadcrumb";
import TmSocialShare from "@/components/TmSocialShare";
import { useStoreContext } from "ssr-common-utils";
import SpecialMenu from "./parts/SpecialMenu";
import { Button, Table, TableColumnsType } from "antd";

export default function ProductSpecialDetail(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const detailData = state.topicDetail;
  const products = detailData?.products || [];
  const baseProductTableColumns: TableColumnsType<any> = [
    {
      title: "品牌",
      align: "center",
      dataIndex: "productBrand",
      width: 90,
      render: text => <span>{text}</span>,
    },
    {
      title: "产品SKU",
      align: "center",
      dataIndex: "productSku",
      width: 90,
      render: text => <span>{text}</span>,
    },
    {
      title: "产品名称",
      align: "center",
      dataIndex: "productName",
      width: 90,
      render: text => <span>{text}</span>,
    },
    {
      title: "包装",
      align: "center",
      dataIndex: "productPacking",
      width: 90,
      render: text => <span>{text}</span>,
    },
    {
      title: "查看|购买",
      align: "center",
      width: 90,
      render: (text, record, index) => (
        <a target="_black" href={`/product/${record.productNo}?sku=${record.productSku}`}>
          <Button type="link" danger>
            查看详情
          </Button>
        </a>
      ),
    },
  ];
  /** 未来可能需要单独处理fields JSON解析 */
  const [productTableColumnOptions, setProductTableColumnOptions] = useState<TableColumnsType<any>>(baseProductTableColumns);

  return (
    <div className={style.wrapper}>
      <div className="detail">
        <div className="menu">
          <SpecialMenu />
        </div>
        <div className="content">
          <div className="special-breadcrumb">
            <TmBreadcrumb breadcrumbItem={[{ label: "产品专题", href: `/product-special/${detailData.categoryCode}` }, { label: "专题详情" }]} />
          </div>
          {/* 专题标题 */}
          <div className="special-head-conetent">
            <h3 className="special-title">{detailData.title}</h3>
            <div className="special-tips">
              <span>{detailData.createdDate && "发布日期: " + detailData.createdDate}</span>
              <span>
                分享到:
                <TmSocialShare sites={["wechat", "weibo", "qq", "qzone"]} title={detailData.title || "光华易购商城"} description={detailData.description || detailData.subTitle || detailData.title} />
              </span>
            </div>
          </div>
          {/* 图文描述 */}
          <div className="intro-box">
            <div className="content-x">
              <div dangerouslySetInnerHTML={{ __html: detailData.content || "" }} />
            </div>
          </div>
          {/* 关联产品 */}
          {products?.length && (
            <div className="products-wrapper">
              <Table rowKey="productSkuId" size="small" bordered={true} pagination={false} className="product-table" columns={productTableColumnOptions} dataSource={products} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
