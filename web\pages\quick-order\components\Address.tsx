import React, { useState, useEffect } from "react";
import { Button, Tag, Radio, Divider, Empty, Space } from "antd";
import { PlusOutlined, SettingOutlined, EditOutlined, SwapOutlined } from "@ant-design/icons";
import styles from "./product.module.less";
import MoreAddressModal from "./MoreAddressModal";
import FormDataModal from "@/components/AddressEditModal";
import crudAddress from "@/apis/member/address";

const QuickOrderAddress = ({ onChange, onSendTypeChange }) => {
  /* 地址列表 */
  const [addressList, setAddressList] = useState<any>([]);
  /* 选中的地址 */
  const [selectedAddress, setSelectedAddress] = useState<any>({});
  /* 地址弹窗 */
  const [moreModalVisible, setMoreModalVisible] = useState(false);
  /* 地址弹窗 */
  const [addressModalVisible, setAddressModalVisible] = useState(false);
  /* 弹窗模式 */
  const [addressMode, setAddressMode] = useState<"add" | "edit">("add");

  useEffect(() => {
    getAddressList();
  }, []);

  /* 获取地址列表 */
  const getAddressList = async (keyword?) => {
    const [err, res] = await crudAddress
      .getList({ size: 200, queryName: keyword })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return false;
    }
    const _addressList = res?.data?.content ?? [];
    setAddressList(_addressList);
    const _selectedAddress = selectedAddress?.id ? _addressList.find(item => item.id === selectedAddress.id) : _addressList.find(item => item.isDefault) ?? _addressList[0];
    setSelectedAddress(_selectedAddress);
    onChange(_selectedAddress);
  };

  const handleAddAddress = () => {
    setAddressModalVisible(true);
    setAddressMode("add");
  };

  const handleEditAddress = address => {
    setAddressModalVisible(true);
    setAddressMode("edit");
    setSelectedAddress(address);
  };

  const handleSelectAddress = address => {
    setSelectedAddress(address);
    setMoreModalVisible(false);
    onChange(address);
  };

  return (
    <div className={`${styles.address} side-box`}>
      <div className="side-box__header">
        <h2 className="side-box__header__title">配送</h2>
        <div className="side-box__header__control">
          <Button style={{ fontSize: 12, color: "#e62129" }} type="text" size="small" icon={<PlusOutlined />} onClick={() => handleAddAddress()}>
            新增
          </Button>
          <Divider type="vertical" />
          <Button style={{ fontSize: 12, color: "#e62129" }} type="text" size="small" icon={<SettingOutlined />} href={"/ucenter/member/address"} target="_blank">
            管理
          </Button>
        </div>
      </div>
      <div className="side-box__body">
        {selectedAddress?.id ? (
          <div key={selectedAddress?.id} className="address-item">
            <div className="address-item__header">
              <div className="address-item__name">
                {selectedAddress?.nickname} {selectedAddress?.phone}
              </div>
              <div className="address-item__tags">
                {selectedAddress?.isDefault ? <Tag color="green">默认</Tag> : null}
                {selectedAddress?.aliasName ? <Tag color="gold">{selectedAddress?.aliasName}</Tag> : null}
              </div>
            </div>
            <div className="address-item__address">{`${selectedAddress?.provinceInfo?.name}${selectedAddress?.cityInfo?.name}${selectedAddress?.districtInfo?.name} ${selectedAddress?.address}`}</div>
            <div className="address-item__control">
              <Space split={<Divider type="vertical" />}>
                <Button type="link" size="small" icon={<EditOutlined />} onClick={() => handleEditAddress(selectedAddress)}>
                  编辑地址
                </Button>
                <Button type="link" size="small" icon={<SwapOutlined />} onClick={() => setMoreModalVisible(true)}>
                  切换地址
                </Button>
              </Space>
            </div>
          </div>
        ) : (
          <Empty style={{ padding: 20 }} image={Empty.PRESENTED_IMAGE_SIMPLE} description="请先添加地址">
            <Button size="middle" icon={<PlusOutlined />} onClick={() => handleAddAddress()}>
              新增地址
            </Button>
          </Empty>
        )}

        <Radio.Group
          style={{ width: "100%" }}
          defaultValue={1}
          size="small"
          onChange={e => {
            onSendTypeChange(e?.target?.value);
          }}
        >
          <Radio.Button style={{ width: "50%", textAlign: "center" }} value={1}>
            物流（站点自提）
          </Radio.Button>
          <Radio.Button style={{ width: "50%", textAlign: "center" }} value={2}>
            物流+送货上门
          </Radio.Button>
        </Radio.Group>
      </div>
      <MoreAddressModal
        visible={moreModalVisible}
        onClose={() => setMoreModalVisible(false)}
        addressList={addressList}
        selectedAddress={selectedAddress}
        onSelectAddress={handleSelectAddress}
        getAddressList={getAddressList}
        onSetDefault={handleEditAddress}
      />
      <FormDataModal callbackFunc={getAddressList} modalVisible={addressModalVisible} changeModalVisible={setAddressModalVisible} mode={addressMode} addressId={selectedAddress?.id} />
    </div>
  );
};

export default QuickOrderAddress;
