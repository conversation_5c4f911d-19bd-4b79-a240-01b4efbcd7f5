const XLSX = require('xlsx');
const path = require('path');

// 创建询报价模板Excel文件
function generateQuoteTemplate() {
  // 创建工作簿
  const workbook = XLSX.utils.book_new();
  
  // 创建数据
  const data = [
    ['产品名称', '产品代码/SKU', '询价数量'],
    ['乙醇', 'C2H5OH-500ML', 10],
    ['甲醇', 'CH3OH-1L', 5],
    ['丙酮', 'C3H6O-250ML', 20],
    ['硫酸', 'H2SO4-1L', 2],
    ['盐酸', 'HCL-500ML', 8]
  ];
  
  // 创建工作表
  const worksheet = XLSX.utils.aoa_to_sheet(data);
  
  // 设置列宽
  worksheet['!cols'] = [
    { width: 20 }, // 产品名称
    { width: 20 }, // 产品代码/SKU
    { width: 15 }  // 询价数量
  ];
  
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, '询报价产品清单');
  
  // 保存文件
  const outputPath = path.join(__dirname, '../public/file/询报价产品批量导入模板.xlsx');
  XLSX.writeFile(workbook, outputPath);
  
  console.log('询报价模板文件已生成:', outputPath);
}

// 执行生成
generateQuoteTemplate();
