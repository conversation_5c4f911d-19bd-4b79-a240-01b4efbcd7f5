import React, { useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import style from "./index.module.less";
import { Button } from "antd";

export default function NotFoundPage(props: SProps) {
  const [params, setParams] = useState<any>(null);
  const getUrlParams = () => {
    const params = new URLSearchParams(props.location.search);
    setParams(params);
  };
  useEffect(() => {
    getUrlParams();
  }, []);
  const renderSubTitle = () => {
    const originUrl = params?.get("origin");
    return (
      <>
        <div className="notfound-container-box-title">{params?.get("message") ?? "抱歉，页面走失了。"}</div>
        {originUrl ? (
          <p className="caption-text">
            错误源地址：<a href={originUrl}>{originUrl}</a>
          </p>
        ) : null}
      </>
    );
  };
  return (
    <>
      <div className={style.wrapper}>
        <div className="notfound-container">
          <div className="notfound-container-box">
            <img className="img-fluid" src="/images/404.png" alt="404" />
            {renderSubTitle()}
            <p className="caption-text">您访问的页面不存在或已被删除！ (｡•ˇ‸ˇ•｡).</p>
            <div className="mt-5">
              <Button type="link" href="/" size="large">
                返回首页
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
