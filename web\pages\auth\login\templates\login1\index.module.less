.login {
  padding: 0;
  background-image: url(./images/login-bg.jpg) !important;
  background-size: 100%;
  overflow: hidden;
  min-height: 100vh;

  :global {
    .loginContainer {
      box-shadow: 1.5px 3.99px 27px 0 rgba(0, 0, 0, 0.1);
      transition: 0.3s;
      background-color: rgba(255, 255, 255, 0.72);
      margin: 7% auto auto auto;

      .imageBox {
        margin: auto;
        padding: 60px 40px;
        border-right: 1px solid #cccccc5e;
        text-align: center;
      }

      .loginBox {
        padding: 60px 50px;
        border-left: 1px solid #cccccc5e;
        margin: auto;

        .loginTitle {
          font-size: 30px;
          margin-bottom: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .loginFooter {
      position: absolute;
      right: 15px;
      bottom: 15px;

      p {
        font-size: 0.85rem;
      }
    }
  }
}
