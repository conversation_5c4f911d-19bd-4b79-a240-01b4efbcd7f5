import React, { useEffect } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import CRUD from "@/components/Crud/crud";
import { Button, Form, Input, Space, Spin, Table } from "antd";
import RROperation from "@/components/Crud/RROperation";
import CrudOperation from "@/components/Crud/CrudOperation";
import MyCrudPagination from "@/components/Crud/Pagination";
import { ColumnsType } from "antd/es/table";
import { INotifiesDataType } from "@/typings/notifies.interface";
import style from "./index.module.less";

export default function MessageIndex(props: SProps) {
  const crud = CRUD({ url: "/api/platform/sys-notifies", pageSize: 10 });
  const dataSource = crud.tableData;
  const [searchForm] = Form.useForm();
  const columns: ColumnsType<INotifiesDataType> = [
    {
      title: "序号",
      width: 50,
      align: "center",
      key: "idx",
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "标题",
      width: 150,
      align: "center",
      key: "",
      ellipsis: true,
      render: (text, record, index) => <a href={`/notifies/${record.code}`} className="blue-tip">{record.title}</a>,
    },
    {
      title: "描述",
      width: 240,
      align: "center",
      key: "",
      ellipsis: true,
      render: (text, record, index) => <a href={`/notifies/${record.code}`}>{record.description}</a>,
    },
    {
      title: "发送方",
      width: 100,
      align: "center",
      key: "author",
      ellipsis: true,
      render: (text, record, index) => <span>{record.author}</span>,
    },
    {
      title: "附件数",
      width: 100,
      align: "center",
      key: "isTop",
      ellipsis: true,
      render: (text, record, index) => <span>{ getAttachNum(record) }</span>,
    },
    {
      title: "日期时间",
      width: 160,
      align: "center",
      key: "",
      render: (text, record, index) => <span>{record.createdDate}</span>,
    },
    {
      title: "操作",
      width: 84,
      align: "center",
      key: "",
      render: (text, record, index) => <Button size="small" danger><a href={`/notifies/${record.code}`}>查看</a></Button>,
    }
  ]

  useEffect(() => {
    crud.refresh()
  }, []);

  const getAttachNum = (item) => {
    try {
      return JSON.parse(item.attachFiles)?.length;
    } catch (e) {
      return '-'
    }
  }

  return (
    <div className={style.wrapper}>
      <UCenterCard title={"我的消息"} />
      <Space align="center">
        {crud.getSearchToggle() ? (
          <div className="search-container">
            <Form layout="inline" form={searchForm} size="small">
              <Form.Item className="blurry" name="blurry">
                <Input size="middle" placeholder="请输入查询内容" autoComplete="off" allowClear />
              </Form.Item>
              <RROperation showResetBtn={false} size="middle" crudInstance={crud} />
            </Form>
            <div className="content-container">
              <CrudOperation crudInstance={crud} />
              {/* 内容区域 */}
              <Spin size="default" spinning={crud.loading}>
                <Table rowClassName="item-custom-table" size="small" rowKey={"id"} dataSource={dataSource} columns={columns} pagination={false} />
              </Spin>
              <div className="content-container-pagination">
                <MyCrudPagination crudInstance={crud} />
              </div>
            </div>
          </div>
        ) : null}
      </Space>
    </div>
  );
}
