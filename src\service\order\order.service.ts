import { orderProductsListDto, orderQueryListDto, orderProductsSalesHistoryListDto } from "~/typings/data/order";
import { OrderLogisticsQueryDto } from "@/dto/order-logistics-query.dto";

export interface IOrderService {
  /**
   * 获取订单分页列表
   *
   * @param memberId 会员ID
   * @param criteria 条件
   */
  getOrderPageList: (memberId: string, criteria: Partial<orderQueryListDto>) => Promise<any>;

  /**
   * 订单详情
   *
   * @param memberId /
   * @param orderNo /
   */
  show: (memberId: string, orderNo: string) => Promise<any>;

  /**
   * 历史订单产品列表
   * @param memberId
   * @param criteria
   */
  getOrderProductsLists: (memberId: string, criteria: Partial<orderProductsListDto>) => Promise<any>;

  /**
   * 取消订单 - 建单状态的订单
   * @param memberId
   * @param orderNo
   */
  cancelOrder: (memberId: string, orderNo: string, data: { reason: string }) => Promise<any>;

  /**
   * 订单追踪信息
   * @param memberId
   * @param orderNo
   */
  getOrderTracking: (orderNo: string) => Promise<any>;

  /**
   * <p>查询订单发货物流信息</p>
   *
   * @param orderLogisticsQueryDto /
   */
  queryDelivery: (orderLogisticsQueryDto: OrderLogisticsQueryDto) => Promise<any>;

  /**
   * 历史订单列表
   * @param memberId
   */
  getMemberOrderHistory: (memberId: string) => Promise<any>;

  /**
   * 产品历史售价
   * @param memberId
   */
  getProductSalesHistory: (criteria: Partial<orderProductsSalesHistoryListDto>) => Promise<any>;
}
