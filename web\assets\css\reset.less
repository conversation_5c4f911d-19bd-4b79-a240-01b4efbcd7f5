/* yarn add normalize.css  webpack 可以使用该方式引入：@import "~normalize.css"; */

/* 样式的重置 */
body,
html,
h1,
h2,
h3,
h4,
h5,
h6,
ul,
ol,
li,
dl,
dt,
dd,
header,
menu,
section,
p,
input,
td,
th,
ins {
  padding: 0;
  margin: 0;
}

ul,
ol,
li {
  list-style: none;
}

a {
  text-decoration: none;
  color: #666;
}

a:hover {
  color: #666;
  text-decoration: none;
}

i,
em {
  font-style: normal;
}

input,
textarea,
button,
select,
a {
  outline: none;
  border: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

img {
  border: none;
  vertical-align: middle;
}

#app,
textarea,
select,
input,
button {
  font-size: 12px;
  color: #333;
  font-family: "SourceHanSansCN-Normal", Arial, Helvetica, sans-serif;
}
body {
  font-family: "SourceHanSansCN-Normal";
}
html {
  overflow-y: auto;
}

/* 省略 */
.text-nowrap {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
#tmLoading {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(250, 250, 250, 0.65);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  font-size: 20px;
}
:root:root {
  body.adm-overflow-hidden {
    overflow: unset !important;
  }
}
