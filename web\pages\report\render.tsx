import React, { useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import style from "./index.module.less";
import { Tabs } from "antd";
import ReportCOA from "./parts/ReportCOA";
import ReportMSDS from "./parts/ReportMSDS";

export default function Report(props: SProps) {
  const [activeKey, setActiveKey] = useState("coa");
  const renderItems = [
    {
      label: "COA查询",
      key: "coa",
      children: <ReportCOA />,
    },
    {
      label: "MSDS查询",
      key: "msds",
      children: <ReportMSDS />,
    },
  ];

  /** 切换tab */
  const onChangeTab = e => {
    setActiveKey(e);
  };

  useEffect(() => {
    setActiveKey("coa");
  }, []);

  return (
    <div className={style.wrapper}>
      <div className="report">
        <Tabs activeKey={activeKey} items={renderItems} className="balance-tabs" onChange={onChangeTab} />
      </div>
    </div>
  );
}
