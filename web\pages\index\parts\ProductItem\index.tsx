import React, { memo, useContext } from "react";
import { IContext } from "ssr-types-react";
import { Image } from "antd";
import { useStoreContext } from "ssr-common-utils";
import "./index.less";
import {ghmallGuidePrice2Show} from "@/utils/price-format.util";
import { useLoginModal } from "@/hooks/useLoginModal";
import { IHomePagePlateProductType } from "@/typings/index.interface";
import { useOss } from "@/hooks/useOss";
import commonConstant from "@/constants/common";

interface ProductProps {
  product: Partial<IHomePagePlateProductType>;
  layout: "vertical" | "horizontal";
  width?: string;
}

export default memo(function ProductItem(props: ProductProps) {
  const { product } = props;
  const { state } = useContext<IContext>(useStoreContext());
  const useLoginModalHooks = useLoginModal();
  const useOssHook = useOss();

  const handleToLogin = e => {
    useLoginModalHooks.open();
    e.stopPropagation();
  };

  /** 跳转产品详情页 */
  const productLink = () => {
    // 有 产品编号 和 sku 则跳转到产品详情
    // 没有则进行查询
    if (product.productNo && product.sku) {
      return `/product/${product.productNo}?sku=${product.sku}`;
    }
    return `/search?keyword=${encodeURIComponent(product.productName || product.sku || product.cas || "华大")}`;
  };

  /** 设置产品的展示图地址 */
  const setSkuImageUrl = product => {
    const temp = product.showImage || product.productHeadImage;
    return temp ? useOssHook.generateOssFullFilepath(temp) : commonConstant.COMMON_IMAGE_PATHS.DEFAULT_PRODUCT;
  };

  /** 渲染-价格 */
  const memberPriceOrGuidePrice = () => {
    return (
      <span className="product-price">
        ￥{product.guidePrice !== null && <span>{ghmallGuidePrice2Show((product?.skuGuidePrice || product?.guidePrice) ?? 0)}</span>}
      </span>
    );
  };

  /** 垂直布局-产品 */
  const renderProductH = () => {
    return (
      <>
        <li className="product-h">
          <a href={productLink()} style={{ width: props.width }} target="_blank">
            <div className="product-info">
              <div className="product-name">{product.productName}</div>
              <div className="product-cas">{`CAS: ${product.cas}`}</div>
              <div className="product-attrs">
                <span className="attr product-spec">
                  <span>规格/</span>
                  <span>{product.packing}</span>
                </span>
                {memberPriceOrGuidePrice()}
              </div>
            </div>
            <div className="product-img-box">
              <Image className="product-img" src={setSkuImageUrl(product)} preview={false} fallback={commonConstant.COMMON_IMAGE_PATHS.DEFAULT_PRODUCT} alt="product-img" />
            </div>
          </a>
          {!state.userLoginState ? (
            <div className="member-price-btn" onClick={handleToLogin}>
              登录查看会员价
            </div>
          ) : null}
        </li>
        <useLoginModalHooks.FormModal />
      </>
    );
  };

  /** 水平布局-产品 */
  const renderProductV = () => {
    return (
      <>
        <li className="product-v">
          <a href={productLink()} target="_blank">
            <div className="product-img-box">
              <Image className="product-img" src={setSkuImageUrl(product)} preview={false} fallback={commonConstant.COMMON_IMAGE_PATHS.DEFAULT_PRODUCT} title={`${product.productName} ${product.cas} ${product.sku}`} alt="product-img" />
            </div>
            <div className="product-info-box">
              <span className="product-name" title={`${product.productName} ${product.cas} ${product.sku}`}>{product.productName || ''} {product.cas || ''}</span>
              <div className="product-spec-price">
                { memberPriceOrGuidePrice() }
                <span className="product-packing"> / { product.packing }</span>
              </div>
            </div>
          </a>
        </li>
      </>
    );
  };

  return props.layout === "vertical" ? renderProductV() : renderProductH();
});
