import { Controller, Get, Inject } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { CacheManager } from "@midwayjs/cache";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IMemberService } from "@/service/member/member.service";

@Controller("/ucenter/member", { middleware: [AuthenticationMiddleware] })
export class MemberController extends BaseController {
  @Inject()
  cacheManager: CacheManager;

  @Inject("MemberService")
  memberService: IMemberService;

  @Get()
  async index() {
    const { ctx } = this;
    const res = await this.memberService.getInfoByMemberId(this.getMemberId());
    ctx.member = res.data;
    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/account-safe")
  async accountSafe() {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/mdf-password")
  async modifyPassword() {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/member/test")
  async test() {
    await this.cacheManager.set("cacheTest", "HAHAHA", { ttl: 1000 }); // ttl的单位为秒
    const value: string = await this.cacheManager.get("cacheTest");
    return "member test cache is:" + value;
  }
}
