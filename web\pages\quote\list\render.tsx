import React, { useState, useEffect, useContext } from "react";
import { Card, Table, Button, Tag, Input, Select, DatePicker, Space, message, Popconfirm } from "antd";
import { PlusOutlined, SearchOutlined, EyeOutlined, DeleteOutlined, FileTextOutlined } from "@ant-design/icons";
import { IContext, SProps } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import { useLoading } from "@/hooks/useLoading";
import quoteApi from "@/apis/quote/quote";
import { QuoteStatus } from "../../../../typings/data/quote";
import style from "./index.module.less";

const { RangePicker } = DatePicker;
const { Option } = Select;

export default function QuoteList(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const useLoadingHook = useLoading();
  
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({
    keyword: "",
    status: "",
    dateRange: null,
  });

  // 获取状态标签
  const getStatusTag = (status: QuoteStatus) => {
    const statusConfig = {
      [QuoteStatus.DRAFT]: { color: "default", text: "草稿" },
      [QuoteStatus.PENDING]: { color: "processing", text: "待报价" },
      [QuoteStatus.QUOTED]: { color: "success", text: "已报价" },
      [QuoteStatus.ACCEPTED]: { color: "success", text: "已接受" },
      [QuoteStatus.REJECTED]: { color: "error", text: "已拒绝" },
      [QuoteStatus.EXPIRED]: { color: "default", text: "已过期" },
    };
    
    const config = statusConfig[status] || { color: "default", text: "未知" };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: "询报价单号",
      dataIndex: "quoteNo",
      key: "quoteNo",
      width: 150,
      render: (quoteNo: string, record: any) => (
        <a href={`/quote/detail/${record.id}`} target="_blank">
          {quoteNo || record.id}
        </a>
      ),
    },
    {
      title: "标题",
      dataIndex: "title",
      key: "title",
      ellipsis: true,
    },
    {
      title: "联系人",
      dataIndex: "contactName",
      key: "contactName",
      width: 100,
    },
    {
      title: "联系电话",
      dataIndex: "contactPhone",
      key: "contactPhone",
      width: 120,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: QuoteStatus) => getStatusTag(status),
    },
    {
      title: "产品数量",
      key: "productCount",
      width: 100,
      render: (_, record: any) => (
        <span>{record.products?.length || 0} 个</span>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (date: string) => date ? new Date(date).toLocaleDateString() : "-",
    },
    {
      title: "有效期",
      dataIndex: "expiresAt",
      key: "expiresAt",
      width: 150,
      render: (date: string) => date ? new Date(date).toLocaleDateString() : "-",
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => window.open(`/quote/detail/${record.id}`, "_blank")}
          >
            查看
          </Button>
          {record.status === QuoteStatus.DRAFT && (
            <Popconfirm
              title="确定要删除这个询报价单吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  // 获取询报价列表
  const fetchQuoteList = async (params = {}) => {
    setLoading(true);
    
    const queryParams = {
      page: pagination.current,
      size: pagination.pageSize,
      keyword: searchParams.keyword,
      status: searchParams.status,
      startDate: searchParams.dateRange?.[0]?.format("YYYY-MM-DD"),
      endDate: searchParams.dateRange?.[1]?.format("YYYY-MM-DD"),
      ...params,
    };

    try {
      const [err, res] = await quoteApi
        .getQuoteRequestList(queryParams)
        .then(res => [null, res])
        .catch(err => [err, null]);

      if (err) {
        message.error(err?.data?.message || "获取询报价列表失败！");
        return;
      }

      setDataSource(res.data.content || []);
      setPagination(prev => ({
        ...prev,
        total: res.data.total || 0,
      }));
    } catch (error) {
      message.error("获取询报价列表失败！");
    } finally {
      setLoading(false);
    }
  };

  // 删除询报价单
  const handleDelete = async (id: number) => {
    try {
      useLoadingHook.showLoading("删除中...");
      
      const [err, res] = await quoteApi
        .deleteQuoteRequest(id)
        .then(res => [null, res])
        .catch(err => [err, null]);

      useLoadingHook.hideLoading();

      if (err) {
        message.error(err?.data?.message || "删除失败！");
        return;
      }

      message.success("删除成功！");
      fetchQuoteList();
    } catch (error) {
      useLoadingHook.hideLoading();
      message.error("删除失败！");
    }
  };

  // 搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchQuoteList({ page: 1 });
  };

  // 重置搜索
  const handleReset = () => {
    setSearchParams({
      keyword: "",
      status: "",
      dateRange: null,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(() => {
      fetchQuoteList({ page: 1, keyword: "", status: "", startDate: "", endDate: "" });
    }, 100);
  };

  // 表格分页变化
  const handleTableChange = (paginationConfig: any) => {
    setPagination(paginationConfig);
    fetchQuoteList({
      page: paginationConfig.current,
      size: paginationConfig.pageSize,
    });
  };

  // 初始化加载数据
  useEffect(() => {
    fetchQuoteList();
  }, []);

  return (
    <div className={style.wrapper}>
      <div className="container">
        <Card 
          title="我的询报价"
          extra={
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              href="/quote/request"
            >
              新建询报价
            </Button>
          }
        >
          {/* 搜索区域 */}
          <div className="search-area">
            <Space wrap>
              <Input
                placeholder="搜索标题、联系人、电话"
                value={searchParams.keyword}
                onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
                style={{ width: 200 }}
                allowClear
              />
              
              <Select
                placeholder="选择状态"
                value={searchParams.status}
                onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
                style={{ width: 120 }}
                allowClear
              >
                <Option value={QuoteStatus.DRAFT}>草稿</Option>
                <Option value={QuoteStatus.PENDING}>待报价</Option>
                <Option value={QuoteStatus.QUOTED}>已报价</Option>
                <Option value={QuoteStatus.ACCEPTED}>已接受</Option>
                <Option value={QuoteStatus.REJECTED}>已拒绝</Option>
                <Option value={QuoteStatus.EXPIRED}>已过期</Option>
              </Select>
              
              <RangePicker
                value={searchParams.dateRange}
                onChange={(dates) => setSearchParams(prev => ({ ...prev, dateRange: dates }))}
                placeholder={["开始日期", "结束日期"]}
              />
              
              <Button 
                type="primary" 
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>
              
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </div>

          {/* 表格 */}
          <Table
            columns={columns}
            dataSource={dataSource}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1000 }}
          />
        </Card>
      </div>
    </div>
  );
}
