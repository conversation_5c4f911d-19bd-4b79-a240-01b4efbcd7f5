// 此文件仅用于本地开发配置IDE在检查 web 文件夹下ts语法时的规则，不用于实际构建
{
  "compileOnSave": true,
  "compilerOptions": {
    "target": "ES2018",
    "module": "commonjs",
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "inlineSourceMap":true,
    "noImplicitThis": true,
    // 不处理变量定义未使用
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noUnusedVars": false,
    "stripInternal": true,
    "pretty": true,
    "declaration": false,
    "strict": false,
    "noEmit": true,
    "baseUrl": "./",
    "strictNullChecks": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "typeRoots": [ "./typings"],
    "paths": {
      "@/*": ["./*"],
      "@@/*": ["./*"],
      "~/*": ["../*"],
      "_build/*": ["../build/*"]
    }
  },
  "include": [
    "."
  ]
}

