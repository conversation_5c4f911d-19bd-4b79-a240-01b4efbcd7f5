import { BaseService } from "@/common/base/base.service";
import { Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { ICollectionCreate, ICollectionQuery } from "~/typings/data/product/collection";
import { IProductCollectionService } from "@/service/product/collection.service";

@Provide("ProductCollectionService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class ProductCollectionServiceImpl extends BaseService implements IProductCollectionService {
  /** 收藏列表查询 */
  async getPageList(memberId: string, criteria: ICollectionQuery): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/product/collections/${memberId}/`, criteria));
  }

  /** 添加收藏 */
  async create(memberId: string, resource: ICollectionCreate): Promise<any> {
    return this.easyResponse(await this.easyHttp.post(`/api/product/collections/${memberId}/`, resource));
  }

  /** 根据收藏id删除 */
  async deleteByCollectId(memberId: string, collectId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete(`/api/product/collections/${memberId}/${collectId}`));
  }

  /** 根据skuId删除 */
  async deleteBySkuId(memberId: string, skuId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete(`/api/product/collections/${memberId}/sku/${skuId}`));
  }

  /** 根据skuId判断是否收藏产品 */
  async isCollection(memberId: string, skuId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/product/collections/${memberId}/sku/${skuId}`));
  }

  /** 删除全部 */
  async deleteAll(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete(`/api/product/collections/${memberId}/overall`));
  }
}
