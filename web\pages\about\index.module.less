@aboutBackgroundColor: #ffffff;
@aboutPageText: 14px;
@aboutPageSubTitle: 16px;
@aboutPageTitle: 18px;
@aboutTextColor: #666666;
@aboutTitleColor: #333333;

.wrapper {
  :global {
    .flex-row();
    .about-container {
      padding: 32px;
      background-color: @main-bg-color-white;

      .about-article-content {
        font-size: @aboutPageText;
      }

      .about-article-flex {
        width: 100%;
        .flex-row();
        gap: 16px;
        div {
          flex: 1;
        }
        .about-article-content {
          text-indent: 27px;
          div {
            margin-top: 8px;
            &:first-child {
              margin-top: 0;
            }
          }
        }
        .img-box {
          img {
            width: 360px;
            height: 240px;
          }
        }
      }
    }
  }
}
