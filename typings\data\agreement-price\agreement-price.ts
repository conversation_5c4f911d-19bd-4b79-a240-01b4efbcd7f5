/**
 * 产品SKU信息
 */
export interface ProductSkuDto {
  /** 主键ID */
  id: number;
  /** 产品sku */
  sku: string;
  /** 产品名称 */
  skuName: string;
  /** 产品指导价,主要价格,单位:元 */
  guidePrice: number;
  /** 是否配置区间折扣价（1:已配置；0:未配置） */
  isOrderQuantityConfiguration: number;
  /** 属性型号 */
  attributeModel?: string;
  /** 关联ERP系统的物料code */
  erpMaterialCode?: string;
  /** 现有库存量 */
  existingStock?: number;
  /** 图片,多个以","分隔 */
  image?: string;
  /** 包装规格,如:25kg */
  packing?: string;
  /** 包装系数,一般为数字 */
  packingRatio?: string;
  /** 关联产品ID */
  productId?: number;
  /** 实际可用库存量 */
  realStock?: number;
  /** 备注 */
  remark?: string;
  /** 排序顺序 */
  sortOrder?: number;
  /** 规格,如:Tech25kg96% */
  spec?: string;
  /** 状态:0-下架;1-上架 */
  state?: number;
  /** 可用库存量 */
  stock?: number;
  /** 库存交货期备注 */
  stockRemark?: string;
  /** 产品计量单位,如:KG,G,L */
  unit?: string;
  /** 产品重量 */
  weight?: string;
}

/**
 * 协议价格查询结果
 */
export interface AgreementPriceResult {
  /** 品牌ID */
  brandId: string;
  /** 创建日期 */
  createdDate?: string;
  /** 创建者 */
  creator?: string;
  /** 是否启用 */
  isEnable?: number;
  /** 会员ID */
  memberId: string;
  /** 修改日期 */
  modifiedDate?: string;
  /** 修改者 */
  modifier?: string;
  /** 下单数量 */
  orderQuantity: number;
  /** 是否配置了区间折扣 */
  orderQuantityConfiguration: boolean;
  /** 产品SKU信息 */
  productSkuDto: ProductSkuDto;
  /** 产品ID */
  productSkuId: number;
}
