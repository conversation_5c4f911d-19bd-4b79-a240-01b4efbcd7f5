.wrapper {
  :global {
    .auth-page {
      .flex-col();
      width: 100vw;
      min-height: 100vh;
      background-image: linear-gradient(to bottom, #eb5447 0%, #ff8e3b 100%);
      background-image: -webkit-linear-gradient(to bottom, #eb5447 0%, #ff8e3b 100%);
      background-image: -moz-linear-gradient(to bottom, #eb5447 0%, #ff8e3b 100%);

      .auth-shading {
        display: flex;
        background-image: url("@@img/m/registerw.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 100%;
        height: 70px;
        margin-top: 30px;
        .mall-logo {
          .flex-center();
          width: 90px;
          height: 90px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.85);
          margin: 0 auto;
          img {
            width: 85px;
            height: 85px;
            border-radius: 50%;
          }
        }
      }
      .auth-container {
        margin: 45px auto 20px auto;
        width: 88%;
        //表单内容
        .auth-content {
          .flex-col();
          padding: 30px 10px 30px 10px;
          background-color: #fff;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
          opacity: 0.9;
          .navbar-box {
            display: flex;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            &-item {
              color: #999999;
              border-bottom: 2px solid #fff;
              padding-bottom: 2px;
              & ~ {
                margin-left: 16px;
              }
            }

            &-on {
              color: #282828;
              border-bottom-color: #f35749;
            }
          }

          .auth-form-box {
            .adm-form-footer {
              padding: 10px;
            }
            .item {
              .flex-row();
              font-size: 14px;
              padding: 2px 1px 2px 8px;
              label {
                width: 70px;
              }
              input {
                display: flex;
                flex: 1;
                font-size: 14px;
              }
              .salesman {
                .adm-input-element {
                  font-weight: 600;
                }
              }
            }

            .auth-submit-btn {
              width: 100%;
              font-size: 16px;
              color: #fff;
              font-weight: bold;
              border-radius: 10px;
              padding: 10px;
              background: linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
              background: -webkit-linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
              background: -moz-linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
              text-align: center;
            }
          }

          .auth-tip {
            color: #cccccc;
            font-size: 14px;
            text-align: center;
            margin-top: 10px;
            &-login {
              color: #fc4141 !important;
            }
          }
          .agree-wrap {
            margin-top: 16px;
            text-align: center;
            font-size: 12px;
            color: #999999;
            line-height: 12px;

            .agree {
              color: #fa9850;
            }
          }
        }
        .auth-bottom {
          background-image: url("@@img/m/registerB.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          height: 19px;
          margin: 0 auto;
        }
      }
    }

    // 分割线
    .line-wrap {
      position: relative;
      width: 100%;
      height: 1px;
      background: #e5e5e5;
      .line {
        z-index: 1;
        position: absolute;
        left: 0;
        bottom: -1px;
        background: #e5e5e5;
        width: 100%;
        height: 2px;
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
      }
      .line-enter {
        width: 0;
        background: #e5e5e5;
      }
      .line-enter-active {
        width: 100%;
        background: red;
        transition: all 1s linear;
      }
      .line-enter-done {
        background: red;
        width: 100%;
      }
      .line-exit {
        background: red;
        width: 100%;
      }
      .line-exit-active {
        background: #e5e5e5;
        width: 0;
        transition: all 1s linear;
      }
      .line-exit-done {
        background: #e5e5e5;
        width: 100%;
      }
    }
    // label动画
    .fade-enter {
      opacity: 0;
      transform: translateY(100%);
    }
    .fade-enter-active {
      opacity: 1;
      transform: translateY(0%);
      transition: all 1s;
    }
    .fade-enter-done {
      opacity: 1;
    }
    .fade-exit {
      opacity: 1;
      transform: translateY(0%);
    }
    .fade-exit-active {
      opacity: 0;
      transform: translateY(100%);
      transition: all 0.4s;
    }
    .fade-exit-done {
      opacity: 0;
    }
    // input动画
    .input-enter {
      transform: translateY(0%);
    }
    .input-enter-active {
      transform: translateY(0%);
      transition: all 1s;
    }
    .input-enter-done {
    }
    .input-exit {
      transform: translateY(0%);
    }
    .input-exit-active {
      transform: translateY(-14px);
      transition: all 1s;
    }
    .input-exit-done {
    }
  }
}

:global {
  .mobile-register-protocol-content {
    .privacy-agreement {
      padding: 0 5px;
      h2 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
      }
    }
  }
}
