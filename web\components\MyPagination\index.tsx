import { Pagination, PaginationProps, Row } from "antd";
import React from "react";
import "@/assets/css/myPagination.less";

const pageSizeOptions = ["10", "20", "30", "50"];

// 上一页、下一页
const itemRender: PaginationProps["itemRender"] = (_, type, originalElement) => {
  if (type === "prev") {
    return <a className="pagination-btn-prev">上一页</a>;
  }
  if (type === "next") {
    return <a className="pagination-btn-next">下一页</a>;
  }
  return originalElement;
};

export interface ssrRequestProps {
  ssrRequestInstance: any;
  isAsync?: boolean;
}

// 分页
export default function MyPagination(props: ssrRequestProps) {
  const ssrRequestInstance = props.ssrRequestInstance;
  const paginationOptions = ssrRequestInstance.getPaginationOptions();
  const pageChangeHandler = async (page: number, pageSize: number) => {
    if (props.isAsync) {
      await ssrRequestInstance.pageChangeAsyncHandler(page, pageSize);
    } else {
      await ssrRequestInstance.pageChangeHandler(page, pageSize);
    }
  };
  return (
    <Row justify="end" className="my-pagination-wrapper">
      <Pagination
        pageSize={Number(paginationOptions.pageSize)}
        showSizeChanger={paginationOptions.showSizeChanger}
        showQuickJumper={paginationOptions.showQuickJumper}
        onChange={pageChangeHandler}
        current={paginationOptions.current}
        total={paginationOptions.total}
        pageSizeOptions={pageSizeOptions}
        showTotal={paginationOptions.showTotal}
        itemRender={itemRender}
      />
    </Row>
  );
}
