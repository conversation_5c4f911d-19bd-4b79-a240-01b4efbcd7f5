import React, { useContext, memo } from "react";
import { <PERSON><PERSON>, Drawer, Empty, Popconfirm } from "antd";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import { CloseOutlined } from "@ant-design/icons";
import style from "./index.module.less";
import * as shoppingCartCrud from "@/apis/trade/shopping-cart";
import { useShoppingCart } from "@/hooks/useShoppingCart";

export default memo(function TmQuickDrawer() {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const shoppingCartHook = useShoppingCart();
  const onClose = () => {
    shoppingCartHook.toggle();
  };
  // 移除购物车产品
  const handleDelShoppingCartEvent = async (product: any) => {
    const [err, res] = await shoppingCartCrud
      .del({ ids: product.id })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err) {
      console.log("result", res.data);
      await shoppingCartHook.init();
    }
  };
  /* 测试数据 */
  const testShoppingCartData = state.shoppingCartData;
  return (
    <>
      <Drawer
        title="我的购物车"
        placement="right"
        width={300}
        onClose={onClose}
        open={state.shoppingCartVisible}
        closable={false}
        zIndex={9999}
        bodyStyle={{ padding: "10px 15px" }}
        extra={<CloseOutlined onClick={onClose} />}
      >
        <div className={style.wrapper}>
          <div className="shopping-cart-box">
            {testShoppingCartData.length ? (
              <>
                <ul className="product-list">
                  {testShoppingCartData.map((record, idx) => {
                    return (
                      <li className="product-list-item" key={idx}>
                        <div className="title-box">
                          <div className="main-title">
                            <span>{record.product?.skuName ? record.product?.skuName : record.product.productNameZh} | </span>
                            {`${record.product.sku} | ${record.product.packing}`}
                          </div>
                          <div className="price">
                            ￥{record.product.discountPrice === 0 ? "询价" : record.product.discountPrice}
                            <span>&nbsp;&nbsp;x{record.quantity}</span>
                          </div>
                        </div>
                        <Popconfirm
                          zIndex={99999}
                          placement="bottomRight"
                          title="您确定删除此条购物车产品吗？"
                          onConfirm={async () => await handleDelShoppingCartEvent(record)}
                          okText="确定"
                          cancelText="取消"
                          style={{ width: "150px" }}
                        >
                          <Button className="del" type="link">
                            删除
                          </Button>
                        </Popconfirm>
                      </li>
                    );
                  })}
                </ul>
                <Button
                  type="primary"
                  size="large"
                  block
                  onClick={() => {
                    window.location.href = "/ucenter/trade/shopping-cart";
                  }}
                >
                  去购物车结算
                </Button>
              </>
            ) : (
              <Empty description={<span className="empty-tip">购物车还是空的</span>}>
                <Button
                  type="primary"
                  size="large"
                  block
                  onClick={() => {
                    window.location.href = "/";
                  }}
                >
                  再去逛逛
                </Button>
                {!state.userLoginState && <span>登录后，才显示购物车产品列表!!!</span>}
              </Empty>
            )}
          </div>
        </div>
      </Drawer>
    </>
  );
});
