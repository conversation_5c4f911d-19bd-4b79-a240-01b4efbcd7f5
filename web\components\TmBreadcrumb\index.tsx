import React, { memo } from "react";
import { Breadcrumb, MenuProps } from "antd";
import style from "./index.module.less";

interface IBreadcrumbItem {
  label: string;
  href?: string;
  className?: string;
  menu?: MenuProps;
  onClick?: React.MouseEventHandler<HTMLAnchorElement | HTMLSpanElement>;
}
interface IProps {
  breadcrumbItem: IBreadcrumbItem[];
  separator?: string | React.ReactNode;
}
export default memo(function TmBreadcrumb(prop: IProps) {
  return (
    <>
      <div className={style.wrapper}>
        <span className="location">当前位置：</span>
        <Breadcrumb className="breadcrumb" separator={prop.separator ?? "/"}>
          <Breadcrumb.Item href={"/"} key={"index"}>
            首页
          </Breadcrumb.Item>
          {prop.breadcrumbItem.map((item, index) => {
            return (
              <React.Fragment key={index}>
                <Breadcrumb.Item key={index} menu={item.menu} onClick={item.onClick} href={item.href}>
                  {item.label}
                </Breadcrumb.Item>
              </React.Fragment>
            );
          })}
        </Breadcrumb>
      </div>
    </>
  );
});
