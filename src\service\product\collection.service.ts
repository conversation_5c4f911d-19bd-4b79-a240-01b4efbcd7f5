import { ICollectionCreate, ICollectionQuery } from "~/typings/data/product/collection";

export interface IProductCollectionService {
  /** 收藏列表查询 */
  getPageList: (memberId: string, criteria: ICollectionQuery) => Promise<any>;

  /** 添加收藏 */
  create: (memberId: string, resource: ICollectionCreate) => Promise<any>;

  /** 根据收藏id删除 */
  deleteByCollectId: (memberId: string, collectId: number) => Promise<any>;

  /** 根据skuId删除 */
  deleteBySkuId: (memberId: string, skuId: number) => Promise<any>;

  /** 根据skuId判断是否收藏产品 */
  isCollection: (memberId: string, skuId: number) => Promise<any>;

  /** 删除全部 */
  deleteAll: (memberId: string) => Promise<any>;
}
