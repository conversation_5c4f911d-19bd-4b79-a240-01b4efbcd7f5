import React, { memo, useContext } from "react";
import style from "./index.module.less";
import { Popover } from "antd";
import { IContext } from "ssr-types-react";
import { STORE_CONTEXT } from "_build/create-context";
import commonConstant from "@/constants/common";
import GhWechatQrcode from "@/components/GhWechatQrcode";

interface NavType {
  path?: string;
  title?: string | React.ReactNode;
  value?: string;
}

interface LinkDataType {
  title: string;
  navigations: NavType[];
}

export default memo(function Footer() {
  const { state } = useContext<IContext>(STORE_CONTEXT);

  const footerNavData: LinkDataType[] = [
    {
      title: "帮助中心",
      navigations: [
        {
          path: "/help#buy-way",
          title: "如何订购",
        },
        {
          path: "/help#online-shop",
          title: "订购说明",
        },
        {
          path: "/help#order-invoice",
          title: "发票制度",
        },
        {
          path: "/help#return-goods",
          title: "退货制度",
        },
        {
          path: "/about/contactus",
          title: "联系我们",
        },
        {
          path: "/ucenter/member/qualifications",
          title: "资质上传",
        },
      ],
    },
    {
      title: "危险品采购",
      navigations: [
        {
          path: "/help#danger-chemical",
          title: "易制毒产品",
        },
        {
          path: "/help#danger-chemical",
          title: "易制爆产品",
        },
        {
          path: "/help#danger-chemical",
          title: "办证说明",
        },
      ],
    },
    {
      title: "关于我们",
      navigations: [
        {
          path: "/",
          title: "股票代码002741",
        },
        {
          path: "/",
          title: "超14万试剂耗材",
        },
        {
          path: "/",
          title: "快捷发货",
        },
        {
          path: "/",
          title: "送货上门",
        },
      ],
    },
    {
      title: "快捷通道",
      navigations: [
        {
          path: "/",
          title: "返回首页",
        },
        {
          path: "/auth/register",
          title: "注册会员",
        },
        {
          path: "/auth/login",
          title: "会员登录",
        },
        {
          path: "https://www.ghtech.com",
          title: <span>光华科技</span>,
        },
        // {
        //   path: state?.layoutInitData?.ccFrontendDomain ?? "http://www2.guanghuayigou.com",
        //   title: <span>旧版商城</span>,
        // },
      ],
    },
  ];
  // /** 光华-微信二维码 */
  // const renderGHWechatQrcode = () => {
  //   return (
  //     <div className="gh-wechat">
  //       <img src={COMMON_IMAGE_PATHS.CUSTOMER_WECHAT_QRCODE} alt="wechat" />
  //       <span>扫一扫关注官方微信</span>
  //     </div>
  //   );
  // };

  return (
    <div className={style.mainFooter}>
      <div className="footer-wrapper">
        <div className="footer-nav">
          {footerNavData.map((navData, index) => (
            <div key={index} className="footer-nav-menu">
              <span className="nav-title">{navData.title}</span>
              <ul className="nav-content">
                {navData.navigations.map((item, idx) => (
                  <li key={idx} className="nav-link">
                    {item.path ? <a href={item.path}>{item.title}</a> : <span>{item.title}</span>}
                  </li>
                ))}
              </ul>
            </div>
          ))}
          {/* 联系我们 */}
          <div className="contract-us">
            <span className="contract-title">联系我们</span>
            <ul className="contract-ul">
              <li>
                <span className="title">
                  <i className="icon icon-phone" />
                  电话：
                </span>
                <span className="content phone">020-84382888转8836</span>
              </li>
              <li>
                <span className="title">
                  <i className="icon icon-email" />
                  邮箱：
                </span>
                <span className="content">{ commonConstant.CONTACT_US_EMAIL }</span>
              </li>
              <li>
                <span className="title">
                  <i className="icon icon-fax" />
                  传真：
                </span>
                <span className="content">020-84382888</span>
              </li>
              <li>
                <span className="title">
                  <Popover overlayClassName="gh-wechat-popover" placement="bottom" content={GhWechatQrcode} trigger="hover">
                    <i className="icon icon-chat" />
                  </Popover>
                  微信
                </span>
              </li>
            </ul>
          </div>
        </div>
        {/* 版权 */}
        <div className="footer-copy-right">
          <i className="app-logo" />
          <div className="copy-right">
            <span>©&nbsp;2018&nbsp;JHD广州市金华大化学试剂有限公司&nbsp;版权所有，保留一切权利。</span>
            <span>全国工业生产许可证号:(粤)XK13-011-00005,危险化学品生产许可证号:440512008,危险化学品经营许可证号:GD&nbsp;35320</span>
            <span>TEL:020-84382888-320&nbsp;FAX:020-84315215</span>
            <span>
              互联网经营许可许号:&nbsp;<a href="//www.beian.gov.cn/portal/registerSystemInfo?recordcode=10005674">粤ICP备10005674号</a>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
});
