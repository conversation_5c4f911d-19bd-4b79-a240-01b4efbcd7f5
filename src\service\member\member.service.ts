import { ReBindEmailVo, ReBindPhoneVo, updateMemberVo, updatePasswordVo } from "~/typings/data/member/member";
import { IUploadParams } from "~/typings/data/file-remote";

export interface IMemberService {
  /**
   * 更新密码
   *
   * @param memberId 会员ID
   * @param updateVo 更新vo
   */
  updatePassword: (memberId: string, updateVo: updatePasswordVo) => Promise<any>;

  /**
   * 获取会员信息
   *
   * @param memberId 会员ID
   */
  getInfoByMemberId: (memberId: string) => Promise<any>;

  /**
   * 更新会员信息
   *
   * @param memberId /
   * @param updateVo /
   */
  updateMember: (memberId: string, updateVo: Partial<updateMemberVo>) => Promise<any>;

  /**
   * 更新会员头像
   *
   * @param memberId /
   * @param uploadData /
   */
  updateMemberAvatar: (memberId: string, uploadData: IUploadParams) => Promise<any>;

  /**
   * 客户专服顾问
   * @param memberId /
   */
  getMemberConsultant: (memberId: string) => Promise<any>;

  /**
   * 绑定或换绑邮箱
   *
   * @param memberId /
   * @param bindVo /
   */
  rebindEmail: (memberId: string, bindVo: ReBindEmailVo) => Promise<any>;

  /**
   * 绑定或换绑手机
   *
   * @param memberId /
   * @param bindVo /
   */
  rebindPhone: (memberId: string, bindVo: ReBindPhoneVo) => Promise<any>;
}
