import { But<PERSON>, Checkbox, Col, Form, Input, Row } from "antd";
import { LockFilled, UserOutlined } from "@ant-design/icons";
import React from "react";
import LoginModule from "./index.module.less";
import LoginBanner from "./images/login-banner.png";
import { DebouncedFunc } from "lodash";
interface Props {
  rules: any;
  defaultForm: object;
  onFinish: DebouncedFunc<(values: any) => Promise<void>>;
  onFinishFailed: (errorInfo: any) => void;
}

function LoginTpl(props: Props) {
  const { rules, defaultForm, onFinish, onFinishFailed } = props;
  const [form] = Form.useForm();
  return (
    <div className={LoginModule.login}>
      <Row className="container">
        <Col span={14} className="loginContainer">
          <Row>
            <Col className="imageBox">
              <img src={LoginBanner} alt="" />
            </Col>
            <Col flex={6}>
              <div className="loginBox">
                <div className="loginTitle">
                  <img
                    src={"/logo.svg"}
                    style={{
                      width: "60px",
                      height: "60px",
                      marginRight: "10px",
                      border: "1px solid #d5d5d5",
                      borderRadius: "6px",
                    }}
                    alt=""
                  />
                  光华易购商城
                </div>
                <Form form={form} initialValues={defaultForm} autoComplete="on" onFinish={onFinish} onFinishFailed={onFinishFailed}>
                  <Form.Item name="phone" rules={rules.phoneRules}>
                    <Input size="large" prefix={<UserOutlined style={{ color: "rgba(0,0,0,.25)" }} />} placeholder="手机号" />
                  </Form.Item>
                  <Form.Item name="password" rules={rules.passwordRules}>
                    <Input.Password size="large" prefix={<LockFilled style={{ color: "rgba(0,0,0,.25)" }} />} placeholder="密码" autoComplete="new-password" />
                  </Form.Item>
                  <Form.Item name="remember" valuePropName="checked">
                    <Checkbox>记住密码</Checkbox>
                  </Form.Item>
                  <Form.Item>
                    <Button size="large" block type="primary" htmlType="submit" className="login-form-button">
                      登录
                    </Button>
                  </Form.Item>
                </Form>
                <div className="no-margin">
                  <p>
                    没有账户 ? <a href="">立即注册</a>
                  </p>
                </div>
              </div>
            </Col>
          </Row>
        </Col>
      </Row>
      <div className="loginFooter">
        <p>
          版权所有{" "}
          <a target="_blank" href="/">
            GH-MALL
          </a>
        </p>
      </div>
    </div>
  );
}
export default LoginTpl;
