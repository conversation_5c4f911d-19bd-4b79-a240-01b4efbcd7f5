import request from "@/utils/request.util";
import { IReceivingAddressType } from "@/typings/member.interface";
import { $tools } from "@/utils/tools.util";

export function getList(params?: object) {
  return request({
    url: "/api/ucenter/addresses",
    method: "get",
    params,
  });
}
export function show(addressId: number | undefined) {
  return request({
    url: `/api/ucenter/addresses/${addressId}/detail`,
    method: "get",
  });
}

/** 更新地址信息 */
export function add(addressInfo: Partial<IReceivingAddressType>) {
  $tools.removeFormFields(addressInfo, ["area"]);

  return request({
    url: `/api/ucenter/addresses`,
    method: "POST",
    data: addressInfo,
  });
}

/** 更新地址信息 */
export function edit(addressId: number, addressInfo: IReceivingAddressType) {
  $tools.removeFormFields(addressInfo, ["area"]);

  return request({
    url: `/api/ucenter/addresses/${addressId}`,
    method: "PUT",
    data: addressInfo,
  });
}

/** 设置默认收货地 */
export function setDefaultAddress(addressId: number) {
  return request({
    url: `/api/ucenter/addresses/${addressId}/default`,
    method: "PUT",
  });
}

/** 主动删除某个收货地 */
export function remove(addressId: number) {
  return request({
    url: `/api/ucenter/addresses/${addressId}`,
    method: "DELETE",
  });
}

export default {
  getList,
  remove,
  setDefaultAddress,
  edit,
  add,
  show,
};
