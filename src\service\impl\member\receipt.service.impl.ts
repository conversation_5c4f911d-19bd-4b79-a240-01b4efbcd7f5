import { Provide, Scope, <PERSON>ope<PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { receiptCreateDto, receiptUpdateDto } from "~/typings/data/member/receipt";
import { IReceiptService } from "@/service/member/receipt.service";

@Provide("ReceiptService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class ReceiptServiceImpl extends BaseService implements IReceiptService {
  /**
   * 获取发票列表
   *
   * @param memberId 会员ID
   * @param criteria 条件
   */
  async getPageList(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/${memberId}/receipt`), "获取数据出错了，请稍后再试！");
  }

  /** 新增发票信息 */
  async create(memberId: string, resource: receiptCreateDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post(`/api/members/${memberId}/receipt`, resource), "获取数据出错了，请稍后再试！");
  }

  /** 删除 */
  async delete(memberId: string, receiptId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete(`/api/members/${memberId}/receipt/${receiptId}`), "获取数据出错了，请稍后再试！");
  }

  /** 设置默认 */
  async setDefault(memberId: string, receiptId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.put(`/api/members/${memberId}/receipt/${receiptId}/default`), "获取数据出错了，请稍后再试！");
  }

  /** 发票详情 */
  async show(memberId: string, receiptId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/${memberId}/receipt/${receiptId}`), "获取数据出错了，请稍后再试！");
  }

  /** 更新发票信息 */
  async update(memberId: string, resource: receiptUpdateDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.put(`/api/members/${memberId}/receipt/${resource.receiptId}`, resource), "获取数据出错了，请稍后再试！");
  }
}
