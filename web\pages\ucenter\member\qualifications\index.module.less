@red-tip: #e02020;
@blue-tip: #0091ff;
@contentPadding: 16px;

.wrapper {
  :global {
    .qualification {
      width: 100%;
      .red-tip {
        color: @red-tip;
      }
      .blue-tip {
        color: @blue-tip;
      }

      &-wrapper.ant-descriptions-item-content {
        span {
          .flex-row();
          gap: @contentPadding;
        }
        .qualification-item {
          .flex-col();
          _:-ms-fullscreen,
          & {
            margin-right: @contentPadding;
            &:last-child {
              margin-right: @contentPadding;
            }
          }
          .qualification-img {
            width: 100%;
            max-width: 120px;
          }
          .qualification-tip {
            width: 120px;
            text-align: center;
            color: @regular-text-color;
            font-size: 12px;
            margin-top: 2px;
          }
        }
      }

      .detail-container {
        padding-bottom: @contentPadding;
      }

      // 空
      .ant-empty-description {
        .flex-center(row);
        .empty-wrapper {
          width: 480px;
          .flex-center(column);
          .empty-wrapper-tips {
            .flex-col(flex-start, center);
          }
          .empty-btn {
            margin: 8px 0;
          }
        }
      }
    }
  }
}
