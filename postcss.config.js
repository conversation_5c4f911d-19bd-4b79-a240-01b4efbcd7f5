module.exports = {
  plugins: {
    autoprefixer: {}, // 用来给不一样的浏览器自动添加相应前缀，如-webkit-，-moz-等等
    'postcss-mobile-forever': {
      unitToConvert: 'px', // 需要转换的单位，默认为"px"
      viewportWidth: 375, // 设计稿的视口宽度
      /**
       * (Array or Regexp) 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
       * 如果值是一个正则表达式，那么匹配这个正则的文件会被忽略
       * 如果传入的值是一个数组，那么数组里的值必须为正则
       */
      //exclude: [/[\\/]node_modules[\\/]/], // 忽略某些文件夹下的文件或特定文件
      // exclude: undefined, // 忽略某些文件夹下的文件或特定文件
      /**
       * (Array or Regexp) 如果设置了include，那将只有匹配到的文件才会被转换，例如只转换 'src/mobile' 下的文件 (include: /\/src\/mobile\//)
       * 如果值是一个正则表达式，将包含匹配的文件，否则将排除该文件             * 如果传入的值是一个数组，那么数组里的值必须为正则
       */
      // include: [/[\\/]web[\\/]pages[\\/]auth[\\/]spread-register[\\/]m[\\/]/],
      include: [
        /[\\/]web[\\/]pages[\\/]mobile[\\/]/,
        /[\\/]web[\\/]components[\\/]mobile[\\/]/,
        /[\\/]web[\\/]components[\\/]layout[\\/]MobileAuthLayout[\\/]/,
        /[\\/]node_modules[\\/]antd-mobile[\\/]/,
        /[\\/]node_modules[\\/]antd-mobile-icons[\\/]/
      ],
      unitPrecision: 5, // 单位转换后保留的精度
      propList: ['*'], // 能转化为vw的属性列表
      viewportUnit: 'vw', // 希望使用的视口单位
      fontViewportUnit: 'vw', // 字体使用的视口单位
      selectorBlackList: [], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
      minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
      mediaQuery: false, // 媒体查询里的单位是否需要转换单位
      replace: true, //  是否直接更换属性值，而不添加备用属性
      landscape: false, //待解决~~~此处有问题‘postcss.atRule is not a constructor’是否处理横屏情况 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
      landscapeUnit: 'vw', // 横屏时使用的单位
      landscapeWidth: 568 // 横屏时使用的视口宽度
    }
  }
}
