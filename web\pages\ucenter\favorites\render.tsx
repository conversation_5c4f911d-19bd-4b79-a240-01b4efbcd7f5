import React, { useEffect } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import style from "./index.module.less";
import { Button, Empty, Modal, Spin, Table } from "antd";
import { useProductCollection } from "@/hooks/useProductCollection";
import CRUD from "@/components/Crud/crud";
import { IProductSkuType } from "@/typings/product.interface";
import { ColumnsType } from "antd/es/table";
import MyCrudPagination from "@/components/Crud/Pagination";
import { price2Thousand } from "@/utils/price-format.util";
import { RedoOutlined, ClearOutlined } from "@ant-design/icons";
import commonConstant from "@/constants/common";

export default function FavoritesIndex(props: SProps) {
  const crud = CRUD({ url: "/api/product/collect", pageSize: 10 });
  const favorites: IProductSkuType[] = crud.tableData;
  const productCollectionHook = useProductCollection();
  const columns: ColumnsType<IProductSkuType> = [
    {
      title: "序号",
      width: 50,
      align: "center",
      key: "idx",
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "产品名称",
      width: 120,
      align: "center",
      key: "productName",
      render: (text, record, index) => <span>{record.productName || record.productNameEn}</span>,
    },
    {
      title: "品牌",
      width: 120,
      align: "center",
      key: "brandName",
      render: (text, record, index) => (
        <a href={`/search?brandId=${record.brandId}`} className="brand">
          {record.brandName}
        </a>
      ),
    },
    {
      title: "产品SKU",
      width: 120,
      align: "center",
      key: "sku",
      render: (text, record, index) => (
        <a href={`/product/${record.originProductNo}?sku=${record.sku}`} className="sku">
          {record.sku}
        </a>
      ),
    },
    {
      title: "包装规格",
      width: 120,
      align: "center",
      key: "spec",
      render: (text, record, index) => <span>{record.spec ? record.spec : `${record.packing}${record.unit ? "|" + record.unit : ""}`}</span>,
    },
    {
      title: "CAS",
      width: 120,
      align: "center",
      key: "cas",
      dataIndex: "cas",
    },
    {
      title: "官方指导价",
      width: 120,
      align: "center",
      key: "guidePrice",
      render: (text, record, index) => <span className="price">￥{record.guidePrice === 0 ? "询价" : price2Thousand(record.guidePrice)}</span>,
    },
    {
      title: "操作",
      align: "center",
      render: (text, record) => (
        <div className="action">
          <Button type="primary" size="small" onClick={() => handleToProductDetail(record)}>
            立即购买
          </Button>
          <Button type="default" size="small" onClick={() => handleCancelCollection(record.skuId)}>
            取消收藏
          </Button>
        </div>
      ),
    },
  ];

  useEffect(() => {
    crud.refresh();
  }, []);

  /**
   * 取消收藏
   * @param skuId
   */
  const handleCancelCollection = (skuId: number) => {
    Modal.confirm({
      content: "确定取消该条收藏吗？",
      title: "提醒",
      type: "warning",
      centered: true,
      closable: true,
      onOk: async () => {
        const success = await productCollectionHook.deleteBySkuId(skuId);
        success && crud.refresh();
      },
    });
  };

  /** 跳转产品详情 */
  const handleToProductDetail = item => {
    location.href = `/product/${item.originProductNo}?sku=${item.sku}`;
  };

  const handleCancelAll = () => {
    Modal.confirm({
      content: "您要清空所有收藏记录吗？",
      title: "警告",
      type: "warning",
      centered: true,
      closable: true,
      onOk: async () => {
        const success = await productCollectionHook.deleteAll();
        success && crud.refresh();
      },
    });
  };

  const renderTitle = () => (
    <div className="title-wrapper">
      <span>我的收藏</span>
      <div className="btn-wrapper">
        <Button type="link" size="small" icon={<RedoOutlined />} disabled={!favorites.length} onClick={() => crud.refresh()}>
          刷新
        </Button>
        <Button className="btn-clear" danger size="small" icon={<ClearOutlined />} disabled={!favorites.length} onClick={handleCancelAll}>
          清空所有收藏
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <div className={style.wrapper}>
        <UCenterCard title={renderTitle()} />
        <div className="favorites">
          {favorites.length ? (
            <>
              <Spin size="default" spinning={crud.loading}>
                <Table rowClassName="item-custom-table" rowKey={"id"} bordered={false} size="small" dataSource={favorites} columns={columns} pagination={false} />
              </Spin>
              <div className="content-container-pagination">
                <MyCrudPagination crudInstance={crud} />
              </div>
            </>
          ) : (
            <Empty
              image={commonConstant.COMMON_IMAGE_PATHS.EMPTY_DEFAULT}
              imageStyle={{
                height: "100%",
              }}
              description={<span className="empty-tip">收藏还是空的</span>}
            />
          )}
        </div>
      </div>
    </>
  );
}
