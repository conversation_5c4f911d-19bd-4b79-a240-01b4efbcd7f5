import React, { memo } from "react";
import { LayoutProps } from "ssr-types-react";
import style from "./index.module.less";

interface Props extends LayoutProps {
  title?: string;
  hideAgreement?: boolean;
}

export default memo(function SimpleLayout(props: Props) {
  const { children, title, hideAgreement } = props;

  return (
    <div className={style.simpleLayoutWrapper}>
      <div className="simple-layout-header">
        <div className="main-width">
          <a href="/" className="logo-link" />
          <span className="welcome-text">{title || "欢迎使用"}</span>
        </div>
      </div>
      <section>{children}</section>
      <div className="simple-layout-footer">
        {hideAgreement ? null : (
          <div className="a-links">
            <a href="/help">帮助</a>
            <a href="/help#privacy-agreement">隐私</a>
            <a href="/help#privacy-agreement">条款</a>
          </div>
        )}
        <div className="copy">© 2023 JHD广州市金华大化学试剂有限公司 版权所有，保留一切权利。</div>
      </div>
    </div>
  );
});
