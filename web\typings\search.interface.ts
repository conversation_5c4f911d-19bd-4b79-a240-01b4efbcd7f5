/** 查询-参数 */
export interface ISearchPrams {
  keyword?: string;
  branch?: number;
}
export type ISkus = {
  id: number;
  productId: number;
  guidePrice: number;
  originalPrice: number;
  sku: string;
  skuName?: string;
  skuNameEn?: string;
  stock: number;
  realStock: number;
  stockRemark: string;
  image: string;
  spec: string;
  packing: string;
  unit: string;
  weight: string;
  sortOrder: string;
  remark: string;
  attributeModel?: string;
};
/** 搜索产品列表项-实际参数待确定 */
export type SearchProductType = {
  id: number;
  productNo: string;
  itemNo: string;
  brandId: number;
  brandName: string;
  productName: string;
  productNameEn: string;
  headImage: string;
  molecularFormula: string;
  molecularWeight: string;
  isProductCommend: number;
  productId: number;
  isDanger: number;
  isExplosion: number;
  isPoison: number;
  isExplode: number;
  description: string;
  content: string;
  skus?: ISkus;
  cas?: string;
  guidePriceRange?: string;
  discountPriceRange?: string;
};
