import React, { useRef, useState } from "react";
import { message, Modal, notification, Upload, Alert, Form } from "antd";
import type { RcFile, UploadFile, UploadProps } from "antd/es/upload/interface";
import { CookieUtil } from "@/utils/cookie.util";
import style from "./index.module.less";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import request from "@/utils/request.util";

interface ITmUploadProps extends UploadProps {
  /** 标记私有上传、普通上传 */
  isPrivate: boolean;
  /** 图片地址前缀 */
  originImagePrefix: string;
  /** 初始化-组装image数组 */
  initImageFiles: UploadFile[];
  /** 上传地址 */
  action: string;
  /** 上传数量限制 */
  maxCount: number;
  /** 图片大小限制: 5m */
  imageSizeLimit: number;
  /** 图片上传格式限制，默认："image/jpg", "image/jpeg", "image/gif", "image/png", "image/bmp" */
  imageTypeLimit: string[];
  /** 图片格式限制提示语 */
  imageTypeLimitTip: string;
  /** 上传提交文本 */
  uploadBtnText: string;
  /** 移除文本提示 */
  removeTipText: string;
  /** 上传成功-回调 */
  onSuccessCallback: (originPaths: string) => void;
}

const TmUploadImage = (props: ITmUploadProps) => {
  const { imageTypeLimitTip, onSuccessCallback, initImageFiles, multiple, action, maxCount, isPrivate, imageSizeLimit, imageTypeLimit, removeTipText, uploadBtnText, originImagePrefix } = props;
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>(initImageFiles ?? []);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const tmUploadRef = useRef<any>(null);

  /** ===================== 事件 start  ========================= */
  // 自定义上传处理
  const handleCustomRequest = async ({ file }) => {
    if (fileList.length >= maxCount) {
      await message.error(`最多只能上传 ${maxCount} 个文件`);
      return;
    }
    const uploadFormData = new FormData();
    uploadFormData.append("file", file);

    setLoading(true);
    await request({
      url: action,
      method: "post",
      headers: { "x-csrf-token": CookieUtil.getInstance().get("csrfToken") },
      data: uploadFormData,
    })
      .then(async res => {
        const imageUrl = isPrivate ? originImagePrefix + res.data.filename : res.data.filepath;
        setFileList([
          ...fileList,
          {
            uid: res.data.filename,
            name: res.data.filename,
            status: "done",
            url: res.data.filename,
            thumbUrl: imageUrl,
          },
        ]);
        await handleChangeEvent();
      })
      .catch(() => {
        message.error("上传失败，请重试~");
      })
      .finally(() => {
        setLoading(false);
      });
  };
  // 预览事件
  const handlePreview = async file => {
    setPreviewImage(file.thumbUrl);
    setPreviewOpen(true);
  };
  // 移除事件处理
  const handleRemove = async file => {
    removeTipText && notification.warn({ message: removeTipText });
    const newFileList = fileList.filter(item => item.uid !== file.uid);
    setFileList(newFileList);
  };
  /** 上传状态监听事件-进行中，完成，异常 */
  const handleChangeEvent = async () => {
    onSuccessCallback(tmUploadRef?.current?.fileList.map(item => item.url).join(",") ?? "");
  };
  // 图片上传校验处理
  const validImageTypeAndSize = async file => {
    const isFormatCheckedImg = !imageTypeLimit.includes(file.type);
    if (isFormatCheckedImg) {
      message.error(imageTypeLimitTip || "上传图片格式不符合规定~");
    }
    const isOutLimitSize = file.size < imageSizeLimit * 1024 * 1024;
    if (!isOutLimitSize) {
      message.error(`图片大小不能超过${isOutLimitSize}mb！`);
    }
    return !isFormatCheckedImg && isOutLimitSize;
  };

  // 上传前校验
  const beforeUpload = async (file: RcFile) => {
    return await validImageTypeAndSize(file);
  };
  /** ===================== 事件 end ========================= */

  /** 渲染上传按钮图标 */
  const renderUploadButton = () => (
    <div className="upload-btn-wrapper">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div className="btn-text">{props.uploadBtnText}</div>
    </div>
  );

  return (
    <div className={style.wrapper}>
      <Upload
        ref={tmUploadRef}
        {...props}
        className="uploader"
        fileList={fileList}
        multiple={multiple}
        maxCount={maxCount}
        customRequest={handleCustomRequest}
        beforeUpload={beforeUpload}
        onPreview={handlePreview}
        onRemove={handleRemove}
        onChange={handleChangeEvent}
      >
        {fileList.length >= maxCount ? null : renderUploadButton()}
      </Upload>
      {uploadBtnText && fileList.length ? <div className="upload-tip">{uploadBtnText}</div> : null}
      {/* 图片预览 */}
      <Modal open={previewOpen} title="图片预览" footer={null} onCancel={() => setPreviewOpen(false)}>
        <img alt="example" style={{ width: "100%" }} src={previewImage} />
      </Modal>
    </div>
  );
};

TmUploadImage.defaultProps = {
  /** 私有上传标识 */
  isPrivate: false,
  /** 上传地址 */
  action: "/api/upload/images",
  /** 图片地址前缀 */
  originImagePrefix: "",
  /** 初始化-组装image数组 */
  initImageFiles: [],
  /** 展示列表 */
  showUploadList: true,
  /** 是否开启多图上传 */
  multiple: false,
  /** 上传数量限制 */
  maxCount: 1,
  /** 图片大小限制: 5m */
  imageSizeLimit: 5,
  /** 图片上传格式限制，默认："image/jpg", "image/jpeg", "image/gif", "image/png", "image/bmp" */
  imageTypeLimit: ["image/jpg", "image/png", "image/jpeg", "image/gif", "image/bmp"],
  /** 图片格式限制提示语 */
  imageTypeLimitTip: "只允许上传 jpg|png|jpeg|gif|bmp 格式图片！",
  /** 上传提交文本 */
  uploadBtnText: "",
  /** 移除文本提示 */
  removeTipText: "请重新上传！",
  /** 列表展示形式 */
  listType: "picture-card",
  /** 上传成功-回调 */
  onSuccessCallback: () => {},
};

export default TmUploadImage;
