import { getSelectorsByUserAgent } from "react-device-detect";

interface DeviceDetectSSR {
  isMacOs?: boolean;
  isMobile?: boolean;
  isDesktop?: boolean;
}

/** 服务端组件判断设备类型 */
export function deviceDetectorSSR(ctx: any): DeviceDetectSSR {
  const userAgent = ctx?.get("user-agent") ?? null;
  const { isMacOs, isMobile, isDesktop } = userAgent && getSelectorsByUserAgent(userAgent);
  return {
    isMacOs,
    isMobile,
    isDesktop,
    // ...
  };
}
