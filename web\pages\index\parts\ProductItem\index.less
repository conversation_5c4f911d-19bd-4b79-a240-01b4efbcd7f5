@product-h-default-height: 150px;
@product-h-gap: 14px;
@product-border: 1px solid #f3f3f3;
@product-hover-bg: #fff4f4;
@product-hover-border: 1px solid #fe8888;

@product-radius: 5px;
@product-price-color: red;
@product-price-size: 14px;

.product-h {
  width: 100%;
  height: @product-h-default-height;
  .flex-row(space-between, center);
  padding: 24px 20px 22px;
  box-sizing: border-box;
  cursor: pointer;
  gap: @product-h-gap;
  border: @product-border;
  position: relative;
  /** 未登录-hover提示登录查看会员价 */
  .member-price-btn {
    display: none;
  }
  &:hover {
    background: @product-hover-bg;
    border: @product-hover-border;
    border-radius: @product-radius;
    box-sizing: border-box;
    .member-price-btn {
      display: inline-block;
      position: absolute;
      bottom: 20px;
      left: 10px;
      font-size: 14px;
      font-weight: 350;
      width: 157px;
      height: 38px;
      background: linear-gradient(to right, rgba(255, 116, 116, 1), rgba(255, 27, 27, 1));
      border-radius: 18px;
      .flex-center(row);
      color: #ffffff;
    }
  }
  a {
    .flex-row(space-between,center);
    .product-info {
      width: 136px;
      position: relative;
      .product-name {
        display: inline-block;
        color: @main-text-color;
        font-size: 21px;
        font-weight: 350;
        line-height: 1;
        .ellipsis();
      }
      .product-cas {
        width: 100%;
        display: inline-block;
        color: rgba(0, 0, 0, 0.5);
        font-size: 16px;
        font-weight: 350;
        line-height: 1;
        margin-top: 8px;
        .ellipsis();
      }
      .product-attrs {
        margin-top: 24px;
        .attr {
          width: 100%;
          display: inline-block;
          font-size: 12px;
          span:last-child {
            font-size: 14px;
            color: #6d7278;
          }
        }
        .product-spec {
          .flex-row(space-between, center);
          position: relative;
          &::after {
            position: absolute;
            content: "";
            left: -3px;
            bottom: 0;
            width: 100%;
            height: 0;
            background-color: #d8d8d8;
            border-bottom: 1px solid #e2e2e2;
          }
        }
        .product-price {
          .flex-row(space-between, center);
          color: @product-price-color;
          font-size: @product-price-size;
        }
      }
    }
    .product-img-box {
      width: 100px;
      height: 100px;
      padding: 16px;
      .flex-row(center, center);
      .product-img {
        width: 100%;
      }
    }
  }
}

.product-v {
  width: 100%;
  border: 1px solid #e3e3e3;
  padding: 4px;
  a {
    position: relative;
    width: 100%;
    .flex(column, center, center);
    .product-img-box {
      width: 136px;
      height: 136px;
      padding: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.6s;
      .product-img {
        width: 100%;
      }
      &:hover {
        .product-img {
          transform: scale(1.05);
        }
      }
    }
    .product-info-box {
      width: 100%;
      height: 60px;
      .flex-center(column);
      .product-name {
        .ellipsis(1);
      }
      .product-spec-price {
        .product-price {
          font-size: 16px;
          color: red;
        }
        .product-packing {}
      }
    }
  }

  &:hover {
    background: @product-hover-bg;
    border: @product-hover-border;
    box-shadow: 2px 2px 7px rgba(234, 167, 167, 0.15);
  }
}
