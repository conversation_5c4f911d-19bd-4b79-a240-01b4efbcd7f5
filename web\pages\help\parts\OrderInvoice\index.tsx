import React, { memo } from "react";

export default memo(function OrderInvoice() {
  return (
    <section className="article">
      <div className="article-paragraph">
        <h2 className="article-title">发票制度说明</h2>
        <div className="article-content">
          光华易购提供13%增值税发票，您可以在订购的收银台填写相关开票信息，光华易购开具的增值税发票有以下2种：
          <ul>
            <li>1. 13%增值税专用发票</li>
            <li>2. 13%增值税普通发票</li>
          </ul>
        </div>
      </div>
      <div className="article-paragraph">
        <h3 className="article-subtitle">开票要求及注意事项</h3>
        <div className="article-content">关于开票需要提交以下信息，在线订购的方式请务必在收银台填写，非在线订购客户请将贵司的开票信息发送给您联系的客服工程师。</div>
        <div className="article-content">请仔细阅读以下要求：</div>
        {/* 嵌套拓展 */}
        <div className="article-extant-content">
          <h3 className="article-subtitle not-pointer-tip">一. 13%增值税专用发票</h3>
          <ul>
            <li>如需开具13%增值税专用发票，请确保您是一般纳税人且必须提供：</li>
            <li>开票单位名称</li>
            <li>国家税务局税号</li>
            <li>开票地址及电话</li>
            <li>开户银行及帐号</li>
          </ul>
        </div>
      </div>
      <div className="article-paragraph">
        {/* 嵌套拓展 */}
        <div className="article-extant-content">
          <h3 className="article-subtitle not-pointer-tip">二. 13%增值税普通发票</h3>
          <ul>
            <li>开具13%增值税普通发票的客户可以是企事业单位、个人，开具时请提供：</li>
            <li>开票单位名称或个人姓名</li>
            <li>开票地址及电话</li>
          </ul>
        </div>
      </div>
      <div className="article-tip">注：以上信息请谨慎提供，由于客户提供资料的错误而导致的开票错误，光华易购不会累计重开。</div>
      <div className="article-paragraph">
        <h3 className="article-subtitle">发票寄送</h3>
        <div className="article-content">
          <div>非签约赊销客户原则上发票不随货寄送，客户另有要求的除外.</div>
          <div>
            我们一般在<a className={"article-link"}> 月末的3-5天 </a>对帐、开票，并由快递公司单独寄送.
          </div>
        </div>
      </div>
    </section>
  );
});
