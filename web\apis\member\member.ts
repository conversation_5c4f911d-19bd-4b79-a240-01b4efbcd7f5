import request from "@/utils/request.util";
import { ReBindEmailVo, ReBindPhoneVo, updateMemberVo, updatePasswordVo } from "~/typings/data/member/member";
import { $tools } from "@/utils/tools.util";

export function modifyPassword(updateVo: updatePasswordVo) {
  return request({
    url: "/api/ucenter/update-password",
    method: "put",
    data: updateVo,
  });
}
export function modifyMember(updateVo: updateMemberVo) {
  $tools.removeFormFields(updateVo, ["area"]);

  return request({
    url: "/api/ucenter/update-member",
    method: "put",
    data: updateVo,
  });
}

/** 修改用户头像 */
export function modifyMemberAvatar(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  return request({
    url: "/api/ucenter/update-avatar",
    method: "post",
    data: formData,
    headers: { "Content-Type": "multipart/form-data" },
  });
}

/**
 * 获取客户信息详情
 */
export function getMemberDetail() {
  return request({
    url: "/api/ucenter/show",
    method: "get",
  });
}
export function rebindPhone(bindVo: ReBindPhoneVo) {
  return request({
    url: "/api/ucenter/rebind-phone",
    method: "put",
    data: bindVo,
  });
}
export function rebindEmail(bindVo: ReBindEmailVo) {
  return request({
    url: "/api/ucenter/rebind-email",
    method: "put",
    data: bindVo,
  });
}
export default {
  modifyPassword,
  modifyMember,
  modifyMemberAvatar,
  getMemberDetail,
  rebindPhone,
  rebindEmail,
};
