import React, { useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import { Segmented, Card } from "antd";
import { BarsOutlined } from "@ant-design/icons";
import SvgIcon from "@/components/SvgIcon";
import style from "./index.module.less";

const defaultTab = "all";

export default function UcenterCouponIndex(props: SProps) {
  const options = [
    {
      label: "全部",
      value: "all",
      icon: <BarsOutlined />,
    },
    {
      label: "物流券",
      value: "logistics",
      icon: <SvgIcon iconClass="logistics" />,
    },
    {
      label: "订单券",
      value: "order",
      icon: <SvgIcon iconClass="order" />,
    },
    {
      label: "可使用",
      value: "usable",
      icon: <SvgIcon iconClass="usable" />,
    },
    {
      label: "已使用",
      value: "used",
      icon: <SvgIcon iconClass="used" />,
    },
    {
      label: "已过期",
      value: "expired",
      icon: <SvgIcon iconClass="expired" />,
    },
  ];
  const [value, setValue] = useState<string | number>(defaultTab);
  useEffect(() => {
    console.log("当前选中的值：", value);
  }, [value]);
  const couponsTest = [
    {
      face: 10,
      type: "物流",
      title: "无门槛",
      condition: "满1234元可用",
      term: "2019.09.20~2019.09.30",
    },
    {
      face: 99,
      type: "订单",
      title: "支付满减券",
      condition: "满1234元可用",
      term: "2019.09.20~2019.09.30",
    },
    {
      face: 200,
      type: "订单",
      title: "物流满减券",
      condition: "满1234元可用",
      term: "2019.09.20~2019.09.30",
    },
    {
      face: 999,
      type: "商品",
      title: "测试卡券",
      condition: "满1234元可用",
      term: "2019.09.20~2019.09.30",
    },
  ];
  return (
    <>
      <div className={style.wrapper}>
        <UCenterCard title={"我的卡券"} />
        <Segmented block size="large" defaultValue={defaultTab} options={options} value={value} onChange={setValue} />
        <Card className="coupon-container" bodyStyle={{ padding: "0" }}>
          <ul>
            {couponsTest.map((item, key) => {
              return (
                <li className="coupon-item" key={key}>
                  <div className="coupon-item-box base-coupons">
                    <div className="coupon-face">￥{item.face}</div>
                    <div className="coupon-info">
                      <p className="coupon-title">{item.title}</p>
                      <p className="coupon-condition">使用条件：{item.condition}</p>
                      <p className="coupon-condition">期限范围：{item.term}</p>
                    </div>
                    <div className="coupon-type">{item.type}</div>
                  </div>
                </li>
              );
            })}
          </ul>
        </Card>
      </div>
    </>
  );
}
