import React, { memo, useCallback, useContext, useEffect, useRef, useState } from "react";
import { FileSearchOutlined, RocketOutlined } from "@ant-design/icons";
import { Input, Select, SelectProps, Button } from "antd";
import { debounce } from "lodash";
import crudSearch from "@/apis/search/search";
import { useStoreContext } from "ssr-common-utils";
import { IContext } from "ssr-types-react";
import style from "./index.module.less";

interface IDefaultProps {
  /* 是否悬浮搜索 */
  isHover?: boolean;
}

interface IHotSearchWordType {
  keywords: string;
  score: number;
}

export default memo(function SearchBox({ isHover = false }: IDefaultProps) {
  const [remindList, setRemindList] = useState<SelectProps["options"]>([]);
  const [value, setValue] = useState<string | any>();
  const { state } = useContext<IContext>(useStoreContext());
  const [hotSearchWords, setHotSearchWords] = useState<IHotSearchWordType[]>([]);
  const [showSuggest, setShowSuggest] = useState<boolean>(false);
  const searchBoxRef = useRef(value);

  useEffect(() => {
    if (state?.layoutInitData?.hotWords) {
      setHotSearchWords(state.layoutInitData.hotWords.sort((pre, next) => next.score - pre.score));
    }
    if (state?.urlCurrentParams?.keyword) {
      setValue(state?.urlCurrentParams?.keyword);
    }
  }, []);

  /** 关键字联想查询 */
  const searchKeywordRemind = useCallback(
    debounce(async e => {
      const keyword = e.target.value.trim();
      if (!keyword) {
        return;
      }
      setRemindList([]);
      const [err, res] = await crudSearch
        .keywordRemindSearch(keyword.trim())
        .then(res => [null, res])
        .catch(err => [err, null]);
      if (err) {
        return;
      }
      setShowSuggest(res?.data.length > 0);
      setRemindList(res?.data);
    }, 300),
    []
  );

  const handleInputKeyDown = e => {
    if (e.keyCode === 13) {
      gotoSearch();
    }
  };

  const handleChange = e => {
    setValue(e.target.value?.trim());
    if (!e.target.value?.trim()) {
      setRemindList([]);
    }
  };

  const gotoSearch = () => {
    setTimeout(() => {
      window.location.href = `/search?keyword=${encodeURIComponent(value?.trim() || "")}`;
    }, 100);
  };

  const gotoQuickOrder = () => {
    window.location.href = state?.userLoginState ? `/quick-order` : `/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`;
  };

  const handleGoSuggestSearch = (keyword: string) => {
    console.log("keyword", keyword);
    window.location.href = `/search?keyword=${encodeURIComponent(keyword?.trim() || "")}`;
  };

  // 渲染悬浮搜索样式
  const renderHoverSearch = () => {
    return (
      <div className="search-box">
        <div className="search-main">
          <Input
            value={value}
            autoFocus={!isHover}
            allowClear
            placeholder="产品名/产品货号/产品代码/SKU/CAS"
            onChange={handleChange}
            onInput={searchKeywordRemind}
            onKeyDown={handleInputKeyDown}
            onBlur={() =>
              setTimeout(() => {
                setShowSuggest(false);
              }, 100)
            }
            onFocus={() => setShowSuggest(true)}
          />
          <Button className="search-btn" onClick={gotoSearch} size={"large"} type={"primary"} icon={<FileSearchOutlined />}>
            搜索
          </Button>

          <Button className="order-btn" onClick={gotoQuickOrder} size={"large"} ghost danger icon={<RocketOutlined />}>
            快速订购
          </Button>
        </div>
        {remindList && remindList.length > 0 && showSuggest ? (
          <div className="search-suggest">
            <ul>
              {remindList.map((item, index) => {
                return (
                  <li
                    key={index}
                    onClick={() => {
                      handleGoSuggestSearch(item.recommend);
                    }}
                  >
                    {item.recommend}
                  </li>
                );
              })}
            </ul>
          </div>
        ) : null}
      </div>
    );
  };
  /* ======================================= 方法 end======================================= */
  return (
    <div className={style.wrapper} ref={searchBoxRef}>
      {isHover ? (
        renderHoverSearch()
      ) : (
        <>
          <div className="search-wrapper">{renderHoverSearch()}</div>
          <div className="hotwords">
            {hotSearchWords.length ? "热门搜索：" : ""}
            {hotSearchWords.map((item, index) => (
              <a key={index} href={`/search?keyword=${item.keywords}`} className="word">
                {item.keywords}
              </a>
            ))}
          </div>
        </>
      )}
    </div>
  );
});
