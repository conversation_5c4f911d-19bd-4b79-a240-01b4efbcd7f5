import React, { useContext, useState } from "react";
import style from "./index.module.less";
import { CloseOutlined } from "@ant-design/icons";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";

// 广告位
export default function Adsense() {
  // 广告关闭控制
  const [isCloseActivityAdvance, setIsCloseActivityAdvance] = useState(false);
  const { state } = useContext<IContext>(useStoreContext());
  const advertisingData = state?.indexData?.homeAdsenseData;

  const handleCloseAd = e => {
    // cookie存储广告变量开关
    e.stopPropagation();
    setIsCloseActivityAdvance(!isCloseActivityAdvance);
  };

  return (
    <>
      {!isCloseActivityAdvance && !!advertisingData ? (
        <div className={style.wrapper}>
          <div className="adsense">
            <span className="adsense-tip">广告</span>
            {/* 广告链接 */}
            <a href={advertisingData.url}>
              <img src={advertisingData.img} alt="adsense" />
            </a>
            <span className="btn-close" onClick={handleCloseAd}>
              <CloseOutlined />
            </span>
          </div>
        </div>
      ) : null}
    </>
  );
}
