import { SProps } from "ssr";
import React, { useContext } from "react";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import style from "./index.module.less";
import { ISalesmanSpreadInfoType } from "@/typings/member.interface";
import { useRegisterForm } from "@/hooks/useRegisterForm";
import { Tabs } from "antd";
import SimpleLayout from "@/components/layout/SimpleLayout";

export default function SpreadRegister(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const salesmanSpreadInfo: ISalesmanSpreadInfoType = state.spreadInfo;
  const useRegisterFormHook = useRegisterForm(salesmanSpreadInfo);
  return (
    <>
      <SimpleLayout title="推荐注册页" hideAgreement={true}>
        <div className={style.wrapper}>
          <div className={style.spreadWrapper}>
            <div className="register">
              <h3 className="register-title">专属业务专员~推荐注册账号</h3>
              <Tabs items={useRegisterFormHook.renderTabItems} className="register-tabs" onChange={useRegisterFormHook.handleChangeTab} />
            </div>
            {/* 协议弹窗 */}
            <useRegisterFormHook.AgreementModal />
          </div>
        </div>
      </SimpleLayout>
    </>
  );
}
