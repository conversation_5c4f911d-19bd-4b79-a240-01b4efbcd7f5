.wrapper {
  :global {
    position: relative;
    .floatbar {
      position: absolute;
      left: -30px;
      top: 136px;
      width: 20px;
      box-sizing: content-box;
      border-top-left-radius: 3px;
      border-bottom-left-radius: 3px;
      background: linear-gradient(120deg, #ff3b3b 0%, #ffa45a 100%);
      box-shadow: 8px 8px 20px 0 rgba(55, 99, 170, 0.1);
      background-clip: padding-box;
      cursor: pointer;
      overflow: hidden;
      transition: all 0.3s ease-in-out;
      text-align: center;
      padding: 10px 5px;
      p {
        margin: 0;
        letter-spacing: 1px;
        font-size: 14px;
        color: #fff;
        line-height: 1.35;
      }
    }
    .wrap {
      position: relative;
      border: 1px solid #eaeaea;
      background: url("@@img/quick-order/header-bg.png") no-repeat center top #fff;
      background-size: 100% 15px;
      &:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 12px;
        background: url("@@img/quick-order/footer-bg.png") no-repeat left bottom;
        background-size: 100% 12px;
      }
      .header {
        text-align: center;
        padding: 35px 0 20px 0;
        border-bottom: 1px dashed #eaeaea;
        h2 {
          position: relative;
          font-size: 30px;
          font-weight: 200;
          margin-bottom: 10px;
        }
        h2:before,
        h2:after {
          content: "";
          position: absolute;
          top: 50%;
          width: 60px;
          height: 1px;
          background-color: #e62129;
        }
        h2:before {
          left: 50%;
          margin-left: -250px;
        }
        h2:after {
          right: 50%;
          margin-right: -250px;
        }
        p {
          font-size: 13px;
          line-height: 1.8;
          color: #888;
        }
      }
    }
    .body {
      display: flex;
      .main {
        flex: 1;
      }
      .side {
        width: 320px;
        border-left: 1px dashed #eaeaea;
        .side-box {
          padding: 20px;
          background-color: #fff;
          &:not(:last-child) {
            border-bottom: 1px dashed #eaeaea;
          }
          .side-box__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .side-box__header__title {
              font-size: 14px;
              font-weight: bold;
              line-height: 1;
            }
            .side-box__header__control {
              display: flex;
              align-items: center;
            }
          }
          .side-box__body {
          }
        }
        .address-item {
          margin: 10px 0;
          padding: 12px;
          background-color: #fafafa;
          border-radius: 4px;
          border: 1px dashed transparent;
          position: relative;
          transition: all 0.3s ease;
          &:hover {
            border: 1px dashed #eaeaea;
            background-color: #fff;
            .address-item__header,
            .address-item__address {
              transition: transform 0.3s ease;
            }
          }
          .address-item__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .address-item__name {
              font-size: 14px;
            }
          }
          .address-item__address {
            font-size: 13px;
            color: #888;
            margin-top: 6px;
          }
          .address-item__control {
            overflow: hidden;
            transition: all 0.3s ease;
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: center;
            height: 30px;
            padding-top: 8px;
            z-index: 9;
            border-top: 1px dashed #eaeaea;
            margin-top: 6px;
            button {
              transition: background-color 0.3s ease;
              color: #e62129;
              font-size: 13px;
            }
          }
        }
        .receipt-item {
          &:first-child {
            margin-top: 20px;
          }
          &:last-child {
            .receipt-item__type {
              span {
                &::after {
                  display: none;
                }
              }
            }
          }
          .receipt-item__type {
            display: flex;
            align-items: center;
            > span {
              position: relative;
              padding-right: 10px;
              margin-right: 10px;
              &::after {
                content: "";
                position: absolute;
                top: 50%;
                right: 0;
                width: 1px;
                height: 12px;
                background-color: #eaeaea;
                margin-top: -6px;
              }
            }
            button {
              font-size: 12px;
              padding: 0;
              color: #e62129;
            }
          }
          .receipt-item__desc {
            margin-bottom: 10px;
            font-size: 12px;
            color: #888;
            margin-top: 3px;
            p {
              margin: 0;
            }
          }
        }
      }
    }
    .order-remark-form {
      .ant-form-item {
        .ant-form-item-label {
          width: 100px;
          label {
            color: #333;
            font-size: 13px;
            span {
              margin-left: 5px;
            }
          }
        }
      }
    }
    .wrap-footer-1 {
      width: 1190px;
      height: 5px;
      margin: 0 auto;
      border: 1px solid #eaeaea;
      border-top: none;
      background-color: #fefefe;
      border-bottom-left-radius: 2px;
      border-bottom-right-radius: 2px;
    }
    .wrap-footer-2 {
      width: 1180px;
      height: 5px;
      margin: 0 auto;
      border: 1px solid #eaeaea;
      border-top: none;
      background-color: #fefefe;
      border-bottom-left-radius: 2px;
      border-bottom-right-radius: 2px;
    }

    .section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        .section-header__title {
          position: relative;
          font-size: 14px;
          font-weight: bold;
          text-indent: 16px;
          &::before {
            width: 3px;
            height: 14px;
            background: #e62129;
            content: "";
            position: absolute;
            left: 0px;
            top: 50%;
            margin-top: -7px;
            border-radius: 3px;
          }
        }
      }
      .section-body {
        padding: 0 16px 16px 16px;
      }
    }
    .submit-box {
      .ant-affix {
        box-shadow: 0 0 40px #ad46461a;
      }
    }
  }
}

.moreModal {
  .ant-modal-content {
    border-radius: 8px; // 圆角
  }

  .listItem {
    cursor: pointer; // 鼠标悬停时显示为手型
    transition: background-color 0.3s; // 添加过渡效果
    padding-left: 12px;
  }

  .listItemActive {
    position: relative;
    border-left: 2px solid #e62129;
    padding-left: 12px;
    background-color: #f5f5f5; // 鼠标悬停时背景色
    &::before {
      content: "✔";
      position: absolute;
      right: 0;
      top: 0;
      width: 25px;
      height: 20px;
      border-bottom-left-radius: 10px;
      background-color: #e62129;
      font-size: 12px;
      color: #fff;
      text-align: center;
      line-height: 20px;
    }
  }

  .nickname {
    font-weight: bold; // 显示昵称加粗
  }

  .phone {
    font-size: 13px;
    margin-left: 10px;
  }

  .addressDescription {
    color: #888; // 地址描述颜色
  }
}

.stepContent {
  display: flex;
  padding: 50px 0;
  flex-direction: column;
  align-items: center;
  p {
    margin-top: 20px;
    color: #888;
  }
}
