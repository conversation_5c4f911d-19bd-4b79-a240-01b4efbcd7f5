/** 余额操作业务类型 */
export const WALLET_SERVICE_TYPE = {
  WALLET_RECHARGE: "余额充值",
  WALLET_PAY: "余额支付",
  WALLET_REFUND: "余额退款",
  WALLET_WITHDRAWAL: "提现",
  WALLET_COMMISSION: "佣金提成",
};

/** 支付状态 */
export const WALLET_PAID_STATUS = {
  UNPAID: "待支付",
  PAID: "已支付",
  CANCEL: "已取消",
};

/** 支付类别 */
export const WALLET_RECHARGE_WAY = {
  ALIPAY: "支付宝",
  OTHER: "其他",
};

/** 是否支付 */
export const WALLET_IS_PAID = {
  "-1": "未知",
  0: "未支付",
  1: "已支付",
};

/** 流水/充值-单笔充值金额限制: 10000元 - 线上转账 */
const WALLET_SINGLE_RECHARGE_LIMIT = 10000;

export default {
  WALLET_SERVICE_TYPE,
  WALLET_PAID_STATUS,
  WALLET_RECHARGE_WAY,
  WALLET_IS_PAID,
  WALLET_SINGLE_RECHARGE_LIMIT
};
