@mainCategoryIcons: url(/images/category-nav/huada.svg), url(/images/category-nav/jhd.svg), url(/images/category-nav/proxy-brand.svg);

@category-text-font-size: @font-size-18; // 分类-文本-字体大小 18px
@category-parent-name-font-size: @font-size-16; // 分类-父级名称-字体大小 16px
@category-parent-name-color: #444; // 分类-父级名称-字体颜色
@category-parent-name-font-weight: 600; // 分类-文本-字体粗细

@sub-category-text-font-size: @font-size-12; // 子分类-文本-字体大小 12px
@sub-category-text-color: @main-text-color; // 子分类-文本-字体颜色
@sub-category-text-font-weight: 500; // 子分类-文本-字体粗细

@category-link-size: 14px; // 四级链接文本大小

.loopCategoryIcon(@index) when (@index <= 3) {
  & {
    .category-parent {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 24px;
        height: 24px;
        background: extract(@mainCategoryIcons, @index) 0 4px no-repeat;
      }
      .category-parent-name {
        margin-left: 28px;
      }
    }
  }
  // 循环增加
  .loopCategoryIcon(@index+1);
}

.wrapper {
  :global {
    position: relative;
    margin-right: 22px;
    // 精选商品分类 文本样式
    .category-text {
      .flex-row(center, center);
      gap: 10px;
      width: 200px;
      height: 40px;
      font-weight: @category-parent-name-font-weight;
      font-size: @category-text-font-size;
      color: @main-text-color-white;
      background-image: linear-gradient(to right, rgba(255, 82, 95, 1), rgba(230, 33, 41, 1));
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      cursor: pointer;
      .icon-category {
        font-size: @font-size-24;
      }
    }
    // 分类列表
    .category-ul {
      position: absolute;
      z-index: 997;
      width: 200px;
      height: 330px;
      background-color: @main-bg-color-white;
      box-sizing: border-box;
      box-shadow: 2px 0 15px #ccc;
      .category-item {
        padding: 10px 16px;
        box-sizing: border-box;
        cursor: pointer;
        position: relative;
        height: 66px;
        border-bottom: 1px solid #eaeaea;
        .category-parent {
          .flex-row(normal, center);
          gap: 20px;
          font-size: @category-parent-name-font-size;
          color: @category-parent-name-color;
          img {
            width: 16px;
            height: 16px;
          }
          &-title {
            font-size: 16px;
            font-weight: 600;
            color: @main-text-color;
            .flex-row(space-between, center);
            .ellipsis();
            .cate-parant-title {
              margin-left: 20px;
            }
          }
        }
        .sub-category-wrapper {
          font-weight: @sub-category-text-font-weight;
          font-size: @sub-category-text-font-size;
          color: #888;
          // margin-top: 4px;
          .ellipsis();
          span {
            margin-right: 8px;
          }
          a {
            margin-right: 8px;
            &:hover {
              color: #e02020;
            }
          }
        }
        .loopCategoryIcon(0);
        // 伪元素
        &::after {
          content: "";
          display: inline-block;
          width: 8px;
          height: 8px;
          position: absolute;
          right: 10px;
          top: 45%;
          transform: rotate(45deg);
          border-top: 2px solid #9d9d9d;
          border-right: 2px solid #9d9d9d;
        }
        // 激活状态
        &:hover {
          box-shadow: 2px 2px 10px rgba(139, 139, 139, 0.33);
          &::after {
            border-top-color: #e02020;
            border-right-color: #e02020;
          }
        }
        &:nth-last-child(2) {
          border-bottom: none;
          &::after{
            display: none;
          }
        }
      }
      // 嵌套-分类面板
      .category-inner-panel {
        position: absolute;
        top: 0;
        left: 199px;
        z-index: 999;
        width: 1001px;
        min-height: 330px;
        background-color: @main-bg-color-white;
        box-shadow: 2px 0 15px #ccc;
        // 推荐版块
        .recommend-area {
          width: 360px;
          min-height: 100%;
          background-repeat: no-repeat;
          background-size: 96%;
          background-position: 50% 50%;
        }
        // 分类内容-带广告
        .category-content-recommend {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          // 新广告栏
          .recommend-ul-wrapper {
            width: 100%;
            border: 1px dotted #d9d9d9;
            padding: 4px 8px;
          }
        }
        // 分类内容
        .category-content {
          flex: 1;
          padding: 24px;
          max-height: 640px;
          overflow-y: auto;
          &-ul {
            margin-bottom: 32px;
            .flex-col();
            // 二级分类标题
            .category-title {
              font-size: 16px;
              font-weight: 600;
              color: #e02020;
            }
            // 三、四级分类内容
            .child-category-wrapper {
              display: flex;
              flex-wrap: wrap;
              gap: 6px 16px;
              .child-category-content {
                .flex-row();
                margin-top: 16px;
                // 三级标题
                .child-category-name {
                  width: 72px;
                  font-size: 16px;
                  font-weight: 600;
                  color: #333333;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
                // 四级链接
                .category-links {
                  .flex();
                  box-sizing: border-box;
                  padding-left: 16px;
                  flex-wrap: wrap;
                  gap: 6px 16px;
                  _:-ms-fullscreen,
                  & {
                    margin: 0 6px 16px 0;
                  }
                  .category-link {
                    _.-ms-fullscreen,
                    & {
                      margin: 4px 16px 0 16px;
                    }
                    color: @main-text-color;
                    &:hover {
                      color: #e02020;
                    }
                  }
                }
              }
            }
          }
        }
      }

      // 国际代理品牌特殊情况样式处理
      .proxy-brand-wrapper {
        display: block;
        margin: 0 -16px -16px 0;
        .category-content-ul {
          float: left;
          margin-right: 16px;
          margin-bottom: 16px;
          a {
            position: relative;
            width: 90px;
            height: 91px;
            border-radius: 2px;
            .flex-center(row);
            border: 1px solid #ddd;
            .category-title {
              display: none;
              font-size: @auxiliary-text-size;
            }
            .category-img {
              width: 100%;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              font-size: 14px;
            }
            .category-item-mask {
              display: none;
            }
          }
          a:hover {
            border: none;
            .category-title {
              display: inline-block;
            }
            .category-img {
              width: 96%;
              transition: all 0.3s ease-in-out;
              opacity: 0.4;
            }
            .category-item-mask {
              position: absolute;
              left: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.6);
              width: 100%;
              height: 100%;
              border-radius: 2px;
              display: inline-flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              font-size: 14px;
              color: #ffffff;
              button {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}
