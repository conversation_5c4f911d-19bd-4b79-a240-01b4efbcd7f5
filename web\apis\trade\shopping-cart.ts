import request from "@/utils/request.util";
import { ICreate, IDelete, ISelectedAll, ISelectedOrNot, IUpdate } from "~/typings/data/trade/shopping-cart";

export function getList() {
  return request({
    url: `/api/trade/shopping-cart`,
    method: "get",
  });
}

export function create(data: Partial<ICreate>) {
  return request({
    url: `/api/trade/shopping-cart`,
    method: "post",
    data,
  });
}
export function update(data: Partial<IUpdate>) {
  return request({
    url: `/api/trade/shopping-cart`,
    method: "put",
    data,
  });
}
export function del(data: Partial<IDelete>) {
  return request({
    url: `/api/trade/shopping-cart`,
    method: "delete",
    data,
  });
}

export function selectedOrNot(data: Partial<ISelectedOrNot>) {
  return request({
    url: `/api/trade/shopping-cart/select`,
    method: "post",
    data,
  });
}

export function selectedAll(data: Partial<ISelectedAll>) {
  return request({
    url: `/api/trade/shopping-cart/select-all`,
    method: "post",
    data,
  });
}
export function getSelectedList(way?: string) {
  return request({
    url: `/api/trade/shopping-cart/selected`,
    method: "get",
    params: { way },
  });
}
export default { getList, create, update, del, selectedOrNot, selectedAll, getSelectedList };
