import { Body, Controller, Inject, Post } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { ProxyBrandStockDto } from "@/dto/proxy-brand-stock.dto";
import { IProxyStockService } from "@/service/proxy-brand-stock/proxy.stock.service";
import { Validate } from "@midwayjs/validate";

/** 产品库存 */
@Controller("/api/proxy-brand/stock")
export class ProxyBrandStockController extends BaseController {
  @Inject("ProxyStockService")
  proxyStockService: IProxyStockService;

  @Post("/query")
  @Validate()
  async queryStock(@Body() criteria: ProxyBrandStockDto) {
    const { ctx } = this;
    const resDesc = await this.proxyStockService.queryStockByBrand(criteria);
    return ctx.getResponseInstance(ctx).setResponseData(resDesc).send();
  }
}
