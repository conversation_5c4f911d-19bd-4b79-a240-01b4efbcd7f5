import React, { memo } from "react";
import "./index.less";

interface SimpleFloorTitleProps {
  title?: string;
  titleClass?: string;
  titleStyle?: object;
}

export default memo(function SimpleFloorHeader(props: SimpleFloorTitleProps) {
  const { title, titleClass, titleStyle } = props;

  return (
    <>
      {title ? (
        <div className={`simple-floor-header ${titleClass || ""}`} style={titleStyle}>
          <span className="title">{title}</span>
        </div>
      ) : null}
    </>
  );
});
