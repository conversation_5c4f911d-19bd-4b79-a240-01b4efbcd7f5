import React, { memo, useEffect, useState } from "react";
import * as orderCrud from "@/apis/order/order";
import { Timeline } from "antd";
import TmModal from "@/components/TmModal";
import style from "./index.module.less";
import { CheckCircleOutlined } from "@ant-design/icons";

interface IOrderTrackingModalProps {
  sn: string;
  onCloseCallback?: Function;
}
export default memo(function OrderTrackingModal(props: IOrderTrackingModalProps) {
  /* ======================================= state start ======================================= */
  // 订单追踪信息
  const [orderTrackingLogs, setOrderTrackingLogs] = useState<any[]>([]);
  // 面板弹窗控制
  const [visible, setVisible] = useState<boolean>(false);
  /* ======================================= state end ======================================= */

  /* ======================================= func start ======================================= */
  const fetchOrderTrackingLog = async (): Promise<void> => {
    if (!props.sn) {
      return;
    }
    setVisible(true);
    // 加载订单追踪信息
    const [err, res] = await orderCrud
      .getOrderTracking(props.sn)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return;
    }
    const trackingLog = res.data.sort((prev: any, next: any) => next.id - prev.id);
    setOrderTrackingLogs(trackingLog);
  };
  useEffect(() => {
    fetchOrderTrackingLog();
  }, [props.sn]);

  /** 操作人-显示控制 */
  const calcOperator = (operator: string) => {
    return isNaN(Number(operator)) ? operator : "工作人员";
  };
  const handleCloseModal = () => {
    setVisible(false);
    props?.onCloseCallback && props.onCloseCallback();
  };

  const renderContent = (logs: any) => {
    const isFinish = (idx): boolean => {
      return idx >= logs.length - 1 && logs.length !== 1;
    };
    return (
      <React.Fragment>
        <div className={style.wrapper}>
          <Timeline mode={"left"} className="log-box">
            {logs.map((tracking, idx) => {
              return (
                <Timeline.Item key={idx} className="log-box-item" dot={isFinish(idx) ? <CheckCircleOutlined /> : null} color={isFinish(idx) ? "green" : "#00CCFF"}>
                  <div className="log-label">
                    <span className="log-label-operator">{calcOperator(tracking.operator)}</span>
                    <span className="log-label-action">{tracking.action}</span>
                  </div>
                  <div className="log-created-date">{tracking.createdDate}</div>
                </Timeline.Item>
              );
            })}
          </Timeline>
        </div>
      </React.Fragment>
    );
  };
  /* ======================================= func end ======================================= */
  return (
    <>
      <TmModal
        title={`订单追踪「${props.sn}」`}
        open={visible}
        footer={false}
        width={550}
        keyboard
        maskClosable
        centered
        onCancel={() => handleCloseModal()}
        onOk={() => handleCloseModal()}
        content={renderContent(orderTrackingLogs)}
      />
    </>
  );
});
