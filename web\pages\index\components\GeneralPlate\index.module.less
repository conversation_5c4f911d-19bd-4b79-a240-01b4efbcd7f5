@theme-logo-width: 214px;
@general-container-padding: 32px;
@product-width: 168px;
@product-container-bg: #f5f5f5;
@product-container-padding: 18px;
@product-container-gap: 12px;

.wrapper {
  :global {
    .general-plate {
      margin-top: @plate-margin-top;
      background-color: @main-bg-color-white;
      padding: 32px;
      .general-plate-container {
        .flex-row(flex-start, center);
        width: 100%;
        margin-top: 16px;
        // ant-carousel 轮播图
        .product-carousel {
          width: calc(100% - @theme-logo-width);
          height: inherit;
          position: relative;
          .ant-carousel {
            padding: 16px;
            background-color: #f5f5f5;
            .recmd-page {
              width: 100%;
              height: inherit;
              display: inline-flex !important;
              gap: 16px;
              li {
                _:-ms-fullscreen,
                & {
                  margin-right: 16px;
                  &:last-child {
                    margin-right: 0;
                  }
                }
                width: @theme-logo-width;
                background-color: #ffffff;
              }
            }
          }
          // 箭头
          .arrow-left,
          .arrow-right {
            display: inline-block;
            width: 24px;
            height: 28px;
            background: rgb(216, 216, 216);
            opacity: 0.8;
            color: #ffffff;
            position: absolute;
            .flex-row(center, center);
            top: 50%;
            transform: translateY(-50%);
            &:hover {
              opacity: 1;
            }
          }
          .arrow-left {
            left: 0;
            border-bottom-right-radius: 50%;
            border-top-right-radius: 50%;
          }
          .arrow-right {
            right: 0;
            border-bottom-left-radius: 50%;
            border-top-left-radius: 50%;
          }
        }
      }
      .general-plate-more {
        .flex-row(normal, center);
        font-size: 16px;
        gap: 20px;
        .general-plate-btn {
          _:-ms-fullscreen,
          & {
            margin-right: 20px;
          }
          .flex();
          gap: 8px;
          .recmd-btn {
            _:-ms-fullscreen,
            & {
              margin: 0 4px;
            }
            width: 20px;
            height: 20px;
            border-radius: 50%;
            font-size: 10px;
            background: #ededed;
            color: #4b4b4b;
            .flex-center(row);
            &:hover {
              background: #ffb6b6;
              color: #ffffff;
            }
            &:active {
              transform: scale(1.009);
            }
          }
        }
      }
    }
  }
}
