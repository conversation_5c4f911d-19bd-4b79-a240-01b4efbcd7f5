import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { IToolService } from "@/service/tool.service";
import { CaptchaObj } from "svg-captcha";
import { BaseService } from "@/common/base/base.service";
const svgCaptcha = require("svg-captcha");

@Provide("ToolService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class ToolServiceImpl extends BaseService implements IToolService {
  // 图形验证码
  async generateCaptchaCode(): Promise<CaptchaObj> {
    return svgCaptcha.create({
      size: 4,
      fontSize: 50,
      width: 80,
      height: 40,
      background: "#cc9966",
    });
  }
}
