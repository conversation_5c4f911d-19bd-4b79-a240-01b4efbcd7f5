import { Inject, Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IHomepageService } from "@/service/homepage.service";
import { FORMAT } from "@midwayjs/core";
import cacheKeyConstant from "@/common/constants/cache.constant";
import { CacheManager } from "@midwayjs/cache";

@Provide("HomepageService")
@Scope(ScopeEnum.Request)
export class HomepageServiceImpl extends BaseService implements IHomepageService {
  cacheKey = cacheKeyConstant.BUSINESS.PLATFORM_BRAND_INTRODUCE;

  @Inject()
  cacheManager: CacheManager;

  /** 获取首页板块-全部关联板块 */
  async getHomePageData(): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/website/homepage"), "获取数据出错了，请重试");
  }

  /** 获取热门品牌介绍信息-加缓存 */
  async getHotBrandIntroduce(): Promise<any> {
    // 热门品牌介绍数据-缓存处理 - 1小时
    let res = await this.cacheManager.get(this.cacheKey);
    if (!res) {
      const result = await this.easyHttp.get("/api/website/homepage/brand-introduce");
      if (result.data?.length) {
        await this.cacheManager.set(this.cacheKey, result.data, { ttl: FORMAT.MS.ONE_HOUR / 1000 });
        res = result.data;
      }
    }
    return res;
  }
}
