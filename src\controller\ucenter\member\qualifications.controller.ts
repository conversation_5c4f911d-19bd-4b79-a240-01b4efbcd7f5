import { Controller, Get } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { render } from "ssr-core-react";
import { Readable } from "stream";

@Controller("/ucenter/member/qualifications", { middleware: [AuthenticationMiddleware] })
export class QualificationsController extends BaseController {
  @Get("/")
  async index() {
    const { ctx } = this;
    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
