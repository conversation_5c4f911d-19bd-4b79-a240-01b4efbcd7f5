// 重置表单当关闭时 reset form fields when modal is form, closed
import { useEffect, useRef } from "react";

export const useResetFormOnCloseModal = ({ form, visible }) => {
  const prevVisibleRef: any = useRef();
  useEffect(() => {
    prevVisibleRef.current = visible;
  }, [visible]);
  const prevVisible = prevVisibleRef.current;
  useEffect(() => {
    if (!visible && prevVisible) {
      form.resetFields();
    }
  }, [form, prevVisible, visible]);
};
