import React, { useContext, useRef, useState } from "react";
import { SProps } from "ssr-types-react";
import { Alert, Button, Form, FormInstance, Input, notification, Tabs } from "antd";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import style from "./index.module.less";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import { price2Thousand } from "@/utils/price-format.util";
import { PlusOutlined } from "@ant-design/icons";
import BillFlow from "@/pages/ucenter/my-wallet/parts/BillFlow/index";
import Recharge from "@/pages/ucenter/my-wallet/parts/Recharge/index";
import TmModal from "@/components/TmModal";
import crudWallet from "@/apis/member/wallet";
import { useLoading } from "@/hooks/useLoading";
import walletConstant from "@/constants/wallet";

export default function MemberWalletIndex(props: SProps) {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const memberWalletData = state.memberWalletData;
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState("bill-flow");
  const rechargeRef = useRef<any>(null);
  const renderItems = [
    {
      label: "账户流水",
      key: "bill-flow",
      children: <BillFlow />,
    },
    {
      label: "充值订单",
      key: "recharge",
      children: <Recharge ref={rechargeRef} />,
    },
  ];
  const rules = {
    price: [
      { required: true, message: "请输入充值金额" },
      {
        validator: async (_, value) => {
          if (value < 0.01) {
            return await Promise.reject(new Error("最小充值金额为：￥0.01"));
          } else if (value > walletConstant.WALLET_SINGLE_RECHARGE_LIMIT) {
            return await Promise.reject(new Error(`单次最大充值金额限制: ￥${price2Thousand(walletConstant.WALLET_SINGLE_RECHARGE_LIMIT)}`));
          }
          return await Promise.resolve();
        },
      },
      { pattern: new RegExp(/^(([1-9]\d*)|\d)(\.\d{1,2})?$/, "g"), message: "请输入正确的金额, 最多支持2位小数" },
    ],
  };
  const [form] = Form.useForm();
  const currentFormRef = useRef<FormInstance>(null);
  const useLoadingHook = useLoading();

  /* ======================================= method start======================================= */
  /** 激活充值订单弹窗 */
  const triggerTradeRechargePanel = () => {
    setModalVisible(true);
  };

  /** 切换tab */
  const onChangeTab = e => {
    setActiveKey(e);
  };

  /** 充值面板-取消-关闭 */
  const handleCancel = () => {
    setModalVisible(false);
  };

  /** 充值弹窗-财务流水 */
  const handleToShowBillFlow = () => {
    setModalVisible(false);
    setActiveKey("bill-flow");
  };

  /** 提交-表单校验通过 */
  const handleFinish = async form => {
    // 提交充值申请
    useLoadingHook.showLoading();
    const [err, res] = await crudWallet
      .tradeRecharge({
        price: Number(form.price),
      })
      .then(res => [null, res])
      .catch(err => [err, null]);
    useLoadingHook.hideLoading();
    if (err) {
      notification.error({
        message: err?.data?.message || "充值订单创建失败，请刷新页面!",
      });
      setModalVisible(false);
      return;
    }

    currentFormRef?.current?.resetFields();
    setModalVisible(false);
    setActiveKey("recharge");
    rechargeRef?.current?.crudRefresh();

    notification.success({
      message: "充值订单已创建，请尽快完成支付！",
    });
    rechargeRef?.current?.handleToPayForward(res.data);
  };
  /* ======================================= method end ======================================= */

  const renderTradeForm = () => {
    return (
      <>
        <Alert type="warning" message={
          <div className="payment-tips">
            <span>充值余额可以用于订单快速支付</span>
            <span>
              充值金额单次最多允许充值: <strong className="payment-tip-bill-flow">￥{price2Thousand(walletConstant.WALLET_SINGLE_RECHARGE_LIMIT)}</strong>
            </span>
            <span>
              关于余额的变动明细，请查看{" "}
              <strong className="payment-tip-billflow" onClick={handleToShowBillFlow}>
                财务流水
              </strong>
            </span>
          </div>
        }/>
        <Form className="payment-form" ref={currentFormRef} form={form} scrollToFirstError autoComplete="off" size="middle" onFinish={handleFinish}>
          <Form.Item label="充值账户">{state?.userData?.companyName || state?.userData?.nickname}</Form.Item>
          <Form.Item label="充值金额" name="price" rules={rules.price} validateFirst>
            <Input size="middle" maxLength={13} placeholder="请输入充值金额（元）" />
          </Form.Item>
          <Form.Item>
            <div className="btn-wrapper">
              <Button size="middle" onClick={handleCancel}>
                取消操作
              </Button>
              <Button size="middle" danger type="primary" htmlType={"submit"}>
                创建订单
              </Button>
            </div>
          </Form.Item>
        </Form>
      </>
    );
  };

  return (
    <div className={style.wrapper}>
      <UCenterCard title={`个人钱包`} />
      <div className="balance-container">
        <div className="balance-head">
          <div className="balance-head-card">
            <div className="account">
              <div className="balance">账户余额: ￥{price2Thousand(memberWalletData.memberWallet)}</div>
              {/* <Button size="large" ghost className="btn-add" icon={<PlusOutlined />} onClick={triggerTradeRechargePanel}>
                账户充值
              </Button> */}
            </div>
          </div>
        </div>
        {/*  tab */}
        <Tabs activeKey={activeKey} items={renderItems} className="balance-tabs" onChange={onChangeTab} />
      </div>
      {/* 充值弹窗 */}
      <TmModal
        maskClosable={false}
        title={<div style={{ textAlign: "center" }}>充值订单</div>}
        width={380}
        centered={true}
        open={modalVisible}
        closable={false}
        className="trade-modal"
        content={renderTradeForm()}
        footer={null}
      />
    </div>
  );
}
