/**
 * 策略
 * <AUTHOR>
 */
export class StockContext {
  private strategy: IStockStrategy;

  private readonly sku: string;
  private readonly quantity: number | undefined;

  constructor(sku: string, quantity?: number | undefined) {
    this.sku = sku;
    this.quantity = quantity;
  }

  // 设置策略
  public setStrategy(strategy: IStockStrategy): void {
    this.strategy = strategy;
  }

  // 执行策略
  public async execute(): Promise<number> {
    return await this.strategy.query(this.sku, this.quantity);
  }
}
