import React, { useState } from "react";
import type { ModalProps } from "antd";
import { Button, Modal } from "antd";
import PrivacyAgreementTemplate from "@/components/AgreementTemplates/PrivacyAgreementTemplate";
import PlatformAgreementTemplate from "@/components/AgreementTemplates/PlatformAgreementTemplate";
import { AGREEMENT_CONFIG } from "@/constants/common";

/** 协议弹窗hook */
export const useAgreementModal = (modalProps: ModalProps = {}) => {
  const [visible, setVisible] = useState(false);
  const [agreementType, setAgreementType] = useState<string>("");

  /**
   * 弹窗打开
   * @param agreementType
   */
  const open = (agreementType: string = AGREEMENT_CONFIG.PRIVACY.value) => {
    setAgreementType(agreementType);
    setVisible(true);
  };

  /**
   * 弹窗关闭
   */
  const close = () => {
    setVisible(false);
  };

  /** 弹窗modal */
  const AgreementModal = (props?: any) => {
    const renderContent = () => {
      return agreementType === AGREEMENT_CONFIG.PRIVACY.value ? <PrivacyAgreementTemplate /> : <PlatformAgreementTemplate />;
    };

    const renderFooter = (
      <div style={{ width: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Button size="large" type="primary" style={{ width: "240px" }} onClick={close}>
          同意并继续
        </Button>
      </div>
    );

    return (
      <Modal
        centered={true}
        width={980}
        open={visible}
        onCancel={close}
        closable={true}
        maskClosable={true}
        footer={renderFooter}
        wrapClassName="modal-wrap"
        okText="提交"
        {...modalProps}
        bodyStyle={{ padding: "16px", height: "600px", overflowY: "auto" }}
      >
        {renderContent()}
      </Modal>
    );
  };

  return {
    AgreementModal,
    open,
  };
};
