@productColor: #eb6262;
@contentLinkColor: #e02020;

.wrapper {
  :global {
    // 搜索栏
    .search-container {
      margin: 8px 0;
      .ant-form-item-label > label {
        height: 31px;
      }
      .date-range-picker {
        .ant-picker-range {
          //兼容IE11
          _:-ms-fullscreen,
          & {
            input {
              min-height: 22px;
            }
          }
        }
      }
    }
    // 表格
    .content-container {
      .product-item-no {
        cursor: pointer;
        &:hover {
          color: @contentLinkColor;
          text-decoration: underline;
        }
      }
      .product-desc {
        cursor: pointer;
        color: @productColor;
        &:hover {
          text-decoration: underline;
        }
      }
      .order-no {
        &:hover {
          text-decoration: underline;
        }
      }
      .product-price {
        color: @productColor;
      }
    }
  }
}
