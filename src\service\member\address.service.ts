import { addressCreateDto, addressQueryListDto, addressUpdateDto } from "~/typings/data/member/address";

export interface IAddressService {
  /**
   * 获取分页列表
   *
   * @param memberId 会员ID
   * @param criteria 条件
   */
  getPageList: (memberId: string, criteria: Partial<addressQueryListDto>) => Promise<any>;

  /**
   * 新增
   *
   * @param resource /
   */
  create: (memberId: string, resource: addressCreateDto) => Promise<any>;

  /**
   *详情
   *
   * @param memberId /
   * @param addressId /
   */
  show: (memberId: string, addressId: number) => Promise<any>;

  /**
   * 删除
   *
   * @param memberId /
   * @param addressId /
   */
  delete: (memberId: string, addressId: number) => Promise<any>;

  /**
   * 更新
   *
   * @param resource /
   */
  update: (resource: addressUpdateDto) => Promise<any>;

  /**
   * 默认
   *
   * @param memberId /
   * @param addressId /
   */
  setDefault: (memberId: string, addressId: number) => Promise<any>;
}
