/**
 * 运费类型
 *
 */
export const FreightTypeConstant = {
  LOGISTICS_SELF_PICKUP: {
    name: "物流（站点自提）",
    value: 1,
  },
  LOGISTICS_TOHOME: {
    name: "物流+送货上门",
    value: 2,
  },
  PREMIER_TOHOME_2: {
    name: "专车+送货上门（2~4天货期）",
    value: 3,
  },
  PREMIER_TOHOME_14: {
    name: "专车+送货上门（约14天货期：免运费）",
    value: 4,
  },
  EXPRESS: {
    name: "快递",
    value: 5,
  },
};
export function findFreightTypeByValue(value: number) {
  return Object.values(FreightTypeConstant).find(item => item.value === value)?.name;
}
export default { FreightTypeConstant, findFreightTypeByValue };
