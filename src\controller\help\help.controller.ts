import { Controller, Get, HttpCode } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";

@Controller("/help")
export class HelpController extends BaseController {
  @Get("/")
  @HttpCode(200)
  async help(): Promise<void> {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
