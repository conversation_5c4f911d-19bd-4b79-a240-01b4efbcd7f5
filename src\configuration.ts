import { Configuration, App, Logger } from "@midwayjs/decorator";
import * as koa from "@midwayjs/koa";
import { join } from "path";
import { initialSSRDevProxy, getCwd } from "ssr-server-utils";
import { ILogger } from "@midwayjs/logger";
import * as axios from "@midwayjs/axios";
import { IMidwayContainer } from "@midwayjs/core";
import { RequestMiddleware } from "@/middleware/request.middleware";
import * as dotenv from "dotenv";
import * as redis from "@midwayjs/redis";
import * as validate from "@midwayjs/validate";
import * as crossDomain from "@midwayjs/cross-domain";
import * as passport from "@midwayjs/passport";
import * as jwt from "@midwayjs/jwt";
import { DefaultErrorFilter } from "@/filter/default.filter";
import { UnauthorizedErrorFilter } from "@/filter/unauthorized.filter";
import * as cache from "@midwayjs/cache";
import * as i18n from "@midwayjs/i18n";
import { ApiResponse } from "@/extend/api.response";
import { Context } from "@midwayjs/koa";
import * as security from "@midwayjs/security";
import * as upload from "@midwayjs/upload";
import * as view from "@midwayjs/view-ejs";
import * as proxy from "@midwayjs/http-proxy";

const koaStatic = require("koa-static-cache");
const cwd = getCwd();

// load .env file in process.cwd
dotenv.config();

@Configuration({
  // 注意 proxy 需要放在 security 前，避免进行csrf校验
  imports: [koa, axios, redis, cache, validate, crossDomain, passport, jwt, i18n, proxy, security, upload, view],
  importConfigs: [join(__dirname, "./config")],
})
export class ContainerLifeCycle {
  @App()
  app: koa.Application;

  @Logger()
  logger: ILogger;

  async onReady(container: IMidwayContainer) {
    this.app.use(koaStatic(join(cwd, "./build")));
    this.app.use(koaStatic(join(cwd, "./public")));
    this.app.use(koaStatic(join(cwd, "./build/client")));
    this.app.useFilter([UnauthorizedErrorFilter, DefaultErrorFilter]);
    // 使用全局中间件
    this.app.useMiddleware(RequestMiddleware);

    await initialSSRDevProxy(this.app);

    const httpService = await container.getAsync(axios.HttpService);
    httpService.interceptors.request.use(
      config => {
        // Do something before request is sent
        config.headers = {
          "Content-Type": "application/json",
          ...config.headers,
        };
        return config;
      },
      async error => {
        // Do something with request error
        return await Promise.reject(error);
      }
    );
    httpService.interceptors.response.use(
      response => {
        return response;
      },
      async error => {
        const code = error?.response?.status || error.response?.data?.status || error.response?.record?.status;
        console.warn(`请求响应失败，状态码为: ${code}`);
        return await Promise.reject(error);
      }
    );
    // 扩展上下文
    await this.extendContext(this.app.context);
  }

  async extendContext(context: any) {
    Object.defineProperties(context, {
      getResponseInstance: {
        value: function (ctx: Context) {
          return new ApiResponse(ctx);
        },
      },
    });
  }
}
