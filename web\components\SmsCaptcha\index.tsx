import React, { useState, useEffect } from "react";

import { Form, Input, message, notification, Space } from "antd";
import { OneToOneOutlined } from "@ant-design/icons";
import style from "./index.module.less";
import { sendSmsCode } from "@/apis/sms";
import { useCountDownCache } from "@/hooks/useCountDown";
import { getCaptchaCode } from "@/apis/auth";
import { $tools } from "@/utils/tools.util";
import { onlyCsr } from "ssr-hoc-react";
import { $localStorage } from "@/utils/storage.util";
import { SmsSendTypeEnum } from "@/enums/SmsSendTypeEnum";

/** 短信验证-组件数据类型 */
interface SmsCaptchaProps {
  countdownSeconds: number; // 倒计时单位秒,默认120秒
  captchaCode: string;
  needCaptchaCode: boolean; // 是否需要图形验证码
  captchaCodeLabel: string; // 图形验证码label
  smsSendType: SmsSendTypeEnum; // 短信验证码类型：register | login
  rules: object; // 校验规则
  smsCode: string | number;
  smsCodeLabel: string;
  uuid: string;
  iconPrefix: any; // 展示前缀图标,为空则隐藏，可传入自定义图标
}

/** 转可选-短信验证模块类型 */
type SmsCaptchaPartialProps = Partial<SmsCaptchaProps>;

const SmsCaptcha = (props: SmsCaptchaPartialProps) => {
  const smsFormRef = Form.useFormInstance();
  const [formParams, setFormParams] = useState({
    validate: {
      smsCode: null,
      captchaCode: null,
    },
    rules: {
      smsCode: [{ required: true, whitespace: true, message: "短信验证码不能为空" }],
      captchaCode: [{ required: false, whitespace: true, message: "图像验证码不能为空" }],
    },
  });
  const rules = Object.assign({}, formParams.rules, props.rules);
  /* 是否倒计时处理中 */
  const [isCountDownIng, setIsCountDownIng] = useState(false);
  // 是否已触发发送
  const [isTrigger, setIsTrigger] = useState(false);
  // const smsCountDownHook =useCountDown(props.countdownSeconds??120,()=>{
  //   setIsCountDownIng(false);
  // })
  //= ============= 客户端渲染才生效 ==============
  const cnptUuid = props.uuid ?? $tools.uid();
  const smsCountDownHook = useCountDownCache(props.countdownSeconds ?? 120, cnptUuid, () => {
    setIsCountDownIng(false);
  });
  useEffect(() => {
    if ($localStorage.get(`timerStartTime-${cnptUuid}`)) {
      setIsTrigger(true);
      setIsCountDownIng(true);
      smsCountDownHook.start();
    }
  }, []);
  //= ============= 客户端渲染才生效 ==============

  /** 获取短信验证码前 */
  const beforeGetSMSCodeEvent = async () => {
    // 校验手机号是否正确输入
    const errMap = await smsFormRef
      .validateFields(["account", "phone"])
      .then(() => null)
      .catch(err => err);
    if (errMap) {
      message.warning("请按要求输入手机号码！");
      return false;
    }
    // 开启时，是否输入图形验证码
    if (props.needCaptchaCode && !smsFormRef.getFieldValue("captchaCode")) {
      message.warning("请输入图形验证码！");
      return false;
    }
    return true;
  };
  // 获取验证码事件 -
  const handleGetSMSCodeEvent = async () => {
    if (!(await beforeGetSMSCodeEvent())) {
      return;
    }

    if (isCountDownIng) {
      return false;
    }
    const phone = smsFormRef.getFieldValue("phone");
    const captchaCode = smsFormRef.getFieldValue("captchaCode");
    const smsSendType = props.smsSendType ?? SmsSendTypeEnum.REGISTER;
    const companyName = smsFormRef.getFieldValue("companyName");

    const [err, res] = await sendSmsCode({ phone, captchaCode, type: smsSendType, companyName })
      .then(data => [null, data])
      .catch(err => [err, null]);
    await getCaptchaCodeEvent();
    if (err) {
      notification.error({
        message: err.data?.message,
        duration: 2,
      });
      return false;
    }

    setIsTrigger(true);
    setIsCountDownIng(true);
    smsCountDownHook.start();
    message.success("验证码已发送" || res.message);
  };
  // 获取图形验证码
  const [captchaContent, setCaptchaContent] = useState("");
  const getCaptchaCodeEvent: any = async () => {
    const [_, res] = await getCaptchaCode()
      .then(data => [null, data])
      .catch(err => [err, null]);
    if (res) {
      setCaptchaContent(res);
    }
  };
  // 组件渲染，数据初始化
  useEffect(() => {
    if (props.needCaptchaCode) {
      getCaptchaCodeEvent();
    }
  }, []);
  const renderIconPrefix = () => {
    if (props.iconPrefix) {
      return props.iconPrefix === "default" ? <OneToOneOutlined className="site-form-item-icon" /> : props.iconPrefix;
    }
    return null;
  };
  const renderCaptchaCode = () => {
    return props.needCaptchaCode ? (
      <Form.Item className="captcha-code">
        <Space>
          <Form.Item name="captchaCode" label={props.captchaCodeLabel ?? ""} rules={rules.captchaCode}>
            <Input prefix={renderIconPrefix()} placeholder="请输入图像验证码" allowClear autoComplete="off" />
          </Form.Item>
          <img className="captcha-code-img" src={`data:image/svg+xml;utf8,${encodeURIComponent(captchaContent)}`} alt="captcha" onClick={getCaptchaCodeEvent} />
        </Space>
      </Form.Item>
    ) : null;
  };
  const renderSmsCode = () => {
    return (
      <span className={`sms-send-btn${isCountDownIng ? " sms-send-btn-ing" : ""}`} onClick={handleGetSMSCodeEvent}>
        {!isTrigger ? "获取验证码" : isCountDownIng ? `倒计时 ${smsCountDownHook.count} 秒` : "重新获取"}
      </span>
    );
  };
  return (
    <>
      <div className={style.smsCaptchaWrap}>
        {renderCaptchaCode()}
        <Form.Item name="smsCode" label={props.smsCodeLabel ?? ""} rules={rules.smsCode}>
          <Input prefix={renderIconPrefix()} placeholder="请输入短信验证码" allowClear suffix={renderSmsCode()} autoComplete="off" />
        </Form.Item>
      </div>
    </>
  );
};

SmsCaptcha.defaultProps = {
  countdownSeconds: 120,
  needCaptchaCode: true,
  captchaCodeLabel: "图形验证码",
  smsCodeLabel: "短信验证码",
  iconPrefix: "default",
  smsSendType: SmsSendTypeEnum.REGISTER,
};

export default onlyCsr(SmsCaptcha) as any;
