import React, { ReactNode } from "react";
import "./index.less";

interface ThemeRenderHeaderProps {
  title: string;
  moreText?: string;
  moreUrl?: string;
  moreRender?: ReactNode;
}

const ThemeRenderHeader = (props: ThemeRenderHeaderProps) => {
  return (
    <div className="theme-render-header">
      <span className="header-title">{props.title}</span>
      {props?.moreRender ? (props.moreRender) : (props?.moreUrl ? <a href={props.moreUrl} className="header-more">{props?.moreText || "更多"}</a> : null)}
    </div>
  );
};

export default ThemeRenderHeader;
