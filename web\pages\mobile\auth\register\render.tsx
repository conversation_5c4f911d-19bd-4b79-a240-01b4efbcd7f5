import React, { useContext } from "react";
import { SProps } from "ssr";
import styles from "@/pages/mobile/auth/spread-register/index.module.less";
import RegisterCnpt from "@/pages/mobile/auth/component/register";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import MobileNavBar from "@/components/mobile/TmFormItem/src/components/MobileNavBar";

export default function RegisterMobile(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());

  return (
    <>
      <div className={styles.wrapper}>
        <div className="auth-page">
          <div className="auth-header">
            <MobileNavBar title="光华易购注册" />
          </div>
          <RegisterCnpt customerCategory={state?.customerCategory} />
        </div>
      </div>
    </>
  );
}
