import { useEffect, useState, useCallback } from "react";

/*
 * callback为回调函数
 *
 * @Description: 用法 const newFunc = useSyncCallback(yourCallback)
 */
const useSyncCallback = callback => {
  const [proxyState, setProxyState] = useState({ current: false });
  const Func = useCallback(() => {
    setProxyState({ current: true });
  }, [proxyState]);

  useEffect(() => {
    if (proxyState.current) setProxyState({ current: false });
  }, [proxyState]);
  useEffect(() => {
    proxyState.current && callback();
  });
  return Func;
};
export default useSyncCallback;
