import { IProxyStockService } from "@/service/proxy-brand-stock/proxy.stock.service";
import { ProxyBrandStockDto } from "@/dto/proxy-brand-stock.dto";
import { httpError } from "@midwayjs/core";
import { StockContext } from "@/service/impl/proxy-brand-stock/kit/stock.context";
import { MerckStrategy } from "@/service/impl/proxy-brand-stock/kit/strategy/merck.strategy";
import { Provide, Scope, ScopeEnum } from "@midwayjs/decorator";

@Provide("ProxyStockService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class ProxyStockServiceImpl implements IProxyStockService {
  async queryStockByBrand(criteria: ProxyBrandStockDto): Promise<any> {
    const stockContext: StockContext = new StockContext(criteria.sku, criteria.quantity);
    switch (criteria.brandName) {
      case "merck":
        stockContext.setStrategy(new MerckStrategy());
        break;
      default:
        throw new httpError.BadRequestError("当前查询库存条件为不合法的请求！");
    }
    return stockContext.execute();
  }
}
