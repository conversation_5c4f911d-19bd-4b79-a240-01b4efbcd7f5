.wrapper {
  /*条件复用*/
  #conditionBtnFun() {
    .act-btn {
      position: absolute;
      right: 10px;
      top: 5px;
      span {
        border: 1px solid #ddd;
        margin-left: 10px;
        color: #999;
        display: inline-block;
        padding: 1px 3px;
        font-size: 12px;
        .anticon {
          border: none;
          margin: 0;
          font-size: 10px;
        }
        &:hover {
          cursor: pointer;
          color: @mallThemeColor;
          border-color: @mallThemeColor;
        }
      }
    }
    .multiple-btn {
      text-align: center;
      margin-top: 10px;
      margin-bottom: 5px;
      .ant-btn {
        font-size: 12px !important;
        &:last-child {
          margin-left: 10px;
        }
        &:hover,
        &:focus {
          color: #fff;
          border-color: @mallThemeColor;
          background: @mallThemeColor;
        }
      }
    }
  }
  :global {
    .filter-box {
      /* x偏移量 | y偏移量 | 阴影模糊半径 | 阴影扩散半径 | 阴影颜色 */
      box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.08);
      /*导航板块*/
      .head-bar {
        position: relative;
        display: flex;
        align-items: center;
        height: 40px;
        padding: 0 10px;
        background: @main-bg-color-white;
        .crumbs {
          > div:first-child {
            padding: 0 8px;
            font-size: @font-size-16;
            font-weight: bold;
            &:hover {
              color: @mallThemeColor;
              cursor: pointer;
            }
          }
          .crumbs-sub {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            &.sub-bar {
              position: relative;
              background: #fff;
              border: 1px solid #999;
              padding: 0 8px;
              min-width: 85px;
              height: 24px;
              text-align: center;
              margin: 0 3px;
              &:hover {
                color: @mallThemeColor;
                border-color: @mallThemeColor;
                border-bottom-color: #fff;
                cursor: pointer;
                ul {
                  display: block;
                }
                .anticon-down {
                  transform: rotate(180deg);
                }
              }
              ul {
                display: none;
                position: absolute;
                top: 18px;
                left: -1px;
                width: 300px;
                padding: 5px 10px;
                background: #fff;
                border: 1px solid @mallThemeColor;
                z-index: 1;
                clear: left;
                &::before {
                  content: "";
                  position: absolute;
                  width: 83px;
                  left: 0;
                  top: -1px;
                  z-index: 2;
                  border-top: 1px solid #fff;
                }
                li {
                  color: #999;
                  float: left;
                  width: 30%;
                  margin: 3px 0;
                  text-align: left;
                  &:hover {
                    color: @mallThemeColor;
                    cursor: pointer;
                  }
                }
              }
            }
          }
          .reset-category {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            right: 10px;
            top: 10px;
          }
        }
        .no-crumbs {
          .label {
            font-size: @font-size-14;
            font-weight: bold;
            &:hover {
              color: @mallThemeColor;
              cursor: pointer;
            }
          }
          .keyword {
            font-weight: bold;
            margin-right: 10px;
          }
        }
        .selected-item {
          font-size: 12px;
          color: #000;
          padding: 2px 22px 2px 8px;
          margin-right: 5px;
          margin-top: 1px;
          max-width: 250px;
          height: 24px;
          overflow: hidden;
          position: relative;
          background-color: #f3f3f3;
          border: 1px solid #ddd;
          &:hover {
            border-color: @mallThemeColor;
            background-color: #fff;
            .anticon {
              color: #fff;
              background-color: @mallThemeColor;
            }
          }
          span:nth-child(2) {
            color: @mallThemeColor;
          }
          .anticon {
            position: absolute;
            right: 0;
            top: 0;
            color: @mallThemeColor;
            font-size: 10px;
            line-height: 24px;
            width: 21px;
            height: 22px;
          }
        }
      }
      /*内容板块*/
      .content-bar {
        margin-top: 10px;
        background: @main-bg-color-white;
        .brand-condition {
          display: flex;
          border-bottom: 1px solid #ddd;
          font-size: 12px;
          .label {
            font-weight: bold;
          }
          > div:first-child {
            width: 100px;
            background: #eee;
            padding: 10px 0 0 10px;
          }
          > div:last-child {
            width: 1100px;
            padding: 10px;
            position: relative;
            ul {
              width: 900px;
              max-height: 100px;
              overflow: hidden;
              padding-top: 1px;
              clear: left;
              li {
                width: 65px;
                height: 65px;
                float: left;
                line-height: 45px;
                border: 1px solid #ddd;
                margin: -1px 5px 0 0;
                overflow: hidden;
                position: relative;
                padding: 2px;
                img {
                  width: 100%;
                  height: 100%;
                }
                &:hover {
                  border: 2px solid @mallThemeColor;
                  top: 0;
                  left: 0;
                  position: relative;
                  z-index: 1;
                  img {
                    display: none;
                  }
                }
                span {
                  display: inline-flex;
                  width: 100%;
                  height: 100%;
                  color: @mallThemeColor;
                  cursor: pointer;
                  font-size: 14px;
                  justify-content: center;
                  align-items: center;
                }
                .corner-icon {
                  position: absolute;
                  right: -1px;
                  bottom: -1px;
                  div {
                    width: 0;
                    border-top: 20px solid transparent;
                    border-right: 20px solid @mallThemeColor;
                  }
                  .anticon {
                    font-size: 12px;
                    position: absolute;
                    bottom: -3px;
                    right: -5px;
                    transform: rotate(-15deg);
                    color: #fff;
                  }
                }
              }
              .border-color {
                border-color: @mallThemeColor;
                z-index: 1;
              }
            }
            .show-more {
              height: auto;
              max-height: 200px;
              overflow: scroll;
            }
            #conditionBtnFun();
          }
        }
        /*其它筛选条件*/
        .other-condition {
          border-bottom: 1px solid #ddd;
          display: flex;
          min-height: 30px;
          font-size: 12px;
          &:last-child {
            border: none;
          }
          > div:first-child {
            width: 100px;
            background: #eee;
            padding-left: 10px;
            line-height: 30px;
          }
          > div:last-child {
            width: 1100px;
            padding: 0 10px;
            position: relative;
            .list {
              width: 900px;
              height: 30px;
              overflow: hidden;
              clear: left;
              .item,
              .ant-checkbox-group-item {
                width: 100px;
                height: 30px;
                float: left;
                line-height: 30px;
                color: #4d9cf1;
                overflow: hidden;
                position: relative;
                font-size: 12px;
                padding: 2px;
                cursor: pointer;
                &:hover {
                  color: @mallThemeColor;
                }
              }
            }
            .show-more {
              height: auto;
            }
            #conditionBtnFun();
          }
        }

        /*更多筛选条件*/
        .more-condition-options {
          margin: 5px;
          color: #4d9cf1;
          font-size: 12px;
          cursor: pointer;
          text-align: right;
          &:hover {
            color: #0165d1;
          }
        }
      }
    }
  }
}
