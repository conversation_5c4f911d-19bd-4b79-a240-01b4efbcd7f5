:global {
  /* 单独修改光华微信二维码popover样式 */
  .gh-wechat-popover {
    .ant-popover-arrow-content {
      --antd-arrow-background-color: #e02020;
    }
    .ant-popover-inner-content {
      background: #e02020;
      .gh-wechat {
        width: 108px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: #ffffff;
        font-size: 10px;
        img {
          width: 100%;
        }
      }
      .tip {
        color: #fff;
      }
    }
  }
}

.mainFooter {
  :global {
    border-top: 2px solid #e02020;
    // 底部快捷链接
    .footer-wrapper {
      width: @main-width;
      margin: 0 auto;
      box-sizing: border-box;
      padding-bottom: 54px;
      // 底部导航
      .footer-nav {
        display: flex;
        justify-content: space-between;
        padding: 38px 96px 0 14px;
        // 列项
        &-menu {
          .nav-title {
            font-size: @sub-title-size;
            font-weight: 700;
            color: @main-text-color;
            text-align: left;
          }
          // 快捷链接内容
          .nav-content {
            font-size: @base-text-size;
            font-weight: 500;
            text-align: left;
            list-style: none;
            margin-top: 4px;
            margin-bottom: 0 !important;
            .nav-link {
              padding: 2px 0;
              color: @main-text-color !important;
              &:first-child {
                padding: 0;
              }
            }
          }
        }
        // 联系我们
        .contract-us {
          height: 100%;
          padding-left: 120px;
          border-left: 1px solid #d0d0d0;
          .flex-col(flex-start, flex-start);
          .contract-title {
            color: #333333;
            font-size: 18px;
            font-weight: 700;
          }
          .contract-ul {
            li {
              .flex-row(normal, center);
              font-size: 16px;
              padding: 1px 0 2px;
              .title {
                position: relative;
                padding-left: 22px;
                i {
                  position: absolute;
                  top: 4px;
                  left: 1px;
                  width: 14px;
                  height: 14px;
                  display: inline-block;
                  &.icon-phone {
                    background: url("@@img/main-layout-sprites.png") -172px -10px;
                  }
                  &.icon-email {
                    background: url("@@img/main-layout-sprites.png") -206px -10px;
                  }
                  &.icon-fax {
                    background: url("@@img/main-layout-sprites.png") -240px -10px;
                  }
                  &.icon-chat {
                    background: url("@@img/main-layout-sprites.png") -274px -10px;
                    cursor: pointer;
                  }
                  &.icon-chat:hover {
                    background: url("@@img/main-layout-sprites.png") -138px -10px;
                    cursor: pointer;
                  }
                }
              }
              .phone {
                font-size: @base-text-size;
                font-weight: 700;
                color: @main-link-hover-color;
              }
            }
          }
        }
      }
      // 版权
      .footer-copy-right {
        margin: 39px 0 0 140px;
        display: flex;
        gap: 16px;
        .app-logo {
          width: 120px;
          height: 47px;
          cursor: pointer;
          display: inline-block;
          background: url("@@img/logo.svg") 50% 50% no-repeat;
        }
        .copy-right {
          color: #6d7278;
          font-size: 14px;
          font-weight: 350;
          line-height: 20px;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
        }
      }
    }
  }
}
