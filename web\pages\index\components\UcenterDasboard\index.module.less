@say-hi-font-size: 13px; // 用户-说Hi-字体大小
@user-quick-nav-font-size: 13px; // 用户-快捷导航-字体大小
@user-quick-nav-color: @main-text-color; // 用户-快捷导航-字体颜色

@message-item-font-size: 13px; // 消息-条目-字体大小
@message-item-color: @regular-text-color; // 消息-条目-字体颜色

.wrapper {
  height: 100%;
  :global {
    .ucenter-dashboard {
      width: 218px;
      height: 100%;
      position: relative;
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px;
      padding: 10px 16px;
      .user {
        float: left;
        padding-bottom: 4px;
        &-avatar-area {
          .avatar {
            float: left;
            display: inline-flex;
            width: 48px;
            height: 48px;
            background-size: cover;
            border-radius: 50%;
            align-items: center;
            justify-content: center;
          }
          .say-hi {
            float: left;
            margin: 8px 0 0 8px;
            font-size: @say-hi-font-size;
            line-height: 12px;
            color: #666;
            width: 105px;
            .hi-name {
              color: #333;
              .ellipsis(1);
            }
            .welcome {
              margin-top: 7px;
              color: #999;
            }
          }
        }
        &-auth-btn {
          float: left;
          height: 30px;
          width: 100%;
          margin: 10px auto;
          .ant-btn {
            width: 100%;
          }
          .ant-btn-primary:not([disabled]) {
            color: #fff;
            background-color: #ef1f1f;
            border-color: #ef1f1f;
            &:hover {
              background-color: #cd1626;
              border-color: #cd1626;
            }
          }
        }
        &-has-login {
          margin: 10px auto;
          width: 100%;
          float: left;
          height: 30px;
          font-size: 12px;
          line-height: 30px;
          text-align: center;
          background: #fff;
          border-radius: 15px;
          box-shadow: 0 0 6px 0 rgb(113 1 1 / 8%);
          border: 1px solid #f0f0f0;
          background: linear-gradient(45deg, rgba(239, 31, 31, 0.9) 0%, rgba(255, 77, 79, 0.85) 100%);
          a {
            color: #fff;
            .ellipsis(1);
            padding: 0 4px;
          }
        }
        &-quick-nav {
          float: left;
          border-bottom: 1px solid #f2f2f2;
          margin-top: 6px;
          .icon-subtitle {
            float: left;
            width: 50%;
            margin-bottom: 10px;
            text-align: center;
            padding: 2px;
            border-radius: 4px;
            &:hover {
              background-color: #dcdcdc;
              box-shadow: 0 0 6px 0 rgb(113 1 1 / 8%);
              .subtitle {
                color: #ef1f1f;
              }
            }
            .subtitle {
              display: block;
              font-size: @user-quick-nav-font-size;
              color: @user-quick-nav-color;
              -webkit-transition: color 0.1s linear;
              transition: color 0.1s linear;
            }
            .icon-img {
              width: 28px;
              height: 28px;
              display: inline-block;
              background-size: cover;
              &.footprint {
                background-image: url("@@img/home-color-footprint.svg");
              }
              &.im {
                background-image: url("@@img/home-color-message.png");
              }
              &.order {
                background-image: url("@@img/home-color-list.png");
              }
              &.cart {
                background-image: url("@@img/home-color-cart.png");
              }
            }
          }
        }
      }
      /*系统公告*/
      .system-message {
        clear: both;
        .ant-list-header,
        .ant-list-footer {
          padding: 0;
        }
        .ant-list-item {
          padding: 2px 0px;
        }
        .message-header {
          .flex-row(space-between, center);
          .title {
            color: @message-item-color;
          }
          .more {
            font-size: @message-item-font-size;
            &:hover {
              color: #ff1b1b;
              text-decoration: underline;
            }
          }
        }
        .message-item {
          font-size: @message-item-font-size;
          color: @message-item-color;
          cursor: pointer;
          a {
            .flex-row(space-between, center);
            width: 100%;
            .content-title {
              flex:1;
              .ellipsis(1);
            }
            .created-date {
              width: 54px;
              font-size: @mini-text-size !important;
              color: #999999;
            }
          }
          &:hover {
            a {
              .content-title {
                color: #ff1b1b;
                text-decoration: underline;
              }
            }
          }
        }
        // empty样式控制
        .ant-list-empty-text {
          padding: 0;
          .ant-empty-normal {
            margin: unset;
            transform: scale(0.64);
          }
        }
      }
    }
  }
}
