import React, { useEffect, useState } from "react";
import { Button, Space } from "antd";
import { EyeOutlined, DeleteOutlined } from "@ant-design/icons";
import { SizeType } from "antd/lib/config-provider/SizeContext";

interface UDOperationProps {
  size?: SizeType;
  needIcon?: boolean;
  row?: any;
  opt?: React.ReactNode;
  showEvent?: Function;
  delEvent?: Function;
}
// 列操作
const UDOperation = (props: UDOperationProps) => {
  const showDetailEvent = () => {
    console.log("ud 详情事件", props.row);
    props?.showEvent && props.showEvent(props.row);
  };
  const deleteEvent = () => {
    console.log("ud 删除事件", props.row);
    props?.delEvent && props.delEvent(props.row);
  };
  return (
    <>
      <Space>
        {props.opt ? props.opt : null}
        <Button ghost type="primary" onClick={showDetailEvent} icon={props.needIcon ? <EyeOutlined /> : null} size={props.size}>
          详情
        </Button>
        <Button ghost type="default" danger onClick={deleteEvent} icon={props.needIcon ? <DeleteOutlined /> : null} size={props.size}>
          删除
        </Button>
      </Space>
    </>
  );
};
UDOperation.defaultProps = {
  needIcon: false,
  size: "small",
};
export default UDOperation;
