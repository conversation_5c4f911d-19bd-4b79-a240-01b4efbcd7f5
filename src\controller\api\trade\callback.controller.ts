import { Controller, Get, Inject, Param, Query, ALL } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { ICallbackService } from "@/service/trade/callback.service";

@Controller("/papi/payment/cashier")
export class CallbackController extends BaseController {
  @Inject("TradeCallbackService")
  tradeCallbackService: ICallbackService;

  /**
   * <p>同步回调转发</p>
   *
   * @param paymentMethod /
   * @param returnUrl /
   * @param req
   */
  @Get("/callback/:paymentMethod")
  async syncCallbackRedirect(@Param("paymentMethod") paymentMethod: string, @Query("returnUrl") returnUrl: string, @Query(ALL) req: any) {
    const { ctx } = this;
    const res = await this.tradeCallbackService.syncCallbackRedirect(paymentMethod, req);
    console.info(`支付回调同步响应结果：%s,回调方法：${ctx?.req?.method ?? "NULL"}`, res);
    return ctx.redirect(returnUrl);
  }
}
