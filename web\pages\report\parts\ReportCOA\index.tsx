import { Col, Empty, Input, message, Row, Spin, Tabs, Tooltip } from "antd";
import React, { useState } from "react";
import style from "./index.module.less";
import crudGhtech from "@/apis/ghtech";
import { FileSearchOutlined } from "@ant-design/icons";
import { GHTECH_COA_PREFIX } from "@/constants/common";
import SvgIcon from "@/components/SvgIcon";

const ReportCOA = (props: { initValue?: string }) => {
  const [emptyText, setEmptyText] = useState("暂无数据，请搜索产品");
  const [loadingState, setLoadingState] = useState(false);
  const [productTabItems, setProductTabItems] = useState<any[]>();
  const { initValue } = props;

  const searchProductCoa = async (word: string) => {
    const productName = word.trim();
    if (!productName) {
      message.warn("请输入产品名称");
      return;
    }

    // if (productName.length < 2) {
    //   message.warn("至少输入2个字以上的产品名称");
    //   return;
    // }

    setLoadingState(true);
    const [err, res] = await crudGhtech
      .getProductCoa(productName)
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoadingState(false);
    if (err) {
      message.warn(err?.data?.message || "网络异常，请重试~");
      err.status === 500 && setEmptyText(`服务器异常，获取 "${productName}" 产品COA失败，请联系业务员或客服~`);
      return;
    }

    if (!res.data.length) {
      setEmptyText(`暂未找到${productName} 产品COA文件！`);
      setProductTabItems([]);
      return;
    }

    // 装载coa数据
    const temp = res.data.map(item => {
      let newReports: any[] = [];
      let oldReports: any[] = [];
      if (item.reportn) {
        newReports = item.reportn
          .split("$$$")
          .reverse()
          .map(report => {
            return {
              name: report.match(/([^/]+)\.([^.]+)$/)[1],
              path: `${GHTECH_COA_PREFIX.NEW}${report}`,
            };
          });
      }
      if (item.report) {
        oldReports = item.report
          .split("[---]")
          .reverse()
          .map(report => {
            const [name, path] = report.split("[###]");
            return {
              name: name,
              path: `${GHTECH_COA_PREFIX.OLD}${path}`,
            };
          });
      }
      return {
        id: item.id,
        title: item.title,
        coas: [...newReports, ...oldReports],
      };
    });
    setProductTabItems(
      temp.map((item, index: number) => {
        return {
          label: item.title,
          key: index + 1,
          children: (
            <div className="coa-result-box">
              <div className="product-wrapper" key={item.id}>
                <ul>
                  {item.coas.map((coa, idx) => {
                    return (
                      <li key={idx}>
                        <div className="coa-item">
                          <Tooltip placement="topLeft" title={coa.name} arrowPointAtCenter>
                            <a href={coa.path} target="_blank">
                              <SvgIcon iconClass="pdf-simple" />
                              &nbsp;{coa.name}
                            </a>
                          </Tooltip>
                        </div>
                      </li>
                    );
                  })}
                </ul>
                <div className="coa-show-more" style={{ display: "none" }}>
                  点击展开剩余项
                </div>
              </div>
            </div>
          ),
        };
      })
    );
  };

  return (
    <div className={style.wrapper}>
      <div className="coa">
        <div className="coa-search-bar">
          <Input.Group>
            <Row gutter={5}>
              <Col span={24}>
                <Input.Search
                  placeholder="请输入产品名称"
                  defaultValue={initValue}
                  enterButton={
                    <span>
                      <FileSearchOutlined />
                      &nbsp;查询COA
                    </span>
                  }
                  size="large"
                  onSearch={searchProductCoa}
                />
              </Col>
            </Row>
          </Input.Group>
        </div>
        <div className="coa-description">通过产品名称支持查询光华旗下产品的分析证书（COA）</div>
        <Spin spinning={loadingState}>
          {productTabItems?.length ? (
            <div className="result-box">
              <div className="result-tip">COA搜索结果：</div>
              <Tabs tabPosition="left" items={productTabItems} />
            </div>
          ) : (
            <Empty description={emptyText} />
          )}
        </Spin>
      </div>
    </div>
  );
}

ReportCOA.defaultProps = {
  initValue: ''
}

export default ReportCOA;
