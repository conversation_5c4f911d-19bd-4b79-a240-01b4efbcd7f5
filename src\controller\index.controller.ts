import { Controller, Get, Inject, Param } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { MidwayI18nService } from "@midwayjs/i18n";
import { IAdvService } from "@/service/spread/adv.service";
import { INewsService } from "@/service/ghtech/news.service";
import { INotifyService } from "@/service/platform/notifies.service";
import { IHomepageService } from "@/service/homepage.service";

@Controller("/")
export class IndexController extends BaseController {
  @Inject()
  i18nService: MidwayI18nService;

  @Inject("SpreadAdvService")
  advService: IAdvService;

  @Inject("NewService")
  newService: INewsService;

  @Inject("NotifiesService")
  notifyService: INotifyService;

  @Inject("HomepageService")
  homepageService: IHomepageService;

  @Get()
  async index(): Promise<void> {
    // 获取初始化数据
    const { ctx } = this;
    // 1、广告轮播
    const adv = await this.advService.getOverall(0);
    // 2、首页新闻数据 - {x}条
    const homeNewsData = [];
    // 获取首页图组新闻推荐数据
    const articleRecommendsRes = await this.newService.getArticleRecommends();
    // 3.获取首页板块数据
    const homePageDataRes = await this.homepageService.getHomePageData();
    const homePageData = (homePageDataRes.data || []).filter(plate => {
      const categories = JSON.parse(plate?.optionalJson || "[]").filter(cate => cate.show_state);
      plate.optionalJson = JSON.stringify(categories);
      plate.products = plate.products.filter(product => categories.some(category => category.classification === product.classification) || !product.classification);
      return plate.products.length;
    });

    // 首页数据
    ctx.indexData = {
      adv: adv?.data || [],
      homeNewsData,
      articleRecommendsData: articleRecommendsRes?.data || [],
      homePageDataArray: homePageData,
    };
    try {
      ctx.body = await render<Readable>(ctx, {
        stream: true,
        mode: "ssr",
      });
    } catch (error) {
      ctx.body = error;
    }
  }

  /** 站内公告-详情渲染页 */
  @Get("/notifies/:code")
  async notify(@Param("code") code: string): Promise<void> {
    const { ctx } = this;
    const res = await this.notifyService.getNotifyDetailByCode(code);
    ctx.notifyDetail = res.data;
    // 处理无公告内容、公告不存在的情况
    if (!res.data) {
      const originUrl = ctx.url;
      return ctx.redirect(`/404?origin=${originUrl}&message=${encodeURIComponent("公告已失效或不存在了，请再逛逛其它页面！")}`);
    }
    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/test2")
  async test2Api(): Promise<void> {
    return this.i18nService.translate("hello", {
      args: {
        username: "harry",
      },
    });
  }
}
