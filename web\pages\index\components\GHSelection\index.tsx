import React, { memo } from "react";
import style from "./index.module.less";
import ProductItem from "@/pages/index/parts/ProductItem";
import IndustryOrSelectionHeader from "@/pages/index/parts/IndustryOrSelectionHeader";
import { IHomepageDataType } from "@/typings/index.interface";
import commonConstant from "@/constants/common";
import { useOss } from "@/hooks/useOss";

/** 光华精选 */
export default memo(function GHSelection(props: { data: IHomepageDataType }) {
  // 板块数据
  const pageData: IHomepageDataType = props.data || {};
  const { generateOssFullFilepath } = useOss();

  // 光华精选-处理逻辑：固定2栏-分类推荐、新品上市
  const ghSelectionCateData = JSON.parse(pageData?.optionalJson || "[]").slice(0, 2);

  const ghSelectionData = ghSelectionCateData.map((item, index) => {
    const hasUrl = pageData.galleryImages?.split(",")[index];
    const imgUrl = hasUrl ? generateOssFullFilepath(hasUrl) : commonConstant.COMMON_IMAGE_PATHS.DEFAULT_PRODUCT;
    const subTitle = pageData.subTitle?.split(",")[index] || "精选推荐";
    return {
      title: item.name,
      subTitle,
      logo: imgUrl,
      products: pageData?.products?.filter(product => product.classification === item.classification),
    };
  });

  return (
    <div className={style.wrapper}>
      <div className="gh-selection">
        <IndustryOrSelectionHeader title={pageData.title} />
        <div className="gh-selection-container">
          {ghSelectionData.length &&
          ghSelectionData.map((item, idx) => (
            <div className="selection-cate" key={idx}>
              <div className="selection-logo">
                <div className="selection-info">
                  <span className="selection-title">{item.title}</span>
                  <span className="selection-sub-title">{item.subTitle}</span>
                </div>
                <img className="selection-img" src={item?.logo} alt="" />
              </div>
              {/* 产品选项 */}
              <ul className="products">
                {item.products.slice(0, 2).map((product, index) => (
                  <ProductItem layout="horizontal" key={index} product={product} />
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});
