import React, { memo, useEffect, useState } from "react";
import { orderLogisticsQueryDto } from "~/typings/data/order";
import crudOrderLogistics from "@/apis/order/logistics";
import TmModal from "@/components/TmModal";
import { Descriptions, Divider, Empty, Popover, Spin, Table, TableColumnsType } from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import style from "./index.module.less";

interface IOrderLogisticsModalProps extends orderLogisticsQueryDto {
  modalVisible: boolean; // 面板弹窗控制
  changeModalVisible: (bool: boolean) => void;
}

export default memo(function OrderLogisticsModal(props: IOrderLogisticsModalProps) {
  /* ======================================= useSate start======================================= */
  /* 物流信息 */
  const [orderDeliveryTmp, setOrderDeliveryTmp] = useState<any>(null);
  const [loadingState, setLoadingState] = useState(false);

  useEffect(() => {
    if (!props.modalVisible) {
      handleQueryLogisticsEvent();
    }
  }, [props.modalVisible]);
  const logisticsTableColumns: TableColumnsType<any> = [
    {
      title: "出库日期",
      align: "center",
      dataIndex: "outboundDate",
      width: 90,
    },
    {
      title: "出库单号",
      align: "center",
      dataIndex: "outboundSn",
      width: 150,
      ellipsis: true,
      render: text => (
        <>
          <Popover content={text}>{text}</Popover>
        </>
      ),
    },
    {
      title: "托运站",
      align: "center",
      dataIndex: "shipper",
      width: 75,
    },
    {
      title: (
        <>
          <Popover content={"汕头区号:0754"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          货运电话
        </>
      ),
      align: "center",
      dataIndex: "shipperPhone",
      width: 105,
    },
    {
      title: "业务员",
      align: "center",
      dataIndex: "salesman",
      width: 75,
    },
    {
      title: "公司名称",
      align: "center",
      dataIndex: "company",
      width: 210,
      ellipsis: true,
    },
    {
      title: "件数",
      align: "center",
      dataIndex: "shipperQuantity",
      width: 50,
    },
    {
      title: "重量",
      align: "center",
      dataIndex: "shipperWeight",
      width: 50,
    },
    {
      title: (
        <>
          <Popover content={"部份中转地区无法提供到站电话,请打发货电话查询"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          物流联系方式
        </>
      ),
      align: "center",
      dataIndex: "shipperArrivalPhoneRemark",
      width: 125,
    },
    {
      title: (
        <>
          <Popover content={"注意：货运到货不含当天发货的时间"}>
            <QuestionCircleOutlined style={{ cursor: "pointer" }} />
            &nbsp;
          </Popover>
          预计到货天数
        </>
      ),
      align: "center",
      dataIndex: "shipperArrivalDays",
      width: 120,
    },
    {
      title: "货运路线",
      align: "center",
      dataIndex: "shipperRoute",
    },
  ];
  /* ======================================= 方法 start======================================= */
  // 查询物流信息
  const handleQueryLogisticsEvent = async () => {
    if (!props.orderNo) {
      return;
    }
    setLoadingState(true);
    const reqParams: orderLogisticsQueryDto = {
      memberId: props?.memberId,
      orderNo: props?.orderNo,
    };
    const [err, res] = await crudOrderLogistics
      .queryDelivery(reqParams)
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoadingState(false);
    if (!err) {
      setOrderDeliveryTmp(res.data);
    }
  };
  const handleCloseModal = () => {
    props.changeModalVisible(false);
  };
  const renderContent = (orderDelivery: any) => {
    return (
      <>
        <div className={style.wrapper}>
          <div className="order-delivery">
            <Spin spinning={loadingState}>
              <div className="search-result" style={orderDelivery ? { border: "solid 1px #f8f8f8", background: "rgba(248,248,248,0.45)" } : { background: "#f8f8f8" }}>
                {orderDelivery ? (
                  <>
                    <Descriptions title="" column={3} bordered>
                      <Descriptions.Item span={1} label="订单号">
                        <span>{orderDelivery?.orderNo || "-"}</span>
                      </Descriptions.Item>
                      <Descriptions.Item span={1} label="客户ID">
                        <span>{orderDelivery?.memberId || "-"}</span>
                      </Descriptions.Item>
                      <Descriptions.Item span={1} label="下单时间">
                        <span>{orderDelivery?.createdDate || "-"}</span>
                      </Descriptions.Item>
                    </Descriptions>
                    <Divider orientation="left">物流明细</Divider>
                    <Table rowKey="id" size="small" bordered={false} className="logistics-table" columns={logisticsTableColumns} dataSource={orderDelivery.logistics} pagination={false} />
                  </>
                ) : (
                  <Empty />
                )}
              </div>
            </Spin>
          </div>
        </div>
      </>
    );
  };
  return (
    <>
      <TmModal
        title={`订单物流「${props.orderNo}」`}
        open={props.modalVisible}
        footer={false}
        width={1200}
        keyboard
        maskClosable
        centered
        onCancel={() => handleCloseModal()}
        onOk={() => handleCloseModal()}
        content={renderContent(orderDeliveryTmp)}
      />
    </>
  );
});
