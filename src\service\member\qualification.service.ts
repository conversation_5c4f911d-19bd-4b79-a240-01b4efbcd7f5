import { qualificationAddDto, qualificationUpdateDto } from "~/typings/data/member/qualification";

/** 客户资质 */
export interface IQualificationService {
  /**
   * 获取客户资质
   * @param memberId
   */
  getMemberQualifications: (memberId: string) => Promise<any>;

  addQualification: (data: qualificationAddDto) => Promise<any>;

  updateQualification: (qualificationId: number, data: qualificationUpdateDto) => Promise<any>;
}
