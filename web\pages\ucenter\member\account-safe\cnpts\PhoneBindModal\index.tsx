import React, { memo, useRef, useState } from "react";
import style from "./index.module.less";
import { <PERSON><PERSON>, Button, Col, Form, Input, message, notification, Row, Spin } from "antd";
import { FormInstance } from "antd/es/form";
import { validateMobile } from "@/utils/form-valid.util";
import { MailOutlined } from "@ant-design/icons";
import TmModal from "@/components/TmModal";
import SmsCaptcha from "@/components/SmsCaptcha";
import { rebindPhone } from "@/apis/member/member";

interface IPhoneBindModalProps {
  modalVisible: boolean;
  changeModalVisible: (bool: boolean) => void;
  callbackFunc?: any;
  title?: string;
  originData?: string;
}
export default memo(function PhoneBindModal(props: IPhoneBindModalProps) {
  const { modalVisible, title, changeModalVisible, callbackFunc, originData } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const currentFormRef = useRef<FormInstance>(null);
  const validatePhoneRepeat = async (obj: any, value: any) => {
    if (value && originData === value) {
      throw "请不要输入当前已绑定的手机号";
    }
    return await Promise.resolve();
  };
  const formRules = {
    phone: [{ required: true, whitespace: true, message: "手机号不能为空" }, { validator: validateMobile }, { validator: validatePhoneRepeat }],
    captcha: [{ required: false, whitespace: true, message: "图像验证码不能为空!" }],
  };
  /** 取消操作 */
  const handleCancel = () => {
    changeModalVisible(false);
    form.resetFields();
  };
  const onFinish = async (form: any) => {
    setLoading(true);
    const [err, res] = await rebindPhone({
      newPhone: form.phone,
      smsCode: form.smsCode,
    })
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoading(false);
    if (err) {
      return notification.error({ message: err?.data?.message ?? "请求出错了" });
    }
    handleCancel();
    message.success("处理成功，账号可使用新手机登录！" || res?.message);
    setLoading(false);
    if (callbackFunc) {
      setTimeout(() => {
        callbackFunc();
      }, 1000);
    }
  };
  const renderFormContent = () => (
    <>
      <Spin className={style.wrapper} spinning={loading}>
        <div className="member-edit">
          {originData ? <Alert style={{ marginBottom: "10px" }} message={`当前已绑定手机号为：${originData}`} type="warning" /> : null}
          <Form
            ref={currentFormRef}
            className="member-form"
            name={"member-edit-form"}
            form={form}
            scrollToFirstError
            onFinish={onFinish}
            autoComplete="off"
            labelAlign="right"
            size="large"
            labelCol={{ style: { width: 100, whiteSpace: "normal", textAlignLast: "justify" } }}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="手机号码" name="phone" rules={formRules.phone} tooltip="请填入需要绑定的手机号">
                  <Input allowClear prefix={<MailOutlined />} placeholder="请输入手机号" />
                </Form.Item>
              </Col>
              <Col span={24}>
                {/* ========== 短信验证码组件 ========== */}
                <SmsCaptcha uuid={"bindAccount"} border countdownSeconds={60} />
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item colon={false} className="email-form-submit">
                  <Button type="default" size="middle" onClick={() => handleCancel()}>
                    取消
                  </Button>
                  <Button type="primary" danger htmlType="submit" size="middle">
                    确认
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Spin>
    </>
  );
  return modalVisible ? (
    <TmModal
      maskClosable={false}
      keyboard={false}
      title={title}
      width={580}
      centered={true}
      open={modalVisible}
      content={renderFormContent()}
      footer={null}
      onOk={handleCancel}
      onCancel={() => handleCancel()}
    />
  ) : null;
});
