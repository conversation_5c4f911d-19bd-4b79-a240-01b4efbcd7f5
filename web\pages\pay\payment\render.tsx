import React, { useContext, useEffect, useState } from "react";
import { IContext, SProps } from "ssr-types-react";
import style from "./index.module.less";
import { Button, Card, Descriptions, Modal, notification, Radio, RadioChangeEvent } from "antd";
import SvgIcon from "@/components/SvgIcon";
import { LeftOutlined } from "@ant-design/icons";
import { useStoreContext } from "ssr-common-utils";
import { price2Thousand } from "@/utils/price-format.util";
import walletImg from "@/assets/images/wallet.png";
import tradeCrud from "@/apis/trade/trade";
import QRCode from "qrcode.react";
import { useLoading } from "@/hooks/useLoading";
import TmConfirmModal from "@/components/TmConfirmModal";
import { ORDER_STATE_MAPPING } from "@/constants/order";

export default function Payment(props: SProps) {
  /* ======================================= useSate start======================================= */
  const { state } = useContext<IContext>(useStoreContext());
  const { order, bankAccount, wallet, memberContact } = state;

  useEffect(() => {
    validOrder();
  }, []);
  const [showPayModal, setShowPayModal] = useState<boolean>(false);
  const [selectedPayment, setSelectedPayment] = useState<string>("alipay");
  const [renderQrcode, setRenderQrcode] = useState<React.ReactNode>(null);
  /* ======================================= useSate end======================================= */
  const paymentOptions = [
    {
      label: (
        <div className="box-item">
          <img src="https://ss3.bdstatic.com/yrwDcj7w0QhBkMak8IuT_XF5ehU5bvGh7c50/logopic/a9936a369e82e0c6c42112674a5220e8_fullsize.jpg" alt="" />
          <span>支付宝支付</span>
        </div>
      ),
      value: "alipay",
    },
    {
      label: (
        <div className="box-item">
          <img src={walletImg} alt="" />
          <span>余额支付</span>
          {selectedPayment === "wallet" ? <span className="wallet-balance">账户余额:￥{price2Thousand(wallet?.memberWallet ?? 0)}</span> : null}
        </div>
      ),
      value: "wallet",
    },
  ];
  const paymentDict = {
    alipay: {
      name: "支付宝支付",
      aliasName: "支付宝",
      wise: "/images/pay/alipay-wise.png",
      qrcode: renderQrcode,
      noModal: false,
    },
    wallet: {
      name: "余额支付",
      aliasName: "钱包",
      noModal: true,
      wise: "/images/pay/wechat-wise.png",
      qrcode: "/images/qrcode9.bmp",
    },
  };
  /* ======================================= hook start ======================================= */
  const useLoadingHook = useLoading();
  /* ======================================= hook end ======================================= */
  /* ======================================= method start======================================= */
  // 判断订单状态
  const validOrder = () => {
    if (!order || order?.orderState !== ORDER_STATE_MAPPING["BUSINESS_AUDIT"].value) {
      return Modal.error({
        content: "请求非支付状态，请不要进行支付操作，即将离开此页面！",
        type: "error",
        centered: true,
        closable: false,
        maskClosable: false,
        keyboard: false,
        okText: "前往首页",
        onOk: async () => {
          window.location.href = "/";
        },
      });
    }
  };
  // 展示支付二维码弹窗
  const showPayModalEvent = async () => {
    switch (selectedPayment) {
      case "wallet":
        // 账户余额支付-判断余额是否满足扣款
        if ((wallet?.memberWallet ?? 0) < order.totalMoney) {
          notification.error({ message: "抱歉当前钱包余额不足支付该订单，请选择其它支付方式或进行余额充值后刷新重试！" });
          return false;
        }
        await walletPayHandle();
        break;
      case "alipay":
        // 支付宝支付
        await alipayH5payOrWebPay();
        break;
      case "wechat":
        notification.warning({ message: "该支付功能暂未开放！" });
        break;
      default:
        notification.error({ message: "不支持当前支付方式，请选择其它支付方式！" });
        break;
    }
  };
  // 生成二维码
  const renderPayQrcode = (value: string) => {
    setRenderQrcode(<QRCode id="qrCode" value={value} size={235} style={{ margin: "auto" }} />);
  };
  // 发起余额支付
  const walletPayHandle = async () => {
    const confirm = await TmConfirmModal({
      title: "付款提醒",
      content: "确定使用账户余额进行支付？",
    });
    if (confirm !== "confirm") {
      return;
    }

    useLoadingHook.showLoading("支付处理中...");
    const [err, res] = await tradeCrud
      .payGateway(selectedPayment, { sn: order.orderNo, paymentClient: "WEB" })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      useLoadingHook.hideLoading();
      return notification.warning({ message: err.data.message || "发起余额失败，请稍后再试！" });
    }
    notification.success({ message: "订单支付成功，即将跳转到订单详情页！" || res.message });
    setTimeout(() => {
      useLoadingHook.hideLoading();
      // 跳转到订单详情页
      window.location.href = `/ucenter/trade/order/${order.orderNo}`;
    }, 2000);
  };
  // 调试支付宝H5支付
  const alipayH5payOrWebPay = async () => {
    const confirm = await TmConfirmModal({
      title: "付款提醒",
      content: "确定使用支付宝进行支付？",
    });
    if (confirm !== "confirm") {
      return;
    }

    useLoadingHook.showLoading("支付请求中...");
    // NATIVE WEB  H5
    const [err, res] = await tradeCrud
      .payGateway(selectedPayment, { sn: order.orderNo, paymentClient: "WEB" })
      .then(res => [null, res])
      .catch(err => [err, null]);
    useLoadingHook.hideLoading();
    if (err) {
      return notification.warning({ message: err.data.message || "调起支付失败，请稍后再试！" });
    }
    // 扫码支付使用以下弹窗
    // renderPayQrcode(res.data);
    // setShowPayModal(true);
    const div = document.createElement("formdiv");
    div.innerHTML = res.data.data; // 支付宝-支付表单-多一层data包装
    document.body.appendChild(div);
    document.forms["punchout_form"].setAttribute("target", "_self");
    document.forms["punchout_form"].submit();
    div.remove();
  };
  // 支付方式变更监听
  const onPaymentChangeEvent = (e: RadioChangeEvent) => {
    setSelectedPayment(e.target.value);
  };
  /* ======================================= method end======================================= */

  return (
    <>
      <div className={style.wrapper}>
        <div className="paymentWrapper">
          <div className="payment-head-box">
            <div className="label">
              订单提交成功，请尽快付款！
              <span className="need-pay">
                应付金额 <span className="price">￥{price2Thousand(order.totalMoney)}</span>&nbsp;元
              </span>
            </div>
            <div className="item-box">
              <div className="sub-tips">请您尽快完成支付，否则订单会被自动取消</div>
              <div className="sub-tips">订单号：{order.orderNo}</div>
              <div className="sub-tips" style={{ display: "none" }}>
                支付倒计时：xx小时xx分钟xx秒
              </div>
            </div>
            {/* 回到订单列表快捷键 */}
            <a className="goto-my-order" href="/ucenter/trade/order">
              <Button type="link" danger>
                前往我的订单
              </Button>
            </a>
          </div>
          {/* 支付宝|微信|网银支付 */}
          <Card title={<span className="pay-card-item-title">支付宝 | 微信 | 余额支付 | 银联支付</span>} className="pay-card-item" bodyStyle={{ padding: "0" }} bordered>
            <div className="payment-content-box">
              <div className="item-box">
                <Radio.Group value={selectedPayment} options={paymentOptions} onChange={onPaymentChangeEvent} />
              </div>
            </div>
            <div className="payment-footer-box">
              <div className="item-box">
                <div className="confirm-btn" onClick={async () => await showPayModalEvent()}>
                  确认支付
                </div>
              </div>
            </div>
          </Card>
          {/* 银行转帐与汇款 */}
          <Card title={<span className="pay-card-item-title">银行转帐与汇款</span>} className="pay-card-item" bodyStyle={{ padding: "0" }} bordered>
            <Descriptions
              column={1}
              bordered
              size="small"
              contentStyle={{
                fontWeight: 400,
                color: "#6D7278",
              }}
              labelStyle={{
                width: 175,
                fontWeight: 400,
                color: "#6D7278",
              }}
            >
              <Descriptions.Item label="开户银行">{bankAccount.bankName}</Descriptions.Item>
              <Descriptions.Item label="开户名">{bankAccount.accountName}</Descriptions.Item>
              <Descriptions.Item label="银行帐号">{bankAccount.accountNo}</Descriptions.Item>
              <Descriptions.Item label={<span className="tip">备注信息</span>}>
                <span className="tip">易购订单号&nbsp;{order.orderNo}</span>
              </Descriptions.Item>
            </Descriptions>
            <div className="be-careful">
              注: 汇款时请务必在汇款单备注栏填写订单编号，请参考上面 <b>“备注信息”</b> 示例.
            </div>
          </Card>
          {/* 货到付款 */}
          <Card title={<span className="pay-card-item-title">货到付款</span>} className="pay-card-item" bodyStyle={{ padding: "0" }} bordered>
            <div className="be-careful2">您还不是信用客户, 不能享受货到付款服务. 如需申请信用帐户,请将以下资料扫描复制件发送到邮箱: { memberContact.email || '暂无，请直接联系您的商务经理或客服' }</div>
            <Descriptions
              column={1}
              bordered
              size="small"
              contentStyle={{
                fontWeight: 400,
                color: "#6D7278",
              }}
              labelStyle={{
                width: 175,
                fontWeight: 400,
                color: "#6D7278",
              }}
            >
              <Descriptions.Item label="公司客户">公司营业执照、组织机构代码证、税务登记证</Descriptions.Item>
              <Descriptions.Item label="科研单位">身份证复印件、单位邮箱地址或其它证明文件</Descriptions.Item>
            </Descriptions>
            <div className="be-careful">如果您想电话申请, 请拨打: { memberContact.telephone || memberContact.phone || '020-84382888转8836' }</div>
          </Card>
        </div>
        {/* 支付弹窗 */}
        <Modal centered title={paymentDict[selectedPayment].name} open={showPayModal} onCancel={() => setShowPayModal(false)} width={850} footer={null} className="paymethod-modal">
          <p className="tip">
            距离二维码过期还有<span className="second">120</span>秒
          </p>
          <p className="tip">过期后系统自动刷新二维码信息</p>
          <div className="canvas">{selectedPayment === "alipay" ? paymentDict[selectedPayment].qrcode : <img src={paymentDict[selectedPayment].qrcode} alt="" />}</div>
          <div className="intro">
            <SvgIcon iconClass="scan" className="scan" />
            <p className="tip2">
              请使用&nbsp;<span className="paymethod-text">{paymentDict[selectedPayment].aliasName}</span>&nbsp;扫一扫
            </p>
            <p className="tip2">扫描二维码支付</p>
          </div>
          <p className="go-back" onClick={() => setShowPayModal(false)}>
            <LeftOutlined />
            &nbsp;选择其他支付方式
          </p>
          <div className="paymethod-img">
            <img src={paymentDict[selectedPayment].wise} alt="" />
          </div>
        </Modal>
      </div>
    </>
  );
}
