// 公用全局变量
@font-size-10: 10px;
@font-size-12: 12px;
@font-size-14: 14px;
@font-size-16: 16px;
@font-size-18: 18px;
@font-size-20: 20px;
@font-size-22: 22px;
@font-size-24: 24px;
@font-size-30: 30px;
@font-size-32: 32px;
@font-size-46: 46px;

// 页面显示内容宽度
@main-width: 1200px;
// 通用板块上边距
@plate-margin-top: 16px;

// 颜色
@secondary-text-color: #e2e2e2; // 辅助文本颜色
@base-black-color: #000000; // 基础黑色
@app-bg-color: #f2f2f2; // app背景颜色
@auxiliary-bg-color: #f4f4f4; // 辅助背景颜色
@main-bg-color-white: #ffffff; // 主背景颜色
@main-text-color: #333333; // 文本主色
@regular-text-color: #545557; // 常规文字颜色
@main-text-color-white: #ffffff; // 白色文本颜色
@main-link-hover-color: #e02020; // 链接-hover-颜色
@main-link-active-color: #e02020; // 链接-激活-颜色
@mallThemeColor: #e02020;

// 字体大小
@headline-size: @font-size-32; // 大标题-专栏大标题
@main-title-size: @font-size-24; // 主标题
@title-size: @font-size-20; // 普通标题
@sub-title-size: @font-size-18; // 小标题
@base-text-size: @font-size-16; // 正文
@sub-text-size: @font-size-14; // 二级正文
@auxiliary-text-size: @font-size-12; // 辅助文字
@mini-text-size: @font-size-10; // 最小字体-提示类文字大小
