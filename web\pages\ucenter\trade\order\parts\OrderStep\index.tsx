import React, { memo, useEffect, useState } from "react";
import { Steps } from "antd";
import { StepProps } from "antd/lib/steps";

interface IOrderStepProps {
  orderState: number;
  createTime?: string;
}

/** 订单状态步骤器 */
export default memo(function OrderStep({ orderState = 10, createTime }: IOrderStepProps) {
  const [currentState, setCurrentState] = useState(0);
  const MY_ORDER_STATE = {
    CANCEL: { value: -1, desc: "订单已取消" },
    BUILD: { value: 0, desc: "待付款" },
    PAID: { value: 10, desc: "待发货" },
    IN_TRANSIT: { value: 50, desc: "运输中" },
    FINISH: { value: 70, desc: "已完成" },
  };
  const [orderStepOptions, setOrderStepOptions] = useState<StepProps[]>([
    {
      title: "下单成功",
      status: "finish",
    },
  ]);

  const checkItemState = state => {
    if (orderState === state) {
      return "process";
    } else if (orderState < state) {
      return "wait";
    } else {
      return "finish";
    }
  };

  const orderNormalSteps: StepProps[] = [
    {
      title: MY_ORDER_STATE.BUILD.desc,
      status: checkItemState(MY_ORDER_STATE.BUILD.value),
    },
    {
      title: MY_ORDER_STATE.PAID.desc,
      status: checkItemState(MY_ORDER_STATE.PAID.value),
    },
    {
      title: MY_ORDER_STATE.IN_TRANSIT.desc,
      status: checkItemState(MY_ORDER_STATE.IN_TRANSIT.value),
    },
    {
      title: MY_ORDER_STATE.FINISH.desc,
      status: checkItemState(MY_ORDER_STATE.FINISH.value),
    },
  ];

  useEffect(() => {
    setCurrentState(Object.values(MY_ORDER_STATE).findIndex(item => item.value === orderState));
    if (orderState === -1) {
      setOrderStepOptions([
        ...orderStepOptions,
        {
          title: "已取消",
          status: "finish",
        },
      ]);
    } else {
      setOrderStepOptions([...orderStepOptions, ...orderNormalSteps]);
    }
  }, []);

  return <Steps current={currentState} status="error" items={orderStepOptions} />;
});
