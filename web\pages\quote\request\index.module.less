.wrapper {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  :global {
    .quote-card {
      .ant-card-head {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-bottom: none;
        
        .ant-card-head-title {
          color: #fff;
          font-size: 18px;
          font-weight: 600;
        }
      }

      .ant-card-body {
        padding: 30px;
      }
    }

    .quote-steps {
      margin-bottom: 40px;
      
      .ant-steps-item-finish .ant-steps-item-icon {
        background-color: #52c41a;
        border-color: #52c41a;
      }
      
      .ant-steps-item-process .ant-steps-item-icon {
        background-color: #1890ff;
        border-color: #1890ff;
      }
    }

    .step-content {
      min-height: 400px;
      margin-bottom: 30px;

      .upload-step {
        .template-section {
          text-align: center;
          padding: 30px;
          background: #fafafa;
          border-radius: 8px;
          margin-bottom: 20px;

          h3 {
            color: #333;
            margin-bottom: 10px;
          }

          p {
            color: #666;
            margin-bottom: 20px;
          }
        }

        .upload-section {
          h3 {
            color: #333;
            margin-bottom: 20px;
          }

          .upload-dragger {
            padding: 40px;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            background: #fafafa;
            transition: all 0.3s;

            &:hover {
              border-color: #1890ff;
              background: #f0f8ff;
            }

            .ant-upload-drag-icon {
              font-size: 48px;
              color: #1890ff;
            }

            .ant-upload-text {
              font-size: 16px;
              color: #333;
              margin: 16px 0 8px;
            }

            .ant-upload-hint {
              color: #999;
              font-size: 14px;
            }
          }
        }
      }

      .final-step {
        text-align: center;
        padding: 40px;

        h3 {
          color: #333;
          margin-bottom: 20px;
          font-size: 20px;
        }

        p {
          color: #666;
          margin-bottom: 30px;
          font-size: 16px;
        }

        .ant-btn {
          height: 50px;
          font-size: 16px;
        }
      }
    }

    .step-actions {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      .ant-btn {
        margin: 0 8px;
        min-width: 100px;
        height: 40px;
      }
    }

    // 基本信息表单样式
    .basic-info-form {
      max-width: 600px;
      margin: 0 auto;

      .ant-form-item-label > label {
        font-weight: 500;
      }

      .ant-input,
      .ant-input-number,
      .ant-select-selector {
        border-radius: 6px;
      }

      .required-mark {
        color: #ff4d4f;
        margin-right: 4px;
      }
    }

    // 产品匹配结果样式
    .match-result {
      .result-summary {
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 20px;

        .summary-item {
          display: inline-block;
          margin-right: 30px;
          
          .label {
            color: #666;
            margin-right: 8px;
          }
          
          .value {
            font-weight: 600;
            color: #333;
            
            &.success {
              color: #52c41a;
            }
            
            &.warning {
              color: #faad14;
            }
            
            &.error {
              color: #ff4d4f;
            }
          }
        }
      }

      .match-table {
        .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
        }

        .status-tag {
          &.matched {
            background: #f6ffed;
            border-color: #b7eb8f;
            color: #52c41a;
          }
          
          &.not-matched {
            background: #fff2e8;
            border-color: #ffbb96;
            color: #fa8c16;
          }
        }
      }

      .unmatched-section {
        margin-top: 30px;
        
        .ant-alert {
          margin-bottom: 16px;
        }
      }
    }
  }
}
