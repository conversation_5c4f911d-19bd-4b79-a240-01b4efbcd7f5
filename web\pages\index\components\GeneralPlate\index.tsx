import React, { useRef, useState } from "react";
import style from "./index.module.less";
import { LeftOutlined, RightOutlined, CaretRightOutlined, CaretLeftOutlined } from "@ant-design/icons";
import ThemeRenderHeader from "@/pages/index/parts/ThemeRenderHeader";
import RenderThemeLogo from "@/pages/index/parts/ThemeRenderLogo";
import ProductItem from "@/pages/index/parts/ProductItem";
import { Carousel } from "antd";
import type { CarouselRef } from "antd/lib/carousel";
import { IHomepageDataType } from "@/typings/index.interface";

// 通用-无分类-板块
export default function GeneralPlate(props: { data: IHomepageDataType }) {
  // 板块数据
  const pageData: IHomepageDataType = props.data || {};
  const renderLogoOptions = {
    title: pageData.title,
    subTitle: pageData.subTitle,
    img: pageData.headImage,
    bgColor: "#F33B46",
    color: "#FFFFFF",
    description: pageData.description,
  };

  const hotRecmdProducts = pageData.products;
  // 内部产品列的分页处理
  const pageSize = 5;
  const pages = Math.ceil(hotRecmdProducts.length / pageSize);
  const carouselRef = useRef<CarouselRef>(null);

  // 修正切换逻辑
  const [currentPage, setCurrentPage] = useState(0);
  const handleNext = () => {
    setCurrentPage((prevPage) => (prevPage + 1) % pages);
  };
  const handlePrev = () => {
    setCurrentPage((prevPage) => (prevPage - 1 + pages) % pages);
  };

  const renderHeaderMore = () => {
    return (
      <div className="general-plate-more">
        {hotRecmdProducts.length > pageSize && pageData?.moreLink ? (
          <span className="general-plate-btn">
            <LeftOutlined className="recmd-btn" onClick={handlePrev} />
            <RightOutlined className="recmd-btn" onClick={handleNext} />
          </span>
        ) : null}
        {pageData.moreLink && (
          <a href={pageData.moreLink} className="header-more">更多</a>
        )}
      </div>
    );
  };

  return (
    <div className={style.wrapper}>
      {!!hotRecmdProducts.length && (
        <div className="general-plate">
          <ThemeRenderHeader title={renderLogoOptions.title} moreRender={renderHeaderMore()} />
          <div className="general-plate-container">
            {RenderThemeLogo(renderLogoOptions)}
            <div className="product-carousel">
              <Carousel dots={false} effect="fade" autoplay={false} ref={carouselRef}>
                {Array.from({ length: pages }).map((item, index) => (
                  <ul key={index} className="recmd-page">
                    {hotRecmdProducts.slice((currentPage * pageSize), (currentPage + 1) * pageSize).map((product, index) => (
                      <ProductItem layout="vertical" key={index} product={product} />
                    ))}
                  </ul>
                ))}
              </Carousel>
              {/* 自定义箭头 */}
              <CaretRightOutlined className="arrow-left" onClick={handleNext} />
              <CaretLeftOutlined className="arrow-right" onClick={handlePrev} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
