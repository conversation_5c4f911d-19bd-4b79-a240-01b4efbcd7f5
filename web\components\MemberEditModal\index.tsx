import React, { memo, useEffect, useRef, useState } from "react";
import { Button, Form, Input, message, notification, Select, Spin, Row, Col } from "antd";
import { GENDER } from "@/constants/member";
import style from "./index.module.less";
import TmAreaCascade from "@/components/TmAreaCascade";
import TmModal from "@/components/TmModal";
import crudMember from "@/apis/member/member";
import type { FormInstance } from "antd/es/form";

interface IEditModalProps {
  modalVisible: boolean;
  changeModalVisible: (bool: boolean) => void;
  callbackFunc?: any;
  title?: string;
}

const formRules = {
  companyName: [{ required: true, message: "请填写公司名称或单位名称" }],
  contactName: [{ required: true, message: "联系人不能为空" }],
  contactPhone: [
    { required: true, message: "联系人手机号不能为空" },
    { size: 11, message: "请填入11位手机号" },
    { pattern: /^1([3-9][0-9])\d{8}$/, message: "请输入正确格式的手机号" },
  ],
  phone: [
    { required: false, message: "联系手机号不能为空" },
    { size: 11, message: "请填入11位手机号" },
    { pattern: /^1([3-9][0-9])\d{8}$/, message: "请输入正确格式的手机号" },
  ],
  email: [{ pattern: /[a-zA-Z0-9]+([-_.][A-Za-zd]+)*@([a-zA-Z0-9]+[-.])+[A-Za-zd]{2,5}$/, message: "请输入正确格式的邮箱地址" }],
  gender: [{ required: true, message: "请选定性别" }],
  contactAddress: [{ required: true, message: "街道地址不能为空" }],
  qq: [{ pattern: /[1-9][0-9]{4,}/, message: "请输入正确格式的qq号" }],
  area: [{ required: true, message: "请选择公司所在省市区" }],
};

export default memo(function MemberEditModal(props: IEditModalProps) {
  const { modalVisible, title, changeModalVisible, callbackFunc } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const areaCascadeRef = useRef<any>(null);
  const currentFormRef = useRef<FormInstance>(null);
  const [memberInfo, setMemberInfo] = useState<any>(null);

  useEffect(() => {
    modalVisible && initMemberData();
  }, [modalVisible]);

  const initMemberData = async () => {
    const [err, res] = await crudMember
      .getMemberDetail()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      handleCancel();
      notification.error({ message: err.data.message || "获取客户资料失败，请重试！" });
      return;
    }

    // 初始化省市区变量
    const memberInfo = res.data;
    // 如果原始数据没有省份id，代表没有省市区信息。清空area选项
    if (memberInfo?.contactProvinceId) {
      memberInfo.area = [memberInfo?.contactProvinceId, memberInfo?.contactCityId, memberInfo?.contactDistrictId];
    }
    areaCascadeRef?.current?.setTargetValFunc(memberInfo.area);
    setMemberInfo(memberInfo);
    form.setFieldsValue(memberInfo);
  };

  /** 省市区选择回调 */
  const handleMonitorAreaChange = area => {
    form.setFieldValue("area", area);
  };

  /** 取消操作 */
  const handleCancel = () => {
    changeModalVisible(false);
    form.resetFields();
  };

  const onFinish = async (memberInfo: any) => {
    // 处理省市区信息
    if (memberInfo.area) {
      memberInfo?.area[0] && (memberInfo.contactProvinceId = memberInfo?.area[0]);
      memberInfo.area[1] && (memberInfo.contactCityId = memberInfo.area[1]);
      memberInfo.area[2] && (memberInfo.contactDistrictId = memberInfo.area[2]);
    }

    setLoading(true);
    const [err, res] = await crudMember
      .modifyMember(memberInfo)
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoading(false);
    if (err) {
      notification.error({ message: err?.data?.message || "修改失败" });
      return;
    }

    message.success(res.message || "修改成功!");
    handleCancel();
    if (callbackFunc) {
      callbackFunc();
    }
  };

  const cancelSubmitEvent = () => {
    handleCancel();
  };

  /* ======================================= method end======================================= */
  const renderFormContent = () => (
    <>
      <Spin className={style.wrapper} spinning={loading}>
        <div className="member-edit">
          <Form
            ref={currentFormRef}
            className="member-form"
            name={"member-edit-form"}
            form={form}
            scrollToFirstError
            onFinish={onFinish}
            autoComplete="off"
            labelCol={{ style: { width: 100, whiteSpace: "normal", textAlign: "right" } }}
            initialValues={{ gender: 0 }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="手机号" name="phone" rules={formRules.phone} tooltip={`换绑手机号，前往 个人中心/账户安全/换绑手机`}>
                  <Input placeholder="请输入手机号" disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Email" name="email" rules={formRules.email} tooltip="换绑邮箱，前往 个人中心/账户安全/换绑邮箱">
                  <Input placeholder="请输入邮箱地址" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="联系人" name="contactName" rules={formRules.contactName} tooltip="请务必填写联系人全名">
                  <Input placeholder="请输入您的姓名" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="性别" name="gender" rules={formRules.gender}>
                  <Select placeholder="请选择性别">
                    {Object.values(GENDER).map((item, idx) => {
                      return (
                        <Select.Option key={idx} value={item.value}>
                          {item.desc}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="联系手机" name="contactPhone" rules={formRules.contactPhone} tooltip="填写联系手机号码">
                  <Input placeholder="填写联系手机号码" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="职务" name="contactPeopleJob">
                  <Input placeholder="填写您的职务" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="固话" name="telephone">
                  <Input placeholder="请输入联系固定电话" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="邮编" name="postalCode">
                  <Input placeholder="请输入邮政编码" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="传真" name="fax">
                  <Input placeholder="请输入传真号码" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="QQ" name="qq">
                  <Input placeholder="请输入QQ号" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item label="账户昵称" name="nickname" tooltip="账户昵称">
              <Input placeholder="请填写账户昵称" />
            </Form.Item>
            <Form.Item
              label="公司名称"
              name="companyName"
              rules={formRules.companyName}
              tooltip={`${memberInfo?.erpCustomerCode ? "客户已审核，公司名称不允许变更" : "企业请填写公司名称,事业单位请填写单位名称,个人请填写姓名"}`}
            >
              <Input placeholder="请输入公司名称" disabled={!!memberInfo?.erpCustomerCode} />
            </Form.Item>
            <Form.Item label="公司所在地" name="area" rules={formRules.area}>
              <TmAreaCascade onMonitorCascadeChange={handleMonitorAreaChange} ref={areaCascadeRef} />
            </Form.Item>
            <Form.Item label="街道地址" name="contactAddress" rules={formRules.contactAddress}>
              <Input placeholder="填写街道地址，乡镇街道及门牌地址" />
            </Form.Item>
            <Form.Item colon={false} className="form-submit">
              <Button type="default" size="middle" onClick={() => cancelSubmitEvent()}>
                取消修改
              </Button>
              <Button type="primary" danger htmlType="submit" size="middle">
                确认修改
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Spin>
    </>
  );
  return modalVisible ? (
    <TmModal
      maskClosable={false}
      keyboard={false}
      title={title}
      width={580}
      centered={true}
      open={modalVisible}
      content={renderFormContent()}
      footer={null}
      onOk={handleCancel}
      onCancel={() => handleCancel()}
    />
  ) : null;
});
