import { Provide, Scope, <PERSON><PERSON><PERSON><PERSON> } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ISmsService } from "@/service/platform/sms.service";
import { PlatformSmsVerifyDto } from "@/dto/platform-sms-verify.dto";
import { PlatformSmsSendDto } from "@/dto/platform-sms-send.dto";

@Provide("PlatformSmsService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class SmsServiceImpl extends BaseService implements ISmsService {
  async sendVerifyCode(dto: PlatformSmsSendDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/api/platform/sms/send-verify-code", dto));
  }

  async checkVerifyCode(dto: PlatformSmsVerifyDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/api/platform/sms/check-verify-code", dto));
  }
}
