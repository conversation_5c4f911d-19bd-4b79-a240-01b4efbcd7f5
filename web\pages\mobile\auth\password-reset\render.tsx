import React, { useRef, useState } from "react";
import { SProps } from "ssr";
import styles from "@/pages/mobile/auth/login/index.module.less";
import MobileNavBar from "@/components/mobile/TmFormItem/src/components/MobileNavBar";
import { Button, Form, Input, Toast } from "antd-mobile";
import { MobileEmailSendCode, MobilePasswordItem, MobileSmsCaptcha } from "@/components/mobile/TmFormItem";
import { FormInstance } from "antd-mobile/es/components/form";
import { debounce } from "lodash";
import { validateEmail, validateMobile } from "@/utils/form-valid.util";
import { EmailSendTypeEnum } from "@/enums/EmailSendTypeEnum";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { findAuthPasswordByEmail, findAuthPwd } from "@/apis/auth";

export default function MobilePasswordReset(props: SProps) {
  const [form] = Form.useForm();
  const formRef = React.createRef<FormInstance>() as any;
  const [navList, setNavList] = useState<string[]>(["手机重置", "邮箱重置"]);
  const [currentNav, setCurrentNav] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  const rules = {
    phone: [{ required: true, whitespace: true, message: "手机号不能为空!" }, { validator: validateMobile }],
    email: [{ required: true, whitespace: true, message: "邮箱不能为空!" }, { validator: validateEmail }],
  };
  /** ======================================== method start ======================================= */
  // 发送请求
  const handleSubmitEvent = debounce(async (values: any) => {
    setLoading(true);
    const { email, verifyCode, phone, smsCode, password, repeat: confirmPassword } = values;
    const encryptPassword = encrypt(password);
    const postData: any = {
      password: encryptPassword,
      confirmPassword: encryptPassword,
    };
    let err, res;
    if (currentNav === 0) {
      postData.phone = phone;
      postData.smsCode = smsCode;
      const [err1, res1] = await findAuthPwd(postData)
        .then(res => [null, res])
        .catch(err => [err, null]);
      err = err1;
      res = res1;
    } else if (currentNav === 1) {
      postData.email = email;
      postData.verifyCode = verifyCode;
      const [err1, res1] = await findAuthPasswordByEmail(postData)
        .then(res => [null, res])
        .catch(err => [err, null]);
      err = err1;
      res = res1;
    } else {
      setLoading(false);
      return Toast.show({
        content: err?.data?.message || "非法请求类型，请重试",
        maskClickable: false,
        duration: 1500,
      });
    }
    if (err) {
      setLoading(false);
      return Toast.show({
        content: err?.data?.message || "网络异常，重置失败，请重试",
        maskClickable: false,
        duration: 1500,
      });
    }
    Toast.show({
      content: res.message || "密码重置成功，返回登录页",
      maskClickable: false,
      duration: 1500,
    });
    setTimeout(() => {
      setLoading(false);
      // 重置成功，返回登录页面
      window.location.href = "/m/auth/login";
    }, 1000);
  }, 300);
  // 切换选项卡
  const handleNavTapChangeEvent = (idx: number) => {
    setCurrentNav(idx);
  };
  /** ======================================== method end ======================================= */
  return (
    <>
      <div className={styles.wrapper}>
        <div className="auth-page">
          <div className="auth-header">
            <MobileNavBar title="光华易购密码重置" />
          </div>
          <div className="auth-container">
            <div className="auth-content">
              <div className="navbar-box">
                {navList.map((item, index) => (
                  <div className={`navbar-box-item ${currentNav === index ? "navbar-box-on" : ""}`} key={index} onClick={() => handleNavTapChangeEvent(index)}>
                    {item}
                  </div>
                ))}
              </div>
              <Form
                onFinish={handleSubmitEvent}
                ref={formRef}
                form={form}
                layout="horizontal"
                className="auth-form-box"
                footer={
                  <Button type="submit" loading={loading} className="auth-submit-btn" color="primary">
                    提交修改
                  </Button>
                }
              >
                {currentNav === 0 ? (
                  <>
                    <Form.Item label="手机号码" className="item" name="phone" rules={rules.phone}>
                      <Input className="input" autoComplete="off" clearable placeholder="请输入手机号或邮箱" />
                    </Form.Item>
                    {/* ========== 短信验证码发送组件 ========== */}
                    <MobileSmsCaptcha uuid={"mobileResetPassword"} smsFormRef={form} />
                  </>
                ) : null}
                {currentNav === 1 ? (
                  <>
                    <Form.Item label="邮箱地址" className="item" name="email" rules={rules.email}>
                      <Input className="input" autoComplete="off" clearable placeholder="请输入手机号" />
                    </Form.Item>
                    {/* ========== 邮件验证码发送组件 ========== */}
                    <MobileEmailSendCode uuid={"mobileForgetPasswordEmail"} formRef={form} sendType={EmailSendTypeEnum.REGISTER} countdownSeconds={60} />
                  </>
                ) : null}
                {/* 渲染密码组件 */}
                <MobilePasswordItem prefixCls={"ghmall-m"} pwdFormRef={form} radius={2} />
              </Form>
            </div>
            <div className="auth-bottom" />
          </div>
        </div>
      </div>
    </>
  );
}
