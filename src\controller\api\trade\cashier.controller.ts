import { Body, Controller, Inject, Param, Post } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { ITradeService } from "@/service/trade/trade.service";
import { TradeCashierPayDto } from "@/dto/trade-cashier-pay.dto";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";

@Controller("/api/payment/cashier", { middleware: [AuthenticationMiddleware] })
export class CashierController extends BaseController {
  @Inject("TradeService")
  tradeService: ITradeService;

  @Post("/pay/:paymentMethod")
  async paymentGateway(@Param("paymentMethod") paymentMethod: string, @Body() tradeCashierPayDto: TradeCashierPayDto) {
    const { ctx } = this;
    if (paymentMethod === "wallet") {
      tradeCashierPayDto.mid = this.getMemberId();
    }
    const res = await this.tradeService.payGateway(paymentMethod, tradeCashierPayDto);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }
}
