import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { INotifyService } from "@/service/platform/notifies.service";
import { INotifiesQuery } from "~/typings/data/spread";

@Provide("NotifiesService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class NotifiesServiceImpl extends BaseService implements INotifyService {
  /** 公告详情 */
  async getNotifyDetailByCode(code: string): Promise<any> {
    return this.easyHttp.get(`/api/spread/sys-notifies/${code}`);
  }

  /** 公告列表 */
  async getNotifies(params: INotifiesQuery): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/spread/sys-notifies", params), "获取数据出错了，请稍后再试！");
  }
}
