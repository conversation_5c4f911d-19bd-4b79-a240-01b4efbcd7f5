import React, { useContext, useEffect, useState } from "react";
import style from "./index.module.less";
import ProductCategory from "@/components/ClassificationNavigation";
import SearchBox from "@/components/SearchBox";
import SvgIcon from "@/components/SvgIcon";
import useWindowScroll from "@/hooks/useWindowScroll";
import { onlyCsr } from "ssr-hoc-react";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import { Badge } from "antd";
import { useShoppingCart } from "@/hooks/useShoppingCart";

const HoverSearch = () => {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const offSet = useWindowScroll();
  const [monitorOffetY, setMonitorOffetY] = useState(0);
  const shoppingCartHook = useShoppingCart();

  useEffect(() => {
    setMonitorOffetY(offSet.y);
  }, [offSet.y]);
  return (
    <>
      {monitorOffetY > 250 ? (
        <div className={style.wrapper}>
          <div className="scroll-hover-search-box">
            <div className="content">
              {/*  精选商品分类 */}
              <div className="category-nav">
                <ProductCategory />
              </div>
              <div className="search-box">
                <SearchBox isHover={true} />
              </div>
              <div className="shopping-cart" onClick={() => shoppingCartHook.toggle()} onMouseEnter={() => shoppingCartHook.init()}>
                <Badge count={state.shoppingCartNum < 100 ? state.shoppingCartNum : 99}>
                  <SvgIcon iconClass="home-cart" />
                </Badge>
              </div>
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
};
export default onlyCsr(HoverSearch);
