// 用户自定义 store，用法查看文档 http://doc.ssr-fc.com/docs/features$communication#React%20%E5%9C%BA%E6%99%AF

import { Action } from "ssr-types-react";
import { state as loginState, reducer as loginReducer } from "./login";
import { state as languageState, reducer as languageReducer } from "./language";
import { state as crudState, reducer as crudReducer } from "./crud";
import { state as drawerState, reducer as drawerReducer } from "./drawer";

const state = {
  ...loginState,
  ...languageState,
  ...crudState,
  ...drawerState,
};

function reducer(state: any, action: Action) {
  return loginReducer(state, action) || languageReducer(state, action) || crudReducer(state, action) || drawerReducer(state, action);
}

export { state, reducer };
