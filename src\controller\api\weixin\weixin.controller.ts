import { BaseController } from "@/controller/base.controller";
import { Body, Controller, Inject, Post } from "@midwayjs/decorator";
import { IWeixinService } from "@/service/weixin/weixin.service";

@Controller("/api/wx")
export class WeixinController extends BaseController {
  @Inject("WeixinService")
  weixinService: IWeixinService;

  @Post("/share")
  async getWxShareData(@Body() wxShareDto: { url?: string }) {
    const { ctx } = this;
    let url = ctx.request?.href;
    if (wxShareDto?.url) {
      url = wxShareDto?.url;
    }
    const result = await this.weixinService.getWxSignature(url);
    return ctx.getResponseInstance(ctx).setResponseData(result).sendSuccess("success");
  }
}
