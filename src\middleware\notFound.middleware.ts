import { Middleware } from "@midwayjs/decorator";
import type { IMiddleware } from "@midwayjs/core";
import type { NextFunction, Context } from "@midwayjs/koa";

@Middleware()
/**
 * <p>使用方法</p>
 * src/configuration.ts 加入:this.app.useMiddleware(NotFoundMiddleware);
 *
 */
export class NotFoundMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      try {
        await next();
      } catch (error) {
        if (ctx.status === 404) {
          // 手动建立 /web/pages/404 相关文件
          ctx.redirect("/404");
        }
      }
    };
  }
}
