import { Controller, Get, Inject, Param } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IQuoteService } from "@/service/quote/quote.service";

@Controller("/quote")
export class QuotePageController extends BaseController {
  @Inject("QuoteService")
  quoteService: IQuoteService;

  /**
   * 询报价页面
   */
  @Get("/request")
  async quoteRequest(): Promise<void> {
    const { ctx } = this;
    
    // 可以在这里预加载一些数据，比如用户信息等
    ctx.quotePageData = {
      pageTitle: "询报价",
      pageDescription: "上传文件批量匹配产品，快速生成报价单或订单"
    };

    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  /**
   * 询报价详情页面
   */
  @Get("/detail/:id")
  async quoteDetail(@Param("id") quoteRequestId: number): Promise<void> {
    const { ctx } = this;
    
    try {
      // 获取询报价详情数据
      const memberId = this.getMemberId();
      const res = await this.quoteService.getQuoteRequestDetail(quoteRequestId, memberId);
      
      if (!res.data) {
        await this.renderNotfoundPage("抱歉，询报价单不存在或已删除！");
        return;
      }

      ctx.quoteDetail = res.data;
      ctx.quotePageData = {
        pageTitle: `询报价详情 - ${res.data.title}`,
        pageDescription: res.data.description || "询报价详情"
      };

      ctx.body = await render<Readable>(ctx, {
        stream: true,
        mode: "ssr",
      });
    } catch (error) {
      console.error("获取询报价详情失败:", error);
      await this.renderNotfoundPage("获取询报价详情失败，请稍后再试！");
    }
  }

  /**
   * 询报价列表页面
   */
  @Get("/list")
  async quoteList(): Promise<void> {
    const { ctx } = this;
    
    ctx.quotePageData = {
      pageTitle: "我的询报价",
      pageDescription: "管理我的询报价单"
    };

    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
