import React, { useEffect, useState } from "react";
import style from "./index.module.less";
import { CheckOutlined, CloseOutlined, DownOutlined, PlusOutlined, RightOutlined, UpOutlined } from "@ant-design/icons";
import { Button, Checkbox } from "antd";
import searchCrud from "@/apis/search/search";
import categoryCrud from "@/apis/product/category";
import { treeToolHandlerInstance } from "@/utils/tree-tool.util";
import { useOss } from "@/hooks/useOss";
import { COMMON_IMAGE_PATHS } from "@/constants/common";

interface IParams {
  categoryId: string;
  keyword: string;
  brand: string;
}
interface IFilterProps {
  params?: IParams | any;
  onMonitorSelectedParams?: Function;
  handleSearchEvent?: Function;
  useSSRQueryRequestHook?: any;
}
interface IFilterParamsValue {
  label: string;
  value: string | number;
  url: string;
}
interface IFilterParams {
  label: string;
  key: string;
  more?: boolean;
  show?: boolean;
  values: IFilterParamsValue | any;
}
const DEFAULT_SHOW_TAG_COUNT: number = 3;
export const SearchFilterBoxPanel = (prop: IFilterProps) => {
  const useOssHook = useOss();
  const currentParams = prop.params;
  const defaultParams = [
    {
      label: "品牌",
      key: "brands",
      more: false,
      show: true,
      values: [],
    },
  ];
  /* ======================================= 测试数据 start======================================= */

  /* ======================================= useState start======================================= */
  const [filterParams, setFilterParams] = useState<IFilterParams[]>(defaultParams);
  // 是否多选点击
  const [multiple, setMultiple] = useState<any>(false);
  // checkbox 多选分类下的item
  const [multiSelectedItem, setMultiSelectedItem] = useState<IFilterParamsValue[]>([]);
  // 已选的item分类集合 顶部展示,需要监听变化
  const [hadSelectedItem, setHadSelectedItem] = useState<any>([]);
  // 记录已选品牌ID
  const [brandIds, setBrandIds] = useState<any>([]);
  // 过滤后的分类Bar数据
  const [categoryBarData, setCategoryBarData] = useState<any>(null);
  // 原始层联数据
  const [categoryCascadeData, setCategoryCascadeData] = useState<any>([]);
  //
  const [categoryBarSelectTip, setCategoryBarSelectTip] = useState<string>("");
  // 展示的搜索项数量,初始化为:DEFAULT_SHOW_TAG_COUNT
  const [showTagCount, setShowTagCount] = useState<number>(DEFAULT_SHOW_TAG_COUNT);

  /* ======================================= useState end======================================= */

  // 接口请求==>获取筛选条件
  const fetchRelatedFilterList = async () => {
    // 请求接口
    const [err, res] = await searchCrud
      .getRelatedFilterList(currentParams)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return [];
    }
    const responseParamsList: any = res.data;
    setFilterParams(param => {
      if (param.length) {
        param[param.findIndex(item => item.key === "brands")].values = responseParamsList["brands"];
        // 其它选项
        responseParamsList.paramOptions.map(item => {
          item["more"] = false;
          item["show"] = true;
          return item;
        });
        param = param.concat(responseParamsList.paramOptions);
      }
      return [...param];
    });
  };
  // 接口请求==>获取商品分类，分类下展示
  const fetchCategoryNav = async (currentCategoryId: number) => {
    const [err, res] = await categoryCrud
      .getCascade()
      .then(data => [null, data])
      .catch(err => [err, null]);
    if (err) {
      return [];
    }
    const categories = res.data;
    setCategoryCascadeData(categories);
    // 处理生成当前层联数据
    const targetCategoryCascade = treeToolHandlerInstance.filter(categories, node => Number(node.id) === Number(currentCategoryId));
    if (targetCategoryCascade[0]?.pid === 0 && targetCategoryCascade[0]?.children?.length === 0) {
      setCategoryBarData(categories.filter(node => parseInt(node.id) === targetCategoryCascade[0].id)[0]);
      setCategoryBarSelectTip("全部");
    } else {
      setCategoryBarData(targetCategoryCascade[0]);
      setCategoryBarSelectTip("");
    }
  };
  const getCategoryBarSelectList = id => {
    const pid = treeToolHandlerInstance.findNode(categoryCascadeData, node => parseInt(node.id) === parseInt(id))?.pid ?? -1;
    return treeToolHandlerInstance.findNode(categoryCascadeData, node => parseInt(node.id) === parseInt(pid))?.children ?? [];
  };
  /* ======================================= 测试数据 end======================================= */

  /* ======================================= method start======================================= */
  // 多选事件
  const multipleSelectEvent = (index): any => {
    setFilterParams(param => {
      param[index].more = true;
      return [...param];
    });
    setMultiple(index);
  };
  // 取消多选事件
  const cancelMultiEvent = () => {
    // 需要置空多选的值
    setMultiSelectedItem([]);
    setFilterParams(param => {
      param[0].more = false;
      return [...param];
    });
    setMultiple(false);
  };
  // 多选确认事件
  const confirmMultiEvent = index => {
    // 处理逻辑
    setHadSelectedItem(hadSelectedItem => {
      hadSelectedItem.push({
        type: filterParams[index].label,
        key: filterParams[index].key,
        name: multiSelectedItem.join("、"),
        value: filterParams[index].values
          .filter(tmp => multiSelectedItem.includes(tmp.label))
          .map(tmp2 => tmp2.value)
          .join("@"),
      });
      return [...hadSelectedItem];
    });
    if (index === 0) {
      // 如果是品牌，获取品牌ID
      filterParams[0].values.forEach(brand => {
        if (multiSelectedItem.includes(brand.label)) {
          setBrandIds(brandItem => {
            brandItem.push(brand);
            return [...brandItem];
          });
        }
      });
    }
    setFilterParams(param => {
      param[index].show = false;
      return [...param];
    });
    cancelMultiEvent();
  };
  // 展示更多按钮事件
  const showMoreBtnEvent = index => {
    setFilterParams(param => {
      param[0].more = !filterParams[index].more;
      return [...param];
    });
  };
  // 分类下单个点击事件
  const selectSingleParamEvent = (item: IFilterParamsValue | any, index: number) => {
    if (multiple !== false) {
      const key = multiSelectedItem.indexOf(item.label);
      if (key > -1) {
        setMultiSelectedItem(multiSelected => {
          multiSelected.splice(key, 1);
          return [...multiSelected];
        });
      } else {
        setMultiSelectedItem(multiSelected => {
          multiSelected.push(item.label);
          return [...multiSelected];
        });
      }
    } else {
      setHadSelectedItem(hadSelectedItem => {
        hadSelectedItem.push({
          type: filterParams[index].label,
          key: filterParams[index].key,
          name: item.label,
          value: item.value,
        });
        return [...hadSelectedItem];
      });
      setFilterParams(param => {
        param[index].show = false;
        return [...param];
      });
      if (index === 0) {
        // 如果是品牌，获取品牌ID
        filterParams[index].values.forEach(brand => {
          setBrandIds(brandItem => {
            brandItem.push(brand);
            return [...brandItem];
          });
        });
      }
    }
  };
  // 顶部栏=>取消已选中的项
  const cancelHadSelectedEvent = (item, index) => {
    if (item.type === "品牌") {
      setBrandIds([]);
    }
    setHadSelectedItem(hadSelected => {
      hadSelected.splice(index, 1);
      return [...hadSelected];
    });
    setFilterParams(param => {
      param.map((tag, index) => {
        if (tag.label === item.type) {
          tag.show = true;
          tag.more = false;
        }
      });
      return [...param];
    });
  };
  // 监听checkbox事件
  const multiSelectedChangeEvent = (checkedValue, options) => {
    const target = options.filter(op => {
      return checkedValue.includes(op.value);
    });
    setMultiSelectedItem(target.map(item => item.label));
  };
  // 分类点击事件
  const categoryBarClickEvent = async (item?: any) => {
    prop?.handleSearchEvent && prop.handleSearchEvent({ categoryId: item.id });
    item?.id && (await fetchCategoryNav(parseInt(item.id)));
  };
  // 展开更多筛选项
  const moreConditionOptionsEvent = () => {
    setShowTagCount(showTagCount === DEFAULT_SHOW_TAG_COUNT ? 50 : DEFAULT_SHOW_TAG_COUNT);
  };
  // 获取筛选品牌的logo地址
  const getBrandLogoPath = (logoPath: string) => {
    return useOssHook.generateOssFullFilepath(logoPath, COMMON_IMAGE_PATHS.DEFAULT);
  };
  /* ======================================= method end======================================= */

  /* ======================================= onMounted watch start======================================= */
  useEffect(() => {
    setFilterParams(defaultParams);
    fetchRelatedFilterList();
    if (currentParams?.categoryId) {
      fetchCategoryNav(parseInt(currentParams?.categoryId));
    }
  }, []);
  // 事件监听--监听已选条件变化
  useEffect(() => {
    if (prop.onMonitorSelectedParams) {
      const filterParams = [...hadSelectedItem];
      if (brandIds.length === 0 && !currentParams?.brandId) {
        filterParams.push({ key: "brands", value: null });
      }
      prop.onMonitorSelectedParams(filterParams);
    }
  }, [hadSelectedItem]);
  /* ======================================= onMounted watch end======================================= */
  // 渲染分类Bar
  const renderCategoryBar = () => {
    // 子分类
    const renderCategoryBarSub = item => {
      return item?.length ? (
        <>
          <RightOutlined />
          <div className="crumbs-sub sub-bar">
            {categoryBarSelectTip || (item[0]["label"] ?? "暂无")}
            <DownOutlined />
            <ul>
              {getCategoryBarSelectList(item[0].id).map((item1, key1) => {
                return (
                  <li key={key1} onClick={async () => await categoryBarClickEvent(item1)}>
                    {item1.label}
                  </li>
                );
              })}
            </ul>
          </div>
          <div className="reset-category">
            <a onClick={async () => await categoryBarClickEvent({ id: currentParams?.categoryId ?? null })}>重置分类</a>
          </div>
          {renderCategoryBarSub(item[0]?.children)}
        </>
      ) : null;
    };
    // 渲染BAR内容
    return (
      <>
        {currentParams?.categoryId ? (
          categoryBarData ? (
            <>
              <div className="crumbs">
                <div className="crumbs-sub" onClick={async () => await categoryBarClickEvent(categoryBarData)}>
                  {categoryBarData?.label}
                </div>
                {renderCategoryBarSub(categoryBarData?.children)}
              </div>
            </>
          ) : null
        ) : (
          <>
            <div className="no-crumbs">
              <span className="label">全部结果</span>
              &nbsp;
              <RightOutlined />
              &nbsp;
              <span className="keyword">“{currentParams?.keyword ?? currentParams?.brandId ?? ""}“</span>
            </div>
          </>
        )}
        {/* 已所选动态条件渲染 */}
        {hadSelectedItem?.length ? <RightOutlined /> : null}
        {hadSelectedItem.map((item, index) => {
          return (
            <React.Fragment key={index}>
              <a title={item.name} className="selected-item" onClick={() => cancelHadSelectedEvent(item, index)}>
                <span>{item.type}：</span>
                <span>{item.name}</span>
                <CloseOutlined />
              </a>
            </React.Fragment>
          );
        })}
      </>
    );
  };
  return (
    <>
      <div className={style.wrapper}>
        <div className="filter-box">
          <div className="head-bar">
            {/* 判断是否有分类并进行渲染 */}
            {renderCategoryBar()}
          </div>
          {/* 筛选内容 */}
          <div className="content-bar">
            {/* ================ 单独处理品牌 ================ */}
            {filterParams[0]?.show && filterParams[0].values.length ? (
              <div className="brand-condition">
                <div className="label">{filterParams[0]["label"]}：</div>
                <div className="content">
                  <ul className={filterParams[0]["more"] ? "show-more" : undefined}>
                    {filterParams[0].values.map((item, key) => {
                      return (
                        <li key={key} className={multiSelectedItem.includes(item) ? "border-color" : undefined} onClick={() => selectSingleParamEvent(item, 0)}>
                          <img src={getBrandLogoPath(item.url)} alt="" />
                          <span>{item.label}</span>
                          <div className="corner-icon" style={{ display: multiSelectedItem.includes(item.label) ? "block" : "none" }}>
                            <div />
                            <CheckOutlined />
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                  <div className="act-btn" style={{ display: multiple !== 0 ? "block" : "none" }}>
                    <span className="btn-more" onClick={() => showMoreBtnEvent(0)}>
                      {!filterParams[0].more ? (
                        <>
                          更多
                          <DownOutlined />
                        </>
                      ) : (
                        <>
                          收起
                          <UpOutlined />
                        </>
                      )}
                    </span>
                    <span className="btn-multiple" onClick={() => multipleSelectEvent(0)}>
                      <PlusOutlined />
                      多选
                    </span>
                  </div>
                  <div className="multiple-btn" style={{ display: multiple === 0 ? "block" : "none" }}>
                    <Button type="default" size="small" onClick={() => confirmMultiEvent(0)}>
                      确定
                    </Button>
                    <Button type="default" size="small" onClick={() => cancelMultiEvent()}>
                      取消
                    </Button>
                  </div>
                </div>
              </div>
            ) : null}
            {/* ================ 其它筛选项 ================ */}
            {filterParams.map((filter, filterIndex) => {
              return (
                <React.Fragment key={filterIndex}>
                  {filterIndex !== 0 && filter.show ? (
                    <div className="other-condition" style={{ display: filterIndex < showTagCount ? "flex" : "none" }}>
                      <div className="label">{filter.label}：</div>
                      <div className="content">
                        <ul style={{ display: multiple !== filterIndex ? "block" : "none" }} className={`list ${filter.more ? "show-more" : undefined}`}>
                          {filter.values.map((fv, key1) => {
                            return (
                              <li className="item" key={key1} onClick={() => selectSingleParamEvent(fv, filterIndex)}>
                                {fv.label}
                              </li>
                            );
                          })}
                        </ul>
                        {/* 多选 */}
                        <Checkbox.Group
                          style={{ display: multiple === filterIndex ? "block" : "none" }}
                          className={`list ${filter.more ? "show-more" : undefined}`}
                          options={filter.values}
                          defaultValue={[]}
                          onChange={checked => multiSelectedChangeEvent(checked, filter.values)}
                        />
                        {/* 右侧按钮 */}
                        <div className="act-btn" style={{ display: multiple !== filterIndex ? "block" : "none" }}>
                          <span className="btn-more" onClick={() => showMoreBtnEvent(filterIndex)} style={{ display: filter.values.length > 9 ? "block" : "none" }}>
                            {!filter.more ? (
                              <>
                                更多
                                <DownOutlined />
                              </>
                            ) : (
                              <>
                                收起
                                <UpOutlined />
                              </>
                            )}
                          </span>
                          <span className="btn-multiple" onClick={() => multipleSelectEvent(filterIndex)}>
                            <PlusOutlined />
                            多选
                          </span>
                        </div>
                        <div className="multiple-btn" style={{ display: multiple === filterIndex ? "block" : "none" }}>
                          <Button type="default" size="small" onClick={() => confirmMultiEvent(filterIndex)}>
                            确定
                          </Button>
                          <Button type="default" size="small" onClick={() => cancelMultiEvent()}>
                            取消
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : null}
                </React.Fragment>
              );
            })}
            {/* ================ 更多筛选项 ================ */}
            {filterParams?.length > DEFAULT_SHOW_TAG_COUNT ? (
              <div onClick={() => moreConditionOptionsEvent()} className="more-condition-options">
                {showTagCount === DEFAULT_SHOW_TAG_COUNT ? "更多筛选项" : "收起筛选项"}
                {showTagCount === DEFAULT_SHOW_TAG_COUNT ? <DownOutlined /> : <UpOutlined />}
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchFilterBoxPanel;
