import { Controller, Get, Inject, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { COMMON_DICT_MAPPING } from "@/common/constants/common.constant";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { ISalesmanService } from "@/service/platform/salesman.service";
import { IDictService } from "@/service/dict.servce";

@Controller("/m/auth")
export class MobileAuthController extends BaseController {
  @Inject("SalesmanService")
  salesmanService: ISalesmanService;

  @Inject("DictService")
  dictService: IDictService;

  /**
   * <p>移动端登录页</p>
   */
  @Get("/login")
  async login(): Promise<void> {
    const { ctx } = this;
    if (!this.isMobileDevice()) {
      return ctx.redirect(`/auth/login?${new URLSearchParams(ctx?.query).toString()}`);
    }
    ctx.body = await render<Readable>(ctx, {
      stream: true,
    });
  }

  /**
   * <p>移动端注册页</p>
   */
  @Get("/register")
  async register(): Promise<void> {
    const { ctx } = this;
    if (!this.isMobileDevice()) {
      return ctx.redirect(`/auth/register?${new URLSearchParams(ctx?.query).toString()}`);
    }
    const customerCategoryRes = await this.dictService.getDictByCode(COMMON_DICT_MAPPING.CUSTOMER_CATEGORY.value);
    ctx.customerCategory = customerCategoryRes?.data?.detail || [];
    ctx.body = await render<Readable>(this.ctx, {
      stream: true,
    });
  }

  /**
   * <p>移动端推荐注册页</p>
   *
   * @param spreadCode /
   */
  @Get("/spread-register")
  async mobileSpreadRegister(@Query("code") spreadCode: string): Promise<void> {
    const { ctx } = this;
    if (!this.isMobileDevice()) {
      return ctx.redirect(`/auth/spread-register?${new URLSearchParams(ctx?.query).toString()}`);
    }
    if (!spreadCode) {
      return ctx.redirect("/auth/register");
    } else {
      const salesmanResult = await this.salesmanService.getSalesmanBySpreadCode(spreadCode);
      if (!salesmanResult.data) {
        const originUrl = ctx.url;
        return ctx.redirect(`/403?origin=${encodeURIComponent(originUrl)}&message=${encodeURIComponent("不合法的推荐码，请联系您的业务经理或重试！")}`);
      } else {
        const salesman = salesmanResult.data;
        ctx.spreadInfo = {
          userid: salesman?.id,
          nickname: salesman?.nickname,
          spreadCode: salesman?.spreadCode,
          spreadAvatar: salesman?.spreadAvatar,
          spreadPhone: salesman?.spreadPhone,
          spreadDescription: salesman?.spreadDescription,
          isPromoter: salesman?.isPromoter,
        };
        const customerCategoryRes = await this.dictService.getDictByCode(COMMON_DICT_MAPPING.CUSTOMER_CATEGORY.value);
        ctx.customerCategory = customerCategoryRes?.data?.detail || [];
        ctx.body = await render<Readable>(this.ctx, {
          stream: true,
        });
      }
    }
  }

  @Get("/password-reset")
  async passwordReset(): Promise<void> {
    const { ctx } = this;
    if (!this.isMobileDevice()) {
      return ctx.redirect(`/auth/password-reset?${new URLSearchParams(ctx?.query).toString()}`);
    }
    ctx.body = await render<Readable>(ctx, {
      stream: true,
    });
  }
}
