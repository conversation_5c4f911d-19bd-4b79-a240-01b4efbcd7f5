.wrapper {
  :global {
    #cornerIconFun(@top:15px,@right:20px) {
      .edit-btn {
        font-size: 12px;
        position: absolute;
        top: @top;
        right: @right;
        color: @mallThemeColor;
        span {
          &:first-child {
            margin-right: 8px;
          }
          &:hover {
            border-bottom: 1px solid @mallThemeColor;
          }
        }
      }
      .corner-icon {
        position: absolute;
        right: -1px;
        bottom: -1px;
        div {
          width: 0;
          border-top: 20px solid transparent;
          border-right: 20px solid @mallThemeColor;
        }
        .anticon {
          font-size: 12px;
          position: absolute;
          bottom: 0;
          right: 1px;
          transform: rotate(-15deg);
          color: #fff;
        }
      }
    }
    .confirmWrapper {
      .invoice-box {
        .tips {
          position: absolute;
          border: 1px solid #ddd;
          width: 310px;
          padding: 3px;
          margin: 0 0 0 10px;
          font-size: 12px !important;
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
          color: #8c8c8c;
          &::before {
            content: "";
            display: inline-block;
            width: 12px;
            height: 17px;
            background: url("@@img/arrow-left.png") 0 0 no-repeat;
            background-color: #fff;
            position: absolute;
            left: -9px;
          }
          .anticon {
            color: #ff8f23;
            margin-right: 3px;
            font-size: 14px;
            font-weight: bold;
          }
        }
        &-content {
          display: flex;
          flex-wrap: wrap;
          > div {
            border: 1px dotted #949494;
            width: 265px;
            height: 80px;
            margin: 5px 20px 0 0;
            padding: 10px;
            cursor: pointer;
            color: #999;
          }
          &-item {
            position: relative;
            text-align: center;
            > div:not(:first-child) {
              margin-top: 7px;
            }
            .type {
              font-size: 14px;
              color: #595959;
            }
            .vat-name {
              font-size: 12px;
            }
            #cornerIconFun(@top:5px,@right:5px);
          }
          .border-red {
            border-color: @mallThemeColor;
          }
        }
      }
      .pay-card-item {
        &.pay-card-step {
          .ant-steps-item-title:after {
            background-color: @mallThemeColor !important;
          }
        }
        margin-bottom: 16px;
        .go2address {
          margin-left: 5px;
          font-size: 12px;
          color: #438cde;
          cursor: pointer;
          &:hover {
            color: @mallThemeColor;
          }
        }
        /*地址管理*/
        .address-manage {
          display: flex;
          flex-wrap: wrap;
          > div {
            border: 1px dotted #949494;
            width: 265px;
            height: 120px;
            margin: 20px 20px 0 0;
            padding: 10px;
            cursor: pointer;
            color: #999;
          }
          .add-address {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            .anticon {
              font-size: 24px;
            }
          }
          &-item {
            position: relative;
            font-size: 12px;
            > div:nth-child(1) {
              margin-bottom: 10px;
              span {
                margin-right: 10px;
              }
              > span:nth-child(1) {
                color: #000000;
                font-size: 14px;
              }
            }
            #cornerIconFun();
          }
          .border-red {
            border-color: @mallThemeColor;
          }
        }
        .more-address {
          font-size: 12px;
          color: #595959;
          cursor: pointer;
          margin-top: 10px;
          display: inline-block;
        }
        /*订单支付金额card*/
        .pay-price-box {
          text-align: right;
          margin-top: 10px;
          font-size: 16px;
          color: #999;
          position: relative;
          padding-right: 220px;
          > div > span:nth-child(2) {
            width: 130px;
            text-align: right;
            display: inline-block;
            margin-top: 10px;
          }
          .actual-price {
            color: @mallThemeColor;
            font-weight: bold;
            font-size: 20px;
          }
          .freight-box {
            min-height: 56px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .left-area {
              .freight-price {
                width: 130px;
                text-align: right;
                display: inline-block;
                margin-top: 10px;
              }
              .tip {
                font-size: 13px;
                color: @mallThemeColor;
              }
            }
          }
          .send-type-box {
            position: absolute;
            top: 0;
            right: -10px;
            text-align: center;
            width: 200px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-left: 1px solid #d9d9d9;
            .ant-space {
              .ant-space-item {
                text-align: left;
              }
            }
          }
        }
        /*订单备注card*/
        &.order-remark-box {
          .sub-tip {
            font-size: 12px;
            color: #999;
            padding: 0 5px;
          }
          .orderRemarkForm {
            .ant-form-item-row {
              .ant-form-item-label {
                width: 95px;
              }
            }
            .form-item-tip {
              font-size: 12px;
              color: #999;
              margin-top: 5px;
            }
          }
        }
      }
      .pay-card-footer {
        position: sticky;
        bottom: 0;
        z-index: 99;
        .pay-footer {
          .flex-row(flex-end,center);
          height: 50px;
          color: #8c8c8c;
          div {
            text-align: center;
          }
          .pay-submit {
            background-color: @mallThemeColor;
            width: 150px;
            font-size: 20px;
            color: #fff;
            height: 100%;
            line-height: 50px;
            cursor: pointer;
            margin-left: 20px;
          }
        }
      }
      .order-products {
        // 产品-危险图标标记
        .danger-icon-tip {
          display: inline-flex;
          width: 18px;
          height: 18px;
          background: url("@@img/hazardous_chemicals.png") 50% 50% no-repeat;
          background-size: 18px 18px;
          transform: translateY(2px);
        }
      }
    }
  }
}
