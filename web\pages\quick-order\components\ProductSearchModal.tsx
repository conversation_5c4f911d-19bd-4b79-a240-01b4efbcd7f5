// components/ProductSearchModal.tsx
import React from "react";
import { Popover, Table, Tag } from "antd";
import style from "./product.module.less";
import { CloseOutlined } from "@ant-design/icons";

interface ProductSearchModalProps {
  visible: boolean;
  onCancel: () => void;
  onSelect: (product: any) => void;
  searchResults: any[];
}

const ProductSearchModal: React.FC<ProductSearchModalProps> = ({ visible, onSelect, onCancel, searchResults, children }) => {
  const columns = [
    {
      title: "",
      dataIndex: "isBuy",
      key: "isBuy",
      align: "center",
      width: "60px",
      render: (text: string) => {
        return <div className="isBugTag">{text ? <Tag color="red">购买过</Tag> : null}</div>;
      },
    },
    {
      title: "产品名称",
      dataIndex: "skuName",
      key: "skuName",
      align: "center",
    },
    {
      title: "产品代码",
      dataIndex: "sku",
      key: "sku",
      align: "center",
    },
    {
      title: "单位",
      dataIndex: "unit",
      key: "unit",
      align: "center",
    },
    {
      title: "包装规格",
      dataIndex: "packing",
      key: "packing",
      align: "center",
    },
    {
      title: "单价",
      dataIndex: "discountPrice",
      key: "discountPrice",
      align: "center",
      sorter: (a: any, b: any) => a.discountPrice - b.discountPrice,
      render: (text: number) => (Number(text) === 0 ? "询价" : text),
    },
  ];

  const renderTable = () => {
    return (
      <Table
        style={{ width: "700px", maxHeight: "360px", overflowY: "auto" }}
        className={style.productTable}
        columns={columns}
        dataSource={searchResults}
        sticky={{
          offsetHeader: 0,
        }}
        rowKey="id"
        pagination={false}
        size="small"
        onRow={record => ({
          onClick: () => onSelect({ ...record, keyword: record.skuName, rowKey: new Date().getTime() }),
          // style: { cursor: "pointer", backgroundColor: record.isBuy ? "#f6ffed" : "transparent", color: record.isBuy ? "#52c41a" : "#333" },
        })}
      />
    );
  };

  const renderTitle = () => {
    return (
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: "5px 0" }}>
        <p style={{ fontSize: "13px", fontWeight: "bold" }}>请点击选择需要订购的产品</p>
        <p style={{ cursor: "pointer" }} onClick={onCancel}>
          <CloseOutlined />
        </p>
      </div>
    );
  };

  return (
    <Popover className={style.popover} open={visible} placement="bottomLeft" title={renderTitle} content={renderTable} destroyTooltipOnHide>
      {children || <></>}
    </Popover>
  );
};

export default ProductSearchModal;
