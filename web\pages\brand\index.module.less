body {
  background-color: #fff;
}
.wrapper {
  :global {
    .brand-wrap {
      padding-top: 30px;
      .section {
        .section-header {
          .section-header-title {
            position: relative;
            height: 40px;
            padding-left: 15px;
            font-size: 20px;
            line-height: 40px;
            color: #1d191a;
            img {
              margin-right: 10px;
            }
            &::before {
              content: "";
              position: absolute;
              left: 0;
              top: 50%;
              width: 4px;
              height: 20px;
              margin-top: -10px;
              background-color: #e6212a;
              border-radius: 3px;
            }
          }
        }
        .section-body {
          margin: 20px 0 45px;
        }
      }
      .section-1 {
        .section-body {
          margin: 20px 0 45px;
          height: 214px;
          background: #f6f6f6;
          position: relative;
          padding: 0 70px 0 300px;
          border-radius: 10px;
          .image-area {
            position: absolute;
            left: 34px;
            top: 75px;
          }
          .text-area {
            font-size: 16px;
            line-height: 2;
            padding-top: 70px;
          }
        }
      }
      .section-2 {
        .list {
          padding: 50px 50px;
          border-top: #e9e9e9 solid 1px;
          li {
            position: relative;
            margin-top: 20px;
            padding: 35px 50px 50px 178px;
            transition: all linear 0.3s;
            border: 1px solid #fff;
            overflow: hidden;
            &:hover {
              border: 1px solid #e6212a;
              border-radius: 5px;
              box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
              -webkit-transform: translate3d(0, -2px, 0);
              transform: translate3d(0, -2px, 0);
              background-color: #fff;
              z-index: 2;
              .ico {
                margin-top: -50px;
                transition: all linear 0.3s;
                img {
                  width: 100px;
                  height: 100px;
                  transition: all linear 0.3s;
                }
              }
              .button {
                right: 0;
                &::after {
                  content: "";
                  display: inline-block;
                  width: 6px;
                  height: 6px;
                  position: absolute;
                  right: 15px;
                  top: 50%;
                  margin-top: -3px;
                  -webkit-transform: rotate(45deg);
                  transform: rotate(45deg);
                  border-top: 2px solid #fff;
                  border-right: 2px solid #fff;
                }
              }
            }
            .ico {
              position: absolute;
              left: 35px;
              top: 50%;
              margin-top: -45px;
              img {
                width: 90px;
                height: 90px;
              }
            }
            .name {
              font-size: 18px;
              color: #1d191a;
              padding: 10px 0;
            }
            .msg {
              font-size: 15px;
              height: 48px;
              color: #b6b6b6;
              line-height: 24px;
            }
            .button {
              position: absolute;
              right: -200px;
              bottom: 0;
              padding: 10px 25px 10px 20px;
              transition: all linear 0.1s;
              background-color: #e6212a;
              color: #fff;
              font-size: 15px;
              border-top-left-radius: 5px;
            }
          }
        }
      }
      .proxy {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 16px 0;
        li {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          overflow: hidden;
          a {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            padding: 20px;
            .flex-center(row);
            border: 1px solid #ddd;
            .category-title {
              display: none;
              font-size: @auxiliary-text-size;
            }
            .category-img {
              width: 100%;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              font-size: 14px;
              border-radius: 50%;
            }
            .category-item-mask {
              position: absolute;
              left: 0;
              top: -120px;
              transition: all linear 0.3s;
            }
          }
          a:hover {
            border: none;
            .category-title {
              display: inline-block;
            }
            .category-img {
              width: 96%;
              transition: all 0.3s ease-in-out;
              opacity: 0.4;
            }
            .category-item-mask {
              top: 0px;
              background: rgba(0, 0, 0, 0.6);
              width: 100%;
              height: 100%;
              display: inline-flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              font-size: 16px;
              color: #ffffff;
              button {
                margin-top: 5px;
                font-size: 13px;
                color: #fff;
                background-color: #e6212a;
                border-color: #e6212a;
              }
            }
          }
        }
      }
    }
  }
}
