import { Config, Inject, Provide } from "@midwayjs/decorator";
import { Context } from "@midwayjs/koa";
import { deviceDetectorSSR } from "@/utils/device.util";

@Provide("BaseController")
export abstract class BaseController {
  @Inject()
  ctx: Context;

  @Config("isProduction")
  isProduction;

  protected getMemberId() {
    return this.ctx.session?.userData?.memberId ?? null;
  }

  protected getCustomerIdId() {
    return this.ctx.session?.userData?.customerId ?? null;
  }

  protected async renderNotfoundPage(message?: string, referer?: string, data?: any) {
    await this.ctx.render("404", {
      message: message ?? "this is server render 404 not found page!",
      referer,
      data,
    });
  }

  protected isMobileDevice(): boolean {
    return deviceDetectorSSR(this.ctx).isMobile;
  }
}
