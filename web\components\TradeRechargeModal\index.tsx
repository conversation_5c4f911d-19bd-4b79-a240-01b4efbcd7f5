import React, { memo, useState } from "react";
import TmModal from "@/components/TmModal";
import { Radio, RadioChangeEvent, Button, notification, Descriptions, Alert } from "antd";
import style from "./index.module.less";
import { useLoading } from "@/hooks/useLoading";
import tradeCrud from "@/apis/trade/trade";
import { price2Thousand } from "@/utils/price-format.util";
import { IRechargeType } from "@/typings/wallet.interface";
import { LoadingOutlined } from "@ant-design/icons";
import TmConfirmModal from "@/components/TmConfirmModal";

interface IEditModalProps {
  modalVisible: boolean;
  changeModalVisible: (bool: boolean) => void;
  callbackFunc?: any;
  order?: IRechargeType;
}

/** 在线-支付弹窗 */
export default memo(function TradeRechargeModal(props: IEditModalProps) {
  const { modalVisible, changeModalVisible, order } = props;
  const [selectedPayment, setSelectedPayment] = useState<string>("alipay");
  const paymentOptions = [
    {
      label: (
        <div className="box-item">
          <img src="https://ss3.bdstatic.com/yrwDcj7w0QhBkMak8IuT_XF5ehU5bvGh7c50/logopic/a9936a369e82e0c6c42112674a5220e8_fullsize.jpg" alt="" />
          <span>支付宝支付</span>
        </div>
      ),
      value: "alipay"
    },
    // 微信支付
    {
      label: (
        <div className="box-item">
          <img src="https://dss1.bdstatic.com/6OF1bjeh1BF3odCf/it/u=3774939867,2826752539&fm=74&app=80&f=JPEG&size=f121,121?sec=1880279984&t=796e842a5ef2d16d9edc872d6f1147ef" alt="" />
          <span>微信支付</span>
        </div>
      ),
      value: "wechat",
      disabled: true,
    },
  ];
  const useLoadingHook = useLoading();

  /** 取消操作 */
  const handleCancel = () => {
    changeModalVisible(false);
  };

  /** 发起支付申请 */
  const handleConfirmPay = async () => {
    switch (selectedPayment) {
      case 'alipay':
        await alipayH5payOrWebPay();
        break;
    }
  }

  /** 支付宝 */
  const alipayH5payOrWebPay = async () => {
    if (!order?.rechargeSn) {
      notification.error({ message: "账单异常，无法进行支付" });
      return;
    }

    const confirm = await TmConfirmModal({
      content: "确定前往支付？"
    })
    if (confirm !== 'confirm') { return }

    useLoadingHook.showLoading();
    const [err, res] = await tradeCrud
      .payGateway(selectedPayment, { sn: order?.rechargeSn, orderType: "RECHARGE", paymentClient: "WEB" })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      useLoadingHook.hideLoading("");
      return notification.warning({ message: err.data.message || "调起支付失败，请稍后再试！" });
    }

    // 页面支付
    const div = document.createElement("formdiv");
    div.innerHTML = res.data.data; // 调用支付宝支付-返回的form被包装多了一层data 兼容
    document.body.appendChild(div);
    document.forms["punchout_form"].setAttribute("target", "_self");
    document.forms["punchout_form"].submit();
    div.remove();
  };

  // 支付方式变更监听
  const onPaymentChangeEvent = (e: RadioChangeEvent) => {
    setSelectedPayment(e.target.value);
  };
  /* ======================================= method end======================================= */

  return modalVisible ? (
    <TmModal
      maskClosable={false}
      title="充值订单"
      width={600}
      centered={true}
      open={modalVisible}
      className="trade-recharge-modal"
      content={
        <div className={style.wrapper}>
          <Alert type="warning" message={
            <div className="payment-tip">
              <LoadingOutlined /> 请您尽快完成支付，否则订单会被自动取消！！！
            </div>}
          />
          <Descriptions className="payment-wrapper" column={1}>
            <Descriptions.Item label="充值单号">
              <span className="blue-tip">{order?.rechargeSn}</span>
            </Descriptions.Item>
            <Descriptions.Item label="订单状态">
              <span className="red-tip">待支付</span>
            </Descriptions.Item>
            <Descriptions.Item label="充值金额">
              <span className="red-tip">￥{price2Thousand(order?.rechargeMoney)}</span>
            </Descriptions.Item>
            <Descriptions.Item label="创建日期">
              <span>{order?.createdDate}</span>
            </Descriptions.Item>
            {/* 支付方式 */}
            <Descriptions.Item label="支付方式">
              <Radio.Group className="payment-group" value={selectedPayment} options={paymentOptions.filter(item => !item?.disabled)} onChange={onPaymentChangeEvent} />
            </Descriptions.Item>
            <Descriptions.Item>
              <div className={"btn-wrapper"}>
                <Button size={"large"} onClick={handleCancel}>
                  取消操作
                </Button>
                <Button size={"large"} danger type="primary" onClick={handleConfirmPay}>
                  确认支付
                </Button>
              </div>
            </Descriptions.Item>
          </Descriptions>
        </div>
      }
      footer={null}
      onOk={() => handleCancel()}
      onCancel={() => handleCancel()}
    />
  ) : null;
});
