import React, { useContext } from "react";
import { I<PERSON>ontext, SProps } from "ssr-types-react";
import style from "./index.module.less";
import { useStoreContext } from "ssr-common-utils";
import { Empty } from "antd";
import SpecialMenu from "./parts/SpecialMenu";
import { useOss } from "@/hooks/useOss";
import commonConstant from "@/constants/common";

export default function ProductSpecialIndex(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const specialCategoryList = state.topicListData?.content;
  const ossHooks = useOss();

  const renderSpecialItemImagesTemp = item => {
    if (item?.showImages.split(",").length > 2) {
      return (
        <div className="title-box">
          <div className="main-title">
            <a href={`/product-special/${item.categoryCode}/${item.code}`}>{item.title}</a>
          </div>
          <div className="head-img-box">
            {item?.showImages.split(",").map((imgUrl, index) => (
              <a key={index} href={`/product-special/${item.categoryCode}/${item.code}`}>
                <img alt="topic-img" src={ossHooks.generateOssFullFilepath(imgUrl, commonConstant.COMMON_IMAGE_PATHS.TOPIC_DEFAULT)} />
              </a>
            ))}
          </div>
        </div>
      );
    } else {
      return (
        <>
          <div className="head-img-box">
            <a href={`/product-special/${item.categoryCode}/${item.code}`}>
              <img alt="topic-head" src={ossHooks.generateOssFullFilepath(item.headImage || item?.showImages.split(",")[0], commonConstant.COMMON_IMAGE_PATHS.TOPIC_DEFAULT)} />
            </a>
          </div>
          <div className="title-box">
            <a href={`/product-special/${item.categoryCode}/${item.code}`}>
              <div className="main-title">{item.title}</div>
            </a>
            <div className="description">
              <span>{item.description}</span>
            </div>
          </div>
        </>
      );
    }
  };

  return (
    <>
      <div className={style.wrapper}>
        <div className="product">
          <div className="menu-left">
            <SpecialMenu />
          </div>
          <div className="special-right">
            {specialCategoryList.length ? (
              <ul className="special-list">
                {specialCategoryList.map((item, idx) => {
                  return (
                    <li key={idx} className="special-list-item">
                      {renderSpecialItemImagesTemp(item)}
                    </li>
                  );
                })}
              </ul>
            ) : (
              <Empty />
            )}
          </div>
        </div>
      </div>
    </>
  );
}
