/**
 * 金额保留n位小数
 *
 * @param type
 * @param n
 * @returns {string|*}
 */
export function priceFilter(type, n) {
  if (typeof type === "undefined") {
    return type;
  } else {
    return Number(type).toFixed(n);
  }
}
const price2ThousandStr = res => {
  if (!res.includes(".")) {
    return res.replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
  } else {
    let left = res.split(".")[0];
    const right = res.split(".")[1];
    left = left.replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
    return `${String(left)}.${String(right)}`;
  }
};
/** 价格转千位字符-显示 */
export function price2Thousand(res) {
  if (!res) {
    return 0;
  }
  // 是否带有小数点
  res = String(priceFilter(res, 2));
  return price2ThousandStr(res);
}

/**
 * 统一处理易购市场价
 *
 * @param price /
 */
export function ghmallGuidePrice2Show(price: number) {
  if (price === 0) {
    return "询价";
  }
  return price2Thousand(price);
}

/**
 * 统一处理折扣价
 *
 * @param price 指导价
 * @param discount 折扣
 */
export function ghmallDiscountPrice2Show(price: number, discount: number) {
  if (price === 0) {
    return "询价";
  }
  return price2Thousand(price * discount);
}
/** 价格转千位字符-显示 */
export function price2ThousandFixed4(res) {
  if (!res) {
    return 0;
  }
  // 是否带有小数点
  res = String(accurateDecimal(res, 4, true));
  return price2ThousandStr(res);
}

/**
 * 精确小数点
 *
 * @param number 为你要转换的数字
 * @param format 要保留几位小数；譬如要保留2位，则值为2
 * @param zeroFill 是否补零。不需要补零可以不填写此参数
 * @returns {*}
 */
function accurateDecimal(number, format, zeroFill) {
  // 判断非空
  if (number) {
    let i;
    // 正则匹配:正整数，负整数，正浮点数，负浮点数
    if (!/^\d+(\.\d+)?$|^-\d+(\.\d+)?$/.test(number)) {
      return number;
    }
    let n = 1;
    for (i = 0; i < format; i++) {
      n = n * 10;
    }
    // 四舍五入
    number = Math.round(number * n) / n;
    let str: string = number.toString();
    // 是否补零
    if (zeroFill) {
      let index;
      if (!str.includes(".")) {
        index = format - 2;
        str += ".";
      } else {
        const digit = str.length - 1 - str.indexOf(".");
        if (Array.from([2, 3]).includes(digit)) {
          index = 0;
        } else if (digit === 1) {
          index = 1;
        } else {
          index = 0;
        }
      }
      for (i = 0; i < index; i++) {
        str += "0";
      }
    }
    return str;
  }
  return number;
}

export default {
  priceFilter,
  price2Thousand,
  price2ThousandFixed4,
  ghmallDiscountPrice2Show,
  ghmallGuidePrice2Show,
};
