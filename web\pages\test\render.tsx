import React, { useContext } from "react";
import { IContext, SProps } from "ssr-types-react";
import { useIntl } from "react-intl";
import { STORE_CONTEXT } from "_build/create-context";

export default function TestPage(props: SProps) {
  const { formatMessage: f } = useIntl();
  const { state, dispatch } = useContext<IContext>(STORE_CONTEXT);
  console.log("test page ", state, state?.hello);
  return (
    <>
      <div>国际化：{f({ id: "test" })}</div>
      <div>国际化22：{state?.hello}</div>
    </>
  );
}
