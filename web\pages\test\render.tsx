import React, { useContext } from "react";
import { IContext, SProps } from "ssr-types-react";
import { useIntl } from "react-intl";
import { STORE_CONTEXT } from "_build/create-context";
import { Card, Button, Space } from "antd";

export default function TestPage(props: SProps) {
  const { formatMessage: f } = useIntl();
  const { state, dispatch } = useContext<IContext>(STORE_CONTEXT);
  console.log("test page ", state, state?.hello);

  return (
    <div style={{ padding: "20px" }}>
      <Card title="测试页面">
        <Space direction="vertical" size="large">
          <div>国际化：{f({ id: "test" })}</div>
          <div>国际化22：{state?.hello}</div>

          <div>
            <h3>询报价功能测试</h3>
            <Space>
              <Button type="primary" href="/quote/request">
                新建询报价
              </Button>
              <Button href="/quote/list">
                询报价列表
              </Button>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
}
