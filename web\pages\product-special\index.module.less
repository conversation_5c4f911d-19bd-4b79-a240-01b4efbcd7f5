.wrapper {
  :global {
    .product {
      background-color: @main-bg-color-white;
      .flex-row();
      padding: 10px;
      .menu-left {
      }
      .special-right {
        border: 1px solid #f0f0f0;
        margin-left: 16px;
        flex: 1;
        padding: 16px;
        ul {
          li {
            width: 100%;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            padding: 10px;
            .head-img-box {
              a {
                width: 100px;
                height: 100px;
                margin-right: 16px;
                display: inline-flex;
                gap: 8px;
              }
              img {
                _:-ms-fullscreen,
                & {
                  margin-right: 8px;
                }
                width: 100%;
                border: 1px solid #f2f2f2;
              }
              img:hover {
                transform: scale(1.01);
              }
            }
            .title-box {
              flex: 1;
              .main-title {
                font-size: 20px;
                cursor: pointer;
                .ellipsis(1);
              }
              .description {
                margin-top: 5px;
                font-size: 14px;
                color: #999999;
                .ellipsis(2);
              }
            }
            .action-btn-box {
              display: flex;
              align-items: center;
              a {
                color: #3497ce;
                font-size: 14px;
              }
            }
          }
          // 高亮样式
          .special-list-item:hover {
            cursor: pointer;
            background: #fdf7f7;
            .main-title {
              a {
                color: #ff1b1b;
                text-decoration: underline;
              }
            }
            .head-img-box {
              a {
              }
            }
            .title-box {
              a {
                .main-title {
                  color: #ff1b1b;
                  text-decoration: underline;
                }
              }
            }
          }
        }
      }
    }
  }
}
