import React, { useContext } from "react";
import { SProps } from "ssr-types-react";
import style from "./index.module.less";
import { useStoreContext } from "ssr-common-utils";
import { IContext } from "ssr-types";
import { message } from "antd";
import { useOss } from "@/hooks/useOss";
import { downloadFromOssFilepath } from "@/utils/download.util";
import TmSocialShare from "@/components/TmSocialShare";

export default function NotifiesPage(props: SProps) {
  const useOssHook = useOss();
  const { state } = useContext<IContext>(useStoreContext());
  const detail = state?.notifyData;
  let attachFiles: any[];
  try {
    attachFiles = detail?.attachFiles ? JSON.parse(detail?.attachFiles) : [];
  } catch (e) {
    attachFiles = [];
  }

  /** 附件下载 */
  const handleDownloadAttachFile = (e, item) => {
    if (!item?.path) {
      message.warning("资源异常，请联系客服！");
      e.preventDefault();
    }
    const lastPrefix = getLastPrefix(item);
    downloadFromOssFilepath(useOssHook.generateOssFullFilepath(item?.path), `${item.title}${lastPrefix}`);
    e.preventDefault();
  };

  /** 路径字符串匹配 '.' */
  const getLastPrefix = item => {
    if (!item.path) {
      return "";
    }
    return item.path.match(/.[^.]+$/)[0];
  };

  return (
    <div className={style.wrapper}>
      <div className="notify-content">
        {/* 头部信息 */}
        <div className="notify-head">
          <h3 className="notify-title">{detail.title}</h3>
          <h4 className="notify-desc">{detail?.description}</h4>
          <div className="notify-tips">
            <span>{`发布日期: ${detail.createdDate}`}</span>
            <span>
              分享到:
              <TmSocialShare sites={["wechat", "weibo", "qq", "qzone"]} title={detail.title || "光华易购商城"} description={detail.description || detail.title} />
            </span>
          </div>
        </div>
        {/* 通知信息渲染 */}
        <div className="notify-content-box">
          <div dangerouslySetInnerHTML={{ __html: detail?.content || "" }} />
        </div>
        {/* 通知附件 */}
        {!!attachFiles.length && (
          <div className="notify-attach-box">
            <ul>
              {!!attachFiles.length &&
                attachFiles.map((item, index) => (
                  <li key={index} className="attach-item">
                    <a onClick={e => handleDownloadAttachFile(e, item)}>{`附件 ${item?.title}${getLastPrefix(item)}`}</a>
                  </li>
                ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
