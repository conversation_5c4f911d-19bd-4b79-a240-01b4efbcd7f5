.wrapper {
  :global {
    .coupon-container {
      margin-top: 20px;
      padding: 10px 0;

      ul {
        .coupon-item {
          padding: 5px 10px;
          display: inline-block;
          cursor: pointer;
          &-box {
            color: #fff !important;
            .coupon-face {
              font-size: 1.5em;
              position: absolute;
              top: 35%;
            }
            .coupon-type {
              position: absolute;
              right: 7px;
              top: 2px;
              color: transparent;
              font-size: 1.15em;
              font-weight: 600;
              background: linear-gradient(45deg, rgba(22, 119, 255, 0.8) 0%, #69b1ff 100%);
              -webkit-background-clip: text;
            }
            .coupon-info {
              font-size: 14px;
              position: absolute;
              left: 25%;
              top: 15%;
              .coupon-title {
                font-size: 1.4em;
                color: #3c3c3c;
              }
              .coupon-condition {
                font-size: 1em;
                color: #8c8c8c;
              }
            }
          }
          &:last-child {
            margin: 0 auto;
          }
        }
      }
    }
    @couponBg: #28a4f2;
    .base-coupons {
      width: 300px;
      height: 115px;
      position: relative;
      background: radial-gradient(circle at right top, transparent 10px, @couponBg 0) top left / 60px 51% no-repeat,
        radial-gradient(circle at right bottom, transparent 10px, @couponBg 0) bottom left / 60px 51% no-repeat,
        radial-gradient(circle at left top, transparent 10px, #eeeeee 0) 60px 0/230px 51% no-repeat,
        radial-gradient(circle at left bottom, transparent 10px, #eeeeee 0) 60px 57.5px /230px 51% no-repeat,
        radial-gradient(circle at 10px 57.5px, transparent 10px, #eeeeee 0) 290px 0/10px 115px no-repeat;
      filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.2));
      &::after {
        content: "";
        height: 95px;
        border: 1px dashed #fff;
        position: absolute;
        left: 60px;
        top: 0;
        bottom: 0;
        margin: auto;
      }
    }
  }
}
