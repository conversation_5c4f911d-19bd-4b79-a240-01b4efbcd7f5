import React from "react";
import { Form, Input, Row, Col } from "antd";

const { TextArea } = Input;

interface QuoteBasicInfoProps {
  form: any;
  onSubmit: (values: any) => void;
}

const QuoteBasicInfo: React.FC<QuoteBasicInfoProps> = ({ form, onSubmit }) => {
  return (
    <div className="basic-info-form">
      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmit}
        initialValues={{
          title: "",
          description: "",
          contactName: "",
          contactPhone: "",
          contactEmail: "",
          companyName: "",
        }}
      >
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label={
                <span>
                  <span className="required-mark">*</span>
                  询报价标题
                </span>
              }
              name="title"
              rules={[
                { required: true, message: "请输入询报价标题" },
                { max: 200, message: "标题不能超过200个字符" },
              ]}
            >
              <Input placeholder="请输入询报价标题，如：化学试剂采购询价" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label="询报价描述"
              name="description"
              rules={[
                { max: 1000, message: "描述不能超过1000个字符" },
              ]}
            >
              <TextArea 
                rows={4} 
                placeholder="请简要描述您的采购需求、用途、特殊要求等（选填）"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={
                <span>
                  <span className="required-mark">*</span>
                  联系人姓名
                </span>
              }
              name="contactName"
              rules={[
                { required: true, message: "请输入联系人姓名" },
                { max: 50, message: "姓名不能超过50个字符" },
              ]}
            >
              <Input placeholder="请输入联系人姓名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={
                <span>
                  <span className="required-mark">*</span>
                  联系电话
                </span>
              }
              name="contactPhone"
              rules={[
                { required: true, message: "请输入联系电话" },
                { 
                  pattern: /^1[3-9]\d{9}$/, 
                  message: "请输入正确的手机号码" 
                },
              ]}
            >
              <Input placeholder="请输入手机号码" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label="联系邮箱"
              name="contactEmail"
              rules={[
                { type: "email", message: "请输入正确的邮箱地址" },
              ]}
            >
              <Input placeholder="请输入邮箱地址（选填）" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="公司名称"
              name="companyName"
              rules={[
                { max: 100, message: "公司名称不能超过100个字符" },
              ]}
            >
              <Input placeholder="请输入公司名称（选填）" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default QuoteBasicInfo;
