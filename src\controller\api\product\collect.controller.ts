import { Body, Controller, Del, Get, Inject, Param, Post, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { ICollectionCreate, ICollectionQuery } from "~/typings/data/product/collection";
import { Validate } from "@midwayjs/validate";
import { IProductCollectionService } from "@/service/product/collection.service";

@Controller("/api/product/collect")
export class CollectController extends BaseController {
  @Inject("ProductCollectionService")
  collectionService: IProductCollectionService;

  /**
   * 是否收藏的产品规格
   *
   * @param skuId
   */
  @Get("/is-collection")
  async isCollection(@Query("sku_id") skuId: number) {
    const { ctx } = this;
    const res = await this.collectionService.isCollection(this.getMemberId(), skuId);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 收藏列表
   */
  @Get()
  async getLists(@Query() criteria: ICollectionQuery) {
    const res = await this.collectionService.getPageList(this.getMemberId(), criteria);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 新增
   */
  @Post()
  @Validate()
  async create(@Body() resource: ICollectionCreate) {
    const res = await this.collectionService.create(this.getMemberId(), resource);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 更加收藏id删除
   */
  @Del("/:collectId")
  async deleteByCollectId(@Param("collectId") collectId: number) {
    const res = await this.collectionService.deleteByCollectId(this.getMemberId(), collectId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 根据skuid删除
   */
  @Del("/sku/:skuId")
  async deleteBySkuId(@Param("skuId") skuId: number) {
    const res = await this.collectionService.deleteBySkuId(this.getMemberId(), skuId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 删除全部
   */
  @Del("/overall")
  async deleteAll() {
    const res = await this.collectionService.deleteAll(this.getMemberId());
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }
}
