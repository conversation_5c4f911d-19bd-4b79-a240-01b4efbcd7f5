import React, { forwardRef, memo } from "react";
import SvgIcon from "@/components/SvgIcon";
import { NavBar } from "antd-mobile";
import style from "./index.module.less";

interface Props {
  title: string;
}
const MobileNavBar = forwardRef((prop: Props, ref?: any) => {
  // 返回上一页
  const handleGo2backPage = () => {
    window.history.go(-1);
  };
  const handleGo2homepage = () => {
    window.location.href = "/";
  };
  return (
    <>
      <div className={style.wrapper}>
        <NavBar
          className="nav-header"
          right={
            <span className="navbar-right" onClick={handleGo2homepage}>
              <SvgIcon iconClass="home" />
            </span>
          }
          onBack={handleGo2backPage}
        >
          {prop.title}
        </NavBar>
      </div>
    </>
  );
});

export default memo(MobileNavBar);
