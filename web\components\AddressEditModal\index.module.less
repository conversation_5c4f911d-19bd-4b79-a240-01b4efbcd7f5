@addressMainFontSize: 14px;
@addressMainColor: #333333;
@addressFormLabelWidth: 100px;
@addressLabelHoverColor: #e02020;

.wrapper {
}

:global {
  .address-form {
    // 控制文本宽度和内容宽度
    .ant-form-item-row {
      .ant-form-item-label {
        width: @addressFormLabelWidth;
      }
    }

    // 标签
    .ant-form-item-extra {
      margin-top: 8px;
      .ant-radio-group {
        display: flex;
        gap: 8px;
        .ant-radio-button-wrapper:hover {
          border: 1px solid @addressLabelHoverColor;
          &::before {
            background-color: unset;
          }
        }
      }
    }

    // 按钮
    .address-btn-box {
    }
  }
}
