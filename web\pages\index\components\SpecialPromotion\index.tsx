import React, { useContext } from "react";
import style from "./index.module.less";
import ThemeRenderHeader from "@/pages/index/parts/ThemeRenderHeader";
import RenderThemeLogo from "@/pages/index/parts/ThemeRenderLogo";
import ProductItem from "@/pages/index/parts/ProductItem";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";

export default function SpecialPromotion() {
  const { state } = useContext<IContext>(useStoreContext());
  const specailData = state?.indexData?.homeSpecialPromotionData || [];

  return (
    <div className={style.wrapper}>
      {!!specailData.length && (
        <div className="special-promotion">
          <ThemeRenderHeader title="特价促销" moreUrl="/product/list?type=SpecialPromotion" />
          <div className="special-promotion-container">
            {specailData.map((specailDataItem, index) => {
              return (
                specailDataItem?.products && (
                  <div key={index} className="special-promotion-layout">
                    {RenderThemeLogo(specailDataItem)}
                    <ul className="products">
                      {specailDataItem.products.slice(0, 2).map(product => (
                        <ProductItem layout="horizontal" key={product.id} width="352px" product={product} />
                      ))}
                    </ul>
                  </div>
                )
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
