import { Provide, <PERSON>ope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IAdvService } from "@/service/spread/adv.service";

@Provide("SpreadAdvService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class AdvServiceImpl extends BaseService implements IAdvService {
  // 获取广告列表
  async getOverall(type: number): Promise<any> {
    return this.easyHttp.get("/api/spread/home-advertise", { type });
  }
}
