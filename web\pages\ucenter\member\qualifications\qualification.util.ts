import crudQualification from "@/apis/member/qualification";
import { IMemberQualificationType } from "@/typings/member.interface";
import { camelCase, snakeCase } from "lodash";
import { message } from "antd";
import { qualificationAddDto, qualificationUpdateDto } from "~/typings/data/member/qualification";
import { $tools } from "@/utils/tools.util";
import commonConstant from "@/constants/common";

/** 资质-字段名称选项配置 */
export const qualificationDetailNameOptions = {
  businessLicense: "营业执照",
  idcardFront: "法人身份证件(正面)",
  idcardBack: "法人身份证件(反面)",
  noDrugCertificate: "非药品类备案证",
  safetyProductionLicense: "安全生产许可证",
  purchaseCertificate: "购买证明",
  employmentZm: "从业单位备案证明或使用证明",
  salesContract: "销售合同",
  chemicalsCertificate: "易制爆化学品从业单位备案证明",
  letterOfAttorney: "易制毒化学品运输委托书",
  hazardousLicense: "危化品经营许可证",
  field1: "其他证件",
  field2: "经办人身份证件(正面)",
  field3: "经办身份证件(反面)",
};
/** 资质-图片/formItem展示设置配置 */
export const qualificationShowOptions = [
  {
    title: "营业执照",
    field: "businessLicense",
    showFields: ["businessLicense"],
    example: {
      img: commonConstant.COMMON_IMAGE_PATHS.QUALIFICATION_BUSINESS,
    },
  },
  {
    title: "身份证件照",
    field: "idcard",
    showFields: ["idcardFront", "idcardBack", "field2", "field3"],
    example: {
      img: commonConstant.COMMON_IMAGE_PATHS.QUALIFICATION_IDCARD,
      tip: "请提交身份证-原件照",
    },
  },
  {
    title: "许可证",
    field: "licence",
    showFields: ["noDrugCertificate", "safetyProductionLicense", "purchaseCertificate", "employmentZm", "salesContract"],
    example: {
      img: commonConstant.COMMON_IMAGE_PATHS.QUALIFICATION_License,
      tip: "许可相关证件，必须加盖公章才有效!",
    },
  },
  {
    title: "化学安全证明",
    field: "chemicalSafety",
    showFields: ["chemicalsCertificate", "letterOfAttorney", "hazardousLicense"],
    example: {
      img: commonConstant.COMMON_IMAGE_PATHS.QUALIFICATION_CHEMICAL,
      tip: "安全相关证件，有效期3个月 + 签字 或 加盖公章!",
    },
  },
  {
    title: "其它证明",
    field: "field1",
    showFields: ["field1"],
  },
];
/** 资质上传字段检验规则 */
export const uploadFieldRules = {
  productType: [{ required: true, message: "请选择产品类型" }],
  memberType: [{ required: true, message: "请选择客户类型" }],
  locationType: [{ required: true, message: "请选择区域类型" }],
  /** 上传选项规则-营业执照 */
  businessLicense: [{ required: true, message: "请上传营业执照" }],
  /** 上传选项规则-身份证件 */
  idcardFront: [{ required: true, message: "请上传法人身份证（正面照）" }],
  idcardBack: [{ required: true, message: "请上传法人身份证（反面照）" }],
  noDrugCertificate: [{ required: true, message: "请上传非药品类备案证" }],
  safetyProductionLicense: [{ required: true, message: "请上传安全生产许可证" }],
  purchaseCertificate: [{ required: true, message: "请上传购买证明" }],
  employmentZm: [{ required: true, message: "请上传从业单位备案证明或使用证明" }],
  salesContract: [{ required: true, message: "请上传销售合同" }],
  chemicalsCertificate: [{ required: true, message: "请上传易制爆化学品从业单位备案证明" }],
  letterOfAttorney: [{ required: true, message: "请上传易制毒化学品运输委托书" }],
  hazardousLicense: [{ required: true, message: "请上传危化品经营许可证" }],
  /** 经办人身份证 */
  field2: [{ required: true, message: "请上传经办人身份证（正面照）" }],
  field3: [{ required: true, message: "请上传经办人身份证（反面照）" }],
};
/** 资质文件上传要求-配置 */
export const qualificationTipCombinations = {
  "2-1-1": {
    key: "2-1-1",
    title: "易制爆-终端客户-广东省内",
    selected: "businessLicense,chemicalsCertificate,idcardBack,idcardFront,field2,field3",
    remark: "《易制爆化学品从业单位备案证明》、《营业执照》、《经办人身份证复印件》(正反面)、《法人身份证复印件》(正反面)",
  },
  "2-1-2": {
    key: "2-1-2",
    title: "易制爆-终端客户-广东省外",
    selected: "businessLicense,employmentZm,idcardBack,idcardFront,field2,field3",
    remark: "《营业执照》、《经办人身份证复印件》(正反面)、《法人身份证复印件》(正反面)、《从业单位备案证明》或《使用证明》（有效期3个月、签字、公章）",
  },
  "2-2-1": {
    key: "2-2-1",
    title: "易制爆-经销商-广东省内",
    selected: "businessLicense,chemicalsCertificate,hazardousLicense,idcardBack,idcardFront,field2,field3",
    remark: "《易制爆化学品从业单位备案证明》、《营业执照》、《经办人身份证复印件》(正反面)、《法人身份证复印件》(正反面)、《危化品经营许可证》（含相应产品范围）",
  },
  "2-2-2": {
    key: "2-2-2",
    title: "易制爆-经销商-广东省外",
    selected: "businessLicense,hazardousLicense,idcardBack,idcardFront,field2,field3",
    remark: "《营业执照》、《经办人身份证复印件》(正反面)、《法人身份证复印件》(正反面)、《危化品经营许可证》（含相应产品范围）",
  },
  "2-3-x": {
    key: "2-3-x",
    title: "易制爆-生产单位",
    selected: "businessLicense,chemicalsCertificate,employmentZm,idcardBack,idcardFront,safetyProductionLicense,field2,field3",
    remark: "《易制爆化学品从业单位备案证明》、《营业执照》、《经办人身份证复印件》(正反面)、《法人身份证复印件》(正反面)、《安全生产许可证》",
  },
  "3-1-x": {
    key: "3-1-x",
    title: "易制毒-终端客户",
    selected: "businessLicense,idcardBack,idcardFront,letterOfAttorney,purchaseCertificate,salesContract,field2,field3",
    remark: "《营业执照》《经办人身份证复印件》(正反面)《法人身份证复印件》(正反面)《购买证》（客户方）《销售合同》（合同章/公章）《易制毒化学品运输委托书》（盖公章）",
  },
  "3-2-x": {
    key: "3-2-x",
    title: "易制毒-经销商",
    selected: "businessLicense,hazardousLicense,idcardBack,idcardFront,letterOfAttorney,noDrugCertificate,purchaseCertificate,salesContract,field2,field3",
    remark:
      "《营业执照》、《经办人身份证复印件》(正反面)、《法人身份证复印件》(正反面)、《危化品经营许可证》（含相应产品范围）、《购买证》（客户方）、《销售合同》（合同章/公章）、《易制毒化学品运输委托书》（盖公章）、《非药品类备案证》（二或三类）",
  },
};

/** selectedFields下划线 转 驼峰 */
export const selectedFieldsCamelCaseConvert = (selectedFields: string) => {
  if (!selectedFields) {
    return "";
  }

  const temps = selectedFields.split(",");
  return temps
    .map(item => {
      if (item.includes("field")) {
        return item;
      } else {
        return camelCase(item);
      }
    })
    .join(",");
};

/** selectedFields 驼峰 转 下划线 */
export const selectedFieldsSnakeCaseConvert = (selectedFields: string) => {
  if (!selectedFields) {
    return "";
  }

  return selectedFields
    .split(",")
    .map(item => {
      if (item.includes("field")) {
        return item;
      } else {
        return snakeCase(item);
      }
    })
    .join(",");
};

/**
 * 获取客户的资质信息
 * 1.初始化发送请求
 * 2.保存数据
 */
export const initMemberQualification = async (): Promise<Partial<IMemberQualificationType> | null> => {
  const [err, res] = await crudQualification
    .getMemberQualifications()
    .then(res => [null, res])
    .catch(err => [err, null]);
  if (err) {
    message.error(err.data.message || "客户资质信息加载失败, 请刷新页面！");
    return null;
  }
  if (res.data?.selectedFields) {
    res.data.selectedFields = selectedFieldsCamelCaseConvert(res.data.selectedFields);
  }

  return res.data;
};

/**
 * 提交客户资质申请-新增-接口请求
 * @param data
 * @returns Promise result: [err, res]
 */
export const addMemberNewQualification = async (data: Partial<qualificationAddDto>) => {
  return crudQualification
    .addNewQualification(data)
    .then(res => [null, res])
    .catch(err => [err, null]);
};

/**
 * 审核不通过资质-修改-接口请求
 * @param data
 * @returns Promise result: [err, res]
 */
export const editMemberQualification = async (data: Partial<qualificationUpdateDto>) => {
  if (data.selectedFields) {
    data.selectedFields = selectedFieldsSnakeCaseConvert(data.selectedFields);
  }
  $tools.removeFormFields(data, ["creator", "modifier", "createdDate", "modifiedDate", "expiredDate", "remark", "auditor", "auditState", "auditDate", "auditReason"]);
  return crudQualification
    .editQualification(data)
    .then(res => [null, res])
    .catch(err => [err, null]);
};

export default {
  initMemberQualification,
  qualificationDetailNameOptions,
  qualificationShowOptions,
  addMemberNewQualification,
  editMemberQualification,
  uploadFieldRules,
  qualificationTipCombinations,
};
