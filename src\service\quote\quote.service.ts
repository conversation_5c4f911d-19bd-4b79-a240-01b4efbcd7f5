import { IQuoteRequest, IQuoteProduct, IQuoteQueryDto } from "~/typings/data/quote";

export interface IQuoteService {
  /**
   * 创建询报价单
   * @param data 询报价单数据
   * @param memberId 会员ID
   */
  createQuoteRequest(data: Partial<IQuoteRequest>, memberId?: string): Promise<any>;

  /**
   * 文件上传并匹配产品
   * @param fileBuffer 文件缓冲区
   * @param fileName 文件名
   * @param quoteData 询报价基础信息
   * @param memberId 会员ID
   */
  uploadFileAndMatchProducts(
    fileBuffer: Buffer,
    fileName: string,
    quoteData: Partial<IQuoteRequest>,
    memberId?: string
  ): Promise<any>;

  /**
   * 获取询报价单列表
   * @param criteria 查询条件
   * @param memberId 会员ID
   */
  getQuoteRequestList(criteria: Partial<IQuoteQueryDto>, memberId?: string): Promise<any>;

  /**
   * 获取询报价单详情
   * @param quoteRequestId 询报价单ID
   * @param memberId 会员ID
   */
  getQuoteRequestDetail(quoteRequestId: number, memberId?: string): Promise<any>;

  /**
   * 更新询报价产品
   * @param quoteRequestId 询报价单ID
   * @param products 产品列表
   * @param memberId 会员ID
   */
  updateQuoteProducts(
    quoteRequestId: number,
    products: Partial<IQuoteProduct>[],
    memberId?: string
  ): Promise<any>;

  /**
   * 生成报价单
   * @param quoteRequestId 询报价单ID
   * @param products 产品列表
   * @param validDays 有效天数
   * @param remark 备注
   * @param memberId 会员ID
   */
  generateQuote(
    quoteRequestId: number,
    products: Partial<IQuoteProduct>[],
    validDays?: number,
    remark?: string,
    memberId?: string
  ): Promise<any>;

  /**
   * 从询报价单创建快速订单
   * @param quoteRequestId 询报价单ID
   * @param selectedProductIds 选中的产品ID列表
   * @param orderData 订单数据
   * @param memberId 会员ID
   */
  createQuickOrderFromQuote(
    quoteRequestId: number,
    selectedProductIds: number[],
    orderData: any,
    memberId?: string
  ): Promise<any>;

  /**
   * 删除询报价单
   * @param quoteRequestId 询报价单ID
   * @param memberId 会员ID
   */
  deleteQuoteRequest(quoteRequestId: number, memberId?: string): Promise<any>;

  /**
   * 产品匹配
   * @param productName 产品名称
   * @param sku 产品SKU
   * @param quantity 数量
   */
  matchProduct(productName: string, sku: string, quantity: number): Promise<any>;
}
