import React, { useState, useEffect } from "react";
import { Radio, Button } from "antd";
import { SettingOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import ReceiptFormModal from "@/components/ReceiptEditModal";
import crudReceipt from "@/apis/member/receipt";

const QuickOrderReceipt = ({ onChange }) => {
  const defaultReceipt = { receiptType: 2, id: "" };

  const [receiptList, setReceiptList] = useState([]);
  const [receiptModalVisible, setReceiptModalVisible] = useState(false);
  const [receiptMode, setReceiptMode] = useState<"add" | "edit">("add");
  const [receiptType, setReceiptType] = useState(null);
  const [currentReceipt, setCurrentReceipt] = useState(null);
  const [selectedReceipt, setSelectedReceipt] = useState(defaultReceipt);

  /* 初始化 */
  useEffect(() => {
    getReceiptList();
  }, []);

  /* 获取发票列表 */
  const getReceiptList = async () => {
    const [err, res] = await crudReceipt
      .getList()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return;
    }
    const _receiptList = res?.data ?? [];
    const _receipt = _receiptList.find(item => item.isDefault) || defaultReceipt;
    setReceiptList(_receiptList);
    // setCurrentReceipt(_receipt);
    setSelectedReceipt(_receipt);
    onChange(_receipt);
  };
  const receiptTypeData: any = [
    {
      typeName: "normal",
      typeValue: 1,
      name: "普通增值税发票",
      data: receiptList?.find(item => item.receiptType === 1),
    },
    {
      typeName: "special",
      typeValue: 0,
      name: "13%增值税专用发票",
      data: receiptList?.find(item => item.receiptType === 0),
    },
    {
      typeName: "none",
      typeValue: 2,
      name: "不开票",
      data: defaultReceipt,
    },
  ];

  const handleEditReceipt = item => {
    setReceiptMode("edit");
    setReceiptModalVisible(true);
    setReceiptType(item.data.receiptType);
    setCurrentReceipt(item.data);
  };

  const handleAddReceipt = receiptType => {
    console.log(receiptType);
    setReceiptMode("add");
    setReceiptModalVisible(true);
    setReceiptType(receiptType);
  };

  const handleOnChangeReceipt = id => {
    const receipt = receiptList.find(item => item?.id === id) || defaultReceipt;
    setSelectedReceipt(receipt);
    onChange(receipt);
  };

  return (
    <div className="side-box">
      <div className="side-box__header">
        <h2 className="side-box__header__title">发票</h2>
        <div className="side-box__header__control">
          <Button style={{ fontSize: 12, color: "#e62129" }} type="text" size="small" icon={<SettingOutlined />} href={"/ucenter/member/receipt"} target="_blank">
            管理
          </Button>
        </div>
      </div>
      <div className="side-box__body">
        <Radio.Group value={selectedReceipt?.id} onChange={e => handleOnChangeReceipt(e.target.value)}>
          {receiptTypeData.map(item => (
            <React.Fragment key={item.typeValue}>
              <Radio className="receipt-item" value={item.data?.id} disabled={!item?.data?.id && item?.data?.receiptType !== 2}>
                <div className="receipt-item__type">
                  <span>{item.name}</span>
                  {item?.data?.id && (
                    <Button type="text" size="small" icon={<EditOutlined />} onClick={() => handleEditReceipt(item)}>
                      编辑
                    </Button>
                  )}
                  {!item?.data?.id && item?.data?.receiptType !== 2 && (
                    <Button type="text" size="small" icon={<PlusOutlined />} onClick={() => handleAddReceipt(item.typeValue)}>
                      配置
                    </Button>
                  )}
                </div>
                <div className="receipt-item__desc">
                  <p>{item.data?.vatName}</p>
                  <p>{item.data?.vatId}</p>
                </div>
              </Radio>
            </React.Fragment>
          ))}
        </Radio.Group>
      </div>
      <ReceiptFormModal
        receiptType={currentReceipt?.receiptType ?? receiptType}
        callbackFunc={getReceiptList}
        modalVisible={receiptModalVisible}
        changeModalVisible={setReceiptModalVisible}
        mode={receiptMode}
        receiptId={currentReceipt?.id}
      />
    </div>
  );
};

export default QuickOrderReceipt;
