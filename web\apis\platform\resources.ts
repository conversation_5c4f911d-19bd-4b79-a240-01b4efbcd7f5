import request from "@/utils/request.util";

/**
 * 获取私有图片
 * @param type 资源类型标识
 * @param filepath 资源路径
 */
export function getPrivateImageResource(type: string, filepath: string) {
  return request({
    url: `/api/platform/resources/${type}/image`,
    method: "get",
    responseType: "blob",
    params: {
      filepath,
    },
  });
}

/**
 * 获取私有文件资源
 * @param type 资源类型标识
 * @param filepath 资源路径
 */
export function getPrivateFileResource(type: string, filepath: string) {
  return request({
    url: `/api/platform/resources/${type}/file`,
    method: "get",
    responseType: "blob",
    params: {
      filepath,
    },
  });
}

export default {
  getPrivateImageResource,
  getPrivateFileResource
};
