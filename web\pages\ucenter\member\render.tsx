import React, { useContext, useState } from "react";
import { SProps } from "ssr-types-react";
import { Ava<PERSON>, <PERSON>ton, Card, Descriptions } from "antd";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import style from "./index.module.less";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import { findGenderByValue } from "@/constants/member";
import SvgIcon from "@/components/SvgIcon";
import MemberEditModal from "@/components/MemberEditModal";
import { UserOutlined } from "@ant-design/icons";
import AvatarImageCrop from "@/components/AvatarImageCrop";
import { useOss } from "@/hooks/useOss";
import TmAreaCascade from "@/components/TmAreaCascade";

export default function MemberIndex(props: SProps) {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  if (state.member?.contactProvinceId) {
    state.member.area = [state.member.contactProvinceId, state.member.contactCityId, state.member.contactDistrictId];
  }

  const memberQuickLinks = [
    { title: "账户安全", link: "/ucenter/member/account-safe", icon: "account-safe" },
    { title: "历史订单", link: "/ucenter/trade/order", icon: "order" },
    { title: "我的地址", link: "/ucenter/member/address", icon: "address" },
    { title: "我的开票", link: "/ucenter/member/receipt", icon: "receipt" },
    { title: "我的收藏", link: "/ucenter/favorites", icon: "favorites" },
    { title: "我的资质", link: "/ucenter/member/qualifications", icon: "qualifications" },
  ];
  /* ======================================= state start======================================= */
  // 编辑弹窗
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  /* ======================================= state end======================================= */

  /* ======================================= method start======================================= */
  const handleOpenMemberInfoEdit = () => {
    setModalVisible(true);
  };
  /** 弹窗操作成功-回调 */
  const handleSuccessCallback = () => {
    history.go(0);
  };
  /** 点击入库菜单 */
  const handleTomMenu = (link: string) => {
    location.href = link;
  };

  /* ======================================= method start======================================= */
  const renderCardTitle = () => {
    const hadBindPhone = () => {
      return state.member?.phone ? (
        <span title={state.member?.phone} style={{ fontSize: "18px", marginLeft: "5px", cursor: "pointer" }}>
          <SvgIcon iconClass="logo-mobile" />
        </span>
      ) : null;
    };
    const hadBindEmail = () => {
      return state.member?.email ? (
        <span title={state.member?.email} style={{ fontSize: "18px", marginLeft: "5px", cursor: "pointer" }}>
          <SvgIcon iconClass="logo-email" />
        </span>
      ) : null;
    };
    return (
      <div className="member-information-header">
        <span>
          个人名片{hadBindPhone()}
          {hadBindEmail()}
        </span>
        <Button className="btn-mdf-info" type="link" onClick={handleOpenMemberInfoEdit}>
          修改个人名片信息
        </Button>
      </div>
    );
  };

  return (
    <div className={style.wrapper}>
      <UCenterCard title={`基本信息「${state?.member?.memberId ?? "-"}」`} />
      <div className="member-container">
        <div className="member-head">
          <div className="member-head-card">
            <div className="avatar-box">
              <Avatar className="avatar" src={useOss().generateOssFullFilepath(state?.member?.avatar)} icon={<UserOutlined />} />
              <AvatarImageCrop />
            </div>
            <div className="info-box">
              <span>{state.member?.companyName}</span>
              <span>
                {state.member?.contactName} {state.member?.contactPhone}
              </span>
              <span>上次登录时间：{state.member?.lastLoginDate || "无"}</span>
              <a href={"/ucenter/member/mdf-password"} className="btn-mdf-pass">
                修改密码
              </a>
            </div>
          </div>
          <div className="member-head-other">
            <ul>
              {memberQuickLinks.map((item, index) => (
                <li key={index} onClick={() => handleTomMenu(item.link)}>
                  <SvgIcon iconClass={item.icon} className="icon" />
                  <a href={item.link}>{item.title}</a>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <Card title={renderCardTitle()} className="member-information-box">
          <Descriptions colon={false} column={2} labelStyle={{ width: "80px", color: "#6D7278", textAlignLast: "justify", display: "inline-block" }} className="member-show">
            <Descriptions.Item label="公司名称：">{state.member?.companyName || " -- "}</Descriptions.Item>
            <Descriptions.Item label="账户昵称：">{state.member?.nickname || " -- "}</Descriptions.Item>
            <Descriptions.Item label="登录手机：">{state.member?.phone || " -- "}</Descriptions.Item>
            <Descriptions.Item label="登录邮箱：">{state.member?.email || " -- "}</Descriptions.Item>
            <Descriptions.Item label="联系人：">{state.member?.contactName || " -- "}</Descriptions.Item>
            <Descriptions.Item label="联系手机：">{state.member?.contactPhone || " -- "}</Descriptions.Item>
            <Descriptions.Item label="固定电话：">{state.member?.telephone || " -- "}</Descriptions.Item>
            <Descriptions.Item label="QQ：">{state.member?.qq || " -- "}</Descriptions.Item>
            <Descriptions.Item label="性别：">{findGenderByValue(state.member?.gender) || " -- "}</Descriptions.Item>
            <Descriptions.Item label="邮编：">{state.member?.postalCode || " -- "}</Descriptions.Item>
            <Descriptions.Item label="省市区：" className="member-pcd">
              <TmAreaCascade placeholder="--" bordered={false} initialValues={state.member.area || []} disabled />
            </Descriptions.Item>
            <Descriptions.Item label="街道地址：">
              <span>{state.member?.contactAddress || " -- "}</span>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>
      {/* 客户信息编辑器 */}
      <MemberEditModal title="编辑个人名片" modalVisible={modalVisible} changeModalVisible={setModalVisible} callbackFunc={handleSuccessCallback} />
    </div>
  );
}
