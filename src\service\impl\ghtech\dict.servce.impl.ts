import { Provide, Scope, <PERSON><PERSON><PERSON>num } from "@midwayjs/decorator";
import { IDictService } from "@/service/dict.servce";
import { BaseService } from "@/common/base/base.service";

@Provide("DictService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class DictServiceImpl extends BaseService implements IDictService {
  async getDictByCode(dictCode: string) {
    return this.easyResponse(await this.easyHttp.get(`/api/platform/dict/${dictCode}`), "获取数据出错了，请稍后再试！");
  }
}
