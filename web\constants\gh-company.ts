import commonConstant from "@/constants/common";

/** 公司位置信息 */
export const GH_COMPANY_ADDRESS = {
  locationMapUrl:
    "//map.baidu.com/poi/%E5%B9%BF%E4%B8%9C%E5%85%89%E5%8D%8E%E5%85%89%E5%8D%8E%E7%A7%91%E6%8A%80%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/@12630838.*********,2613015.2772536427,18.44z?uid=189ffa554e46d097471a327a&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo&da_src=shareurl",
  mapImgUrl: "/location-map.png",
  companyName: "广州市金华大化学试剂有限公司",
  companyTellPhone: "020-********",
  companyFax: "020-********",
  companyEmail: commonConstant.CONTACT_US_EMAIL,
  companyAddress: "广州市番禺区创启路63号清华科技园光华科技大厦A座",
  companyCode: "510450",
};

/** 光华-收款银行信息 */
export const GH_COMPANY_BANDS = {
  JHD: {
    /** 主体ing */
    img: "jhd.png",
    /** 收款公司名 */
    company: "广州市金华大化学试剂有限公司",
    /** 公司地址 */
    address: "广州市番禺区创启路63号清华科技园光华科技大厦A座",
    /** 联系电话 */
    telephone: "020-********",
    /** 传真 */
    fax: "020-********",
    /** 银行名称 */
    receiptBrand: "工商银行广州云山支行",
    /** 收款银行账号 */
    brandAccount: "3602006019200168575",
    /** 支付宝账户 */
    zhifubao: {
      value: "<EMAIL>",
      title: "广州市金华大化学试剂有限公司",
    },
  },
  GH: {
    /** 主体ing */
    img: "gh.png",
    /** 收款公司名 */
    company: "广东光华科技股份有限公司",
    /** 公司地址 */
    address: "广东省汕头市大学路295号",
    /** 联系电话 */
    telephone: "0754-********",
    /** 传真 */
    fax: "0754-********",
    /** 银行名称 */
    receiptBrand: "中国银行股份有限公司汕头科技支行",
    /** 收款银行账号 */
    brandAccount: "************",
  },
};

export default {
  GH_COMPANY_ADDRESS,
  GH_COMPANY_BANDS,
};
