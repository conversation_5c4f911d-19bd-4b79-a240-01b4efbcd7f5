import { ReactMidwayKoaFetch } from "ssr-types-react";
import { getAuthData } from "@/apis/auth";
import { ISEOParams } from "~/typings/data/seo/seo";

const fetch: ReactMidwayKoaFetch<{
  layoutService: {
    initSiteLayoutData: () => Promise<any>;
  };
}> = async ({ ctx, routerProps }) => {
  const layoutInitData = __isBrowser__ ? {} : await ctx!.layoutService?.initSiteLayoutData();

  const currentPath: string = __isBrowser__ ? location.pathname : ctx?.request.path;
  // 获取path后缀默认选中
  let navSelectedKeys: string[] = [];
  if (currentPath) {
    navSelectedKeys = [currentPath.substring(currentPath.lastIndexOf("/") + 1)];
  }
  let userData = ctx?.session?.userData ?? null;
  let userLoginState = ctx?.session?.userLoginState ?? false;
  // 获取登录信息
  if (__isBrowser__ && !userData) {
    // 处理csr模式下登录
    const [err, res] = await getAuthData()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err && res.status === 200) {
      userData = res.data.userData;
      userLoginState = res.data.userLoginState;
    }
  }
  const currentLocalPath = ctx?.path;
  const handlePatternSeo = () => {
    // 处理seo
    let seoDefaultParams: ISEOParams = {
      description:
        "光华易购是光华科技精心打造的电商平台，旨在于为企业和科研机构提供实验室一站式打包服务。品种齐全：平台经营产品超过14万种，几乎覆盖高校实验室所需所有试剂（国产、进口），品质可靠：公司深耕试剂行业四十余年，为国内外超过2万家客户提供高品质产品服务，优惠多多，报账无忧，物流高效，订单智能化",
      keyword: "光华易购，金华大化学试剂，华大，华大试剂，进口试剂，金华大，JHD，HUADA，GHTECH",
      title: "光华易购_光华科技旗下电商平台_股票代码:002741",
    };
    try {
      const seoDataList: ISEOParams[] = layoutInitData?.seoData;
      const pattern2Seo = seoDataList?.find(seo => {
        if (!seo.isReg) {
          return seo.path === currentLocalPath;
        } else {
          const reg = new RegExp("^" + seo.path, "g");
          return reg.test(currentLocalPath) && seo.isReg;
        }
      });
      pattern2Seo && (seoDefaultParams = pattern2Seo);
      delete layoutInitData?.seoData;
    } catch (e) {
      console.error("pattern seo error：", e);
    }
    return seoDefaultParams;
  };

  return {
    userData,
    userLoginState,
    layoutInitData,
    patternSeo: handlePatternSeo(),
    websiteLanguage: ctx?.websiteLanguage || "zh",
    redirect: ctx?.url,
    currentPath: currentLocalPath,
    navSelectedKeys,
    urlCurrentParams: ctx?.query || {},
    isEnabledBDTJ: process.env.BAIDU_TJ_ENABLED === "1",
    isOpenProductMinPackingRatioControl: process.env.OPEN_CHECK_PRODUCT_PACKING_RATIO_CONTROL === "true",
  };
};
export default fetch;
