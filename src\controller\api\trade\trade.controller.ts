import { BaseController } from "@/controller/base.controller";
import { Body, Config, Controller, Inject, Post } from "@midwayjs/decorator";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { ITradeService } from "@/service/trade/trade.service";
import { IMemberService } from "@/service/member/member.service";
import { IAddressService } from "@/service/member/address.service";
import { IOrderFreightService } from "@/service/order/freight.service";
import { TradeCreateVoDto } from "@/dto/trade-create.dto";
import { Validate } from "@midwayjs/validate";
import { OrderSourceEnum } from "@/common/enums/order.source.enum";
import { OrderFreightTypeEnum } from "@/common/enums/order.freight.type.enum";

@Controller("/api/trade", { middleware: [AuthenticationMiddleware] })
export class TradeController extends BaseController {
  @Config("systemModalOpenConfig")
  systemModalOpenConfig;

  @Inject("TradeService")
  tradeService: ITradeService;

  @Inject("MemberService")
  memberService: IMemberService;

  @Inject("AddressService")
  addressService: IAddressService;

  @Inject("OrderFreightService")
  freightService: IOrderFreightService;

  @Post("/create")
  @Validate()
  async createTrade(@Body() orderCreateVo: TradeCreateVoDto) {
    const { ctx } = this;

    // 添加配置项控制是否开启erp检查
    // 检测客户是否有关联了erp账户，未关联-不能下单
    if (this.systemModalOpenConfig.checkMemberIsErpControl) {
      const memberInfoRes = await this.memberService.getInfoByMemberId(this.getMemberId());
      if (!memberInfoRes?.data || !memberInfoRes?.data?.erpCustomerCode) {
        return ctx.getResponseInstance(ctx).sendFail("亲爱的客户，您的账户还未通过系统身份认证，暂时无法下订单，请联系业务员或客服！", 403);
      }
    }

    // 客户编号
    orderCreateVo.memberId = this.getMemberId();
    // 订单运费，默认运费为: 0
    orderCreateVo.freightFee = 0;
    // 订单来源渠道:1-后台;2-官网;3-小程序;4-app
    orderCreateVo.sourceChannel = OrderSourceEnum.Website;
    // 运费类型：运费类型,0-不显示此项;1-产品单价包含运费;2-指定运费
    orderCreateVo.freightType = OrderFreightTypeEnum.DesignatedFreight;
    const res = await this.tradeService.createTrade(orderCreateVo);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Post("/quickOrderCreate")
  @Validate()
  async createQuickOrderTrade(@Body() orderCreateVo: TradeCreateVoDto) {
    const { ctx } = this;

    // 添加配置项控制是否开启erp检查
    // 检测客户是否有关联了erp账户，未关联-不能下单
    if (this.systemModalOpenConfig.checkMemberIsErpControl) {
      const memberInfoRes = await this.memberService.getInfoByMemberId(this.getMemberId());
      if (!memberInfoRes?.data || !memberInfoRes?.data?.erpCustomerCode) {
        return ctx.getResponseInstance(ctx).sendFail("亲爱的客户，您的账户还未通过系统身份认证，暂时无法下订单，请联系业务员或客服！", 403);
      }
    }

    // 客户编号
    orderCreateVo.memberId = this.getMemberId();
    // 订单运费，默认运费为: 0
    orderCreateVo.freightFee = 0;
    // 订单来源渠道:1-后台;2-官网;3-小程序;4-app
    orderCreateVo.sourceChannel = OrderSourceEnum.Website;
    // 运费类型：运费类型,0-不显示此项;1-产品单价包含运费;2-指定运费
    orderCreateVo.freightType = OrderFreightTypeEnum.DesignatedFreight;
    const res = await this.tradeService.createQuickOrderTrade(orderCreateVo);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Post("/commit")
  async editTrade(@Body() orderCreateVo: TradeCreateVoDto) {
    const { ctx } = this;

    // 添加配置项控制是否开启erp检查
    // 检测客户是否有关联了erp账户，未关联-不能下单
    if (this.systemModalOpenConfig.checkMemberIsErpControl) {
      const memberInfoRes = await this.memberService.getInfoByMemberId(this.getMemberId());
      if (!memberInfoRes?.data || !memberInfoRes?.data?.erpCustomerCode) {
        return ctx.getResponseInstance(ctx).sendFail("亲爱的客户，您的账户还未通过系统身份认证，暂时无法下订单，请联系业务员或客服！", 403);
      }
    }

    // 客户编号
    orderCreateVo.memberId = this.getMemberId();
    // 订单运费，默认运费为: 0
    orderCreateVo.freightFee = 0;
    // 订单来源渠道:1-后台;2-官网;3-小程序;4-app
    orderCreateVo.sourceChannel = OrderSourceEnum.Website;
    // 运费类型：运费类型,0-不显示此项;1-产品单价包含运费;2-指定运费
    orderCreateVo.freightType = OrderFreightTypeEnum.DesignatedFreight;
    const res = await this.tradeService.editTrade(orderCreateVo);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }
}
