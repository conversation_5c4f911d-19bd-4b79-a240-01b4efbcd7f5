// const qs = require('querystring')

export abstract class HttpCallable {
  abstract send(method: string, url: string, data?: object, headers?: object): Promise<any>;

  public async get(url: string, data?: object, headers?: object) {
    return await this.send("GET", url, data, headers);
  }

  public async post(url: string, data?: object, headers?: object) {
    return await this.send("POST", url, data, headers);
  }

  public async put(url: string, data?: object, headers?: object) {
    return await this.send("PUT", url, data, headers);
  }

  public async delete(url: string, data?: object, headers?: object) {
    // url = `${url}?${qs.stringify(data)}`
    return await this.send("DELETE", url, data, headers);
  }
}
