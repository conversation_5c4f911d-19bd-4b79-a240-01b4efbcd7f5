import React, { forwardRef, useImperative<PERSON>andle, useState } from "react";
import { getPrefixCls } from "@/utils/props-tools.util";
import { useIntlMessage } from "@/hooks/useIntlMessage";
import { $tools } from "@/utils/tools.util";
import { Form, Input, Popover } from "antd-mobile";
import "./index.module.less";
import { useForm } from "rc-field-form";
import { EyeInvisibleOutline, EyeOutline, CloseOutline, CheckOutline } from "antd-mobile-icons";

interface PasswordProps {
  prefixCls: string;
  passwordLabel: string;
  repeatLabel: string;
  repeat: boolean;
  minLength: number;
  maxLength: number;
  complexity: boolean;
  complexityTip: string;
  level: object;
  rules: object;
  width: number;
  height: number;
  radius: number;
  bgColor: string;
  showLineTransition: any; // 是否展示分割线动画，传入组件即可
}
type PasswordPartialProps = Partial<PasswordProps> & { pwdFormRef: typeof useForm | any };

const PasswordItem = forwardRef((props: PasswordPartialProps, ref: any) => {
  const { t } = useIntlMessage();
  const prefixCls = getPrefixCls("password", props.prefixCls);
  const langCls = getPrefixCls(`lang-zh`);
  const level = props.level ?? {
    1: t("password.lv1"),
    2: t("password.lv2"),
    3: t("password.lv3"),
    4: t("password.lv4"),
  };
  const tip = props.complexityTip ?? t("password.tip");

  const passwordFormRef: any = props.pwdFormRef;
  const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
  const checkPassword = async (_rule: any, value: string) => {
    if ($tools.isEmpty(value)) {
      setParams(params => {
        params.password = {
          strength: 0,
          tips: null,
          length: false,
          format: false,
          complexity: false,
        };
        return { ...params };
      });
      throw t("password.setting");
    } else {
      setParams(params => {
        params.password.format = true;
        return { ...params };
      });
      if (props.minLength && value.length < props.minLength) {
        setParams(params => {
          params.password.length = false;
          params.password.strength = 0;
          params.password.tips = null;
          return { ...params };
        });
        throw t("password.least", { min: props.minLength });
      }
      if (props.complexity) {
        if ($tools.checkPassword(value)) {
          const strength = $tools.getPasswordStrength(value);
          setParams(params => {
            params.password.length = true;
            params.password.strength = strength;
            params.password.tips = level[strength];
            return { ...params };
          });
          if (strength <= 1) {
            setParams(params => {
              params.password.complexity = false;
              return { ...params };
            });
            throw tip;
          } else {
            setParams(params => {
              params.password.complexity = true;
              return { ...params };
            });
            return await Promise.resolve();
          }
        }
      } else {
        const strength = $tools.getPasswordStrength(value);
        setParams(params => {
          params.password.length = true;
          params.password.strength = strength;
          params.password.complexity = true;
          params.password.tips = level[strength];
          return { ...params };
        });
        return await Promise.resolve();
      }
    }
  };
  const checkRepeat = async (_rule: any, value: string) => {
    if ($tools.isEmpty(value)) {
      throw t("password.repeat");
    } else {
      if (params.form.validate.password !== value) {
        throw t("password.different");
      }
      return await Promise.resolve();
    }
  };

  const [params, setParams] = useState({
    visible: false,
    repeatVisible: false,
    password: {
      strength: 0,
      tips: null,
      length: false,
      format: false,
      complexity: false,
    },
    form: {
      validate: {
        password: null,
        repeat: null,
      },
      rules: {
        password: [{ required: true, validator: checkPassword }],
        repeat: [{ required: props.repeat, validator: checkRepeat }],
      },
    },
  });
  const rules = Object.assign({}, params.form.rules, props.rules);
  /* 方法事件 */
  const onInput = (val: any) => {
    setPopoverVisible(true);
    setParams(params => {
      params.form.validate.password = val;
      return { ...params };
    });
    /* 更新值 */
    if (props.repeat && params.form.validate.repeat) {
      passwordFormRef?.validateFields(["repeat"]);
    }
  };
  const onVisible = (visible: boolean) => {
    setParams(params => {
      return {
        ...params,
        visible: visible,
      };
    });
  };
  const onRepeatInput = (val: any) => {
    setParams(params => {
      params.form.validate.repeat = val;
      return { ...params };
    });
  };
  const onRepeatVisible = (repeatVisible: boolean) => {
    setParams(params => {
      return {
        ...params,
        repeatVisible: repeatVisible,
      };
    });
  };
  const handlePopoverVisibleEvent = (field?: string, visible?: boolean) => {
    field === "password" && setPopoverVisible(visible ?? false);
  };
  const renderPassword = (value: any, inputHandler: (...args: any) => any, inputVisible: boolean, field?: string, placeholder?: string) => {
    placeholder = placeholder ?? t("password.placeholder");
    let inputType = "password";
    if (inputVisible) {
      inputType = "text";
    }
    return (
      <Input
        maxLength={props.maxLength}
        className="input"
        value={value}
        onChange={inputHandler}
        placeholder={placeholder}
        autoComplete="off"
        style={{
          width: $tools.convert2Rem(props.width),
          height: $tools.convert2Rem(props.height),
          borderRadius: $tools.convert2Rem(props.radius),
          background: props.bgColor ?? "",
        }}
        onBlur={() => handlePopoverVisibleEvent(field, false)}
        onFocus={() => handlePopoverVisibleEvent(field, true)}
        type={inputType}
      />
    );
  };

  const renderPopover = () => {
    const strengthCls = getPrefixCls("password-strength", props.prefixCls);
    return (
      <div className={`${prefixCls}-tips`}>
        <div className={`${strengthCls}-item`}>
          <span className={`${strengthCls}-password-strong`}>{t("password.strong")}</span>
          <div className={`${strengthCls}-group`}>
            <i className={`${strengthCls}${params.password.strength > 0 ? " active" : ""}`} />
            <i className={`${strengthCls}${params.password.strength > 1 ? " active" : ""}`} />
            <i className={`${strengthCls}${params.password.strength > 2 ? " active" : ""}`} />
            <i className={`${strengthCls}${params.password.strength > 3 ? " active" : ""}`} />
          </div>
          <span className="theme-color" dangerouslySetInnerHTML={{ __html: params.password.tips ?? "" }} />
        </div>
        <div className={`${strengthCls}-item`}>
          {params.password.length ? <CheckOutline className="success" /> : <CloseOutline className="failed" />}
          <span>{t("password.size", { min: props.minLength, max: props.maxLength })}</span>
        </div>
        <div className={`${strengthCls}-item`}>
          {params.password.format ? <CheckOutline className="success" /> : <CloseOutline className="failed" />}
          <span>{t("password.format")}</span>
        </div>
        {props.complexity ? (
          <div className={`${strengthCls}-item`}>
            {params.password.complexity ? <CheckOutline className="success" /> : <CloseOutline className="failed" />}
            <span>{tip}</span>
          </div>
        ) : null}
      </div>
    );
  };

  const renderRepeat = () => {
    return props.repeat ? (
      <Form.Item
        name="repeat"
        label={props.repeatLabel}
        className="item"
        rules={rules.repeat}
        extra={<>{!params.repeatVisible ? <EyeInvisibleOutline onClick={() => onRepeatVisible(true)} /> : <EyeOutline onClick={() => onRepeatVisible(false)} />}</>}
      >
        {renderPassword(params.form.validate.repeat, onRepeatInput, params.repeatVisible, t("password.repeat"))}
      </Form.Item>
    ) : null;
  };

  const validateFields = async (fields: []) => {
    await passwordFormRef.validateFields(fields);
  };

  useImperativeHandle(ref, () => ({
    validateFields,
  }));

  return (
    <>
      <Popover trigger="click" placement="top" content={renderPopover()} visible={popoverVisible}>
        <Form.Item
          name="password"
          label={props.passwordLabel}
          className="item"
          rules={rules.password}
          extra={<>{!params.visible ? <EyeInvisibleOutline onClick={() => onVisible(true)} /> : <EyeOutline onClick={() => onVisible(false)} />}</>}
        >
          {renderPassword(params.form.validate.password, onInput, params.visible, "password")}
        </Form.Item>
      </Popover>
      {props?.showLineTransition ? props?.showLineTransition : null}
      {renderRepeat()}
    </>
  );
});

// 设置默认值，保证 props的值不为undefined，避免报错
PasswordItem.defaultProps = {
  repeat: true,
  complexity: true,
  minLength: 6,
  maxLength: 32,
  height: 40,
  radius: 5,
  passwordLabel: "登录密码",
  repeatLabel: "确认密码",
};
// @ts-expect-error
export default PasswordItem;
