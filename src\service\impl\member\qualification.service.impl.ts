import { Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IQualificationService } from "@/service/member/qualification.service";
import { qualificationAddDto, qualificationUpdateDto } from "~/typings/data/member/qualification";

@Provide("QualificationService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class QualificationServiceImpl extends BaseService implements IQualificationService {
  /**
   * 获取客户的资质列表
   * @param memberId
   */
  async getMemberQualifications(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/qualifications/${memberId}`));
  }

  /**
   * 新增资质
   * @param data
   */
  async addQualification(data: qualificationAddDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post(`/api/members/qualifications`, data));
  }

  /**
   * 修改资质
   * @param qualificationId
   * @param data
   */
  async updateQualification(qualificationId: number, data: qualificationUpdateDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.put(`/api/members/qualifications/${qualificationId}`, data));
  }
}
