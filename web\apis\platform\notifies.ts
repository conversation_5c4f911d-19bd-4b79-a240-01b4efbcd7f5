import request from "@/utils/request.util";
import { INotifiesQuery } from "~/typings/data/spread";

export function getNotifies(params: INotifiesQuery) {
  return request({
    url: `/api/platform/sys-notifies`,
    method: "get",
    params
  });
}

export function getNotifyDetailByCode(code: string) {
  return request({
    url: `/api/platform/sys-notifies/${code}`,
    method: "get",
  });
}

export default {
  getNotifies,
  getNotifyDetailByCode
};
