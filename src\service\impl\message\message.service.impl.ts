import { Provide, Scope, <PERSON><PERSON><PERSON><PERSON> } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IMessageService } from "@/service/message/message.service";
import { logisticsQueryListDto } from "~/typings/data/message/message";

@Provide("MessageService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class MessageServiceImpl extends BaseService implements IMessageService {
  async getPageList(criteria: Partial<logisticsQueryListDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/order/erp2outbound-logistics/getOrderErp2outboundLogisticsList`, criteria));
  }

}
