const DEFAULT_CONFIG = {
  id: "id",
  children: "children",
  pid: "pid",
};

/**
 * 处理树的工具类，参考 tree-tool.js改造
 */
class TreeTool {
  private static instance: TreeTool;
  private static config: object;

  static getInstance(config?: object) {
    if (!this.instance) {
      this.instance = new TreeTool(config);
    }
    return this.instance;
  }

  private constructor(config?: object) {
    TreeTool.config = config || {};
  }

  getConfig() {
    return Object.assign({}, DEFAULT_CONFIG, TreeTool.config);
  }

  setConfig(config: object) {
    TreeTool.config = config;
  }

  /**
   * 列表结构转树结构
   *
   * @param list
   */
  fromList(list) {
    const config = this.getConfig();
    const nodeMap = new Map();
    const result = [];
    const { id, children, pid } = config;
    for (const node of list) {
      node[children] = node[children] || [];
      nodeMap.set(node[id], node);
    }
    for (const node of list) {
      const parent = nodeMap.get(node[pid]);
      (parent ? parent.children : result).push(node);
    }
    return result;
  }

  /**
   * 树结构转列表结构
   *
   * @param tree
   */
  toList(tree) {
    const config = this.getConfig();
    const { children } = config;
    const result = [...tree];
    for (let i = 0; i < result.length; i++) {
      if (!result[i][children]) continue;
      result.splice(i + 1, 0, ...result[i][children]);
    }
    return result;
  }

  /**
   * 查找符合条件的单个节点[返回广度优先遍历查找到的第一个符合条件(callback(node)为true)的节点，没有则返回null]
   *
   * @param tree
   * @param func
   */
  findNode(tree, func: Function) {
    const config = this.getConfig();
    const { children } = config;
    const list = [...tree];
    for (const node of list) {
      if (func(node)) return node;
      node[children] && list.push(...node[children]);
    }
    return null;
  }

  /**
   * 查找符合条件的所有节点
   *
   * @param tree
   * @param func
   */
  findNodeAll(tree, func: Function) {
    const config = this.getConfig();
    const { children } = config;
    const list = [...tree];
    const result = [];
    for (const node of list) {
      // @ts-expect-error
      func(node) && result.push(node);
      node[children] && list.push(...node[children]);
    }
    return result;
  }

  /**
   * 查找符合条件的单个节点的路径
   * 返回符合条件(callback(node)为true)的节点的所有祖先节点有序组成的数组，没有找到节点则返回null
   *
   * @param tree
   * @param func
   */
  findPath(tree, func: Function) {
    const config = this.getConfig();
    const path = [];
    const list = [...tree];
    const visitedSet = new Set();
    const { children } = config;
    while (list.length) {
      const node = list[0];
      if (visitedSet.has(node)) {
        path.pop();
        list.shift();
      } else {
        visitedSet.add(node);
        node[children] && list.unshift(...node[children]);
        // @ts-expect-error
        path.push(node);
        if (func(node)) return path;
      }
    }
    return null;
  }

  /**
   * 查找符合条件的所有节点的路径
   *
   * 返回符合条件(callback(node)为true)的节点路径组成的数组
   * @param tree
   * @param func
   */
  findPathAll(tree, func: Function) {
    const config = this.getConfig();
    const path = [];
    const list = [...tree];
    const result = [];
    const visitedSet = new Set();
    const { children } = config;
    while (list.length) {
      const node = list[0];
      if (visitedSet.has(node)) {
        path.pop();
        list.shift();
      } else {
        visitedSet.add(node);
        node[children] && list.unshift(...node[children]);
        // @ts-expect-error
        path.push(node);
        // @ts-expect-error
        func(node) && result.push([...path]);
      }
    }
    return result;
  }

  /**
   * 返回符合筛选条件(callback(node)为true)的树节点构成的树，一个节点符合条件，其祖先节点也会被保留返回
   * @param tree
   * @param func
   */
  filter(tree, func: Function) {
    const config = this.getConfig();
    const { children } = config;
    function listFilter(list) {
      return list
        .map(node => ({ ...node }))
        .filter(node => {
          node[children] = node[children] && listFilter(node[children]);
          return func(node) || node[children]?.length;
        });
    }
    return listFilter(tree);
  }

  /**
   * 树结构遍历
   *
   * 对于所有节点node调用callback(node)，深度优先
   * @param tree
   * @param func
   */
  forEach(tree, func: Function) {
    const config = this.getConfig();
    const list = [...tree];
    const { children } = config;
    for (let i = 0; i < list.length; i++) {
      func(list[i]);
      list[i][children] && list.splice(i + 1, 0, ...list[i][children]);
    }
  }

  _insert(tree, node, targetNode, after) {
    const config = this.getConfig();
    const { children } = config;
    function insert(list) {
      const idx = list.indexOf(node);
      idx < 0 ? list.forEach(n => insert(n[children] || [])) : list.splice(`${idx} ${after}`, 0, targetNode);
    }
    // @ts-expect-error
    insert(tree, node);
  }

  /**
   * 在指定oldNode前插入newNode
   * 如果树中没有oldNode，则不会改变原数组。注意oldNode和newNode的参数顺序，和它们在树中的顺序一致
   * @param tree
   * @param newNode
   * @param oldNode
   */
  insertBefore(tree, newNode, oldNode) {
    this._insert(tree, oldNode, newNode, 0);
  }

  /**
   * 在指定oldNode后插入newNode
   * 如果树中没有oldNode，则不会改变原数组。注意oldNode和newNode的参数顺序，和它们在树中的顺序一致
   * @param tree
   * @param oldNode
   * @param newNode
   */
  insertAfter(tree, oldNode, newNode) {
    this._insert(tree, oldNode, newNode, 1);
  }

  /**
   * 删除符合条件的所有节点
   *
   * 删除符合条件(callback(node)为true)的所有节点及其子节点
   * @param tree
   * @param func
   */
  removeNode(tree, func: Function) {
    const config = this.getConfig();
    const { children } = config;
    const list = [tree];
    while (list.length) {
      const nodeList = list.shift();
      // eslint-disable-next-line no-sequences
      const delList = nodeList.reduce((r, n, idx) => (func(n) && r.push(idx), r), []);
      delList.reverse();
      delList.forEach(idx => nodeList.splice(idx, 1));
      const childrenList = nodeList.map(n => n[children]).filter(l => l?.length);
      list.push(...childrenList);
    }
  }
}

export const treeToolHandlerInstance: TreeTool = TreeTool.getInstance({});
