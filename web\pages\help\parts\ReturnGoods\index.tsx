import React, { memo } from "react";
import "./index.less";

export default memo(function ReturnGoods() {
  return (
    <section className="article">
      <div className="article-paragraph">
        <h2 className="article-title">关于退换货</h2>
        <ul className="return-goods-explain">
          <li>1. 为保障您的权益，请在收货时当着配送人员的面检查外包装有无破损、泄漏；如果发现有此类问题请拒收并立即联系我们。</li>
          <li>
            2. 光华易购电商承诺自顾客收到商品之日起7日内（以配送签收日期为准），符合以下情况的，我们给予退换货：
            <ul className="conditionds">
              <li>1）货不对板的产品。</li>
              <li>2）产品质量问题：产品的外观、状态、理化特性等指标，如与该产品质量标准中的指标不符，在与我方技术部门沟通，确定属于质量问题的产品。</li>
              <li>3）物流运输问题：在运输过程中造成产品的泄漏、破碎、污损等。</li>
            </ul>
          </li>
        </ul>
      </div>
      <div className="article-paragraph">
        <h3 className="article-subtitle">说明：</h3>
        <div className="article-content">
          <ul>
            <li>1）退换货时请尽量保持出售时的原质原样，不影响二次销售；</li>
            <li>2）确保商品及配件、或者赠品、发票、发货清单等齐全；</li>
            <li>3）填写退换货售后申请；</li>
          </ul>
        </div>
      </div>
      <div className="article-paragraph">
        <h3 className="article-subtitle">退换货流程：</h3>
        <div className="article-content">
          <ul>
            <li>
              1.双方沟通 {"=>"} 2.购买方填写退换货售后申请 {"=>"} 3.我方对退换货理由进行核实确认 {"=>"} 4.退货 {"=>"} 5.换货或退款。
            </li>
            <li>
              2. 以下情况恕不予办理退换货服务：
              <ul>
                <li>1）易制爆、易制毒化学品、监控品；</li>
                <li>2）因客户保存不当或使用环境限制造成的与实际情况不符的产品；</li>
                <li>3）任何非经光华易购公司售出的光华易购产品以及产品无标签，或标签已无法识别是否属于光华易购试剂的产品；</li>
                <li>4）已超过受理期限的产品； </li>
              </ul>
            </li>
            <li>3. 当您发生要退/换货时，请先联系客服人员：020-84383047。</li>
            <li>4. 退货地址：请在退货前与我们的客服人员联系具体退货地址。</li>
            <li>5. 以上各条款，光华易购保留根据需要随时更改或终止的权利，并拥有最终解释权。</li>
          </ul>
        </div>
      </div>
    </section>
  );
});
