import React, { useContext, useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import { List, Button } from "antd";
import { KeyOutlined, MobileOutlined, MailOutlined } from "@ant-design/icons";
import PhoneBindModal from "./cnpts/PhoneBindModal";
import EmailBindModal from "./cnpts/EmailBindModal";
import style from "./index.module.less";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";

const phoneBindMapping: any = {
  bind: {
    title: "绑定手机",
    desc: "关联手机号，可用于登录与找回密码。",
  },
  rebind: {
    title: "换绑手机",
    desc: "若您手机需要进行替换，请在此处进行操作。",
  },
};
const emailBindMapping = {
  bind: {
    title: "绑定邮箱",
    desc: "关联安全邮箱，可用于登录，便于找回密码。",
  },
  rebind: {
    title: "换绑邮箱",
    desc: "若您邮箱需要进行替换，请在此处进行操作。",
  },
};

export default function MemberQualificationIndex(props: SProps) {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  /* ======================================= state start======================================= */
  // 邮箱绑定弹窗
  const [emailBindModalVisible, setEmailBindModalVisible] = useState<boolean>(false);
  // 手机绑定弹窗
  const [phoneBindModalVisible, setPhoneBindModalVisible] = useState<boolean>(false);
  const [phoneBindSituation, setPhoneBindSituation] = useState<any>(phoneBindMapping.bind);
  const [emailBindSituation, setEmailBindSituation] = useState<any>(emailBindMapping.bind);
  /* ======================================= state end======================================= */

  useEffect(() => {
    initBindSituation();
  }, []);
  /* ======================================= method start======================================= */
  const initBindSituation = () => {
    const member = state?.userData;
    if (member?.phone) {
      phoneBindMapping.rebind.desc += "当前已绑定手机：" + member?.phone;
      setPhoneBindSituation(phoneBindMapping.rebind);
    }
    if (member?.email) {
      emailBindMapping.rebind.desc = emailBindMapping.rebind.desc + "当前已绑定邮箱：" + member?.email;
      setEmailBindSituation(emailBindMapping.rebind);
    }
  };
  const handleEmailBindCallback = () => {
    window.location.reload();
  };
  const handlePhoneBindCallback = () => {
    window.location.reload();
  };
  const gotoModifyPassword = () => {
    window.location.href = "/ucenter/member/mdf-password";
  };
  /* ======================================= method end======================================= */
  return (
    <>
      <UCenterCard title={"账户安全"} />
      <div className={style.wrapper}>
        <List size="default" bordered={false} className="account-safe-box">
          <List.Item
            className="account-safe-box-item"
            extra={
              <div className="action">
                <Button onClick={gotoModifyPassword}>修改密码</Button>
              </div>
            }
          >
            <div className="item-icon">
              <KeyOutlined />
            </div>
            <List.Item.Meta title={<span className="title">登录密码</span>} description={<span className="title subtitle">互联网账号存在被盗风险，建议您定期更改密码以保护账户安全。</span>} />
          </List.Item>
          <List.Item
            className="account-safe-box-item"
            extra={
              <div className="action">
                <Button onClick={() => setPhoneBindModalVisible(true)}>{phoneBindSituation.title}</Button>
              </div>
            }
          >
            <div className="item-icon">
              <MobileOutlined />
            </div>
            <List.Item.Meta title={<span className="title">{phoneBindSituation.title}</span>} description={<span className="title subtitle">{phoneBindSituation.desc}</span>} />
          </List.Item>
          <List.Item
            className="account-safe-box-item"
            extra={
              <div className="action">
                <Button onClick={() => setEmailBindModalVisible(true)}>{emailBindSituation.title}</Button>
              </div>
            }
          >
            <div className="item-icon">
              <MailOutlined />
            </div>
            <List.Item.Meta title={<span className="title">{emailBindSituation.title}</span>} description={<span className="title subtitle">{emailBindSituation.desc}</span>} />
          </List.Item>
        </List>
      </div>
      {/* 换绑邮箱弹窗 */}
      <EmailBindModal
        title={emailBindSituation.title}
        originData={state?.userData?.email}
        modalVisible={emailBindModalVisible}
        changeModalVisible={setEmailBindModalVisible}
        callbackFunc={handleEmailBindCallback}
      />

      {/* 换绑手机号弹窗 */}
      <PhoneBindModal
        title={phoneBindSituation.title}
        originData={state?.userData?.phone}
        modalVisible={phoneBindModalVisible}
        changeModalVisible={setPhoneBindModalVisible}
        callbackFunc={handlePhoneBindCallback}
      />
    </>
  );
}
