version: '3.6'
x-default-volumes: &node_portal_volumes
  volumes:
    - ${DOCKER_NODE_LOG_MAPPING}:/app/logs:rw,cached
    - ./src:/app/src:rw,rslave
    - ./web:/app/web:rw,rslave
    - ./public:/app/public:rw,rslave
    - ./typings:/app/typings:rw,rslave
    - ./config.ts:/app/config.ts:rw,rslave
    - ./pm2.config.js:/app/pm2.config.js:rw,rslave
    - ./pm2.prod.config.js:/app/pm2.prod.config.js:rw,rslave
    - /etc/localtime:/etc/localtime
    - /usr/share/zoneinfo/Asia/:/usr/share/zoneinfo/Asia/
services:
  node-ghmall-portal:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: ${DOCKER_CONTAINER_NAME}
    restart: unless-stopped
    privileged: true
    ports:
      - "3002:3001"
    extra_hosts:
      - java-api.guanghuayigou.com:**************
    environment:
      - TZ=Asia/Shanghai
      - NODEJS_PM2_OPTS=${NODEJS_PM2_OPTS}
    <<: *node_portal_volumes
    networks:
      ghmall_network:
        ipv4_address: ${DOCKER_IP_ADDRESS}
networks:
  ghmall_network:
    external: true
