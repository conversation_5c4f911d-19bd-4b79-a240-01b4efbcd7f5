import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import style from "./index.module.less";
import TmModal from "@/components/TmModal";
import TmUploadImage from "@/components/TmUploadImage";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { qualificationAddDto } from "~/typings/data/member/qualification";
import { QUALIFICATION_PRODUCT_TYPE as productDangerTypes, QUALIFICATION_MEMBER_TYPE as memberTypes, QUALIFICATION_LOCATION_TYPE as locationTypes } from "@/constants/qualification";
import { downloadFromOssFilepath } from "@/utils/download.util";
import commonConstant from "@/constants/common";
import type { FormInstance } from "antd/es/form";
import { IMemberQualificationType } from "@/typings/member.interface";
import { Space, Card, Form, Image, Segmented, Button, message, notification, Row, Col, Spin, Modal, Alert } from "antd";
import {
  initMemberQualification,
  qualificationDetailNameOptions,
  qualificationShowOptions,
  addMemberNewQualification,
  editMemberQualification,
  uploadFieldRules,
  qualificationTipCombinations,
} from "@/pages/ucenter/member/qualifications/qualification.util";
import type { UploadFile } from "antd/es/upload/interface";
import TmConfirmModal from "@/components/TmConfirmModal";

interface IQualificationProp {
  /** 弹窗操作-成功回调 */
  modalActionSuccessCallback: () => void;
}

/** 上传进度标识 */
const UPLOAD_STEP_CONSTANT = {
  PRODUCT: "0",
  MEMBER: "1",
  UPLOAD: "2",
  COMMIT: "3",
};

/**
 * 资质组件
 * 新增 - 产品类型+客户类型+区域类型=>需要上传哪些材料需要上传
 * 编辑 - 只有资质申请被不通过，才能修改重新申请，其余情况不能修改
 */
const QualificationEditModal = forwardRef((props: IQualificationProp, ref: any) => {
  const [memberQualification, setMemberQualification] = useState<Partial<IMemberQualificationType>>();
  const [initLoading, setInitLoading] = useState(false);
  // 设置客户编号-用于区别资质的新增还是编辑
  const [memberId, setQualificationEditMember] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  // 资质面板弹窗控制
  const [visible, setVisible] = useState<boolean>(false);
  // 产品类型、客户类型、区域位置 tab选项栏配置
  const [productType, setProductType] = useState("");
  const [memberType, setMemberType] = useState("");
  const [locationType, setLocation] = useState("");
  // 产品-客户-区域-选中的记录
  const [targetMap, setTargetMap] = useState("");
  const [form] = Form.useForm();
  /** 上传formRef */
  const qualificationUploadFormRef = useRef<FormInstance>(null);
  /** 需要展示的上传项字符串 */
  const [selectQualificationDesc, setSelectQualificationDesc] = useState("");
  // 上传图片大小限制
  const uploadImageSizeLimit = 5;
  // 上传图片格式提示
  const imageTypeLimitTip = "只允许上传 jpg|png|jpeg 格式图片！";
  // 上传图片格式限制
  const imageTypeLimit = ["image/jpg", "image/png", "image/jpeg"];
  const [loading, setLoading] = useState(false);
  // 步骤-进度
  const uploadStepArray = ["产品类型", "选择身份", "上传资质", "提交审核"];
  const [currentUploadStep, setCurrentUploadStep] = useState("-1");
  const [formInitialValues, setFormInitialValues] = useState<any>({
    productType: "",
    memberType: "",
    locationType: "",
  });
  /* ======================================= 初始化 =========================================== */

  /* ======================================= 监听 start =========================================== */
  useEffect(() => {
    if (productType) {
      watchProductMemberAndLocationChange();
    }
  }, [productType, memberType, locationType]);

  useEffect(() => {
    // 当设置了memberId,即为编辑模式
    if (memberId) {
      setIsEditMode(true);
      // 初始化qualificationForm
      initQualificationData();
    }
  }, [memberId]);
  /* ======================================== 监听 send ========================================== */

  /* ======================================== 对外方法 =============================================== */
  useImperativeHandle(ref, () => {
    return {
      setVisible,
      setQualificationEditMember,
    };
  });
  /* ======================================== 对外方法 end =============================================== */

  /* ======================================= method start======================================= */
  /** 选项栏-变化监听 */
  const watchProductMemberAndLocationChange = () => {
    let target = `${productType}-${memberType}-${locationType}`;
    // 产品类型为易制爆(2) && 客户类型为生成单位(3)
    if (productType === "2" && memberType === "3") {
      target = target.replace(/3.*$/, "3-x");
    }
    // 产品类型为易制毒(3) && 客户类型为生成单位(3)
    if (productType === "3" && memberType === "3") {
      target = "";
    } else if (productType === "3" && memberType !== "3") {
      // 产品类型为易制毒(3) && 客户类型不是生成单位(3)
      target = target.replace(/3.*$/, `3-${memberType}-x`);
    }
    // 保存-产品-客户-区域-选中的记录
    setTargetMap(target);
    // 匹配 产品-客户-区域-选中的 对应上传配置，设置 操作进度
    if (qualificationTipCombinations[target]) {
      setSelectQualificationDesc(qualificationTipCombinations[target].selected);
      setCurrentUploadStep(UPLOAD_STEP_CONSTANT.UPLOAD);
    }
  };

  /** 切换产品类型-选项 */
  const handleSwitchProductDangerType = async (type: string) => {
    setProductType(type);
    console.log("执行 设置 产品类型");
    qualificationUploadFormRef?.current?.validateFields(["productType"]);
    if (!memberType) {
      setCurrentUploadStep(UPLOAD_STEP_CONSTANT.PRODUCT);
    }
  };

  /** 切换客户类型-选项 */
  const handleMemberType = async (type: string) => {
    setMemberType(type);
    console.log("执行 设置 客户类型");
    qualificationUploadFormRef?.current?.validateFields(["memberType"]);
    if (Number(currentUploadStep) < Number(UPLOAD_STEP_CONSTANT.UPLOAD)) {
      setCurrentUploadStep(UPLOAD_STEP_CONSTANT.MEMBER);
    }
  };

  /** 切换区域位置-选项 */
  const handleLocationType = async (type: string) => {
    setLocation(type);
    console.log("执行 设置 区域位置");
    qualificationUploadFormRef?.current?.validateFields(["locationType"]);
    if (Number(currentUploadStep) < Number(UPLOAD_STEP_CONSTANT.UPLOAD)) {
      setCurrentUploadStep(UPLOAD_STEP_CONSTANT.MEMBER);
    } else {
      setCurrentUploadStep(UPLOAD_STEP_CONSTANT.UPLOAD);
    }
  };

  /** 资质编辑前校验-是否无需触发提交请求 */
  const checkNotChangeWithQualificationBefore = (newQualification) => {
    if (!isEditMode) { return false }
    if (!memberQualification) { return true }
    try {
      return Object.keys(newQualification).every(key => newQualification[key] === memberQualification[key]);
    } catch (e) {
      return true;
    }
  }

  /** 资质表单确认事件-表单提交 */
  const handleCommitEvent = async (targetForm: Partial<qualificationAddDto> & { id?: number }) => {
    // 校验通过
    setCurrentUploadStep(UPLOAD_STEP_CONSTANT.COMMIT);
    // 触发校验
    qualificationUploadFormRef?.current?.validateFields();
    // 组装数据
    targetForm.selectedFields = selectQualificationDesc;
    // 提交前检测
    if (checkNotChangeWithQualificationBefore(targetForm)) {
      message.error(memberQualification?.auditorReason || "您还未根据要求，重新上传不合规的资质资源，请检查！");
      setLoading(false);
      return
    }
    // 确认
    const confirm = await TmConfirmModal({ content: '资质信息确认无误，确定提交' })
    if (confirm !== 'confirm') { return }

    setLoading(true);
    const result = !isEditMode ? (await addMemberNewQualification(targetForm)) : (await editMemberQualification(Object.assign(targetForm, { id: memberQualification?.id })));
    setLoading(false);
    const [err, res] = result;
    if (err) {
      notification.error({ message: err?.data?.message || "网络异常，提交失败，请重试！" });
      return;
    }

    message.success(res.message || "提交成功");
    setVisible(false);
    props.modalActionSuccessCallback();
  };

  /** 提交时-校验失败处理 */
  const handleCommitValidateFailed = () => {
    notification.error({ message: "请根据要求提交必要的资质材料！" });
  };

  /** 加载客户资质信息 */
  const initQualificationData = async () => {
    setInitLoading(true);
    const memberQualification = await initMemberQualification();
    setInitLoading(false);
    if (!memberQualification) {
      notification.error({ message: "网络出现异常，客户资质加载失败，请刷新页面重新编辑" });
      setVisible(false);
      return;
    }
    setMemberQualification(memberQualification);
    // 初始化表单默认值
    setFormInitialValues(item => {
      memberQualification?.selectedFields?.split(",").forEach(field => {
        item[field] = memberQualification[field];
      });
      return { ...item };
    });
    // 加载客户资质后-初始化-产品类型、客户类型、区域位置 + form
    setProductType(String(memberQualification.productType));
    setMemberType(String(memberQualification.customerIdentity));
    setLocation(String(memberQualification.customerPosition));
    qualificationUploadFormRef?.current?.setFieldsValue(memberQualification);
  };

  /** form绑定上传图片-filepath */
  const handleSetImageToForm = (key: string, filepath: string) => {
    form?.setFieldValue(key, filepath);
    form?.validateFields([key]);
    // 保证进度到上传步骤
    currentUploadStep !== UPLOAD_STEP_CONSTANT.UPLOAD && setCurrentUploadStep(UPLOAD_STEP_CONSTANT.UPLOAD);
  };

  /** 根据条件判断字段显示 */
  const checkUploadComponentNeedShowByShowFields = (arr: string[]) => {
    if (!selectQualificationDesc) {
      return false;
    }
    return arr.some(item => selectQualificationDesc.includes(item));
  };
  /* ======================================= method end======================================= */

  /** 渲染区域选择器 */
  const renderLocationSegmented = () => {
    // (产品类型为易制爆 + 客户类型为生产单位) || 产品类型为易制毒 = 没有区域选择项
    if ((productType === "2" && memberType === "3") || productType === "3" || !targetMap) {
      return null;
    }

    return (
      <Form.Item name={isEditMode ? undefined : "locationType"} rules={uploadFieldRules.locationType} label="所在区域">
        {/* @ts-expect-error */}
        {!isEditMode ? <Segmented options={locationTypes} onChange={handleLocationType} /> : <span className="blue-tip">{locationTypes.find(item => item.value === locationType)?.label || ""}</span>}
      </Form.Item>
    );
  };

  /** 渲染上传组件细节 */
  const renderUploadDetail = (qualificationFieldKey, index) => {
    const initData: UploadFile[] = memberQualification
      ? memberQualification[qualificationFieldKey].split(",").map(filename => {
          return {
            uid: filename,
            name: filename,
            status: "done",
            url: filename,
            thumbUrl: commonConstant.QUALIFICATION_RESOURCE.QUALIFICATION_IMAGE_BASE_URL + filename,
          };
        })
      : [];

    return (
      <Form.Item className="upload-item-box" rules={uploadFieldRules[qualificationFieldKey]} key={index} name={qualificationFieldKey}>
        {/* 上传 */}
        <TmUploadImage
          initImageFiles={initData}
          uploadBtnText={qualificationDetailNameOptions[qualificationFieldKey]}
          action={commonConstant.QUALIFICATION_RESOURCE.QUALIFICATION_IMAGE_UPLOAD_URL}
          originImagePrefix={commonConstant.QUALIFICATION_RESOURCE.QUALIFICATION_IMAGE_BASE_URL}
          isPrivate={true}
          imageTypeLimitTip={imageTypeLimitTip}
          imageTypeLimit={imageTypeLimit}
          onSuccessCallback={originFilePath => handleSetImageToForm(qualificationFieldKey, originFilePath)}
        />
      </Form.Item>
    );
  };

  /** 需要上传的组件项 */
  const renderNeedShowUploadBoxBySelected = () => {
    return qualificationShowOptions.map((item, index) => {
      return checkUploadComponentNeedShowByShowFields(item.showFields) ? (
        <Row key={index}>
          <Col span={15}>
            <Form.Item label={<span className="form-label-required">{item.title}</span>}>
              <Space direction="horizontal" className="need-upload-wrapper">
                {/* 上传组件渲染 */}
                {item.showFields.map((key, index) => {
                  return checkUploadComponentNeedShowByShowFields([key]) ? renderUploadDetail(key, index) : null;
                })}
              </Space>
            </Form.Item>
          </Col>
          <Col span={9}>
            {/* 示范 */}
            {exampleIdcard(item)}
          </Col>
        </Row>
      ) : null;
    });
  };

  /** 渲染-分栏-提示信息 */
  const exampleIdcard = current => {
    return (
      <div className="example-box">
        <ul className="example-tips">
          <li>提示：</li>
          <li>
            图片仅支持 {imageTypeLimit.map(item => item.replace("image/", "")).join("、")} 格式，且不能超过{uploadImageSizeLimit}m
          </li>
          <li>必须看清证件信息,证件信息不能被遮挡</li>
          {current?.example?.tip && <li>{current?.example?.tip}</li>}
        </ul>
        <div className="example-img">
          <span>示例</span>
          <Image src={current?.example?.img} alt="img" />
        </div>
      </div>
    );
  };

  /** 渲染-资质审核不通过的原因 */
  const renderAuditFailedMessage = () => {
    const isShow = isEditMode && memberQualification?.auditState === 2 && memberQualification?.auditorReason;
    return isShow && <Alert style={{ fontSize: "16px", marginBottom: "16px", color: "#A0522D" }} type="error" message={`资质审核未通过：${memberQualification.auditorReason}`} />;
  };

  // @ts-ignore
  return (
    <div className={style.wrapper}>
      <TmModal
        title="资质上传"
        open={visible}
        footer={false}
        width={1000}
        keyboard={false}
        maskClosable={false}
        onCancel={() => setVisible(false)}
        onOk={() => setVisible(false)}
        content={
          <Spin spinning={initLoading || loading}>
            {/* 编辑模式-审核不通过的提醒 */}
            {renderAuditFailedMessage()}
            <div className="credential">
              <ul className="progress">
                {uploadStepArray.map((item, index) => {
                  return (
                    <li className={`progress-item${Number(currentUploadStep) >= index ? " current" : ""}`} key={index}>
                      {item}
                    </li>
                  );
                })}
              </ul>
              <Card bodyStyle={{ padding: "15px" }}>
                <Form
                  form={form}
                  ref={qualificationUploadFormRef}
                  className="qualification-form"
                  onFinish={handleCommitEvent}
                  onFinishFailed={handleCommitValidateFailed}
                  initialValues={formInitialValues}
                >
                  <Form.Item name={isEditMode ? undefined : "productType"} rules={uploadFieldRules.productType} label="产品类型">
                    {/* @ts-expect-error */}
                    {!isEditMode ? (
                      <Segmented options={productDangerTypes} onChange={handleSwitchProductDangerType} />
                    ) : (
                      <span className="blue-tip">{productDangerTypes.find(item => item.value === productType)?.label || ""}</span>
                    )}
                  </Form.Item>
                  <Form.Item name={isEditMode ? undefined : "memberType"} rules={uploadFieldRules.memberType} label="客户身份">
                    {/* @ts-expect-error */}
                    {!isEditMode ? (
                      <Segmented options={memberTypes} onChange={handleMemberType} />
                    ) : (
                      <span className="blue-tip">{memberTypes.find(item => item.value === memberType)?.label || ""}</span>
                    )}
                  </Form.Item>
                  {/* 渲染选择器 */}
                  {renderLocationSegmented()}
                  {/* 渲染上传组件 */}
                  {targetMap && renderNeedShowUploadBoxBySelected()}
                  {!!targetMap && (
                    <Form.Item className="form-item-submit">
                      <Button
                        danger
                        type="primary"
                        size="large"
                        disabled={currentUploadStep < UPLOAD_STEP_CONSTANT.UPLOAD}
                        htmlType="submit"
                        className="credential-submit"
                        style={{ textAlign: "center" }}
                      >
                        确认无误，提交申请
                      </Button>
                    </Form.Item>
                  )}
                </Form>
              </Card>
              {/* 问答&下载 */}
              <Card title={<span className="main-title">问答&下载</span>} bodyStyle={{ padding: "15px" }}>
                <div className="qa-box">
                  <ul>
                    <div className="title">问答</div>
                    <li className="qa-item">
                      <div className="question">
                        <QuestionCircleOutlined />
                        &nbsp;危险化学品有哪些？
                      </div>
                      <div className="answer">&nbsp;&nbsp;&nbsp;&nbsp;危险化学品分为一般危险化学品和特殊危险化学品。特殊危险化学品主要包括：剧毒化学品、易制毒化学品、爆炸化学品和易制爆化学品。</div>
                    </li>
                    <li className="qa-item">
                      <div className="question">
                        <QuestionCircleOutlined />
                        &nbsp;危险化学品的标注是什么？
                      </div>
                      <div className="answer">
                        <p>&nbsp;&nbsp;&nbsp;&nbsp;● 强腐蚀化学品：（腐蚀）</p>
                        <p>&nbsp;&nbsp;&nbsp;&nbsp;● 夏季禁运商品：（）</p>
                        <p>&nbsp;&nbsp;&nbsp;&nbsp;● 怕热商品：（-20℃）、（+4℃）、（怕热）</p>
                        <p>&nbsp;&nbsp;&nbsp;&nbsp;● 快递禁运商品：（危规编号—快递禁运）</p>
                      </div>
                    </li>
                    <li className="qa-item">
                      <div className="question">
                        <QuestionCircleOutlined />
                        &nbsp;易制爆化学品从业单位备案证明办理流程？
                      </div>
                      <div className="answer">
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <Image width={300} alt="" preview={false} src={commonConstant.COMMON_IMAGE_PATHS.QUALIFICATION_FILING_PROCESS} />
                      </div>
                    </li>
                  </ul>
                  <div className="data-download">
                    <div className="data-download-item">
                      <div className="title">模板下载</div>
                      <div className="data-download-item-list">
                        <a onClick={() => downloadFromOssFilepath(commonConstant.COMMON_FILE_PATHS.TEMPLATE_TRANSPORTATION_PRECURSOR_CHEMICALS, "易制毒化学品运输委托书模板.docx")}>
                          易制毒化学品运输委托书模板
                        </a>
                      </div>
                    </div>
                    <div className="data-download-item">
                      <div className="title">资料下载</div>
                      <div className="data-download-item-list">
                        <a onClick={() => downloadFromOssFilepath(commonConstant.COMMON_FILE_PATHS.TEMPLATE_TRANSPORTATION_PRECURSOR_CHEMICALS, "易制毒化学品运输委托书模板.docx")}>
                          易制毒化学品运输委托书模板
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Spin>
        }
      />
    </div>
  );
});

QualificationEditModal.defaultProps = {
  modalActionSuccessCallback: () => {},
};

export default QualificationEditModal;
