import React, { useContext, useRef } from "react";
import { LayoutProps } from "ssr-types-react";
import Header from "./parts/Header";
import Footer from "./parts/Footer";
import style from "./index.module.less";
import { <PERSON>u, Card } from "antd";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import ModifyPasswordFormModal from "@/components/ModifyPasswordFormModal";
import { useUCenterLayout } from "@/hooks/useUCenterLayout";
import Cookies from "js-cookie";

const UcenterLayout = (props: LayoutProps) => {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  // 判断客户是否需要开启初始密码弹窗提醒
  const modifyPasswordRef = useRef<any>(null);
  // 初始密码弹窗提醒 24小时内只提醒一次
  const lastShowPwdModalTime = Cookies.get('lastShowPwdModalTime') && new Date().getTime() < Number(Cookies.get('lastShowPwdModalTime')) 
  if (__isBrowser__ && state?.layoutInitData?.checkMemberIsDefaultPwdModalControl && state?.userData?.isDefaultPwd === 1 && !lastShowPwdModalTime) {
    setTimeout(() => {
      modifyPasswordRef.current?.setVisibleFunc(true);
      Cookies.set('lastShowPwdModalTime', (new Date().getTime() + 24 * 60 * 60 * 1000).toString())
    }, 100);
  }
  // 侧边栏菜单配置
  const navItems = useUCenterLayout(state).getUCenterLayoutMenuItems()

  return (
    <>
      <div className={style.ucenterContainerWrapper}>
        <Header />
        <div className="ucenter-container">
          <Card className="ucenter-container-nav" bodyStyle={{ padding: "0" }}>
            <Menu items={navItems} mode={"inline"} key="key" selectedKeys={state?.navSelectedKeys} defaultOpenKeys={["info", "trade"]} />
          </Card>
          <Card className="ucenter-container-content" bodyStyle={{ padding: "0 10px" }}>
            <div id="ucenterApp">{props.children}</div>
          </Card>
          {/* 初始密码弹窗修改提醒 */}
          <ModifyPasswordFormModal ref={modifyPasswordRef} />
        </div>
        <Footer />
      </div>
    </>
  );
};
export default UcenterLayout;
