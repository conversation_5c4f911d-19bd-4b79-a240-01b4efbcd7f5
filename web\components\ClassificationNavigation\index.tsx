import React, { useContext, useState } from "react";
import style from "./index.module.less";
import SvgIcon from "@/components/SvgIcon";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";
import { Button } from "antd";
import commonConstant, { PROXY_BRAND_NAME } from "@/constants/common";
import { useOss } from "@/hooks/useOss";

interface IProductCategoryProps {
  /** 距离顶部偏移位置 */
  offsetTop?: number;
  /* 是否默认展开面板 */
  defaultOpenPanel?: boolean;
}

/** 预定分类数据类型结构 */
interface ICategoryDataType {
  id: number;
  name: string;
  pid: number;
  keywords: string;
  level: number;
  sortOrder: number;
  description?: string;
  children?: ICategoryDataType[];
  icon?: string;
  recommend?: Array<{ id: number; type: number; remark?: string; url?: string; img?: string }>;
}

/**
 * 产品分类组件
 * @param offsetTop
 * @param defaultOpenPanel
 * @constructor
 */
export default function ProductCategory({ offsetTop = 0, defaultOpenPanel = false }: IProductCategoryProps) {
  // 服务端产品分类数据
  const { state } = useContext<IContext>(useStoreContext());
  const hotBrandIntroduceData = state.layoutInitData?.hotBrandIntroduce || [];
  const useOssHook = useOss();
  // 状态控制
  const [isShowCategoryList, setIsShowCategoryList] = useState(false);
  const [isShowSubCategoryPanel, setIsShowSubCategoryPanel] = useState(false);
  const [currentActiveCategory, setCurrentActiveCategory] = useState<ICategoryDataType>();
  const [currentActiveCategoryData, setCurrentActiveCategoryData] = useState<ICategoryDataType[]>();
  const [categories, setCategories] = useState<ICategoryDataType[]>();

  if (!categories) {
    // 避免父组件重复渲染，方法重复执行
    setCategories(state.layoutInitData?.cascade.sort((pre, next) => pre.sortOrder - next.sortOrder) || []);
  }

  // 分类广告数据
  const categoryAdvertData = [
    {
      id: 1,
      img: "/images/category-recommend/jhd-recomend.png",
      key: "化学试剂",
      title: "化学试剂",
      remark: "",
      description:
        "广东光华科技股份有限公司（股票代码：002741）成立于1980年，是高性能电子化学品、高品质化学试剂与产线专用化学品等定制开发和技术服务提供商，拥有国家企业技术中心，获CNAS国家权威认可，主持或参与修订30余项国家、行业标准。自建的300亩绿色生产基地、200多个网点为客户提供专业服务，赢得如宝洁、安利、龙沙、三星等全球标杆企业信赖。",
      link: "",
      logo: "",
    },
    {
      id: 2,
      img: "/images/category-recommend/ghtech-recomend.gif",
      key: "电子化学品",
      title: "电子化学品",
      remark: "",
      description: "光华科技全面布局了相关的湿电子化学品，如mSAP化学镀铜、电镀铜、完成表面处理OSP、镍钯金以及棕化、褪膜等工艺技术及化学品；在先进封装上布局了RDL电镀铜、电镀锡等相关技术及化学品。",
      link: "",
      logo: "",
    },
    {
      id: 3,
      img: "/images/category-recommend/ghtech-recomend.gif",
      key: "专用化学品",
      title: "专用化学品",
      remark: "",
      description:
        "光华科技从事专用化学品的历史长达40年，定位于专用化学品的高端领域，坚持自主品牌的运营，是罗门哈斯、霍尼韦尔、美维电子、富士康、宝洁、安利、高露洁、依利安达、惠亚集团等国际知名跨国企业的供应商。",
      link: "",
      logo: "",
    },
  ];
  // 获取专项-分类广告信息
  const [currentCategoryAdvertDetail, setCurrentCategoryAdvertDetail] = useState<any>();

  // 判断激活的主分类是否为代理分类
  const checkIsBrandCate = cate => {
    return cate?.description === PROXY_BRAND_NAME || cate?.productUnit === PROXY_BRAND_NAME;
  };

  // 渲染代理品牌展示
  const renderCommonBrandItem = categoryItem => {
    const logoPath = useOssHook.generateOssFullFilepath(hotBrandIntroduceData?.find(item => item.brand_id === categoryItem.id)?.logo_path, commonConstant.COMMON_IMAGE_PATHS.DEFAULT);
    return (
      <>
        {categoryItem.description ? <img src={logoPath} className="category-img" alt="" /> : <div className="category-img">{categoryItem.name}</div>}
        <div className="category-item-mask">
          <span>{categoryItem.name}</span>
          <Button size="small" danger ghost>
            查看
          </Button>
        </div>
      </>
    );
  };

  /** 展示主级分类列表 */
  const onShowCategoryList = () => {
    !isShowCategoryList && setIsShowCategoryList(true);
  };

  /** 隐藏主分类列表 */
  const onHideCategoryList = () => {
    !defaultOpenPanel && setIsShowCategoryList(false);
  };

  /** 关闭分类详情面板显示 */
  const onHideEvent = () => {
    setIsShowSubCategoryPanel(false);
  };

  /** 激活某个主分类项 */
  const onActiveOneCategoryItem = firstLevelCategory => {
    if (firstLevelCategory.name === "电池材料") {
      setIsShowSubCategoryPanel(false);
      return;
    }
    setIsShowSubCategoryPanel(true);
    setCurrentActiveCategory({ ...firstLevelCategory });
    const childCategoryData = categories?.find(item => item.id === firstLevelCategory.id);
    childCategoryData && setCurrentActiveCategoryData(childCategoryData.children?.sort((pre, next) => pre.sortOrder - next.sortOrder));
    setCurrentCategoryAdvertDetail(categoryAdvertData.find(item => item.key === firstLevelCategory.name));
  };

  return (
    <div className={style.wrapper} onMouseLeave={onHideCategoryList}>
      <span className="category-text" onMouseEnter={onShowCategoryList}>
        <SvgIcon iconClass="category" className="icon-category" />
        精选商品分类
      </span>
      {/* 分类列表 + 分类面板 */}
      <div className="category-panel">
        <ul
          className="category-ul"
          style={{
            display: `${defaultOpenPanel || isShowCategoryList ? "inline-block" : "none"}`,
            top: `${defaultOpenPanel ? `calc(${offsetTop}px + 42px)` : `calc(${offsetTop}px + 39px)`}`,
            borderRadius: `${defaultOpenPanel ? "4px" : "0"}`,
          }}
          onMouseLeave={onHideEvent}
        >
          {categories?.map((cate, index) => (
            <li key={index} className="category-item" onMouseEnter={() => onActiveOneCategoryItem(cate)}>
              {/* 一级分类名 */}
              <a href={`/search?categoryId=${cate.id}`} className="category-parent">
                {cate.icon ? <img src={cate.icon} alt="icon" /> : null}
                <span className="category-parent-name">{cate.name}</span>
              </a>
              {/* 更换为keyword关键字提示 */}
              <div className="sub-category-wrapper">
                {cate.keywords.split("|").map((item, index) => {
                  return <span key={index}>{item}</span>;
                })}
              </div>
            </li>
          ))}
          {/* ul的分类详情面板 */}
          <div className="category-inner-panel" style={{ display: `${isShowSubCategoryPanel ? "inline-flex" : "none"}` }}>
            <div className={`category-content ${checkIsBrandCate(currentActiveCategory) ? "clearfix proxy-brand-wrapper" : "category-content-recommend"}`}>
              {/* 数据-循环-ul */}
              {currentActiveCategoryData?.map(categoryItem => (
                <ul className="category-content-ul" key={categoryItem.id}>
                  <a href={checkIsBrandCate(currentActiveCategory) ? `/search?brandId=${categoryItem.id}` : `/search?categoryId=${categoryItem.id}`}>
                    {checkIsBrandCate(currentActiveCategory) ? renderCommonBrandItem(categoryItem) : <span className="category-title">{categoryItem.name}</span>}
                  </a>
                  {
                    // 有子项
                    categoryItem?.children ? (
                      <li className="child-category-wrapper">
                        {categoryItem.children
                          .sort((pre, next) => pre.sortOrder - next.sortOrder)
                          .map(category => (
                            <div className="child-category-content" key={category.id}>
                              {/* 三级标题 */}
                              <a href={`/search?categoryId=${category.id}`}>
                                <span className="child-category-name" title={category.name}>
                                  {category.name}
                                </span>
                              </a>
                              {/* 四级内容 */}
                              <div className="category-links">
                                {category.children
                                  ?.sort((pre, next) => pre.sortOrder - next.sortOrder)
                                  .map(item => (
                                    <a href={`/search?categoryId=${item.id}`} className="category-link" key={item.id}>
                                      {item.name}
                                    </a>
                                  ))}
                              </div>
                            </div>
                          ))}
                      </li>
                    ) : null
                  }
                </ul>
              ))}
              {!checkIsBrandCate(currentActiveCategory) && currentCategoryAdvertDetail && <div className="recommend-ul-wrapper">{currentCategoryAdvertDetail?.description || ""}</div>}
            </div>
            {/* 推荐图标 */}
            {!checkIsBrandCate(currentActiveCategory) && currentCategoryAdvertDetail && <div className="recommend-area" style={{ backgroundImage: `url(${currentCategoryAdvertDetail.img})` }} />}
          </div>
        </ul>
      </div>
    </div>
  );
}
