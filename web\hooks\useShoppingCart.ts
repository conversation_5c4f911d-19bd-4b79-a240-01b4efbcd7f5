import * as shoppingCartCrud from "@/apis/trade/shopping-cart";
import { useContext } from "react";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import { ICreate, IUpdate } from "~/typings/data/trade/shopping-cart";
import { message } from "antd";

export const useShoppingCart = () => {
  const { state, dispatch } = useContext<IContext>(STORE_CONTEXT);
  // 获取购物车列表
  const init = async () => {
    // 未登录不请求！
    if (!state?.userLoginState) {
      return;
    }
    const [err, res] = await shoppingCartCrud
      .getList()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err && res?.data) {
      dispatch?.({
        type: "SHOPPING_CART_NUM_UPDATE",
        payload: {
          shoppingCartNum: Number(res?.data.length),
          shoppingCartData: res.data,
        },
      });
    }
  };
  // 购物车drawer开关
  const toggle = () => {
    dispatch?.({
      type: "DRAWER_SHOPPING_CART_TOGGLE",
      payload: {
        shoppingCartVisible: !state.shoppingCartVisible,
      },
    });
  };
  /* 加入到购物车 */
  const insert = async (data: Partial<ICreate>, needSuccessMsg = true) => {
    if (!state?.userLoginState) {
      message.warning("请登录后再进行该操作!");
      return;
    }
    const [err, res] = await shoppingCartCrud
      .create(data)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      message.error(err?.data?.message ?? "加入购物车失败");
      return false;
    }
    needSuccessMsg && message.success("加入购物车成功" || res.message);
    await init();
    return true;
  };
  /* 修改 */
  const update = async (data: Partial<IUpdate>) => {
    const [err, res] = await shoppingCartCrud
      .update(data)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err && res) {
      console.log("购物车更新成功：", data);
    }
  };
  /* 获取已选择的购物车产品 */
  const getSelectedLists = async (way?: string) => {
    const [err, res] = await shoppingCartCrud
      .getSelectedList(way)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return [];
    }
    return res.data;
  };
  return {
    init,
    toggle,
    insert,
    update,
    getSelectedLists,
  };
};
