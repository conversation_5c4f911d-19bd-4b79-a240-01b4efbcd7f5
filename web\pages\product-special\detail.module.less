@contentTimeColor: #666666;
@contentTitleColor: #333333;
@contentTitleFontSize: 30px;
@contentDescriptionFontSize: 18px;
@contentDescriptionColor: @regular-text-color;
@contentMargin: 16px;

.wrapper {
  #labelBeforeFlag() {
    &:before {
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      margin-right: 10px;
      background-color: #e02020;
    }
  }
  :global {
    .detail {
      background-color: @main-bg-color-white;
      .flex-row();
      flex-shrink: 0;
      padding: 16px;
      .menu {
        min-height: 100vh;
      }
      .content {
        width: calc(100% - 200px - 16px);
        margin-left: 16px;
        border: 1px solid #f0f0f0;
        padding: 16px;
        // 面包屑导航
        .special-breadcrumb {
          width: 100%;
          border-bottom: 1px solid #f5f5f5;
        }
        // 专题标题
        .special-head-conetent {
          margin: 24px 0;
          .special-title {
            font-size: @contentTitleFontSize;
            color: @contentTitleColor;
            font-weight: 600;
            text-align: center;
            .ellipsis(2);
          }
          .special-tips {
            font-size: 14px;
            .flex-row(space-between, center);
            border-bottom: 1px solid #f2f2f2;
            color: @contentTimeColor;
            span {
              display: inline-flex;
              align-items: center;
              gap: @contentMargin * 0.5;
            }
            span.icon {
              display: inline-block;
              width: 24px;
              height: 24px;
              .flex-center();
              border-radius: 2px;
              cursor: pointer;
              align-items: center;
            }
            .weibo {
              background: url("@@img/weibo.png") 0 0 no-repeat;
            }
            .wechat {
              background: url("@@img/wechat.png") 0 0 no-repeat;
            }
          }
        }
        // 图文
        .intro-box {
          .content {
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 100%;
            }
          }
        }
        // 关联产品
        .products-wrapper {
          width: 100%;
          margin-top: @contentMargin;
          padding-bottom: @contentMargin * 2;
        }
      }
    }
  }
}
