@headerBackgroundColor: #ff4401;
@headerFontColor: #fff;
@arrowBorderColor: #ff4401 #ff4401 #fff;
.ucenterHeaderWrapper {
  :global {
    .ucenter-header-wrapper {
      .flex-row(center,center);
      min-width: @main-width;
      height: 55px;
      background: @headerBackgroundColor;
      /* x偏移量 | y偏移量 | 阴影模糊半径 | 阴影扩散半径 | 阴影颜色 */
      box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.16);
      .ucenter-header {
        width: @main-width;
        &-logo {
          font-size: 24px;
          font-weight: 600;
          a {
            color: @headerFontColor;
          }
        }
        /*导航栏*/
        &-nav {
          color: @headerFontColor;
          float: right;
          .link-item {
            .user-info {
              cursor: pointer;
              margin-right: 8px;
            }
            .flex-center();
            float: left;
            font: 400 14px/36px tahoma;
            &-name {
              a {
                color: @headerFontColor;
                padding: 0 8px;
                border-left: 1px solid @headerFontColor;
                &:hover {
                  color: #ffccc7;
                }
              }
            }
            &:last-child {
              a {
                padding-right: 0;
                border-right: none;
              }
            }
          }
        }
        /*搜索*/
        &-search {
          display: inline-flex;
          float: right;
          margin-left: 20px;
          .search {
            width: 200px;
            border-radius: 5px;
          }
        }
      }
    }
  }
}
:global {
  .ucenterHeaderNavMorePopover {
    ul.site-nav-menu-list {
      li.site-nav-menu-list-item {
        border-bottom: 1px solid rgb(207, 206, 206);
        text-align: center;
        padding: 5px 0;
        &:last-child {
          border: none;
          padding-bottom: 0;
        }
        a {
          font-size: 14px;
          &:hover {
            cursor: pointer;
            color: #ff5c58;
          }
        }
      }
    }
  }
}
