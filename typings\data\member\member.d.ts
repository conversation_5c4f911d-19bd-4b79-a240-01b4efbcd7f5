
export interface updatePasswordVo {
  newPass: string;
  oldPass: string;
}
export interface updateMemberVo {
  birthday: string;
  companyName: string;
  contactAddress: string;
  contactCityId: number;
  contactDistrictId: number;
  contactName: string;
  contactPhone: string;
  contactPeopleJob: string;
  contactProvinceId: number;
  fax: string;
  gender: number;
  idCard: string;
  nickname: string;
  postalCode: string;
  qq: string;
  telephone: string;
  wechat: string;
}
export interface ReBindPhoneVo {
  newPhone: string;
  smsCode: string;
}
export interface ReBindEmailVo {
  email: string;
  verifyCode: string;
}
