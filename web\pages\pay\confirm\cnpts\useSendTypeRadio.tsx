import React, { useState } from "react";
import { Radio, Space } from "antd";
import { FreightTypeLessConstant } from "@/constants/freight";

interface ISendTypeRadioParams {
  sendTypeChangeCallback: (sendType: number) => void;
}

/** 特定钩子-配送方式 */
export const useSendTypeRadio = (params: ISendTypeRadioParams) => {
  const { sendTypeChangeCallback } = params;
  const [selectedSendType, setSelectedSendType] = useState(1); // 默认：物流（站点自提）

  /** 可选项数据 */
  const [sendTypeRadioFilters, setSendTypeRadioFilters] = useState(Object.values(FreightTypeLessConstant));

  /** 重置-初始化-更新渲染 */
  const refresh = (district: string) => {
    if (district) {
      // 激活首选项
      onSendTypeChange({ target: { value: selectedSendType } });
    }
  };

  /** 监听选项主动选择 */
  const onSendTypeChange = e => {
    const currentType = e?.target?.value;
    setSelectedSendType(currentType);
    sendTypeChangeCallback(currentType);
  };

  /** 渲染-物流发货方式选择器 */
  const renderSendTypeSelector = () => {
    return (
      <Radio.Group onChange={onSendTypeChange} value={selectedSendType}>
        <Space direction="vertical">
          {sendTypeRadioFilters.map((item, index) => (
            <Radio key={index} value={item.value}>
              {item.name}
            </Radio>
          ))}
        </Space>
      </Radio.Group>
    );
  };

  return {
    refresh,
    renderSendTypeSelector,
  };
};
