import { Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ITradeService } from "@/service/trade/trade.service";
import { ICreateVo } from "~/typings/data/trade/trade";
import { TradeCashierPayDto } from "@/dto/trade-cashier-pay.dto";
import { findFreightTypeByValue } from "@/common/constants/freight.constant";

@Provide("TradeService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class TradeServiceImpl extends BaseService implements ITradeService {
  async createTrade(orderCreateVo: ICreateVo): Promise<any> {
    if (orderCreateVo?.sendType) {
      orderCreateVo.transportType = findFreightTypeByValue(orderCreateVo.sendType) ?? "";
    }
    return this.easyResponse(await this.easyHttp.post("/api/trade/create", orderCreateVo));
  }

  // 快速下单创建交易
  async createQuickOrderTrade(orderCreateVo: ICreateVo): Promise<any> {
    if (orderCreateVo?.sendType) {
      orderCreateVo.transportType = findFreightTypeByValue(orderCreateVo.sendType) ?? "";
    }
    return this.easyResponse(await this.easyHttp.post("/api/trade/quickOrderCreate", orderCreateVo));
  }

  async editTrade(orderCreateVo: ICreateVo): Promise<any> {
    if (orderCreateVo?.sendType) {
      orderCreateVo.transportType = findFreightTypeByValue(orderCreateVo.sendType) ?? "";
    }
    return this.easyResponse(await this.easyHttp.post("/api/trade/commit", orderCreateVo));
  }

  async payGateway(paymentMethod: string, tradeCashierPayDto: TradeCashierPayDto): Promise<number> {
    let paymentClient: string = "";
    switch (paymentMethod) {
      case "alipay":
        paymentClient = tradeCashierPayDto?.paymentClient ?? "WEB"; // NATIVE WEB  H5
        break;
      case "wechat":
        paymentClient = "MP";
        break;
      case "wallet":
        paymentClient = "WEB";
        break;
      default:
        throw new Error(`不支持[${paymentMethod}]付款方式，请重试！`);
    }
    if (tradeCashierPayDto.orderType === "ORDER") {
      tradeCashierPayDto.returnUrl = "/ucenter/trade/order?status=1";
    } else if (tradeCashierPayDto.orderType === "RECHARGE") {
      tradeCashierPayDto.returnUrl = "/ucenter/my-wallet?status=1";
    } else {
      tradeCashierPayDto.returnUrl = "/ucenter/member";
    }
    return this.easyResponse(await this.easyHttp.get(`/api/payment/cashier/pay/${paymentMethod.toUpperCase()}/${paymentClient}`, tradeCashierPayDto));
  }
}
