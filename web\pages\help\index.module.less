@helpBackgroundColor: #ffffff;
@helpContentPadding: 16px;
@helpTabsBackgroundColor: #ffffff;
@helpTabsItemTextColor: #434343;
@helpTabsItemActiveTextColor: #ff5c58;
@helpTabsItemActiveBackgroundColor: #fff3f0;
@helpTitleTipBackgroundColor: #e02020;

.wrapper {
  :global {
    .help {
      .flex-row(flex-start, flex-start);
      gap: @helpContentPadding;

      // 设置菜单样式、清除ant-card-body 内边距
      .ant-menu,
      .ant-menu-sub,
      .ant-menu-inline {
        color: @helpTabsItemTextColor;
        background-color: @helpTabsBackgroundColor !important;
      }
      .ant-card-body {
        padding: 0;
      }
      // 设置子菜单展开样式
      .ant-menu-submenu > .ant-menu {
        background-color: @helpTabsBackgroundColor !important;
      }
      .ant-menu-submenu-title {
        color: @helpTabsItemTextColor !important;
      }
      // 去掉右边框
      .ant-menu-inline {
        border: none;
        margin: 0;
      }
      // 设置 a 链接样式
      .ant-menu-item a {
        color: @helpTabsItemTextColor !important;
      }
      .ant-menu-item a:hover {
        color: @helpTabsItemActiveTextColor !important;
      }
      .ant-menu-item a:active:before {
        color: @helpTabsItemActiveTextColor !important;
      }
      .ant-menu-item a:active:after {
        color: @helpTabsItemActiveTextColor !important;
      }
      // 下拉箭头样式
      .ant-menu-submenu-arrow {
        color: @helpTabsItemTextColor !important;
      }
      // sider 的背景色
      .ant-layout-sider {
        background-color: @helpTabsItemActiveBackgroundColor !important;
      }
      // 选中菜单状态
      .ant-menu-item-selected {
        color: @helpTabsItemActiveTextColor !important;
        a {
          color: @helpTabsItemActiveTextColor !important;
        }
        &:after {
          border-right: 3px solid @helpTabsItemActiveTextColor;
        }
        background-color: @helpTabsItemActiveBackgroundColor !important;
      }
      // 设置未选中菜单项鼠标滑过样式
      .ant-menu-item-active {
        background-color: transparent;
        color: @helpTabsItemActiveTextColor !important;
      }

      // tab导航
      &-nav {
        .ant-card-body {
          width: 200px !important;
          // 头部标题样式-帮助中心
          .ant-menu-submenu-title {
            height: 56px;
            margin: 0;
            border-bottom: 1px solid @helpTabsItemActiveTextColor;
            .ant-menu-title-content {
              font-size: 24px;
              color: @helpTabsItemActiveTextColor;
              text-align: center;
            }
            .ant-menu-submenu-arrow {
              display: none;
            }
          }
        }
      }

      // 内容
      &-content {
        width: 100%;
        padding: 32px;
        .article {
          .flex-col(flex-start, flex-start);
          gap: 30px;
        }
      }
    }
  }
}
