import React, { memo } from "react";

export default memo(function OnlineShop() {
  return (
    <>
      <section className="article">
        <div className="article-paragraph">
          <h2 className="article-title">网站购物流程</h2>
        </div>
        <div className="article-paragraph">
          <h3 className="article-subtitle">如何注册？</h3>
          <div className="article-content">
            <ul>
              <li>
                1、点击页面顶部{" "}
                <a href="/auth/register" className="article-link">
                  【立即注册】
                </a>
                ,进入注册页面；
              </li>
              <li>2、填写【用户名、密码、邮箱、开票资料】等个人信息进行注册；</li>
              <li>3、如果您之前有过在光华易购电商平台订购经历，无需再次注册，请直接联系我们的客服专员为您核实资料并提供登录帐户。</li>
            </ul>
          </div>
        </div>
        <div className="article-paragraph">
          <h3 className="article-subtitle">如何购买产品？</h3>
          <div className="article-content">
            <ul>
              <li>
                1、浏览要购买的商品，点击{" "}
                <a href="/card" className="article-link">
                  【加入购物车】
                </a>{" "}
                商品会自动添加到{" "}
                <a href="/card" className="article-link">
                  【购物车】
                </a>{" "}
                里；
              </li>
              <li>
                2、如果需要更改商品数量，需在商品所在栏目后的商品数量框中输入购买数量，并点击{" "}
                <a href="/ucenter/trade/shopping-cart" className="article-link">
                  【更新购物车】
                </a>{" "}
                ；
              </li>
              <li>
                3、选好商品后点击{" "}
                <span className="article-link">【去结算】</span>
                ，进行详细填写，收货人信息、付款方式、发票信息、配送方式等信息。
              </li>
              <li>
                4、下单备注: 如有备注信息，在 <strong>【提交订单页】</strong> 下方的 <strong>【备注信息】</strong> 中留言，留言不得超过200字；
              </li>
              <li>
                5、确认无误后点击{" "}
                <strong className="article-link">
                  【提交订单】
                </strong>
                ，生成有效商城订单。关于订单付款付款，请联系业务员。我们推荐： <strong>线下转账</strong>。
              </li>
              <li>
                6、订单详细信息可进入 <a href="/ucenter/trade/order" className="article-link">{`【会员中心】 -> 【我的订单】`}</a> 查看。
              </li>
            </ul>
          </div>
        </div>
        <div className="article-paragraph">
          <h3 className="article-subtitle">订单状态</h3>
          <div className="article-content">
            <ul>
              <li>1、【进行中】：表示您的订单正在处理，请耐心等待，我们系统会在您成功【提交订单】立即进行确认。</li>
              <li>2、【未付款】：表示我们还没收到该订单款项，请您尽快付款。</li>
              <li>3、【已完成】：代表双方就此订单已经完成所有商务流程。</li>
              <li>4、【已取消】：代表此订单已取消。</li>
            </ul>
          </div>
        </div>
      </section>
    </>
  );
});
