.simpleLayoutWrapper {
  :global {
    .main-width {
      width: @main-width;
      margin: 0 auto;
    }
    .simple-layout-header {
      width: 100%;
      height: 100px;
      border-bottom: 2px solid @secondary-text-color;
      .flex-row(normal, center);
      .main-width {
        .flex-row(normal, center);
      }
      .logo-link {
        width: 110px;
        height: 46px;
        cursor: pointer;
        display: inline-block;
        background: url("@@img/logo.svg") 0 0 no-repeat;
        transform: scale(1.4);
      }
      .welcome-text {
        font-size: 24px;
        font-weight: 500;
        padding-left: 36px;
      }
    }
    // 版权
    .simple-layout-footer {
      width: 100%;
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
      position: fixed;
      bottom: 20px;
      z-index: 10;
      div {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        width: 100%;
        &.a-links {
          a {
            padding: 10px 25px;
            color: rgba(0, 0, 0, 0.45);
            &:hover {
              color: rgba(0, 0, 0, 0.9);
            }
          }
        }
      }
    }
  }
}
:global {
  body {
    background-color: @main-bg-color-white;
  }
}
