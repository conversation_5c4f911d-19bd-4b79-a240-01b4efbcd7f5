/** layout层处理特殊页面seo配置-钩子 */
import { ISEOParams } from "~/typings/data/seo/seo";
import { useUCenterLayout } from "@/hooks/useUCenterLayout";

export const useLayoutSeoSetting = state => {
  let patternSeo: ISEOParams = {
    description:
      "光华易购是光华科技精心打造的电商平台，旨在于为企业和科研机构提供实验室一站式打包服务。品种齐全：平台经营产品超过14万种，几乎覆盖高校实验室所需所有试剂（国产、进口），品质可靠：公司深耕试剂行业四十余年，为国内外超过2万家客户提供高品质产品服务，优惠多多，报账无忧，物流高效，订单智能化",
    keyword: "光华易购，金华大化学试剂，华大，华大试剂，进口试剂，金华大，JHD，HUADA，GHTECH",
    title: "光华易购_光华科技旗下电商平台_股票代码:002741",
  };
  const useUCenterLayoutHook = useUCenterLayout(state);

  /**
   * 字符串替换
   * @param {String} originString 元素字符
   * @param {String} target 替换成的目标
   * @param {RegExp} regular 正则
   * @return 替换后的字符串
   */
  const replaceWords = (originString, target, regular) => {
    return originString.replace(regular, target);
  };

  /**
   * seo整体包装处理
   * @return 返回当前页面seo信息
   */
  const execute = () => {
    if (!state || !state?.patternSeo) {
      return patternSeo;
    }

    // 数据拷贝
    patternSeo = JSON.parse(JSON.stringify(state.patternSeo));

    // 特殊需要特殊处理的seo配置
    if (patternSeo.isReg && patternSeo.meta) {
      // 取出当前 patternSeo的meta
      switch (patternSeo.meta) {
        case "product$keyword": {
          // 产品详情
          const keyword = `${state?.product?.brandName} ${state?.product?.productName} ${state?.product?.cas}`;
          const title = keyword || "产品详情-";
          patternSeo.title = replaceWords(patternSeo.title, title, /\${keyword}/g);
          patternSeo.keyword = replaceWords(patternSeo.keyword, keyword + "，", /\${keyword}/g);
          patternSeo.description = replaceWords(patternSeo.description, keyword || "产品", /\${keyword}/g);
          break;
        }
        case "search$keyword": {
          // 产品搜索
          let keyword = "华大，JHD，代理品牌等产品";
          const key = state.urlCurrentParams.keyword;
          key && (keyword = key);
          patternSeo.title = replaceWords(patternSeo.title, keyword + "-", /\${keyword}/g);
          patternSeo.keyword = replaceWords(patternSeo.keyword, keyword + "，", /\${keyword}/g);
          patternSeo.description = replaceWords(patternSeo.description, keyword, /\${keyword}/g);
          break;
        }
        case "ucenter$title": {
          // 个人中心-此处需要设置菜单路由标题
          let currentTitle = "个人中心";
          if (state?.navSelectedKeys?.length) {
            const key = state?.navSelectedKeys[0] || "";
            currentTitle = useUCenterLayoutHook.getUCenterLayoutMenuArray().find(item => item.key === key)?.label ?? currentTitle;
          }
          patternSeo.title = replaceWords(patternSeo.title, currentTitle + "-", /\${title}/g);
          patternSeo.keyword = replaceWords(patternSeo.keyword, currentTitle + "-", /\${title}/g);
          break;
        }
        case "product-special$title": {
          // 产品专题
          break;
        }
        default:
          break;
      }
    }

    return patternSeo;
  };

  return {
    execute,
  };
};
