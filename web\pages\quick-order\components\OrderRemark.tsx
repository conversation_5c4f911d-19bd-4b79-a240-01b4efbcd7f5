import React, { useState, useEffect } from "react";
import { Form, Input, Space, Row, Col, AutoComplete } from "antd";
import * as orderCrud from "@/apis/order/order";

const OrderRemark = ({ orderRemarkFormRef }) => {
  const [deliveryOptions, setDeliveryOptions] = useState<string[]>([]);

  useEffect(() => {
    fetchDeliveryRequirements();
  }, []);

  // 获取历史订单送货要求选项
  const fetchDeliveryRequirements = async () => {
    try {
      const [err, res] = await orderCrud
        .getMemberOrderHistory()
        .then(res => [null, res])
        .catch(err => [err, null]);

      if (err) {
        console.error("获取历史订单数据失败", err);
        return;
      }

      // 从历史订单中提取不重复的送货要求
      const requirements = res?.data?.content?.map(order => order.deliveryRequirements).filter(req => req && req.trim() !== "");
      // 去重
      const uniqueRequirements = [...new Set(requirements)];
      setDeliveryOptions(uniqueRequirements as string[]);
    } catch (error) {
      console.error("获取历史订单送货要求失败", error);
    }
  };
  return (
    <Form className="order-remark-form" ref={orderRemarkFormRef} name="orderRemarkForm" autoComplete="off" size="middle">
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item label="异常联系人" tooltip="订单异常联系人，如：张三 1866666666">
            <Input.Group compact>
              <Form.Item name="exceptionContact" noStyle>
                <Input style={{ width: "40%" }} placeholder="异常联系人" />
              </Form.Item>
              <Form.Item name="exceptionContactPhone" noStyle>
                <Input style={{ width: "50%" }} placeholder="异常联系人电话" />
              </Form.Item>
            </Input.Group>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="客户PO号" name="customerPo" tooltip="客户PO号，将显示在出货单上">
            <Input placeholder="请输入客户PO号" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="送货要求" name="deliveryRequirements">
        <AutoComplete placeholder="请输入或选择送货要求" options={deliveryOptions.map(item => ({ value: item, label: item }))} />
      </Form.Item>
      <Form.Item name="remark" label="订单备注" tooltip="提示：请勿填写有关支付、收货、发票方面的信息，填写对此订单的特殊处理要求。">
        <Space.Compact block direction="vertical">
          <Input.TextArea style={{ width: "100%" }} rows={3} placeholder="请输入订单备注  提示：请勿填写有关支付、收货、发票方面的信息，填写对此订单的特殊处理要求。" maxLength={200} />
        </Space.Compact>
      </Form.Item>
    </Form>
  );
};

export default OrderRemark;
