import React, { RefObject, useState } from "react";
import { Button, Form, Input, Picker, PickerRef, Popup, Space, Toast } from "antd-mobile";
import { FormInstance } from "antd-mobile/es/components/form";
import { CSSTransition } from "react-transition-group";
import { ISalesmanSpreadInfoType } from "@/typings/member.interface";
import { validateEmail, validateMobile } from "@/utils/form-valid.util";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { registerByEmailPassword, registerByPassword } from "@/apis/auth";
import { MobileAreaCascade, MobileEmailSendCode, MobilePasswordItem, MobileSmsCaptcha } from "@/components/mobile/TmFormItem";
import { EmailSendTypeEnum } from "@/enums/EmailSendTypeEnum";
import PrivacyAgreementTemplate from "@/components/AgreementTemplates/PrivacyAgreementTemplate";
import { PickerVal<PERSON>, <PERSON>erValueExtend } from "antd-mobile/es/components/picker-view";

interface RegisterCnptProps {
  customerCategory: any;
  salesmanSpreadInfo?: ISalesmanSpreadInfoType | null | undefined;
}

// 分割线动画组件
const LineTransition = (props: { show: boolean }) => {
  return (
    <div className="line-wrap">
      <CSSTransition in={props.show} timeout={5000} classNames="line">
        <div className="line" />
      </CSSTransition>
    </div>
  );
};

export default function RegisterCnpt(props: RegisterCnptProps) {
  const [form] = Form.useForm();
  const registerRef = React.createRef<FormInstance>() as any;
  const [protocolVisible, setProtocolVisible] = useState<boolean>(false);
  const [navList, setNavList] = useState<string[]>(["手机注册", "邮箱注册"]);
  const [currentNav, setCurrentNav] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const categoryColumns = [props?.customerCategory];
  const salesmanSpreadInfo: ISalesmanSpreadInfoType | null | undefined = props?.salesmanSpreadInfo;
  /** 注册表单数据 */
  const [registerForm] = useState<any>({
    companyName: "",
    phone: "",
    smsCode: "",
    email: "",
    verifyCode: "",
    password: "",
    repeat: "",
    agreement: undefined,
    customerCategoryCode: "",
    areas: [],
    address: "",
    principalMainUserid: salesmanSpreadInfo?.userid ?? undefined,
    principalMainUsername: salesmanSpreadInfo?.nickname ?? undefined,
    contactName: null,
  });
  // 注册校验规则
  const rules = {
    /* 手机注册验证-s */
    phone: [{ required: true, whitespace: true, message: "手机号不能为空" }, { validator: validateMobile }],
    smsCode: [
      { required: true, whitespace: true, message: "短信验证码不能为空" },
      { pattern: /^[0-9]{1,}$/, message: "请输入短信验证码" },
    ],
    /* 手机注册验证-e */
    /* 邮件注册验证-s */
    email: [{ required: true, whitespace: true, message: "邮箱不能为空" }, { validator: validateEmail }],
    verifyCode: [{ required: true, whitespace: true, message: "邮件验证码不能为空" }],
    /* 邮件注册验证-e */
    captcha: [{ required: false, whitespace: true, message: "图像验证码不能为空" }],
    agreement: [
      {
        validator: async (_, value) => {
          if (!value) {
            throw new Error("勾选-同意光华易购平台协议");
          }
          return await Promise.resolve();
        },
      },
    ],
    companyName: [
      { required: true, whitespace: true, message: "请输入公司名称或单位全称" },
      { max: 50, message: "您输入公司/单位名称超过50个字，请简写~" },
    ],
    customerCategory: [{ required: true, message: "请选择公司或单位所属类别" }],
    address: [
      { required: true, message: "请填入详细地址" },
      { max: 100, message: "详细地址超过100个字了，请简写~" },
    ],
    areas: [{ required: true, message: "请选择省市区" }],
    principalMainUserid: [],
    principalMainUsername: [{ required: true, message: "请选择专属业务员" }],
    contactName: [{ required: true, message: "请输入联系人姓名" }],
  };
  // 是否获取焦点
  const [focusField, setFocusField] = useState<any>({
    companyNameFocus: false,
    phoneFocus: false,
    smsCodeFocus: false,
    emailFocus: false,
    contactPhoneFocus: false,
    verifyCodeFocus: false,
    captchaCodeFocus: false,
    passwordFocus: false,
    repeatFocus: false,
    agreementFocus: false,
    customerCategoryFocus: false,
    areasFocus: false,
    addressFocus: false,
    principalMainUsernameFocus: false,
    contactName: false,
  });
  /** ======================================== method start ======================================= */
  const handleSubmitRegisterEvent = async () => {
    if (loading) {
      return;
    }
    // 参数校验
    const [formErr, formRes] = await form
      .validateFields()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (formErr && !formRes) {
      return;
    }
    const params = form.getFieldsValue();
    const encryptPassword = encrypt(params.password);
    const registerData: any = {
      companyName: params.companyName,
      password: encryptPassword,
      confirmPassword: encryptPassword,
      address: params.address,
      areas: params.areas,
      customerCategoryCode: params.customerCategoryCode,
      contactName: params.contactName,
    };
    if (salesmanSpreadInfo?.userid) {
      registerData.principalMainUserid = salesmanSpreadInfo?.userid;
    }
    let err, res;
    setLoading(true);
    if (currentNav === 0) {
      registerData.phone = params.phone;
      registerData.smsCode = params.smsCode;
      const [error1, res1] = await registerByPassword(registerData)
        .then(response => [null, response])
        .catch(error => [error, null]);
      err = error1;
      res = res1;
    } else if (currentNav === 1) {
      registerData.email = params.email;
      registerData.verifyCode = params.verifyCode;
      registerData.contactPhone = params.contactPhone;
      const [error2, res2] = await registerByEmailPassword(registerData)
        .then(response => [null, response])
        .catch(error => [error, null]);
      err = error2;
      res = res2;
    } else {
      setLoading(false);
      return Toast.show({
        content: "非法注册请求类型，请重试",
        maskClickable: false,
        duration: 1500,
      });
    }
    if (err) {
      setLoading(false);
      return Toast.show({
        icon: "fail",
        content: err.data?.message || "注册失败",
        maskClickable: false,
        duration: 1500,
      });
    }
    Toast.show({
      content: "注册成功" || res?.message,
      maskClickable: false,
      duration: 1500,
    });
    setTimeout(() => {
      setLoading(false);
      window.location.href = "/";
    }, 800);
  };
  // 切换选项卡
  const handleNavTapChangeEvent = (idx: number) => {
    setCurrentNav(idx);
  };
  /** 省市区选择回调 */
  const handleMonitorAreaChange = areas => {
    if (!areas) {
      form.setFieldValue("areas", "");
      return;
    }
    form.setFieldValue("areas", areas);
    form.validateFields(["areas"]);
  };
  const renderFormTypeCnpt = () => {
    return (
      <>
        {currentNav === 0 ? (
          <>
            <Form.Item className="item" name="phone" label="手机号码" rules={rules.phone}>
              <CSSTransition in={registerForm.phone.length > 0} timeout={400} classNames="input">
                <Input
                  className="input"
                  onFocus={() => {
                    setFocusField({ phoneFocus: true });
                  }}
                  onBlur={() => {
                    setFocusField({ phoneFocus: false });
                  }}
                  placeholder="请输入手机号"
                  autoComplete="off"
                />
              </CSSTransition>
            </Form.Item>
            <LineTransition show={focusField.phoneFocus} />
            {/* ========== 短信验证码发送组件 ========== */}
            <MobileSmsCaptcha uuid={"mobileRegisterPassword"} smsFormRef={form} showLineTransition={<LineTransition show={focusField.captchaCodeFocus} />} />
            <LineTransition show={focusField.smsCodeFocus} />
          </>
        ) : (
          <>
            <Form.Item className="item" name="email" label="电子邮箱" rules={rules.email}>
              <CSSTransition in={registerForm.email.length > 0} timeout={400} classNames="input">
                <Input
                  className="input"
                  onFocus={() => {
                    setFocusField({ emailFocus: true });
                  }}
                  onBlur={() => {
                    setFocusField({ emailFocus: false });
                  }}
                  placeholder="请输入电子邮箱"
                  autoComplete="off"
                />
              </CSSTransition>
            </Form.Item>
            <LineTransition show={focusField.emailFocus} />
            {/* ========== 邮件验证码发送组件 ========== */}
            <MobileEmailSendCode uuid={"registerMobileMemberEmail"} formRef={form} sendType={EmailSendTypeEnum.REGISTER} countdownSeconds={60} />
            <LineTransition show={focusField.verifyCodeFocus} />
            <Form.Item className="item" name="contactPhone" label="联系手机" rules={rules.phone}>
              <CSSTransition in={registerForm.email.length > 0} timeout={400} classNames="input">
                <Input
                  className="input"
                  onFocus={() => {
                    setFocusField({ contactPhoneFocus: true });
                  }}
                  onBlur={() => {
                    setFocusField({ contactPhoneFocus: false });
                  }}
                  autoComplete="off"
                  placeholder="请输入联系手机号"
                />
              </CSSTransition>
            </Form.Item>
            <LineTransition show={focusField.contactPhoneFocus} />
          </>
        )}
      </>
    );
  };
  return (
    <>
      <div className="auth-container">
        <div className="auth-content">
          <div className="navbar-box">
            {navList.map((item, index) => (
              <div className={`navbar-box-item ${currentNav === index ? "navbar-box-on" : ""}`} key={index} onClick={() => handleNavTapChangeEvent(index)}>
                {item}
              </div>
            ))}
          </div>
          <Form
            ref={registerRef}
            form={form}
            initialValues={registerForm}
            layout="horizontal"
            className="auth-form-box"
            footer={
              <Button loading={loading} className="auth-submit-btn" color="primary" onClick={handleSubmitRegisterEvent}>
                注册
              </Button>
            }
          >
            {salesmanSpreadInfo?.userid ? (
              <>
                <Form.Item required={false} className="item" name="principalMainUsername" label="业务专员" rules={rules.principalMainUsername}>
                  <CSSTransition in={true} timeout={400} classNames="input">
                    <Input
                      className="input salesman"
                      readOnly
                      autoComplete="off"
                      onFocus={() => {
                        setFocusField({ principalMainUsernameFocus: true });
                      }}
                      onBlur={() => {
                        setFocusField({ principalMainUsernameFocus: false });
                      }}
                      placeholder="请填入推荐业务员"
                    />
                  </CSSTransition>
                </Form.Item>
                <LineTransition show={focusField.principalMainUsernameFocus} />
              </>
            ) : null}
            <Form.Item className="item" name="companyName" label="公司名称" rules={[{ required: true, message: "公司名称不能为空" }]}>
              <CSSTransition in={registerForm.companyName.length > 0} timeout={400} classNames="input">
                <Input
                  className="input"
                  onFocus={() => {
                    setFocusField({ companyNameFocus: true });
                  }}
                  onBlur={() => {
                    setFocusField({ companyNameFocus: false });
                  }}
                  autoComplete="off"
                  placeholder="请输入公司名称"
                />
              </CSSTransition>
            </Form.Item>
            <LineTransition show={focusField.companyNameFocus} />
            {/* 渲染不同类型注册 */}
            {renderFormTypeCnpt()}
            {/* 渲染密码组件 */}
            <MobilePasswordItem prefixCls={"ghmall-m"} pwdFormRef={form} showLineTransition={<LineTransition show={focusField.passwordFocus} />} radius={2} />
            <LineTransition show={focusField.repeatFocus} />
            <Form.Item name="contactName" className="item" label="联系人" rules={rules.contactName}>
              <Input
                className="input"
                onFocus={() => {
                  setFocusField({ contactName: true });
                }}
                onBlur={() => {
                  setFocusField({ contactName: false });
                }}
                placeholder="请输入联系人姓名"
                autoComplete="off"
              />
            </Form.Item>
            <Form.Item
              className="item"
              name="customerCategoryCode"
              label="公司类别"
              rules={rules.customerCategory}
              trigger="onConfirm"
              onClick={(e, pickerRef: RefObject<PickerRef>) => {
                pickerRef.current?.open();
                setFocusField({ customerCategoryFocus: true });
              }}
            >
              <>
                <Picker
                  columns={categoryColumns}
                  onClose={() => {
                    setFocusField({ customerCategoryFocus: false });
                  }}
                  onConfirm={(value: PickerValue[], extend: PickerValueExtend) => {
                    const _code = categoryColumns[0].find(item => item.value === value[0])?.code;
                    form.setFieldValue("customerCategoryCode", _code);
                  }}
                >
                  {(items, { open }) => {
                    return (
                      <Space align="center" onClick={open}>
                        <Input readOnly className="input" value={items.every(item => item === null) ? "请选择公司类别" : items.map(item => item?.label ?? "请选择公司类别").join(" - ")} />
                      </Space>
                    );
                  }}
                </Picker>
              </>
            </Form.Item>
            <LineTransition show={focusField.customerCategoryFocus} />
            <Form.Item className="item" name="areas" label="公司属地" rules={rules.areas}>
              <CSSTransition in={false} timeout={400} classNames="input">
                <MobileAreaCascade onMonitorCascadeChange={handleMonitorAreaChange} />
              </CSSTransition>
            </Form.Item>
            <LineTransition show={focusField.areasFocus} />
            <Form.Item className="item" name="address" label="详细地址" rules={rules.address}>
              <CSSTransition in={false} timeout={400} classNames="input">
                <Input
                  className="input"
                  onFocus={() => {
                    setFocusField({ addressFocus: true });
                  }}
                  onBlur={() => {
                    setFocusField({ addressFocus: false });
                  }}
                  autoComplete="off"
                  placeholder="详细街道门牌号"
                />
              </CSSTransition>
            </Form.Item>
            <LineTransition show={focusField.addressFocus} />
          </Form>
          <div className="auth-tip">
            已有账号？
            <a href={"/m/auth/login"} className="auth-tip-login">
              立即登录
            </a>
          </div>
          <div
            className="agree-wrap"
            onClick={e => {
              setProtocolVisible(true);
            }}
          >
            注册即代表您已同意<span className="agree">《用户注册协议》</span>
          </div>
        </div>
        <div className="auth-bottom" />
      </div>
      {/* ----协议弹窗---- */}
      <Popup
        getContainer={() => document.body}
        showCloseButton
        visible={protocolVisible}
        position="bottom"
        onMaskClick={() => {
          setProtocolVisible(false);
        }}
        onClose={() => {
          setProtocolVisible(false);
        }}
        bodyStyle={{ height: "65vh", overflowY: "scroll", padding: "20px" }}
        bodyClassName="protocol-modal-center"
      >
        <div className="mobile-register-protocol-content">{<PrivacyAgreementTemplate />}</div>
      </Popup>
    </>
  );
}
