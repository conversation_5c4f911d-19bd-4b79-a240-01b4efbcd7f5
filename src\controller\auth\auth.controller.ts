import { Controller, Get, Inject, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IAuthService } from "@/service/auth.service";
import { RedisService } from "@midwayjs/redis";
import { IDictService } from "@/service/dict.servce";
import { COMMON_DICT_MAPPING } from "@/common/constants/common.constant";
import { ISalesmanService } from "@/service/platform/salesman.service";

@Controller("/auth")
export class AuthController extends BaseController {
  @Inject("AuthService")
  authService: IAuthService;

  @Inject()
  redisService: RedisService;

  @Inject("SalesmanService")
  salesmanService: ISalesmanService;

  @Inject("DictService")
  dictService: IDictService;

  @Get("/login")
  async login(): Promise<void> {
    const { ctx } = this;
    if (this.isMobileDevice()) {
      return ctx.redirect(`/m${ctx.url}`);
    }
    ctx.body = await render<Readable>(ctx, {
      stream: true,
    });
  }

  @Get("/register")
  async register(): Promise<void> {
    const { ctx } = this;
    if (this.isMobileDevice()) {
      return ctx.redirect(`/m${ctx.url}`);
    }
    const customerCategoryRes = await this.dictService.getDictByCode(COMMON_DICT_MAPPING.CUSTOMER_CATEGORY.value);
    ctx.customerCategory = customerCategoryRes?.data?.detail || [];
    ctx.body = await render<Readable>(this.ctx, {
      stream: true,
    });
  }

  @Get("/spread-register")
  async spreadRegister(@Query("code") spreadCode: string): Promise<void> {
    const { ctx } = this;
    if (this.isMobileDevice()) {
      return ctx.redirect(`/m${ctx.url}`);
    }
    if (!spreadCode) {
      return ctx.redirect("/auth/register");
    } else {
      const salesmanResult = await this.salesmanService.getSalesmanBySpreadCode(spreadCode);
      if (!salesmanResult.data) {
        const originUrl = ctx.url;
        return ctx.redirect(`/403?origin=${encodeURIComponent(originUrl)}&message=${encodeURIComponent("不合法的推荐码，请联系您的业务经理或重试！")}`);
      } else {
        const salesman = salesmanResult.data;
        ctx.spreadInfo = {
          userid: salesman?.id,
          nickname: salesman?.nickname,
          spreadCode: salesman?.spreadCode,
          spreadAvatar: salesman?.spreadAvatar,
          spreadPhone: salesman?.spreadPhone,
          spreadDescription: salesman?.spreadDescription,
          isPromoter: salesman?.isPromoter,
        };
        const customerCategoryRes = await this.dictService.getDictByCode(COMMON_DICT_MAPPING.CUSTOMER_CATEGORY.value);
        ctx.customerCategory = customerCategoryRes?.data?.detail || [];
        ctx.body = await render<Readable>(this.ctx, {
          stream: true,
        });
      }
    }
  }

  @Get("/logout")
  async logout(@Query("redirect") redirect: string): Promise<void> {
    const { ctx } = this;
    const session = ctx.session;
    if (session) {
      session.userLoginState = null;
      session.userData = null;
    }
    ctx.redirect(decodeURIComponent(redirect) ?? "/");
  }

  @Get("/password-reset")
  async passwordReset(): Promise<void> {
    const { ctx } = this;
    if (this.isMobileDevice()) {
      return ctx.redirect(`/m${ctx.url}`);
    }
    ctx.body = await render<Readable>(ctx, {
      stream: true,
    });
  }
}
