import { treeToolHandlerInstance } from "@/utils/tree-tool.util";
import SvgIcon from "@/components/SvgIcon";
import React from "react";

/** 个人中心layout钩子 */
export const useUCenterLayout = (state) => {
  /** 个人中心菜单数据-添加新选项请在此配置
   * 注意注意：配置个人中心nav需要遵守以下规则
   * 1、key的值需要等于path最后的值，例如`我的订单` path为：/ucenter/trade/order，则key应设置为：order
   * 2、尽量默认打开所有子节点
   * 3.icon: icon-svg的名称,如果需要设置图标需引入对应的svg文件
   * 4.id 和 pid代表上下级关系，pid=0为顶级菜单
   */
  const layoutMenuItemsFlatArray = [
    { id: "1", pid: "0", label: "我的交易", key: "trade", icon: "trademark", path: "" },
    { id: "2", pid: "0", label: "我的信息", key: "info", icon: "user", path: "" },
    { id: "3", pid: "0", label: "历史产品", key: "product", icon: "product", path: "/ucenter/product" },
    { id: "4", pid: "0", label: "库存查询", key: "stock", icon: "gold", path: "/ucenter/stock" },
    { id: "5", pid: "0", label: "联系客服", key: "contactus", icon: "comment", path: "/ucenter/contactus" },
    { id: "1-1", pid: "1", label: "我的订单", key: "order", icon: "", path: "/ucenter/trade/order" },
    { id: "1-2", pid: "1", label: "我的购物车", key: "shopping-cart", icon: "", path: "/ucenter/trade/shopping-cart" },
    { id: "1-3", pid: "1", label: "我的收藏", key: "favorites", icon: "", path: "/ucenter/favorites" },
    { id: "2-1", pid: "2", label: "基本信息", key: "member", icon: "", path: "/ucenter/member" },
    { id: "2-2", pid: "2", label: "个人钱包", key: "my-wallet", icon: "", path: "/ucenter/my-wallet" },
    { id: "2-3", pid: "2", label: "账户安全", key: "account-safe", icon: "", path: "/ucenter/member/account-safe" },
    { id: "2-4", pid: "2", label: "收货地址", key: "address", icon: "", path: "/ucenter/member/address" },
    { id: "2-5", pid: "2", label: "开票资料", key: "receipt", icon: "", path: "/ucenter/member/receipt" },
    { id: "2-6", pid: "2", label: "我的资质", key: "qualifications", icon: "", path: "/ucenter/member/qualifications" },
    { id: "2-7", pid: "2", label: "我的足迹", key: "footprint", icon: "", path: "/ucenter/footprint" },
    { id: "2-8", pid: "2", label: "系统消息", key: "message", icon: "", path: "/ucenter/message" }
  ]
  // 是否添加旧版商城数据
  const oldMallMenu = { id: "6", pid: "0", label: "旧版商城", key: "old-mall", icon: "shop", path: "/ucenter/old-mall" }
  if (state?.layoutInitData?.ccOldMemberLoginOpen) {
    !layoutMenuItemsFlatArray.filter(item => item.key === oldMallMenu.key).length && layoutMenuItemsFlatArray.push(oldMallMenu)
  }

  /** 获取个人中心可渲染的menu菜单-生成a标签和图标 */
  const getUCenterLayoutMenuItems = () => {
    const navItems = treeToolHandlerInstance.fromList(getUCenterLayoutMenuArray())
    treeToolHandlerInstance.forEach(navItems, (item) => {
      if (item.path) {
        item.label = <a href={item.path}>{ item.label }</a>
      }
      if (item.icon && typeof item.icon === 'string') {
        item.icon = <SvgIcon iconClass={item.icon} />
      }
      if (item.children && item.children.length === 0) {
        item.children = null
      }
    })
    return navItems
  }

  /** 获取个人中心菜单源数据 */
  const getUCenterLayoutMenuArray = () => {
    return layoutMenuItemsFlatArray
  }

  return {
    getUCenterLayoutMenuItems,
    getUCenterLayoutMenuArray
  }
}
