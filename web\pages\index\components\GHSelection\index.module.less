@contentPaddingOrMargin: 16px;

.wrapper {
  :global {
    .gh-selection {
      margin-top: @plate-margin-top;
      &-container {
        display: flex;
        gap: @contentPaddingOrMargin;
        .selection-cate {
          flex: 1;
          width: 555px;
          height: 476px;
          background-color: @main-bg-color-white;
          padding: @contentPaddingOrMargin;
          cursor: pointer;

          .selection-logo {
            width: 100%;
            height: 270px;
            background-color: #d7eefb;
            color: @main-text-color;
            .flex-col(space-between, center);
            padding: @contentPaddingOrMargin 0;
            .selection-info {
              .flex-col(normal, center);
              .selection-title {
                font-size: 30px;
                font-weight: 700;
                line-height: 1;
              }
              .selection-sub-title {
                margin-top: 10px;
                font-size: 16px;
                font-weight: 350;
                line-height: 1;
              }
            }
            .selection-img {
              width: 200px;
              height: 177px;
              display: inline-block;
            }
          }

          .products {
            box-sizing: border-box;
            margin-top: @contentPaddingOrMargin;
            display: flex;
            position: relative;
            gap: 8px;
            > li {
              width: 276px;
              _:-ms-fullscreen,
              & {
                margin-right: 16px;
                &:last-child {
                  margin-right: 0;
                }
              }
            }
            // 鼠标移除隐藏中线
            &:hover {
              &::after {
                display: none;
              }
            }
          }
          &:nth-child(2) {
            .theme {
              background-color: #fbe9d7;
            }
          }
        }

        .selection-new-arrival {
          .selection-logo {
            background-color: #fbe9d7;
          }
        }
      }
    }
  }
}
