import { useState } from "react";
import crudStock from "@/apis/product/stock";
import { debounce } from "lodash";
import { message } from "antd";

/** 库存结果对象键值对 */
interface ISkuStockResultsType {
  [key: string]: string;
}

/**
 * 查库存hooks
 */
export const useQueryStock = () => {
  // 库存sku查询标记位
  const [skuStockResults, stSkuStockResults] = useState<ISkuStockResultsType>(Object.assign({}));

  /** sku查库存 */
  const getStockRemarkBySkuId = debounce(async (skuId: number, inputVal: number) => {
    const [err, res] = await crudStock
      .getSkuStockBySkuId(skuId, inputVal)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      message.error(err?.data?.message ?? "网络异常，请重试~");
      return;
    }
    stSkuStockResults({
      ...skuStockResults,
      ...res.data,
    });
  }, 200);

  return {
    skuStockResults,
    getStockRemarkBySkuId,
  };
};
