import React, { useContext, useEffect, useRef, useState } from "react";
import { IContext, SProps } from "ssr-types-react";
import { Space, Table, Card, Form, Input, DatePicker, Spin, Button, Typography, Modal, message, notification, Empty, Tooltip, Radio } from "antd";
import type { ColumnsType } from "antd/es/table";
import MyCrudPagination from "@/components/Crud/Pagination";
import RROperation from "@/components/Crud/RROperation";
import CrudOperation from "@/components/Crud/CrudOperation";
import CRUD from "@/components/Crud/crud";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import style from "./index.module.less";
import { findOrderDescByValue } from "@/constants/order";
import { price2Thousand, price2ThousandFixed4, ghmallGuidePrice2Show } from "@/utils/price-format.util";
import crudOrder from "@/apis/order/order";
import { $tools } from "@/utils/tools.util";
import RenderDownloadOrderContractBtn from "./cnpts/RenderDownloadOrderContractBtn";
import OrderTrackingModal from "../parts/OrderTrackingModal";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { IOrderProductColumnType } from "@/typings/order.interface";
import { useStoreContext } from "ssr-common-utils";
import { PRODUCT_QUANTITY_DECIMAL_DIGITS } from "@/constants/common";

interface ICancelReason {
  label: string;
  value: string;
}

const { Text } = Typography;

export default function TradeOrderIndex(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const cancelReasonDictList: ICancelReason[] = state.cancelReasonDict;
  // 获取数据
  const crud = CRUD({ url: "/api/ucenter/orders", pageSize: 5 });
  const dataSource = crud.tableData;
  const shoppingCartHook = useShoppingCart();

  // 初始化，请求订单列表数据
  useEffect(() => {
    crud.refresh();
  }, []);

  // 表单筛选
  const [searchForm] = Form.useForm();

  // 分页搜索带日期
  useEffect(() => {
    if (searchForm.getFieldValue("createdDate")) {
      crud.setQueryParams({ createdDate: searchForm.getFieldValue("createdDate") });
    }
  }, [crud.pagination.page]);

  const RangePicker: any = DatePicker.RangePicker;
  // 选择点击的订单号
  const [currentSn, setCurrentSn] = useState<string>("");
  const orderTabList = [
    {
      key: "all",
      tab: "全部订单",
    },
    {
      key: "tobeBusinessAudit",
      /* 订单初始状态 */
      tab: <span title="待业务审核">待审核</span>,
    },
    {
      key: "tobePaid",
      /* 即业务已审核未付款 */
      tab: <span title="待付款">待付款</span>,
    },
    {
      key: "tobeReceivedOrFinanceHadConfirm",
      tab: <span title="财务确认/待收货">待收货/已确认/已付款</span>,
    },
    {
      key: "finish",
      tab: "已完成",
    },
    {
      key: "cancel",
      tab: "已取消",
    },
  ];
  const orderTabRef = useRef(null);
  const myRef = useRef(null);
  const [reasonForm] = Form.useForm();
  const reasonFormRules = {
    reason: [{ required: true, message: "请选择取消原因" }],
  };

  /** 渲染表格表头文字-带说明 */
  const renderOtherQuantityTitle = () => {
    return (
      <div className="quantity-header-title">
        <span>
          <Tooltip title="产品取消数量">
            <QuestionCircleOutlined />
          </Tooltip>
          &nbsp;取消
        </span>
        <span>
          <Tooltip title="产品发货数量">
            <QuestionCircleOutlined />
          </Tooltip>
          &nbsp;发货
        </span>
        <span>
          <Tooltip title="产品开票数量">
            <QuestionCircleOutlined />
          </Tooltip>
          &nbsp;开票
        </span>
      </div>
    );
  };

  // 订单产品明细
  const subColumns: ColumnsType<IOrderProductColumnType> = [
    {
      title: "序号",
      width: 44,
      align: "center",
      key: "idx",
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "SKU",
      dataIndex: "productSku",
      key: "productSku",
      width: 96,
      ellipsis: false,
      align: "center",
      render: (text, record, index) => (
        <a className="product-detail-link" href={`/product/${record.productNo}?sku=${text}`} target="_blank">
          {text}
        </a>
      ),
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      key: "productName",
      align: "center",
      width: 100,
      ellipsis: false,
      render: (text, record) => (
        <span className="product-name">
          <a className="product-detail-link" href={`/product/${record.productNo}`} target="_blank">
            {text}
          </a>
        </span>
      ),
    },
    {
      title: "包装",
      dataIndex: "productPacking",
      key: "productPacking",
      align: "center",
      width: 90,
      ellipsis: false,
      render: (text, record, index) => <span>{!record?.productPacking || record.productPacking === "1" ? "-" : record.productPacking}</span>,
    },
    {
      title: "品牌",
      dataIndex: "productBrand",
      key: "productBrand",
      width: 70,
      align: "center",
      ellipsis: false,
    },
    {
      title: "单位",
      dataIndex: "productUnit",
      key: "productUnit",
      width: 50,
      align: "center",
      ellipsis: false,
    },
    {
      title: "单价",
      dataIndex: "productPrice",
      key: "productPrice",
      width: 90,
      align: "center",
      render: (text, record, index) => <span className="product-common-link">{ghmallGuidePrice2Show(record.productPrice)}</span>,
    },
    {
      title: "数量",
      dataIndex: "productQuantity",
      key: "productQuantity",
      align: "center",
      width: 60,
    },
    {
      title: "小计",
      dataIndex: "totalPrice",
      key: "totalPrice",
      width: 108,
      align: "center",
      render: (text, record, index) => `${record.productTaxPrice === 0 ? "询价" : price2Thousand(record.productTaxPrice * record.productQuantity)}`,
    },
    // {
    //   title: renderOtherQuantityTitle,
    //   width: 140,
    //   dataIndex: "cancelQuantity",
    //   key: "cancelQuantity",
    //   align: "center",
    //   className: "quantity-row",
    //   render: (text, record, index) => (
    //     <span className="quantity-body-title">
    //       <span>{record?.cancelQuantity || 0}</span>
    //       <span>{record?.deliverQuantity || 0}</span>
    //       <span>{record?.receiptQuantity || 0}</span>
    //     </span>
    //   ),
    // },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
      align: "center",
      className: "remark",
      ellipsis: true,
      render: (text, record, index) => (
        <Tooltip placement="topLeft" title={record.remark}>
          <div className="text-ellipsis">{record.remark}</div>
        </Tooltip>
      ),
    },
    {
      title: "操作",
      width: 96,
      align: "center",
      ellipsis: false,
      render: (text, record, index) => (
        <Button
          type="link"
          onClick={() => {
            handleBuyAgain(record);
          }}
        >
          再次购买
        </Button>
      ),
    },
  ];

  // 自定义render
  const renderCardTitle = order => {
    return (
      <div className="item-title">
        <span className="item-title-label">{order.createdDate}</span>
        <span className="item-title-label">
          订单号：<span className="sn">{order.orderNo}</span>
        </span>
        <span className="item-title-label">
          订货人：<span>{order.customerContactName}</span>
        </span>
        {order.customerPo ? (
          <span className="item-title-label">
            客户PO：<span>{order.customerPo}</span>
          </span>
        ) : null}
      </div>
    );
  };

  const handleOnMonitorTabChange = tab => {
    // 发送请求数据
    const orderState = getOrderStateWidthTabKey(tab);
    searchForm.setFieldValue("orderState", orderState);
    crud.setQueryParams({ orderState: orderState });
    crud.toQuery();
  };

  /**
   * 删除订单
   * @param item
   */
  const handleCancelOrder = item => {
    if (!item.orderNo) {
      return;
    }
    const renderCancelReasonForm = () => {
      return (
        <Form form={reasonForm} name="reasonForm" layout="vertical" className="reason-form">
          <Form.Item name="reason" label="请选择订单取消原因:" rules={reasonFormRules.reason}>
            <Radio.Group className="cancel-reason-group">
              {cancelReasonDictList.map((item, index) => (
                <Radio key={index} value={item.label}>
                  {item.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        </Form>
      );
    };
    Modal.confirm({
      content: renderCancelReasonForm(),
      type: "warning",
      centered: true,
      closable: true,
      getContainer: () => myRef?.current ?? document.body,
      onOk: async () => {
        // 校验取消表单
        await reasonForm.validateFields();

        const [err, res] = await crudOrder
          .cancel(item.orderNo, reasonForm.getFieldsValue())
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (err) {
          notification.warning({ message: err.data.message || "取消失败，请重试" });
          return;
        }
        message.success("订单取消成功！" || res.message);
        reasonForm.resetFields(["reason"]);
        crud.refresh();
      },
      onCancel: () => {
        reasonForm.resetFields(["reason"]);
      },
      className: "cancel-reason-modal",
    });
  };

  /**
   * 根据tab获取订单当前状态类型
   * -1->已取消;0->建单【待业务审核】;1->待支付;2->待收货/财务确认;5->已完成
   * */
  const getOrderStateWidthTabKey = (tabKey: string) => {
    let orderState = "";
    switch (tabKey) {
      case "all":
        orderState = "";
        break;
      case "tobeBusinessAudit":
        orderState = "0";
        break;
      case "tobePaid":
        orderState = "1";
        break;
      case "tobeReceivedOrFinanceHadConfirm":
        orderState = "2";
        break;
      case "finish":
        orderState = "5";
        break;
      case "cancel":
        orderState = "-1";
        break;
    }

    return orderState;
  };

  // 已取消的订单，可重新下单
  const handleToReorder = async item => {
    // 重新加入购物车
    const shopCartProducts = item.products
      .map(product => {
        if (product?.productSkuId) {
          return {
            selected: 1,
            quantity: product.productQuantity,
            productSkuId: product.productSkuId,
          };
        }
        return null;
      })
      .filter(item => item);
    if (shopCartProducts.length === 0) {
      message.warn("该订单不允许重新加购，可能原因为产品已下架！");
      return false;
    }
    const res = await shoppingCartHook.insert(shopCartProducts, false);
    if (res) {
      message.success("加购成功，请前往我的购物车页进行下单操作！");
    }
    return false;
  };

  // 再次购买商品
  const handleBuyAgain = async item => {
    // 重新加入购物车
    const shopCartProducts = item?.productSkuId ? {
      selected: 1,
      quantity: item.productQuantity,
      productSkuId: item.productSkuId,
    } : null
    if (!shopCartProducts) {
      message.warn("该产品不允许重新加购，可能产品已下架！");
      return false;
    }
    const res = await shoppingCartHook.insert(shopCartProducts, false);
    if (res) {
      message.success("加购成功，请前往我的购物车页进行下单操作！");
    }
    return false;
  };

  return (
    <>
      <div className={style.orderWrapper} ref={myRef}>
        <UCenterCard ref={orderTabRef} title={"我的订单"} tabs={orderTabList} onMonitorTabChange={handleOnMonitorTabChange} />
        <Space align="center" style={{ marginBottom: 16 }}>
          {crud.getSearchToggle() ? (
            <div className="search-container">
              <Form layout="inline" form={searchForm} size="small">
                <Form.Item className="date-range-picker" label="时间" name="createdDate">
                  <RangePicker size="middle" format={"YYYY-MM-DD"} placeholder={["起始日期", "结束时间"]} />
                </Form.Item>
                <Form.Item className="order-no" label="单号" name="orderNo">
                  <Input size="middle" placeholder="订单号" autoComplete="off" allowClear onKeyDown={e => crud.handleOnkeydownEvent(e, searchForm.getFieldsValue())} />
                </Form.Item>
                <Form.Item name="orderState" style={{ display: "none" }}>
                  <Input hidden placeholder="订单状态" autoComplete="off" />
                </Form.Item>
                <Form.Item className="order-no" label="产品" name="blurry">
                  <Input size="middle" placeholder="请输入产品名称或代码" autoComplete="off" allowClear onKeyDown={e => crud.handleOnkeydownEvent(e, searchForm.getFieldsValue())} />
                </Form.Item>
                <RROperation size="middle" crudInstance={crud} />
              </Form>
            </div>
          ) : null}
        </Space>
        <div className="content-container">
          <CrudOperation crudInstance={crud} />
          {/* 内容区域 */}
          <Spin size="default" spinning={crud.loading}>
            <div className="order-content">
              {dataSource.length ? (
                dataSource.map(item => {
                  const subDataSource = [...(item?.products || [])];
                  subDataSource.sort((a, b) => a.rowNum - b.rowNum); // sku产品列表根据行号排序
                  return (
                    <Card
                      className="order-content-item"
                      loading={crud.loading}
                      type="inner"
                      title={renderCardTitle(item)}
                      key={item.orderNo}
                      headStyle={{ padding: "0 20px", background: "#F2F2F2" }}
                      extra={
                        <span className="item-state" onClick={() => setCurrentSn(item.orderNo)}>
                          {findOrderDescByValue(parseInt(item.orderState))}
                        </span>
                      }
                    >
                      <Table
                        rowClassName="item-custom-table"
                        rowKey={"id"}
                        size="small"
                        dataSource={subDataSource}
                        columns={subColumns}
                        pagination={false}
                        footer={pageData => {
                          return (
                            <>
                              <div className="table-footer-custom">
                                <div className="table-footer-custom-action">
                                  {item.orderState === 0 ? (
                                    <>
                                      <Button className="del" ghost type="default" onClick={() => handleCancelOrder(item)}>
                                        取消订单
                                      </Button>
                                    </>
                                  ) : null}
                                  {(item.orderState === -1 || item.orderState === 20) && (
                                    <>
                                      <Button className="del" ghost type="default" onClick={async () => await handleToReorder(item)}>
                                        {item.orderState === -1 ? "重新下单" : "再次购买"}
                                      </Button>
                                    </>
                                  )}
                                  {item.submitStatus === 0 ? (
                                    <>
                                      <Button className="del" ghost type="default">
                                        <a href={`/ucenter/trade/order/edit/${item.orderNo}`}>编辑订单</a>
                                      </Button>
                                    </>
                                  ) : null}
                                  {item.orderState === 20 && item.isPaid === 0 ? (
                                    <Button className="del" ghost type="default">
                                      <a target="_blank" href={`/pay/payment?sn=${item.orderNo}`}>
                                        前往支付
                                      </a>
                                    </Button>
                                  ) : null}
                                  {RenderDownloadOrderContractBtn(item?.attachments)}
                                  <Button className="detail" ghost type="default">
                                    <a href={`/ucenter/trade/order/${item.orderNo}`}>查看详情</a>
                                  </Button>
                                </div>
                              </div>
                            </>
                          );
                        }}
                        summary={pageData => {
                          let totalQuantity = 0;
                          let totalAmount = 0;
                          let cancelTotal = 0;
                          pageData.forEach(({ productQuantity, productTaxPrice, cancelQuantity }) => {
                            totalQuantity += productQuantity;
                            totalAmount += productTaxPrice;
                            cancelTotal += cancelQuantity ?? 0;
                          });
                          return (
                            <>
                              <Table.Summary.Row className="table-summary-custom">
                                <Table.Summary.Cell className="invoice" colSpan={4} index={0}>
                                  发票类型：<i>{$tools.generateOrderReceiptObj(item?.receiptInfo).receipt_type_name}</i>
                                </Table.Summary.Cell>
                                <Table.Summary.Cell className="summary-cell" index={1} colSpan={2}>
                                  <Text>
                                    运费：<i className="fee">￥{item.freightFee ?? 0}</i>
                                  </Text>
                                </Table.Summary.Cell>
                                <Table.Summary.Cell className="summary-cell" index={2} colSpan={2}>
                                  <Text>
                                    数量合计：
                                    <i className="quantity showWeight">
                                      {$tools.keepDigitDecimal(totalQuantity, PRODUCT_QUANTITY_DECIMAL_DIGITS)}
                                      {cancelTotal ? <i>（取消: {cancelTotal}）</i> : null}
                                    </i>
                                  </Text>
                                </Table.Summary.Cell>
                                <Table.Summary.Cell className="summary-cell" index={3} colSpan={3}>
                                  <Text>
                                    金额：<i className="total showWeight">￥{pageData.findIndex(item => item.productPrice === 0) === -1 ? price2Thousand(item.totalMoney) : "询价"}</i>
                                  </Text>
                                </Table.Summary.Cell>
                              </Table.Summary.Row>
                            </>
                          );
                        }}
                      />
                    </Card>
                  );
                })
              ) : (
                <Empty />
              )}
            </div>
          </Spin>
          <div className="content-container-pagination">
            <MyCrudPagination crudInstance={crud} pageChangeCallback={() => window.scrollTo(0, 0)} />
          </div>
        </div>
      </div>
      <OrderTrackingModal sn={currentSn} onCloseCallback={() => setCurrentSn("")} />
    </>
  );
}
