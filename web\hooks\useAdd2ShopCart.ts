import { useContext, useRef, useState } from "react";
import $message from "@/components/TmMessage";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";

/** 购物输入框钩子 */
export const useAdd2ShopCartInput = (onQuantityChange?: (quantity: number) => void) => {
  const { state } = useContext<IContext>(useStoreContext());
  /** 输入框引用 */
  const add2CartRef = useRef<any>(null);
  /** 输入框采购数量 */
  const [purchaseQuantity, setPurchaseQuantity] = useState<any>(1);
  /** 选购数量 */
  const [cartStep, setCartQuantityStep] = useState<number>(1);

  /** 直接修改 */
  const add2CartBlurEvent = e => {
    if (!e.target.value) {
      return;
    }
    let value = Number(e.target.value);

    if (state.isOpenProductMinPackingRatioControl) {
      // 处理规则，判断是否最小起订量倍数
      if (value < cartStep) {
        $message.info(`不能小于最小起订数量: ${cartStep}！`);
        value = cartStep;
      } else if (value % cartStep !== 0) {
        value = Math.round(value / cartStep) * cartStep;
      }
    }

    const finalValue = value <= 0 ? 1 : value;
    setPurchaseQuantity(finalValue);

    // 触发数量变化回调
    if (onQuantityChange) {
      onQuantityChange(finalValue);
    }
  };

  /** 减少 */
  const add2CartReduceEvent = () => {
    const val1 = Number(add2CartRef.current.value);

    const minMessage = state.isOpenProductMinPackingRatioControl ? `该规格产品最小起订数量：${cartStep}` : `该规格产品最小购买数量为：1`
    // const targetCartStep = state.isOpenProductMinPackingRatioControl ? cartStep : 1;
    const targetCartStep = cartStep;
    let val = val1 - targetCartStep;
    if (val <= 0) {
      $message.info(minMessage);
      return;
    }
    if (targetCartStep > 1) {
      val = Math.floor(val / targetCartStep) * targetCartStep;
    }

    const finalValue = val <= 0 ? 1 : val;
    setPurchaseQuantity(finalValue);

    // 触发数量变化回调
    if (onQuantityChange) {
      onQuantityChange(finalValue);
    }
  };

  /** 增加 */
  const add2CartPlusEvent = () => {
    const val1 = Number(add2CartRef.current.value);

    // const targetStep = state.isOpenProductMinPackingRatioControl ? cartStep : 1;
    const targetStep = cartStep;
    let val = val1 + targetStep;
    if (targetStep > 1) {
      val = Math.floor(val / targetStep) * targetStep;
    }

    setPurchaseQuantity(val);

    // 触发数量变化回调
    if (onQuantityChange) {
      onQuantityChange(val);
    }
  };

  return {
    add2CartRef,
    add2CartBlurEvent,
    purchaseQuantity,
    setPurchaseQuantity,
    add2CartReduceEvent,
    add2CartPlusEvent,
    cartStep,
    setCartQuantityStep,
  };
};
