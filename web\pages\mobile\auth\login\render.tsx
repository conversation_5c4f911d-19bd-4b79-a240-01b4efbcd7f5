import React, { useState } from "react";
import { SProps } from "ssr";
import styles from "./index.module.less";
import { Button, Form, Input, Toast, Popup, CheckList } from "antd-mobile";
import { FormInstance } from "antd-mobile/es/components/form";
import { validateGhmallLoginAccount } from "@/utils/form-valid.util";
import { MobileSmsCaptcha } from "@/components/mobile/TmFormItem";
import { SmsSendTypeEnum } from "@/enums/SmsSendTypeEnum";
import { SYSTEM_LOGIN_TYPES } from "@/constants/system";
import { EyeInvisibleOutline, EyeOutline } from "antd-mobile-icons";
import { loginByPassword, loginBySms, getAccountInfoByPhone } from "@/apis/auth";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { debounce } from "lodash";
import MobileNavBar from "@/components/mobile/TmFormItem/src/components/MobileNavBar";

export default function LoginMobile(props: SProps) {
  const [form] = Form.useForm();
  const loginRef = React.createRef<FormInstance>() as any;
  const [navList, setNavList] = useState<string[]>(["密码登录", "游客登录"]);
  const [currentNav, setCurrentNav] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [params, setParams] = useState<any>({
    visible: false,
    inputType: "password",
  });
  const [phoneBindCustomers, setPhoneBindCustomers] = useState<object[]>([]);
  const [pickerVisible, setPickerVisible] = useState<boolean>(false);
  const [pickerSelectedValue, setPickerSelectedValue] = useState<string>("");
  // 表单默认值
  const isProduction = process.env.NODE_ENV === "production";

  const [loginForm] = useState<any>({
    phone: isProduction ? "" : "***********",
    password: isProduction ? "" : "abc123456@",
    loginType: SYSTEM_LOGIN_TYPES.PASSWORD.value,
  });
  // 登录表单校验
  const rules = {
    account: [{ required: true, whitespace: true, message: "登录账号不能为空" }, { validator: validateGhmallLoginAccount }],
    phone: [
      { required: true, whitespace: true, message: "手机号不能为空" },
      { pattern: /^1([3-9][0-9])\d{8}$/, message: "请输入正确格式的手机号" },
    ],
    password: [
      { required: true, whitespace: true, message: "登录密码不能为空" },
      { max: 20, min: 6, message: "密码不能小于6位或大于20位" },
    ],
    companyName: [{ required: true, message: "请选择账号绑定的公司" }],
  };
  /** ======================================== method start ======================================= */
  // 检查手机号/邮箱绑定多客户情况
  const checkedPhoneBindCustomer = async (e, form) => {
    form
      .validateFields(["phone"])
      .then(res => {
        if (res["phone"]) {
          loginRef.current?.setFieldValue("companyName", null);
          getAccountInfoByPhone(res["phone"]).then(cb => {
            const _data = cb.data?.data?.map(item => item.companyName);
            setPhoneBindCustomers(_data);
          });
        }
      })
      .catch(err => {
        setPhoneBindCustomers([]);
        console.log("err", err);
      });
  };
  // 登录请求
  const handleSubmitEvent = debounce(async (values: any) => {
    setLoading(true);
    let response;
    if (phoneBindCustomers.length === 1) {
      values.companyName = phoneBindCustomers[0];
    }
    if (currentNav === 0) {
      // 密码登录
      response = await loginByPassword({
        phone: values.phone,
        password: encrypt(values.password),
        companyName: values.companyName ? values.companyName : undefined,
      })
        .then(res => [null, res])
        .catch(err => [err, null]);
    } else if (currentNav === 1) {
      // 短信登录
      response = await loginBySms({
        phone: values.phone,
        smsCode: values.smsCode,
        companyName: values.companyName ? values.companyName : undefined,
      })
        .then(res => [null, res])
        .catch(err => [err, null]);
    } else {
      setLoading(false);
      return Toast.show({
        content: "未处理的登录类型",
        maskClickable: false,
        duration: 1500,
      });
    }
    const [err, res] = response;
    if (err) {
      setLoading(false);
      return Toast.show({
        content: err?.data?.message ?? "手机号或密码错误，登录失败",
        maskClickable: false,
        duration: 1500,
      });
    }
    Toast.show({
      content: res.message || "登录成功",
      maskClickable: false,
      duration: 1500,
    });
    setTimeout(() => {
      setLoading(false);
      const params = new URLSearchParams(props.location.search);
      window.location.href = params.get("redirect") || "/";
    }, 3000);
  }, 300);
  // 切换选项卡
  const handleNavTapChangeEvent = (idx: number) => {
    setPhoneBindCustomers([]);
    setCurrentNav(idx);
  };
  const onVisible = (visible: boolean) => {
    setParams(params => {
      return {
        ...params,
        visible: visible,
      };
    });
  };
  /** ======================================== method end ======================================= */
  return (
    <>
      <div className={styles.wrapper}>
        <div className="auth-page">
          <div className="auth-header">
            <MobileNavBar title="光华易购登录" />
          </div>
          <div className="auth-container">
            <div className="auth-content">
              <div className="navbar-box">
                {navList.map((item, index) => (
                  <div className={`navbar-box-item ${currentNav === index ? "navbar-box-on" : ""}`} key={index} onClick={() => handleNavTapChangeEvent(index)}>
                    {item}
                  </div>
                ))}
              </div>
              <Form
                onFinish={handleSubmitEvent}
                ref={loginRef}
                form={form}
                initialValues={loginForm}
                layout="horizontal"
                className="auth-form-box"
                footer={
                  <Button type="submit" loading={loading} className="auth-submit-btn" color="primary">
                    登录
                  </Button>
                }
              >
                {currentNav === 0 ? (
                  <>
                    <Form.Item label="登录账号" required={false} className="item" name="phone" rules={rules.account}>
                      <Input
                        className="input"
                        autoComplete="off"
                        clearable
                        placeholder="请输入手机号或邮箱"
                        onChange={e => {
                          checkedPhoneBindCustomer(e, form);
                        }}
                      />
                    </Form.Item>
                    {phoneBindCustomers.length > 1 && (
                      <Form.Item label="登录公司" className="item" name="companyName" rules={rules.companyName}>
                        <div
                          className="input"
                          onClick={() => {
                            setPickerVisible(true);
                          }}
                        >
                          {pickerSelectedValue ? pickerSelectedValue : "请选择账号绑定的公司"}
                        </div>
                      </Form.Item>
                    )}
                    <Form.Item
                      label="登录密码"
                      required={false}
                      className="item"
                      name="password"
                      rules={rules.password}
                      extra={
                        <>
                          {!params.visible ? (
                            <EyeInvisibleOutline
                              onClick={() => {
                                setParams({ inputType: "text" });
                                onVisible(true);
                              }}
                            />
                          ) : (
                            <EyeOutline
                              onClick={() => {
                                setParams({ inputType: "password" });
                                onVisible(false);
                              }}
                            />
                          )}
                        </>
                      }
                    >
                      <Input className="input" autoComplete="off" clearable placeholder="请输入登录密码" type={params?.inputType ?? "password"} />
                    </Form.Item>
                  </>
                ) : null}
                {currentNav === 1 ? (
                  <>
                    <Form.Item label="手机号码" required={false} className="item" name="phone" rules={rules.phone}>
                      <Input
                        className="input"
                        autoComplete="off"
                        clearable
                        placeholder="请输入手机号"
                        onChange={e => {
                          checkedPhoneBindCustomer(e, form);
                        }}
                      />
                    </Form.Item>
                    {phoneBindCustomers.length > 1 && (
                      <Form.Item label="登录公司" className="item" name="companyName" rules={rules.companyName}>
                        <div
                          className="input"
                          onClick={() => {
                            setPickerVisible(true);
                          }}
                        >
                          {pickerSelectedValue ? pickerSelectedValue : "请选择账号绑定的公司"}
                        </div>
                      </Form.Item>
                    )}
                    {/* ========== 短信验证码组件 ========== */}
                    <MobileSmsCaptcha showRequired={false} uuid={"mobileloginPassword"} smsSendType={SmsSendTypeEnum.LOGIN} smsFormRef={form} />
                  </>
                ) : null}
              </Form>
              {currentNav === 0 ? (
                <>
                  <div className="auth-tip">
                    <a href={"/m/auth/password-reset"} className="auth-tip-forget">
                      忘记密码
                    </a>
                    未有账号？
                    <a href={"/m/auth/register"} className="auth-tip-login">
                      免费注册
                    </a>
                  </div>
                </>
              ) : null}
            </div>
            <div className="auth-bottom" />
            <Popup
              visible={pickerVisible}
              onMaskClick={() => {
                setPickerVisible(false);
              }}
              destroyOnClose
            >
              <div>
                <CheckList
                  onChange={val => {
                    setPickerSelectedValue(val.toString());
                    loginRef.current?.setFieldValue("companyName", val.toString());
                    setPickerVisible(false);
                  }}
                  value={[pickerSelectedValue]}
                >
                  {phoneBindCustomers.map(item => (
                    <CheckList.Item key={item} value={item}>
                      {item}
                    </CheckList.Item>
                  ))}
                </CheckList>
              </div>
            </Popup>
          </div>
        </div>
      </div>
    </>
  );
}
