import { Button, DatePicker, Descriptions, Form, message, Modal, notification, Table } from "antd";
import RROperation from "@/components/Crud/RROperation";
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import CRUD from "@/components/Crud/crud";
import { ColumnsType } from "antd/es/table";
import { price2Thousand } from "@/utils/price-format.util";
import MyCrudPagination from "@/components/Crud/Pagination";
import style from "./index.module.less";
import TradeRechargeModal from "@/components/TradeRechargeModal";
import { IRechargeType } from "@/typings/wallet.interface";
import walletConstants from "@/constants/wallet";
import CrudOperation from "@/components/Crud/CrudOperation";
import { useLoading } from "@/hooks/useLoading";
import crudWallet from "@/apis/member/wallet";
import TmModal from "@/components/TmModal";

const RechargeIndex = forwardRef((prop: any, ref?: any) => {
  const [searchForm] = Form.useForm();
  const RangePicker: any = DatePicker.RangePicker;
  const crud = CRUD({
    url: "/api/ucenter/my-wallet/recharge",
    pageSize: 10,
  });
  const columnsOptions: ColumnsType<IRechargeType> = [
    {
      title: "序号",
      width: 55,
      align: "center",
      render: (text, record, index) => <span>{index + 1}</span>,
    },
    {
      title: "充值编号",
      width: 180,
      align: "center",
      key: "rechargeSn",
      render: (text, record, index) => (
        <span className="blue-tip" onClick={async () => await handleShowRechargeDetail(record)}>
          {record.rechargeSn}
        </span>
      ),
    },
    {
      title: "充值客户",
      width: 150,
      align: "center",
      key: "memberName",
      dataIndex: "memberName",
      ellipsis: true,
    },
    {
      title: "充值金额",
      width: 150,
      align: "center",
      key: "money",
      dataIndex: "money",
      render: (text, record, index) => <span className="red-tip">￥{price2Thousand(record.rechargeMoney)}</span>,
    },
    {
      title: "支付状态",
      width: 100,
      align: "center",
      key: "memberName",
      render: (text, record, index) => <span className={record.payStatus === "PAID" ? "blue-tip" : "red-tip"}>{walletConstants.WALLET_PAID_STATUS[record.payStatus]}</span>,
    },
    {
      title: "创建时间",
      width: 160,
      align: "center",
      key: "detail",
      dataIndex: "createdDate",
      ellipsis: true,
    },
    {
      title: "操作",
      align: "center",
      render: (text, record, index) => (
        <div className="btn-wrapper">
          {record.payStatus === "UNPAID" ? (
            <>
              <Button size="small" danger onClick={async () => await handleToPay(record)}>
                去支付
              </Button>
              <Button size="small" danger onClick={() => cancelPayOrder(record)}>
                取消订单
              </Button>
            </>
          ) : (
            <Button className="btn-has-end" size="small" disabled={true} danger>
              {record.payStatus === "PAID" ? "已完成" : "已取消"}
            </Button>
          )}
        </div>
      ),
    },
  ];
  const [tradeRechargeState, setTradeRechargeState] = useState(false);
  const [currentRow, setCurrentRow] = useState<IRechargeType>();
  const useLoadingHook = useLoading();
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    crud.refresh();
  }, []);

  const crudRefresh = async () => {
    await crud.refresh();
  };

  const handleToPayForward = async (item: IRechargeType) => {
    await handleToPay(item);
  };

  useImperativeHandle(ref, () => {
    return { crudRefresh, handleToPayForward };
  });

  /** 取消订单 */
  const cancelPayOrder = (item: IRechargeType) => {
    if (!item || !item.rechargeSn || item.payStatus !== "UNPAID") {
      return message.warning("充值订单不存在或异常，无法操作！");
    }

    Modal.confirm({
      content: `您确定撤销「${item.rechargeSn}」充值订单？`,
      type: "warning",
      centered: true,
      onOk: async () => {
        useLoadingHook.showLoading("充值订单撤销中...");
        const [err, res] = await crudWallet
          .cancelRechargeOrder(item.rechargeSn)
          .then(res => [null, res])
          .catch(err => [err, null]);
        useLoadingHook.hideLoading();
        if (err) {
          return notification.error({ message: err?.data?.message || "充值订单取消失败，请联系[客服 或 您的专属业务]处理！" });
        }

        message.success(res.message || `「${item.rechargeSn}」充值订单已取消成功！`);
        crud.refresh();
      },
    });
  };

  /** 充值订单-支付前 */
  const handleToPayBefore = async (record: IRechargeType) => {
    setModalVisible(false);
    await handleToPay(record);
  };

  /** 触发激活支付处理 */
  const handleToPay = async (item: IRechargeType) => {
    if (!item || !item.rechargeSn || item.payStatus !== "UNPAID") {
      return await message.warning("充值订单不存在或异常，无法进行支付！");
    }

    setCurrentRow(item);
    setTradeRechargeState(true);
  };

  const handleShowRechargeDetail = async record => {
    record !== currentRow && setCurrentRow(record);
    await setModalVisible(true);
  };

  /** 渲染充值订单详情 */
  const renderRechargeDetail = () => {
    return (
      <div>
        <Descriptions column={1} className="recharge-detail">
          <Descriptions.Item label="充值编号">
            <span className="blue-tip">{currentRow?.rechargeSn}</span>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">{currentRow?.createdDate}</Descriptions.Item>
          <Descriptions.Item label="充值账户">{currentRow?.memberName}</Descriptions.Item>
          <Descriptions.Item label="充值状态">
            <span className={currentRow?.payStatus === "PAID" ? "blue-tip" : "red-tip"}>{currentRow?.payStatus && walletConstants.WALLET_PAID_STATUS[currentRow.payStatus]}</span>
          </Descriptions.Item>
          <Descriptions.Item label="充值金额">
            <span className="red-tip">￥{price2Thousand(currentRow?.rechargeMoney)}</span>
          </Descriptions.Item>
          {currentRow?.payStatus === "UNPAID" && (
            <Descriptions.Item contentStyle={{ justifyContent: "center" }}>
              <Button danger onClick={async () => await handleToPayBefore(currentRow)}>
                去支付
              </Button>
            </Descriptions.Item>
          )}
          {currentRow?.receivableNo && (
            <>
              <Descriptions.Item label="支付方式">{walletConstants.WALLET_RECHARGE_WAY[currentRow?.rechargeWay]}</Descriptions.Item>
              <Descriptions.Item label="支付时间">{currentRow?.payTime}</Descriptions.Item>
              <Descriptions.Item label="交易流水">{currentRow?.receivableNo}</Descriptions.Item>
            </>
          )}
        </Descriptions>
      </div>
    );
  };

  return (
    <div className={style.wrapper}>
      <div className="search-bar">
        {crud.getSearchToggle() ? (
          <Form layout="inline" form={searchForm} size="small">
            <Form.Item className="date-range-picker" label="创建时间" name="createdDate">
              <RangePicker size={"middle"} format={"YYYY-MM-DD"} placeholder={["起始时间", "结束时间"]} />
            </Form.Item>
            <RROperation size={"middle"} crudInstance={crud} />
          </Form>
        ) : null}
      </div>
      <CrudOperation crudInstance={crud} />
      <Table rowClassName="item-custom-table" rowKey={"id"} size="small" dataSource={crud.tableData} columns={columnsOptions} pagination={false} />
      <div className="content-container-pagination">
        <MyCrudPagination crudInstance={crud} />
      </div>
      <TradeRechargeModal modalVisible={tradeRechargeState} order={currentRow} changeModalVisible={setTradeRechargeState} />
      {/* 充值订单详情modal */}
      <TmModal title={`充值订单详情`} width={320} centered={true} open={modalVisible} onCancel={() => setModalVisible(false)} content={renderRechargeDetail()} closable={true} footer={null} />
    </div>
  );
});

export default RechargeIndex;
