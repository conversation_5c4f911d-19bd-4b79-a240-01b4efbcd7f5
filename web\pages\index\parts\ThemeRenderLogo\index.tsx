import React from "react";
import "./index.less";
import { useOss } from "@/hooks/useOss";
import commonConstant from "@/constants/common";

interface ThemeRenderLogoProps {
  title: string;
  brand?: string;
  subTitle?: string;
  img?: string;
  height?: string;
  description?: string;
  tags?: string[],
  attrs?: {
    remark: string;
    price: string;
  };
  bgColor?: string;
  color?: string;
  moreLink?: string;
  onClick?: () => void;
}

const RenderThemeLogo = (props: ThemeRenderLogoProps) => {
  const useOssHook = useOss();
  return (
    <a className="theme-render-logo" style={{ display: "inline-block", background: props.bgColor, color: props.color, height: props?.height || "inherit" }} onClick={props.onClick}>
      <span className="title" style={{ color: props.color }}>
        <span>{props.subTitle || props.title}</span>
      </span>
      {props.description ? <span className="sub-title">{props.description}</span> : null}
      {props?.tags?.length ? (
        <ul className="theme-tags">
          { props.tags.map((tag, index) => <li key={index} className='theme-tag'>{ tag }</li>) }
        </ul>
      ) : null}
      <div className="attrs">
        <img className="img" width="100%" src={props.img ? useOssHook.generateOssFullFilepath(props.img) : commonConstant.COMMON_IMAGE_PATHS.HOMEPAGE_PLATE_DEFAULT} alt={props.img} />
        {props?.moreLink && (
          <a href={props.moreLink} style={{ textAlign: "right", fontSize: "12px" }}>
            查看更多&gt;&gt;&gt;
          </a>
        )}
      </div>
    </a>
  );
};

export default RenderThemeLogo;
