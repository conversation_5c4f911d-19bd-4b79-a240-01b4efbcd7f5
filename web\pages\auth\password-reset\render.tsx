import React, { useRef, useState } from "react";
import { SProps } from "ssr";
import SimpleLayout from "@/components/layout/SimpleLayout";
import { Button, Form, FormInstance, Input, message, notification, Tabs, Select } from "antd";
import style from "./index.module.less";
import PasswordItem from "@/components/PasswordItem";
import SmsCaptcha from "@/components/SmsCaptcha";
import { MailOutlined, MobileOutlined } from "@ant-design/icons";
import { findAuthPasswordByEmail, findAuthPwd, getAccountInfoByPhone } from "@/apis/auth";
import { useLoading } from "@/hooks/useLoading";
import { SmsSendTypeEnum } from "@/enums/SmsSendTypeEnum";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { validateEmail, validateMobile } from "@/utils/form-valid.util";
import EmailSendCodeCnpt from "@/components/EmailSendCodeCnpt";
import { EmailSendTypeEnum } from "@/enums/EmailSendTypeEnum";

export default function PasswordReset(props: SProps) {
  const [form] = Form.useForm();
  const formRef = useRef<FormInstance>(null) as any;
  const useLoadingHook = useLoading();
  const [currentTab, setCurrentTab] = useState<string>("phoneTab");
  const [phoneBindCustomers, setPhoneBindCustomers] = useState<object[]>([]);
  const [flag, setFlag] = useState<boolean>(false);

  const passwordResetRules = {
    phone: [{ required: true, whitespace: true, message: "手机号不能为空!" }, { validator: validateMobile }],
    email: [{ required: true, whitespace: true, message: "邮箱不能为空!" }, { validator: validateEmail }],
    companyName: [{ required: true, message: "请选择账号绑定的公司!" }],
  };
  /** ======================================== method start ======================================= */
  const handleResetSubmitEvent = async (form: any) => {
    useLoadingHook.showLoading();
    const { email, verifyCode, phone, smsCode, password, repeat: confirmPassword, companyName } = form;
    const encryptPassword = encrypt(password);
    const postData: any = {
      password: encryptPassword,
      confirmPassword: encryptPassword,
    };
    let err, res;
    if (currentTab === "phoneTab") {
      postData.phone = phone;
      postData.smsCode = smsCode;
      postData.companyName = companyName;
      if (phoneBindCustomers.length === 1) {
        postData.companyName = phoneBindCustomers[0]?.value;
      }
      const [err1, res1] = await findAuthPwd(postData)
        .then(res => [null, res])
        .catch(err => [err, null]);
      err = err1;
      res = res1;
    } else if (currentTab === "emailTab") {
      postData.email = email;
      postData.verifyCode = verifyCode;
      postData.companyName = companyName;
      if (phoneBindCustomers.length === 1) {
        postData.companyName = phoneBindCustomers[0]?.value;
      }
      const [err1, res1] = await findAuthPasswordByEmail(postData)
        .then(res => [null, res])
        .catch(err => [err, null]);
      err = err1;
      res = res1;
    } else {
      return notification.error({ message: "非法请求类型，请重试！" });
    }
    if (err) {
      useLoadingHook.hideLoading();
      notification.error({
        message: err?.data?.message || "网络异常，重置失败，请重试！",
      });
      return;
    }
    // 重置成功，返回登录页面
    await message.success(res.message || "密码重置成功，返回登录页");
    window.location.href = "/auth/login";
  };
  const handleChangeTab = (tab: string) => {
    setPhoneBindCustomers([]);
    setCurrentTab(tab);
    form?.resetFields();
  };

  // 检查手机号/邮箱绑定多客户情况
  const checkedPhoneBindCustomer = async (e, filed) => {
    form
      .validateFields([filed])
      .then(res => {
        if (res[filed]) {
          form.setFieldValue("companyName", null);
          getAccountInfoByPhone(res[filed]).then(cb => {
            setFlag(cb?.status === 200);
            const _data = cb.data?.data?.map(item => {
              return { label: item.companyName, value: item.companyName };
            });
            setPhoneBindCustomers(_data);
          });
        }
      })
      .catch(err => {
        setFlag(false);
        setPhoneBindCustomers([]);
        console.log("err", err);
      });
  };

  const renderFormCnpt = (type: string) => {
    const dynamicsCnpt = {
      phoneTab: (
        <>
          <Form.Item className="phone" name="phone" label="手机号码" rules={passwordResetRules.phone}>
            <Input
              prefix={<MobileOutlined />}
              placeholder="请输入手机号"
              autoComplete="off"
              onChange={e => {
                checkedPhoneBindCustomer(e, "phone");
              }}
            />
          </Form.Item>
          {phoneBindCustomers.length > 1 && (
            <Form.Item className="form-select" name="companyName" label="公司" rules={passwordResetRules.companyName}>
              <Select className="form-select-control" options={phoneBindCustomers} placeholder="请选择公司" />
            </Form.Item>
          )}
          {/* ========== 短信验证码组件 ========== */}
          <SmsCaptcha uuid={"resetPassword"} smsSendType={SmsSendTypeEnum.RESET} />
        </>
      ),
      emailTab: (
        <>
          <Form.Item className="email" name="email" label="邮箱地址" rules={passwordResetRules.email}>
            <Input
              prefix={<MailOutlined />}
              placeholder="请输入邮箱地址"
              autoComplete="off"
              onChange={e => {
                checkedPhoneBindCustomer(e, "email");
              }}
            />
          </Form.Item>
          {phoneBindCustomers.length > 1 && (
            <Form.Item className="form-select" name="companyName" label="公司" rules={passwordResetRules.companyName}>
              <Select className="form-select-control" options={phoneBindCustomers} placeholder="请选择公司" />
            </Form.Item>
          )}
          {/* ========== 邮件验证码发送组件 ========== */}
          <EmailSendCodeCnpt uuid={"forgetPasswordEmail"} sendType={EmailSendTypeEnum.FIND_PASSWORD} countdownSeconds={60} />
        </>
      ),
    };
    return (
      <Form ref={formRef} className="reset-form" form={form} size="large" onFinish={handleResetSubmitEvent} labelAlign="right">
        {dynamicsCnpt[type]}
        {/* ============ 密码组件 ============ */}
        <PasswordItem prefixCls={"ghmall"} passwordLabel={"新密码"} radius={2} />
        {/* 提交按钮 */}
        <Form.Item className="form-submit">
          <Button htmlType="submit" className="form-submit-btn" disabled={!flag}>
            提交修改
          </Button>
        </Form.Item>
      </Form>
    );
  };
  const renderTabItems = [
    {
      label: "手机重置",
      key: "phoneTab",
      children: currentTab === "phoneTab" ? renderFormCnpt("phoneTab") : null,
    },
    {
      label: "邮箱重置",
      key: "emailTab",
      children: currentTab === "emailTab" ? renderFormCnpt("emailTab") : null,
    },
  ];
  /** ======================================== method end ======================================= */

  return (
    <SimpleLayout title="忘记密码">
      <div className={style.wrapper}>
        <div className="password-reset">
          <h3 className="reset-title">密码重置</h3>
          <Tabs items={renderTabItems} className="forget-password-tabs" onChange={handleChangeTab} />
        </div>
      </div>
    </SimpleLayout>
  );
}
