import { Provide } from "@midwayjs/decorator";
import { PREFIX_OSS_RESOURCE_PATHS } from "@/common/constants/oss_filepath.constant";
import { httpError } from "@midwayjs/core";

@Provide()
export class CommonUtil {
  /** 搜索日期转换-查询时间添加时分秒 */
  static convertSearchCreatDate(params: object) {
    if (params["createdDate"]) {
      let [startTime, endTime] = params["createdDate"].split("~");
      startTime = startTime + " 00:00:00";
      endTime = endTime + " 23:59:59";
      params["createdDate"] = startTime + "~" + endTime;
    }
    return params;
  }

  /**
   * 判断资源路径类型是否合法
   * @param type
   * @returns boolean true: 合法的, false: 不合法,不存在的路径配置
   */
  static checkFilePathTypeIsLegal(type: number): boolean {
    const legalTypes: number[] = Object.keys(PREFIX_OSS_RESOURCE_PATHS).map(item => {
      return PREFIX_OSS_RESOURCE_PATHS[item].key;
    });
    return legalTypes.includes(type);
  }

  /**
   * 根据合法资源类型获取对应的资源配置
   * @param type
   * @returns 返回 object | undefine
   */
  static getFilePathByIsLegalType(type: number): { name: string; key: number; value: string; isPrivate: boolean } | null {
    return Object.values(PREFIX_OSS_RESOURCE_PATHS).find(item => item.key === type);
  }

  /** 快速校验私有图片地址是否合规合法 */
  static quickValidPrefixImageIsIsLegal(filepath: string, type: number): boolean {
    if (!filepath) {
      throw new httpError.NotFoundError("无资源");
    }
    if (!CommonUtil.checkFilePathTypeIsLegal(type)) {
      throw new httpError.BadRequestError({ message: "资源参数不合法,资源加载失败！" });
    }
    if (!/(jpeg|png|jpg|gif|bmp)$/.test(filepath)) {
      throw new httpError.BadRequestError({ message: "资源加载错误！" });
    }
    return true;
  }
}
