import React, { useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import { IReceiptType } from "@/typings/member.interface";
import { Button, Descriptions, Empty, Tag, Radio } from "antd";
import style from "./index.module.less";
import { $tools } from "@/utils/tools.util";
import ReceiptEditModal from "@/components/ReceiptEditModal";
import { useMemberReceipt } from "@/hooks/useMemberReceipt";

export default function MemberReceiptIndex(props: SProps) {
  const tabs = [
    {
      key: "normal",
      tab: "普通发票",
    },
    {
      key: "special",
      tab: "13%增值税专用发票",
    },
  ];
  const { ucardRef, actTabKey, defaultTab, setActTabKey, currentReceipt, initReceiptData, resetLoadReceiptData, handleSetDefaultReceipt } = useMemberReceipt();
  // ----------------------------------------
  const [mode, setMode] = useState<"add" | "edit">("add");
  const [currentRow, setCurrentRow] = useState<IReceiptType>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  // ----------------------------------------

  useEffect(() => {
    initReceiptData();
  }, []);

  /** 激活编辑弹窗 */
  const handleOpenReceiptPanel = (mode: string) => {
    // 编辑或新增
    if (!currentReceipt?.id) {
      // 新增模式
      setMode("add");
      setModalVisible(true);
    } else {
      // 编辑模式
      setMode("edit");
      setCurrentRow(currentReceipt);
      setModalVisible(true);
    }
  };

  /** 面板保存的回调 */
  const handleReceiptCallBack = async () => {
    await resetLoadReceiptData();
  };

  /** 渲染发票信息 */
  const renderDescriptions = () => {
    if (!currentReceipt) {
      return (
        <div className="receipt-container-empty">
          <Empty />
          <Button onClick={() => handleOpenReceiptPanel("add")} type="primary">
            新增开票信息
          </Button>
        </div>
      );
    }

    return (
      <div className="receipt-container">
        <Descriptions size={"small"} column={1} labelStyle={{ width: "120px", color: "#606060" }}>
          {currentReceipt?.vatName ? <Descriptions.Item label="发票抬头">{currentReceipt?.vatName}</Descriptions.Item> : null}
          {currentReceipt?.vatName ? <Descriptions.Item label="社会代码/税号">{currentReceipt?.vatId}</Descriptions.Item> : null}
          {currentReceipt?.email ? <Descriptions.Item label="接收电子邮箱">{currentReceipt?.email}</Descriptions.Item> : null}
          {currentReceipt?.receiptPeople ? <Descriptions.Item label="收票人姓名">{currentReceipt?.receiptPeople}</Descriptions.Item> : null}
          {currentReceipt?.receiptPeoplePhone ? <Descriptions.Item label="收票人手机">{currentReceipt?.receiptPeoplePhone}</Descriptions.Item> : null}
          <Descriptions.Item label="发票邮寄地址">{$tools.getAddressByReceiptData(currentReceipt)}</Descriptions.Item>
          {/* 增值税发票条目 */}
          {actTabKey !== "normal" ? (
            <>
              {currentReceipt.receiptBank ? <Descriptions.Item label="开户银行名称">{currentReceipt?.receiptBank}</Descriptions.Item> : null}
              {currentReceipt.receiptBankAccount ? <Descriptions.Item label="开户银行账号">{currentReceipt?.receiptBankAccount}</Descriptions.Item> : null}
              {currentReceipt.receiptPeoplePhone ? <Descriptions.Item label="注册电话">{currentReceipt?.receiptRegisterPhone}</Descriptions.Item> : null}
              {currentReceipt.receiptRegisterAddress ? <Descriptions.Item label="税务注册地址">{currentReceipt?.receiptRegisterAddress}</Descriptions.Item> : null}
            </>
          ) : null}
          <Descriptions.Item label={currentReceipt?.isDefault ? "是否默认" : "设为默认开票"}>
            {currentReceipt?.isDefault !== 1 ? (
              <Radio.Group value={currentReceipt?.isDefault} className="tm-radios" buttonStyle="solid" onChange={e => handleSetDefaultReceipt(e)}>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            ) : (
              <Tag color="success">是</Tag>
            )}
          </Descriptions.Item>
        </Descriptions>
        <Button type="primary" className="btn-edit" onClick={() => handleOpenReceiptPanel("edit")}>
          编辑发票信息
        </Button>
      </div>
    );
  };

  return (
    <div className={style.wrapper}>
      <UCenterCard setActTabKey={setActTabKey} ref={ucardRef} title={"开票信息"} tabs={tabs} actDefaultTab={defaultTab} />
      {renderDescriptions()}
      {/* 编辑表单 */}
      <ReceiptEditModal
        receiptType={actTabKey === "normal" ? 1 : 0}
        callbackFunc={handleReceiptCallBack}
        modalVisible={modalVisible}
        changeModalVisible={setModalVisible}
        mode={mode}
        receiptId={currentRow?.id}
      />
    </div>
  );
}
