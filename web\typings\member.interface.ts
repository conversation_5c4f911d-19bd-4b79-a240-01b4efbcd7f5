/** 客户负责人类型 */
export interface ICustomerPrincipalParams {
  userId: number;
  nickname: string;
  username: string;
  phone?: string;
  avatarPath?: string;
  employeeId?: string;
  sex?: number;
}

interface IAddressLocation {
  id: number;
  initial: string;
  name: string;
}

/** 发票数据类型 */
export interface IReceiptJSONType {
  id?: number;
  vat_id?: string;
  receipt_id?: number;
  receipt_bank?: string;
  receipt_type?: number;
  receipt_people?: string;
  receipt_address?: string;
  receipt_bank_account?: string;
  receipt_people_phone?: string;
  receipt_register_phone?: string;
  receipt_people_telephone?: string;
  receipt_register_address?: string;
}

/** 收货地址-数据类型 */
export interface IReceivingAddressType {
  fax: string;
  email: string;
  phone: string;
  address: string;
  nickname: string;
  zipCode: string;
  telephone: string;
  companyName: string;
  cityId: number;
  citiInfo: IAddressLocation | undefined;
  districtId: number;
  districtInfo: IAddressLocation | undefined;
  provinceId: number;
  provinceInfo: IAddressLocation | undefined;
  isDefault: number;
  // 别名+标签
  aliasName: string;
  label: string;
  id: number;
  area?: number[] | string[];
}

/** 发票-数据类型 */
export interface IReceiptType {
  isDefault: number;
  receiptAddress: string;
  receiptBank: string;
  receiptBankAccount: string;
  receiptCityId: number;
  receiptDistrictId: number;
  receiptPeople: string;
  receiptPeoplePhone: string;
  receiptPeopleTelephone: string;
  receiptProvinceId: number;
  receiptRegisterAddress: string;
  receiptRegisterPhone: string;
  receiptType: number;
  vatId: string;
  vatName: string;
  id: number;
  email?: string;
}

/** 发票信息 */
export interface IReceiptUnderlineType {
  vat_id: string;
  vat_name: string;
  receipt_type_name: string;
  receipt_bank: string;
  receipt_type: number;
  receipt_people: string;
  receipt_address: string;
  receipt_bank_account: string;
  receipt_people_phone: string;
  receipt_register_phone: string;
  receipt_people_telephone: string;
  receipt_register_address: string;
}

/** 客户个人信息类型 */
export interface IMemberInformationType {
  avatar: string;
  birthday: string;
  companyName: string;
  customerCategoryCode: string;
  customerSource: number;
  email: string;
  gender: number;
  lastLoginDate: string;
  lastLoginIp: string;
  memberId: string;
  nickname: string;
  phone: string;
  postalCode: string;
  qq: string;
  receiptType: number;
  telephone: string;
  wechat: string;
  id: number;
  contactAddress: string;
  contactCityId: number;
  contactDistrictId: number;
  contactProvinceId: number;
  contactPeopleJob: string;
}

export interface IMemberQualificationType {
  id: number;
  memberId: string;
  productType: number;
  customerIdentity: number;
  customerPosition: number;
  chemicalsCertificate: string;
  businessLicense: string;
  idcardFront: string;
  idcardBack: string;
  employmentZm: string;
  hazardousLicense: string;
  purchaseCertificate: string;
  salesContract: string;
  letterOfAttorney: string;
  noDrugCertificate: string;
  safetyProductionLicense: string;
  field1: string;
  field2: string;
  field3: string;
  selectedFields: string;
  expiredDate: string;
  auditState: number;
  auditor: string;
  auditDate: string;
  auditorReason: string;
  remark: string;
  isExpire: boolean;
  createdDate: string;
}

/** 客户分类 */
export interface ICustomerCategoryType {
  label: string;
  value: string;
  code: string;
}

/** 推荐业务员信息 */
export interface ISalesmanSpreadInfoType {
  userid: number;
  nickname: string;
  spreadCode: string;
  spreadAvatar: string;
  spreadPhone: string;
  spreadDescription: string;
  isPromoter: number;
}
