/** 产品危险性类型 */
import { GH_COMPANY_ADDRESS, GH_COMPANY_BANDS } from "@/constants/gh-company";

export const DANGER_OPTIONS = {
  DANGER: {
    title: "危险化学品",
    value: "DANGER",
  },
  EXPLODE: {
    title: "易制爆",
    value: "EXPLODE",
  },
  POISON: {
    title: "易制毒",
    value: "POISON",
  },
  EXPLOSION: {
    title: "爆炸品",
    value: "EXPLOSION",
  },
};

/**
 *
 * @param show 显示标记，0 不显示，1显示
 * @param type 危险品类型
 */
export function getProductDangerText(type: string | boolean) {
  if (!type) {
    return "";
  }
  return DANGER_OPTIONS[String(type)]?.title || "";
}
