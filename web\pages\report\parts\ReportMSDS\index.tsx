import { Button, Empty, Input, message, Spin } from "antd";
import React, { useState } from "react";
import style from "./index.module.less";
import crudGhtech from "@/apis/ghtech";
import { DownloadOutlined, FileSearchOutlined } from "@ant-design/icons";
import { useOss } from "@/hooks/useOss";
import { downloadFromOssFilepath } from "@/utils/download.util";

export default function ReportMSDS() {
  const [loadingState, setLoadingState] = useState(false);
  const [msdsData, setMsdsData] = useState<any[]>([]);
  const useOssHook = useOss();
  const [emptyText, setEmptyText] = useState("暂无数据，请搜索产品");

  /** 搜索产品msds */
  const handleSearchProductMsds = async (word: string) => {
    const productName = word.trim();
    if (!productName) {
      message.warn("请输入产品名称");
      return;
    }

    setLoadingState(true);
    const [err, res] = await crudGhtech
      .getMSDSByProductName(productName)
      .then(res => [null, res])
      .catch(err => [err, null]);
    setLoadingState(false);
    if (err) {
      message.warn(err?.data?.message || "网络异常，请重试~");
      return;
    }
    !res.data.length && setEmptyText("未找不到资源，请联系客服！");
    setMsdsData(res.data);
  };

  const setProductMsdsPreviewPath = (path: string) => {
    return useOssHook.generateOssFullFilepath(path);
  };

  return (
    <div className={style.wrapper}>
      <div className="msds">
        <div className="search-bar">
          <Input.Search
            placeholder="请输入产品名称"
            enterButton={
              <span>
                <FileSearchOutlined />
                &nbsp;查询MSDS
              </span>
            }
            size="large"
            onSearch={handleSearchProductMsds}
          />
          <div className="description">
            MSDS（Material Safety Data Sheet）和SDS（Safety Data Sheet）仅仅是不同标准中对安全技术说明书的两种不同缩写，在供应链上所起的作用完全一致。MSDS所包含的范围更大，2008年重新修订的标准GB/T
            16483-2008 <a href="http://www.gdsafety.org.cn/data/upload/ueditor/20150912/55f3e42452003.pdf">《化学品安全技术说明书 内容和项目顺序》</a>
            中，与国际标准化组织进行了统一，缩写为SDS。SDS、CSDS、MSDS三个简称都是对于《化学品安全技术说明书》的不同表述。国内企业惯称为
            MSDS，旨为化学物质及其制品提供有关安全、健康和环境保护方面的各种信息。
          </div>
        </div>
        <Spin spinning={loadingState}>
          <div className="search-result">
            {msdsData?.length ? (
              <>
                <div className="result-tip">MSDS化学品安全说明书:</div>
                <ul>
                  {msdsData.map((item, index) => (
                    <li key={index}>
                      <span>华大 - {item.productName}</span>
                      <a onClick={() => downloadFromOssFilepath(setProductMsdsPreviewPath(item.filepath), `华大-${item.productName}-msds.pdf`)}>
                        <Button icon={<DownloadOutlined />} size={"small"} type="primary" danger>
                          下载
                        </Button>
                      </a>
                    </li>
                  ))}
                </ul>
              </>
            ) : (
              <Empty description={emptyText} />
            )}
          </div>
        </Spin>
      </div>
    </div>
  );
}
