/** 充值订单类型 */
export interface IRechargeType {
  id: string;
  memberId: string;
  memberName: string;
  rechargeMoney: number;
  payStatus: string;
  rechargeSn: string;
  createdDate: string;
  payTime: string;
  receivableNo: string;
  rechargeWay: string;
}

/** 流水单类型 */
export interface IBillFlowType {
  id: string;
  memberId: string;
  memberName: string;
  money: number;
  balance: number;
  serviceType: string;
  detail: string;
  createdDate: string;
}
