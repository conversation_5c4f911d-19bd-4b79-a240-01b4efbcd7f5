import React from "react";
import { onlyCsr } from "ssr-hoc-react";
import type { RcFile, UploadFile, UploadProps } from "antd/es/upload/interface";
import { Button, message, notification, Upload } from "antd";
import "antd/es/modal/style";
import "antd/es/slider/style";
import { UploadChangeParam } from "antd/lib/upload";
import { useLoading } from "@/hooks/useLoading";
import { CookieUtil } from "@/utils/cookie.util";

interface IImageProp {
  title?: React.ReactNode;
}

const AvatarImageCrop = (props: Partial<IImageProp>) => {
  const ImgCrop: any = require("antd-img-crop")?.default;
  const useLoadingHook = useLoading();
  if (ImgCrop === undefined) {
    return <>浏览器不支持</>;
  }
  const limitSize = 4; // 上传文件大小限制: 4m
  const imageTypeArr = ["image/jpeg", "image/gif", "image/png", "image/bmp"];

  // 头像上传
  const beforeUploadAvatarEvent = (file: RcFile) => {
    return validImageTypeAndSize(file);
  };

  // 上传之后处理
  const handleChangeAvatarEvent: UploadProps["onChange"] = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === "uploading") {
      useLoadingHook.showLoading("头像上传中...", "ucenterApp");
      return;
    }
    if (info.file.status === "done") {
      useLoadingHook.hideLoading("ucenterApp");
      document.getElementById("tmLoading")?.remove();
      notification.success({ message: "修改成功！" });
      history.go(0);
    }
    if (info.file.status === "error") {
      useLoadingHook.hideLoading("ucenterApp");
      document.getElementById("tmLoading")?.remove();
      notification.error({ message: info.file.response.message || "修改失败，请稍后重试！" });
    }
  };

  // 上传校验
  const validImageTypeAndSize = (file: RcFile) => {
    const isFormatCheckedImg = !imageTypeArr.includes(file.type);
    if (isFormatCheckedImg) {
      message.error("只允许上传png|jpeg|gif格式图片！");
    }
    const isOutLimitSize = file.size < limitSize * 1024 * 1024;
    if (!isOutLimitSize) {
      message.error(`图片大小不能超过${limitSize}mb！`);
    }
    return !isFormatCheckedImg && isOutLimitSize;
  };

  // 裁剪前逻辑处理
  const beforeImageCropEvent = (file: RcFile, fileList: RcFile[]) => {
    return validImageTypeAndSize(file);
  };

  return (
    <>
      <ImgCrop grid rotate modalTitle="图片裁剪" modalOk={"确定"} modalCancel={"取消"} beforeCrop={beforeImageCropEvent}>
        <Upload
          name="file"
          action={"/api/ucenter/update-avatar"}
          headers={{ "x-csrf-token": CookieUtil.getInstance().get("csrfToken") }}
          showUploadList={false}
          beforeUpload={beforeUploadAvatarEvent}
          onChange={handleChangeAvatarEvent}
        >
          <Button size="small">更换头像</Button>
        </Upload>
      </ImgCrop>
    </>
  );
};
export default onlyCsr(AvatarImageCrop) as any;
