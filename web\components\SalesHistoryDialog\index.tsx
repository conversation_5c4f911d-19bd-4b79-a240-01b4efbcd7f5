import React, { useState, useEffect } from "react";
import { Modal, Table, message, Pagination } from "antd";
import * as orderCrud from "@/apis/order/order";
import { ArrowDownOutlined, ArrowUpOutlined } from "@ant-design/icons";
import "./index.less";

interface SalesHistoryDialogProps {
  visible: boolean;
  currentProduct: any;
  onClose: () => void;
}

interface SalesRecord {
  id: number;
  productPrice: number;
  productQuantity: number;
  totalPrice: number;
  orderNo: string;
  orderCreatedDate: string;
}

const SalesHistoryDialog: React.FC<SalesHistoryDialogProps> = ({ visible, currentProduct, onClose }) => {
  const [tableData, setTableData] = useState<SalesRecord[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState({
    current: 0,
    pageSize: 50,
    total: 0,
  });

  const fetchData = async () => {
    if (!visible) return;

    setLoading(true);
    try {
      const [err, res] = await orderCrud
        .getProductSalesHistory({
          productSku: currentProduct?.sku,
          page: pagination.current,
          size: pagination.pageSize,
        })
        .then(res => [null, res])
        .catch(err => [err, null]);
      console.log("res", res);
      setTableData(res?.data?.content || []);
      setPagination({
        ...pagination,
        total: res?.data?.total || 0,
      });
      if (err) {
        console.error("获取历史售卖数据失败", err);
        message.error("获取历史售卖数据失败");
      }
    } catch (error) {
      console.error("获取历史售卖数据失败", error);
      message.error("获取历史售卖数据失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchData();
    }
  }, [visible, pagination.current, pagination.pageSize]);

  const handlePageChange = (page: number, pageSize?: number) => {
    setPagination({
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize,
    });
  };

  const handleSizeChange = (current: number, size: number) => {
    setPagination({
      ...pagination,
      current: 1,
      pageSize: size,
    });
  };

  // 判断价格是否变化（低于当前价格）
  const isPriceDown = (productPrice: number) => {
    return Number(productPrice) < Number(currentProduct?.discountPrice);
  };

  const isPriceUp = (productPrice: number) => {
    return Number(productPrice) > Number(currentProduct?.discountPrice);
  };

  const columns = [
    {
      title: "序号",
      dataIndex: "index",
      width: 70,
      align: "center" as const,
      render: (text, record, index) => `${Number(index) + 1}`,
    },
    {
      title: "售卖价格",
      dataIndex: "productPrice",
      width: 120,
      sorter: (a: SalesRecord, b: SalesRecord) => a.productPrice - b.productPrice,
      render: (productPrice: number) => (
        <span style={{ color: isPriceDown(productPrice) ? "green" : isPriceUp(productPrice) ? "red" : "" }}>
          {productPrice}
          {isPriceDown(productPrice) && <ArrowDownOutlined style={{ color: "green", marginLeft: 4 }} />}
          {isPriceUp(productPrice) && <ArrowUpOutlined style={{ color: "red", marginLeft: 4 }} />}
        </span>
      ),
    },
    {
      title: "售卖数量",
      dataIndex: "productQuantity",
      width: 120,
      sorter: (a: SalesRecord, b: SalesRecord) => a.productQuantity - b.productQuantity,
    },
    {
      title: "小计",
      dataIndex: "totalPrice",
      width: 120,
    },
    {
      title: "单号",
      dataIndex: "orderNo",
      render: (orderNo: string) => (
        <a href={"/ucenter/trade/order/" + orderNo} className="order-link">
          {orderNo}
        </a>
      ),
    },
    {
      title: "下单时间",
      dataIndex: "orderCreatedDate",
      width: 180,
    },
  ];

  return (
    <Modal title="产品历史售卖" open={visible} onCancel={onClose} width="880px" footer={null} destroyOnClose>
      <div className="sales-history-container">
        <div className="price-info">当前订单售价: ¥ {currentProduct?.discountPrice}</div>

        <Table dataSource={tableData} columns={columns} rowKey="id" pagination={false} loading={loading} bordered size="middle" />

        <div className="pagination-container">
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={total => `共 ${total} 条`}
            onChange={handlePageChange}
            onShowSizeChange={handleSizeChange}
            pageSizeOptions={["10", "20", "50", "100"]}
          />
        </div>
      </div>
    </Modal>
  );
};

export default SalesHistoryDialog;
