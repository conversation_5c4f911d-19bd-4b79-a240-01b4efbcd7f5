/* eslint-disable */
const en_US = {
  language: "English",
  start: "start",
  switch: "switch",
  say: "{name} say:hello!",
  test: "Test | Test | Test | Test",
  "mall.success": "success success！",
  "lang.changeSuccess": "lang switch success",
  "lang.changeFail": "lang switch fail",
  searchText: "search",
  /*======== 配置密码组件国际化-start ========*/
  "password.lv1": "Weak",
  "password.lv2": "General",
  "password.lv3": "Good",
  "password.lv4": "Great",
  "password.tip": "Must contain two or more combinations of letters, numbers and special characters.",
  "password.placeholder": "Please enter password",
  "password.repeat": "Please enter the password again",
  "password.strong": "password strength",
  "password.size": "{min}-{max} characters, case sensitive, no spaces before or after.",
  "password.format": "Letters, numbers, English and special characters.",
  "password.different": "Different passwords",
  "password.setting": "Please set your password",
  "password.least": "At least {min} characters",
  /*======== 配置密码组件国际化-end ========*/
};
export default en_US;
