import { Controller, Get, Inject, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { IAgreementPriceService } from "@/service/agreement-price/agreement-price.service";

@Controller("/api/agreement-price", { middleware: [AuthenticationMiddleware] })
export class AgreementPriceController extends BaseController {
  @Inject("AgreementPriceService")
  agreementPriceService: IAgreementPriceService;

  /**
   * 动态获取会员产品区间折扣
   */
  @Get("/member-volume-discount")
  async getMemberVolumeDiscount(
    @Query("orderQuantity") orderQuantity: number,
    @Query("brandId") brandId?: string,
    @Query("productSkuId") productSkuId?: number
  ) {
    const { ctx } = this;
    
    // 从 session 中获取会员ID
    const memberId = this.getMemberId();
    
    if (!memberId) {
      return ctx.getResponseInstance(ctx).sendFail("未获取到会员信息", 400);
    }

    if (!orderQuantity || orderQuantity <= 0) {
      return ctx.getResponseInstance(ctx).sendFail("订购数量必须大于0", 400);
    }

    const params = {
      orderQuantity: Number(orderQuantity),
      brandId,
      memberId,
      productSkuId: productSkuId ? Number(productSkuId) : undefined,
    };

    const result = await this.agreementPriceService.getMemberVolumeDiscount(params);
    
    return ctx.getResponseInstance(ctx).setResponseData(result).send();
  }
}
