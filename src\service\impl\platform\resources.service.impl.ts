import { Inject, Provide, Scope, <PERSON>ope<PERSON>num } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IReSourcesService } from "@/service/platform/resources.service";
import { IResourceUploadParamDto } from "~/typings/data/resources";
import { HttpService } from "@midwayjs/axios";

@Provide("ResourcesService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class ResourcesServiceImpl extends BaseService implements IReSourcesService {
  @Inject()
  httpService: HttpService;

  /** 私有图片-上传 */
  async uploadPrivateImage(uploadData: IResourceUploadParamDto): Promise<void> {
    return this.easyResponse(await this.easyHttp.post(`/api/platform/sources/upload/pri-image`, uploadData, { "Content-Type": "multipart/form-data" }));
  }

  /** 私有文件-上传 */
  async uploadPrivateFile(uploadData: IResourceUploadParamDto): Promise<void> {
    return this.easyResponse(await this.easyHttp.post("/api/platform/sources/upload/pri-file", uploadData));
  }

  /** 私有图片-下载 */
  async getPrivateImage(filepath: string): Promise<any> {
    return await this.httpService.request({
      url: "/api/platform/sources/upload/pri-image",
      method: "get",
      responseType: "stream",
      params: { filepath },
    });
  }

  /** 私有文件-下载 */
  async getPrivateFile(filepath: string): Promise<any> {
    return await this.httpService.request({
      url: "/api/platform/sources/upload/download-pri-file",
      method: "get",
      responseType: "stream",
      params: { filepath },
    });
  }
}
