import React, { memo, useContext, useEffect, useState } from "react";
import { ISkus, SearchProductType } from "@/typings/search.interface";
import { Descriptions, Table, TableColumnsType, Tooltip } from "antd";
import style from "./index.module.less";
import { useSSRQueryRequest } from "@/hooks/useSSRQueryRequest";
import MyPagination from "@/components/MyPagination";
import { IContext, SProps } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import SearchFilterBoxPanel from "@/pages/search/parts/SearchFilterBoxPanel";
import Add2ShopCart from "@/pages/search/parts/Add2ShopCart";
import { ArrowDownOutlined, ArrowUpOutlined, MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { ghmallDiscountPrice2Show, ghmallGuidePrice2Show, price2Thousand } from "@/utils/price-format.util";
import { useLoginModal } from "@/hooks/useLoginModal";
import { getProductDangerText } from "@/constants/product";
import { useOss } from "@/hooks/useOss";
import { useAgreementPrice } from "@/hooks/useAgreementPrice";
import commonConstant from "@/constants/common";

/**
 * 1、处理针对特定产品-展示特惠标识，先判断是否询价产品
 * 2、dangerType===“管制类” 没有折扣价
 * 3、优惠折扣计算规则：先判断是否有指定价格，再判断品牌折扣
 */
export default memo(function Search(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const ossService = useOss();
  const agreementPriceHook = useAgreementPrice();
  // 实例hook
  const useSSRQueryRequestHook = useSSRQueryRequest({
    queryParams: {},
    pageSize: 10,
    showSizeChanger: false,
    showTotalText: false,
  });
  const renderProductName = (text: string, record: any) => {
    const tmp = text.replace(searchFilterParams?.keyword, '<samp style="color: #eca4a4">' + searchFilterParams?.keyword + "</samp>");
    return <a className="product-detail-link" dangerouslySetInnerHTML={{ __html: tmp }} href={`/product/${record.productNo}`} target="_blank" />;
  };
  const renderDangerText = product => {
    const { brandId, isDanger, isExplode, isExplosion, isPoison } = product;
    const hasDangerTag = isExplode || isExplosion || isPoison;
    if (isDanger && hasDangerTag) {
      return `危险化学品（${getProductDangerText(isExplode && "EXPLODE")} ${getProductDangerText(isPoison && "POISON")} ${getProductDangerText(isExplosion && "EXPLOSION")}）`;
    } else if (!isDanger && hasDangerTag) {
      return `非危险化学品（${getProductDangerText(isExplode && "EXPLODE")} ${getProductDangerText(isPoison && "POISON")} ${getProductDangerText(isExplosion && "EXPLOSION")}）`;
    } else if (isDanger && !hasDangerTag) {
      return `危险化学品`;
    } else {
      return ownerBrand.includes(brandId) ? `非危险化学品（普通货物）` : `暂无`;
    }
  };
  // 渲染的产品列表首页数据-每页10条
  const tableColumns = [
    {
      title: "品牌",
      align: "center",
      dataIndex: "brandName",
      fixed: "left",
    },
    {
      title: "原厂货号",
      align: "center",
      dataIndex: "itemNo",
      fixed: "left",
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      width: 200,
      align: "center",
      fixed: "left",
      render: (text, record) => renderProductName(text, record),
    },
    {
      title: "英文名称",
      dataIndex: "productNameEn",
      align: "center",
      render: productNameEn => (
        <>
          <Tooltip placement="topLeft" title={productNameEn}>
            <span>{productNameEn || "-"}</span>
          </Tooltip>
        </>
      ),
      fixed: "left",
      ellipsis: true,
    },
    {
      title: "产品描述+危险性",
      dataIndex: "description",
      ellipsis: true,
      fixed: "left",
      width: 180,
      align: "center",
      render: (description, record) => (
        <Tooltip
          placement="topLeft"
          title={
            <>
              <div>{description}</div>
              <div>{renderDangerText(record)}</div>
            </>
          }
        >
          <>
            <div>{description}</div>
            <div>{renderDangerText(record)}</div>
          </>
        </Tooltip>
      ),
    },
    {
      title: "指导价区间￥",
      align: "center",
      fixed: "left",
      width: 200,
      dataIndex: "guidePriceRange",
      render: guidePriceRange => <span>{guidePriceRange}</span>,
    },
    {
      title: "优惠价￥",
      align: "center",
      fixed: "left",
      width: 200,
      dataIndex: "discountPriceRange",
      render: discountPriceRange =>
        discountPriceRange !== "登录可见" ? (
          <span className="tagging">{discountPriceRange}</span>
        ) : (
          <button className="need-login-btn" onClick={handleToLogin}>
            登录可见
          </button>
        ),
    },
    Table.EXPAND_COLUMN,
  ];
  const searchFilterParams: any = useSSRQueryRequestHook.getUrlParams();
  /* ======================================= useSate start======================================= */
  // 价格排序
  const [sortPriceIndex, setSortPriceIndex] = useState<string>("desc");
  const [sortIndex, setSortIndex] = useState<string>("default");
  const [tableData, setTableData] = useState<SearchProductType[]>([]);
  // 最匹配-用于展示产品
  const [bestMatchSearch, setBestMatchSearch] = useState<SearchProductType | null>();
  // 展开行
  const [tableExpandedRowKeys, setTableExpandedRowKeys] = useState<number[]>([]);
  const [collapseFlag, setCollapseFlag] = useState<Boolean>(false);
  const [ownerBrand, setOwnerBrand] = useState<string[]>([]);
  // 存储更新后的SKU价格信息
  const [updatedSkuPrices, setUpdatedSkuPrices] = useState<{[key: string]: number}>({});
  // 登录弹窗hook
  const useLoginModalHooks = useLoginModal();
  /* ======================================= useSate end======================================= */

  /* ======================================= 方法 start======================================= */
  const getProductDangerTip = (record: any) => {
    return `${getProductDangerText(record?.isDanger && "DANGER")} ${getProductDangerText(record?.isExplode && "EXPLODE")} ${getProductDangerText(record.isPoison && "POISON")}`.trim();
  };
  const handleToLogin = () => {
    useLoginModalHooks.open();
  };
  const openOrCloseEvent = (expanded?: boolean, record?: any) => {
    let rowKeys: number[] = [];
    if (record) {
      rowKeys = tableExpandedRowKeys;
      if (expanded) {
        rowKeys.push(record.id);
      } else {
        rowKeys.splice(
          rowKeys.findIndex(e => e === record.id),
          1
        );
      }
      setTableExpandedRowKeys(rowKeys);
    } else {
      setCollapseFlag(!collapseFlag);
      !collapseFlag && (rowKeys = tableData.map(item => item.id));
      setTableExpandedRowKeys(rowKeys);
    }
  };
  // 触发新的查询操作
  const handleNewSearchEvent = async (params: object) => {
    await useSSRQueryRequestHook.searchAsyncEvent(params);
  };
  const onMonitorSelectedParams = async searchParams => {
    const newSearchParams: any = {};
    searchParams.forEach(param => param.key === "brands" && (newSearchParams.brandId = param.value));
    await handleNewSearchEvent(newSearchParams);
  };
  // 排序方法处理
  const orderByEvent = async (sort: string, order = "desc") => {
    let orderBy = "";
    if (sort === "guide_price") {
      order = order === "desc" ? "asc" : "desc";
      setSortPriceIndex(order);
      orderBy = `${sort},${order}`;
    }
    setSortIndex(sort);
    await handleNewSearchEvent({ orderBy });
  };
  const fetchTableData = () => {
    setTableExpandedRowKeys([]);
    setCollapseFlag(false);
    const tableData1 = useSSRQueryRequestHook.getTableData.content.map(item => {
      const maxPrice = Math.max.apply(
        null,
        item.skus.map(item => item.guidePrice)
      );
      const minPrice = Math.min.apply(
        null,
        item.skus.map(item => item.guidePrice)
      );
      item.guidePriceRange = maxPrice === minPrice ? ghmallGuidePrice2Show(minPrice) : `${ghmallGuidePrice2Show(minPrice)} - ${ghmallGuidePrice2Show(maxPrice)}`;
      if (!state.userLoginState) {
        item.discountPriceRange = "登录可见";
      } else {
        const aptDiscountRange = item.skus.map(item => item.aptDiscount);
        const discountMaxPrice = ghmallDiscountPrice2Show(maxPrice, Math.max.apply(null, aptDiscountRange));
        const discountMinPrice = ghmallDiscountPrice2Show(minPrice, Math.min.apply(null, aptDiscountRange));
        item.discountPriceRange = discountMaxPrice === discountMinPrice ? discountMinPrice : `${discountMinPrice} - ${discountMaxPrice}`;
      }
      return item;
    });

    if (tableData1.length === 1) {
      setBestMatchSearch(tableData1[0]);
    } else {
      setBestMatchSearch(null);
    }
    setTableData(tableData1);
    // 默认排序处理
    const urlParams: any = useSSRQueryRequestHook.getUrlParams();
    if (urlParams?.orderBy) {
      const orderByArr = urlParams?.orderBy.split(",");
      if (orderByArr.length === 2) {
        setSortIndex(orderByArr[0]);
        setSortPriceIndex(orderByArr[1]);
      }
    }
  };

  const getBestProductHeaderImage = (bestMatchSearch: any): string => {
    const imgUrlPath = ossService.generateOssFullFilepath(bestMatchSearch?.headImage, commonConstant.COMMON_IMAGE_PATHS.DEFAULT);
    return imgUrlPath || commonConstant.COMMON_IMAGE_PATHS.DEFAULT_PRODUCT;
  };
  /* ======================================= 方法 end======================================= */

  /* ======================================= 初始化 start======================================= */
  useEffect(() => {
    fetchTableData();
    setOwnerBrand(state.layoutInitData.hotBrandIntroduce.filter(item => item?.brand_type === "owner")?.map(item => item.brand_id));
  }, [useSSRQueryRequestHook.getTableData.content]);

  // 查询SKU时，自动展开产品项
  useEffect(() => {
    if (tableData.length === 1) {
      // 只有一个产品，自动展开sku
      openOrCloseEvent(true);
    }
  }, [tableData]);
  /* ======================================= 初始化 end======================================= */

  /* ======================================= 渲染dom start======================================= */
  // 子表格
  const expandedRowRender = record1 => {
    // skus 渲染列
    const isDangerSpu = !!(record1?.isDanger || record1?.isExplode || record1?.isPoison);
    const skuTableColumns: TableColumnsType<ISkus> = [
      {
        title: "产品代码(SKU)",
        align: "center",
        dataIndex: "sku",
        render: (text, record) => (
          <div>
            <span className="isBuyTag">{record?.isBuy ? "购买过" : ""}</span>
            <span className={text.indexOf(searchFilterParams?.keyword) !== -1 ? "sku" : ""}>{text}</span>
          </div>
        ),
      },
      {
        title: "产品信息",
        align: "center",
        dataIndex: "info",
        render: (value, record) => (
          <div className="product-sku-info">
            <span>{record?.skuName || record?.skuNameEn || "暂无"}</span>
          </div>
        ),
      },
      {
        title: "包装规格",
        align: "center",
        dataIndex: "packing",
        render: (text, record) => <span>{!text || text === "1" ? "-" : record?.attributeModel ? text + "（" + record?.attributeModel + "）" : text}</span>,
      },
      {
        title: state.isOpenProductMinPackingRatioControl ? "最小起订量" : "建议起订量",
        align: "center",
        dataIndex: "packingRatio",
      },
      {
        title: "价格单位",
        align: "center",
        dataIndex: "unit",
      },
      {
        title: "市场价",
        align: "center",
        dataIndex: "guidePrice",
        render: guidePrice => <span style={{ fontSize: "14px" }}>￥{ghmallGuidePrice2Show(guidePrice)}</span>,
      },
      {
        title: "特惠价",
        align: "center",
        width: 175,
        dataIndex: "discountPrice",
        render: (discountPrice, record) => renderDiscountPriceNode(discountPrice, record, isDangerSpu),
      },
      {
        title: "详情",
        align: "center",
        dataIndex: "detail",
        render: (text, record) => (
          <div className="product-sku-action-box">
            <a className="product-detail-link" href={`/product/${record1.productNo}?sku=${record.sku}`} target="_blank">
              产品详情
            </a>
          </div>
        ),
      },
    ];
    // 渲染价格
    const renderProductPrice = (price, discount) => {
      return (
        <>
          {price === 0 ? (
            <a className="enquiry-link" href={`/ucenter/contactus`} target="_blank" title="请联系客服">
              询价
            </a>
          ) : (
            <span className="price">￥{ghmallDiscountPrice2Show(price, discount)}</span>
          )}
        </>
      );
    };
    // 优先渲染服务器-产品折扣价
    const renderProductDiscountPrice = price => {
      return (
        <>
          {price === 0 ? (
            <a className="enquiry-link" href={`/ucenter/contactus`} target="_blank" title="请联系客服">
              询价
            </a>
          ) : (
            <span className="price">{"￥" + price2Thousand(price)}</span>
          )}
        </>
      );
    };
    /** 判断是否显示优惠价的title属性 */
    const checkShowSkuDiscountTitle = record => {
      const { discountPrice, guidePrice, aptDiscount } = record;
      if (guidePrice === 0) {
        return false;
      }
      if (price2Thousand(discountPrice) !== price2Thousand(guidePrice * aptDiscount)) {
        return false;
      }
      return aptDiscount < 1 && aptDiscount > 0;
    };
    // 根据登录条件渲染特惠价列-操作栏内容
    const renderDiscountPriceNode = (price, record, isDangerSpu) => {
      // 创建特定于当前SKU的数量变化回调
      const handleSkuQuantityChange = (quantity: number) => {
        handleQuantityChange(quantity, record);
      };

      // 检查是否有更新的价格
      const updatedPrice = updatedSkuPrices[record.id];
      const displayPrice = updatedPrice || record.discountPrice;

      return (
        <div className="login-discount-price-box">
          <div className="login-discount-price">
            <span className="product-price-inventory" title={`${checkShowSkuDiscountTitle(record) ? `${record.aptDiscount * 10}折` : ""}`}>
              {!isDangerSpu && price !== 0 && record.aptDiscount < 1 ? <i className="indulgence-icon-tip" /> : null}
              {displayPrice ? renderProductDiscountPrice(displayPrice) : renderProductPrice(record.guidePrice, record.aptDiscount)}
            </span>
            <Add2ShopCart item={record} showHeaderExpand={true} showHeaderExpandClass={"header-expand"} onQuantityChange={handleSkuQuantityChange} />
          </div>
        </div>
      );
    };
    /** 设置sku行样式，用作处理特殊高亮行 */
    const setSkuRowClassName = (record, index) => {
      if (record.sku === searchFilterParams?.keyword) {
        return "search-selected-row";
      }
      return "";
    };

    // 子表数据排序
    const skuTableDataSource = record1.skus.sort((a, b) => {
      if (a.sku === searchFilterParams?.keyword) {
        return -1;
      } else if (b.sku === searchFilterParams?.keyword) {
        return 1;
      }
      const buySort = b?.isBuy - a?.isBuy;
      if (buySort !== 0) {
        return buySort;
      }
      return a.guidePrice > b.guidePrice ? 1 : -1;
    });

    /** 处理数量变化时的协议价格查询 */
    const handleQuantityChange = async (quantity: number, skuRecord?: any) => {
      // 只有在用户登录时才查询协议价格
      if (state?.userLoginState && skuRecord) {
        try {
          const result = await agreementPriceHook.queryMemberVolumeDiscount(
            quantity,
            record1.brandId?.toString() || "941", // 使用产品的品牌ID，默认华大品牌ID
            skuRecord.id
          );
          // console.log('搜索页面协议价格查询结果:', result);

          // 如果查询成功且有协议价格配置，更新SKU的价格
          if (result && result.orderQuantityConfiguration && result.productSkuDto?.discountPrice) {
            // console.log('搜索页面更新协议价格:', result.productSkuDto.discountPrice);

            // 更新特定SKU的价格
            setUpdatedSkuPrices(prev => ({
              ...prev,
              [skuRecord.id]: result.productSkuDto.discountPrice
            }));
          }
        } catch (error) {
          console.error("搜索页面查询协议价格失败:", error);
        }
      }
    };
    return (
      <div className="sku-box-wrapper">
        <Descriptions
          bordered
          column={2}
          colon={false}
          size="small"
          contentStyle={{
            color: "#6D7278",
            fontSize: "14px",
            backgroundColor: "#fafafa",
          }}
          labelStyle={{
            width: "225px",
            fontSize: "14px",
            fontWeight: 400,
            textAlign: "center",
            color: "#6D7278",
          }}
        >
          <Descriptions.Item label="CAS">{record1.cas || "暂无"}</Descriptions.Item>
          <Descriptions.Item label="产品描述">{record1.description || "暂无"}</Descriptions.Item>
        </Descriptions>
        <Table rowKey="id" size="small" className="product-sku-table" rowClassName={setSkuRowClassName} columns={skuTableColumns} dataSource={skuTableDataSource} pagination={false} />
      </div>
    );
  };
  /* ======================================= 渲染dom end======================================= */
  return (
    <div className={style.wrapper}>
      <div className="search">
        {/* 条件过滤器 */}
        <SearchFilterBoxPanel
          useSSRQueryRequestHook={useSSRQueryRequestHook}
          onMonitorSelectedParams={onMonitorSelectedParams}
          handleSearchEvent={handleNewSearchEvent}
          params={{
            categoryId: searchFilterParams?.categoryId,
            keyword: searchFilterParams?.keyword,
            brandId: searchFilterParams?.brandId,
          }}
        />
        {/* 排序工具栏 */}
        <div className="tool-wrapper">
          <div className="tool-label">排序方式:</div>
          <div onClick={async () => await orderByEvent("default")} className={`tool-label ${sortIndex === "default" && "tool-active"}`}>
            默认
          </div>
          <div onClick={async () => await orderByEvent("guide_price", sortPriceIndex)} className={`tool-label ${sortIndex === "guide_price" && "tool-active"}`}>
            价格&nbsp;{sortPriceIndex === "desc" ? <ArrowDownOutlined /> : <ArrowUpOutlined />}
          </div>
          <div className="tool-result">
            <span>
              总共&nbsp;<span>{useSSRQueryRequestHook.getPaginationOptions().total}</span>&nbsp;条记录
            </span>
          </div>
        </div>
        {/* 初始化空关键字-不显示 */}
        {searchFilterParams.keyword && bestMatchSearch ? (
          <div className="product-wrapper">
            <img className="product-img" src={getBestProductHeaderImage(bestMatchSearch)} alt="product" />
            <div className="product-spu">
              <div className="product-name">
                <a dangerouslySetInnerHTML={{ __html: bestMatchSearch?.productName }} href={`/product/${bestMatchSearch.productNo}`} target="_blank" />
              </div>
              <div className="product-desc">
                CasNo：{bestMatchSearch?.cas || "-"}&nbsp;&nbsp;原厂货号：{bestMatchSearch?.itemNo || "暂无"}
              </div>
              <div className="product-desc">
                英文名：{bestMatchSearch?.productNameEn || "暂无"}&nbsp;&nbsp;描述：{bestMatchSearch?.description || "暂无"}
              </div>
              <div className="product-hazard-type">
                产品危化类型:{" "}
                <span>
                  <span className={`danger${bestMatchSearch?.isDanger ? "" : " hollow-out"}`}>危化品</span>
                  <span className={`danger${bestMatchSearch?.isExplosion ? "" : " hollow-out"}`}>爆炸品</span>
                  <span className={`danger${bestMatchSearch?.isPoison ? "" : " hollow-out"}`}>易制毒</span>
                  <span className={`danger${bestMatchSearch?.isExplode ? "" : " hollow-out"}`}>易制爆</span>
                </span>
              </div>
            </div>
          </div>
        ) : null}
        {/* 结果渲染 */}
        <div className="result-wrapper">
          <Table
            rowKey="id"
            key="search"
            className="product-table"
            columns={tableColumns}
            dataSource={tableData}
            pagination={false}
            size={"large"}
            expandedRowKeys={tableExpandedRowKeys}
            onExpand={(expanded, record) => openOrCloseEvent(expanded, record)}
            expandable={{
              columnTitle: (
                <div className="table-action">
                  操作&nbsp;
                  <span onClick={() => openOrCloseEvent()}>{!collapseFlag ? <PlusCircleOutlined /> : <MinusCircleOutlined />}</span>
                </div>
              ),
              columnWidth: 85,
              indentSize: 0,
              fixed: "right",
              expandIcon: ({ expanded, onExpand, record }) =>
                expanded ? (
                  <span style={{ cursor: "pointer" }} onClick={e => onExpand(record, e)}>
                    <MinusCircleOutlined />
                    &nbsp;收起
                  </span>
                ) : (
                  <span style={{ cursor: "pointer" }} onClick={e => onExpand(record, e)}>
                    <PlusCircleOutlined />
                    &nbsp;展开
                  </span>
                ),
              expandedRowRender: record => expandedRowRender(record),
            }}
          />
          {/* 分页 */}
          <div className="my-pagination">
            <MyPagination ssrRequestInstance={useSSRQueryRequestHook} isAsync={true} />
          </div>
        </div>
      </div>
      {/* 快速登录组件 */}
      <useLoginModalHooks.FormModal />
    </div>
  );
});
