import { baseQueryDto } from "./base";

// 询报价单基础信息
export interface IQuoteRequest {
  id?: number;
  quoteNo?: string; // 询报价单号
  title: string; // 询报价标题
  description?: string; // 询报价描述
  contactName: string; // 联系人姓名
  contactPhone: string; // 联系人电话
  contactEmail?: string; // 联系人邮箱
  companyName?: string; // 公司名称
  status: QuoteStatus; // 询报价状态
  memberId?: string; // 会员ID
  createdAt?: string;
  updatedAt?: string;
  expiresAt?: string; // 报价有效期
}

// 询报价状态枚举
export enum QuoteStatus {
  DRAFT = 'DRAFT', // 草稿
  PENDING = 'PENDING', // 待报价
  QUOTED = 'QUOTED', // 已报价
  ACCEPTED = 'ACCEPTED', // 已接受
  REJECTED = 'REJECTED', // 已拒绝
  EXPIRED = 'EXPIRED' // 已过期
}

// 询报价产品项
export interface IQuoteProduct {
  id?: number;
  quoteRequestId?: number;
  productNo?: string; // 产品编号
  productName: string; // 产品名称
  sku: string; // 产品SKU
  specification?: string; // 规格
  quantity: number; // 询价数量
  unit?: string; // 单位
  estimatedPrice?: number; // 预估价格
  quotedPrice?: number; // 报价
  remark?: string; // 备注
  matchStatus: ProductMatchStatus; // 匹配状态
  productSkuId?: number; // 匹配到的产品SKU ID
  brandId?: number; // 品牌ID
  brandName?: string; // 品牌名称
  productImage?: string; // 产品图片
  packingRatio?: number; // 包装规格
}

// 产品匹配状态
export enum ProductMatchStatus {
  MATCHED = 'MATCHED', // 已匹配
  PARTIAL_MATCH = 'PARTIAL_MATCH', // 部分匹配
  NOT_MATCHED = 'NOT_MATCHED', // 未匹配
  MANUAL_INPUT = 'MANUAL_INPUT' // 手动输入
}

// 文件上传匹配请求
export interface IQuoteFileUploadRequest {
  file: File;
  title: string;
  description?: string;
  contactName: string;
  contactPhone: string;
  contactEmail?: string;
  companyName?: string;
}

// 文件上传匹配响应
export interface IQuoteFileUploadResponse {
  quoteRequestId: number;
  matchedProducts: IQuoteProduct[];
  unmatchedRows: IUnmatchedRow[];
  totalRows: number;
  matchedCount: number;
  unmatchedCount: number;
}

// 未匹配的行数据
export interface IUnmatchedRow {
  rowIndex: number;
  productName?: string;
  sku?: string;
  quantity?: number;
  reason: string; // 未匹配原因
}

// 询报价查询参数
export interface IQuoteQueryDto extends baseQueryDto {
  status?: QuoteStatus;
  keyword?: string; // 搜索关键词
  startDate?: string;
  endDate?: string;
  memberId?: string;
}

// 生成报价单请求
export interface IGenerateQuoteRequest {
  quoteRequestId: number;
  products: IQuoteProduct[];
  validDays?: number; // 报价有效天数，默认30天
  remark?: string; // 报价备注
}

// 生成快速订单请求
export interface ICreateQuickOrderFromQuoteRequest {
  quoteRequestId: number;
  selectedProducts: IQuoteProduct[];
  receivingAddressId: number;
  receiptId?: number;
  remark?: string;
  sendType?: string;
}

// 报价单详情
export interface IQuoteDetail extends IQuoteRequest {
  products: IQuoteProduct[];
  totalAmount?: number; // 总金额
  quotedBy?: string; // 报价人
  quotedAt?: string; // 报价时间
}
