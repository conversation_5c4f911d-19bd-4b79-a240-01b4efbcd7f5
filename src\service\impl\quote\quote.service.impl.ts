import { Provide, Scope, <PERSON>ope<PERSON>num, Inject } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IQuoteService } from "@/service/quote/quote.service";
import { IQuoteRequest, IQuoteProduct, IQuoteQueryDto } from "~/typings/data/quote";

@Provide("QuoteService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class QuoteServiceImpl extends BaseService implements IQuoteService {

  /**
   * 创建询报价单
   */
  async createQuoteRequest(data: Partial<IQuoteRequest>, memberId?: string): Promise<any> {
    const requestData = {
      ...data,
      memberId,
      status: 'DRAFT'
    };
    
    return this.easyResponse(
      await this.easyHttp.post("/api/quote-requests", requestData, this.getDefaultHeaders()),
      "创建询报价单失败，请稍后再试！"
    );
  }

  /**
   * 文件上传并匹配产品
   */
  async uploadFileAndMatchProducts(
    fileBuffer: Buffer,
    fileName: string,
    quoteData: Partial<IQuoteRequest>,
    memberId?: string
  ): Promise<any> {
    // 先创建询报价单
    const quoteRequest = await this.createQuoteRequest(quoteData, memberId);
    const quoteRequestId = quoteRequest.data.id;

    // 这里应该调用实际的后端服务来解析文件和匹配产品
    // 目前返回模拟数据
    const mockResult = {
      quoteRequestId,
      matchedProducts: [
        {
          id: 1,
          quoteRequestId,
          productName: "乙醇",
          sku: "C2H5OH-500ML",
          quantity: 10,
          productSkuId: 12345,
          brandId: 941,
          brandName: "华大",
          productImage: "/images/default-product.png",
          packingRatio: 500,
          unit: "ML",
          estimatedPrice: 25.50,
          matchStatus: "MATCHED"
        }
      ],
      unmatchedRows: [
        {
          rowIndex: 3,
          productName: "未知产品",
          sku: "UNKNOWN-001",
          quantity: 5,
          reason: "未找到匹配的产品"
        }
      ],
      totalRows: 2,
      matchedCount: 1,
      unmatchedCount: 1
    };

    return this.easyResponse({ data: mockResult });
  }

  /**
   * 产品匹配
   */
  async matchProduct(productName: string, sku: string, quantity: number): Promise<any> {
    const searchParams = {
      keyword: sku || productName,
      page: 1,
      size: 1,
      brandId: 941 // 华大品牌ID
    };

    const searchResult = await this.easyHttp.get("/api/product/search", searchParams, this.getDefaultHeaders());
    
    if (searchResult.data?.content?.length > 0) {
      const products = this.getProductAllSku(searchResult.data.content);
      
      // 精确匹配SKU
      if (sku) {
        const exactMatch = products.find(item => item.sku === sku);
        if (exactMatch) {
          return this.easyResponse({ data: exactMatch });
        }
      }
      
      // 如果没有精确匹配，返回第一个结果
      if (products.length > 0) {
        return this.easyResponse({ data: products[0] });
      }
    }

    return this.easyResponse({ data: null });
  }

  /**
   * 获取产品所有SKU（复用快速订单的逻辑）
   */
  private getProductAllSku(products: any[]): any[] {
    const results: any[] = [];
    products.forEach(product => {
      if (product.skus && product.skus.length > 0) {
        product.skus.forEach(sku => {
          results.push({
            ...sku,
            productName: product.productName,
            productNo: product.productNo,
            brandId: product.brandId,
            brandName: product.brandName,
            productImage: product.productImage
          });
        });
      }
    });
    return results;
  }

  /**
   * 保存询报价产品
   */
  private async saveQuoteProducts(quoteRequestId: number, products: IQuoteProduct[]): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.post(`/api/quote-requests/${quoteRequestId}/products`, { products }, this.getDefaultHeaders()),
      "保存询报价产品失败"
    );
  }

  /**
   * 获取询报价单列表
   */
  async getQuoteRequestList(criteria: Partial<IQuoteQueryDto>, memberId?: string): Promise<any> {
    const params = { ...criteria, memberId };
    return this.easyResponse(
      await this.easyHttp.get("/api/quote-requests", params, this.getDefaultHeaders()),
      "获取询报价单列表失败"
    );
  }

  /**
   * 获取询报价单详情
   */
  async getQuoteRequestDetail(quoteRequestId: number, memberId?: string): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.get(`/api/quote-requests/${quoteRequestId}`, { memberId }, this.getDefaultHeaders()),
      "获取询报价单详情失败"
    );
  }

  /**
   * 更新询报价产品
   */
  async updateQuoteProducts(
    quoteRequestId: number,
    products: Partial<IQuoteProduct>[],
    memberId?: string
  ): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.put(`/api/quote-requests/${quoteRequestId}/products`, { products, memberId }, this.getDefaultHeaders()),
      "更新询报价产品失败"
    );
  }

  /**
   * 生成报价单
   */
  async generateQuote(
    quoteRequestId: number,
    products: Partial<IQuoteProduct>[],
    validDays: number = 30,
    remark?: string,
    memberId?: string
  ): Promise<any> {
    const requestData = {
      products,
      validDays,
      remark,
      memberId
    };

    return this.easyResponse(
      await this.easyHttp.post(`/api/quote-requests/${quoteRequestId}/generate-quote`, requestData, this.getDefaultHeaders()),
      "生成报价单失败"
    );
  }

  /**
   * 从询报价单创建快速订单
   */
  async createQuickOrderFromQuote(
    quoteRequestId: number,
    selectedProductIds: number[],
    orderData: any,
    memberId?: string
  ): Promise<any> {
    const requestData = {
      selectedProductIds,
      ...orderData,
      memberId
    };

    return this.easyResponse(
      await this.easyHttp.post(`/api/quote-requests/${quoteRequestId}/create-order`, requestData, this.getDefaultHeaders()),
      "创建订单失败"
    );
  }

  /**
   * 删除询报价单
   */
  async deleteQuoteRequest(quoteRequestId: number, memberId?: string): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.delete(`/api/quote-requests/${quoteRequestId}`, { memberId }, this.getDefaultHeaders()),
      "删除询报价单失败"
    );
  }
}
