import { Provide, Scope, <PERSON><PERSON><PERSON><PERSON>, Inject } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IQuoteService } from "@/service/quote/quote.service";
import { IQuoteRequest, IQuoteProduct, IQuoteQueryDto, ProductMatchStatus } from "../../../typings/data/quote";
import * as XLSX from 'xlsx';

@Provide("QuoteService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class QuoteServiceImpl extends BaseService implements IQuoteService {

  /**
   * 创建询报价单
   */
  async createQuoteRequest(data: Partial<IQuoteRequest>, memberId?: string): Promise<any> {
    const requestData = {
      ...data,
      memberId,
      status: 'DRAFT'
    };
    
    return this.easyResponse(
      await this.easyHttp.post("/api/quote-requests", requestData, this.getDefaultHeaders()),
      "创建询报价单失败，请稍后再试！"
    );
  }

  /**
   * 文件上传并匹配产品
   */
  async uploadFileAndMatchProducts(
    fileBuffer: Buffer,
    fileName: string,
    quoteData: Partial<IQuoteRequest>,
    memberId?: string
  ): Promise<any> {
    try {
      // 解析Excel文件
      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // 跳过标题行，从第二行开始处理
      const dataRows = jsonData.slice(1) as any[][];
      
      // 创建询报价单
      const quoteRequest = await this.createQuoteRequest(quoteData, memberId);
      const quoteRequestId = quoteRequest.data.id;

      const matchedProducts: IQuoteProduct[] = [];
      const unmatchedRows: any[] = [];

      // 处理每一行数据
      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];
        if (!row || row.length === 0) continue;

        const productName = row[0]?.toString()?.trim();
        const sku = row[1]?.toString()?.trim();
        const quantity = parseFloat(row[2]) || 1;

        if (!productName && !sku) {
          continue; // 跳过空行
        }

        try {
          // 尝试匹配产品
          const matchResult = await this.matchProduct(productName, sku, quantity);
          
          if (matchResult.data) {
            matchedProducts.push({
              id: Date.now() + Math.random(), // 临时ID
              quoteRequestId,
              productName: matchResult.data.productName,
              sku: matchResult.data.sku,
              quantity,
              productSkuId: matchResult.data.id,
              brandId: matchResult.data.brandId,
              brandName: matchResult.data.brandName,
              productImage: matchResult.data.productImage,
              packingRatio: matchResult.data.packingRatio,
              unit: matchResult.data.unit,
              estimatedPrice: matchResult.data.discountPrice || matchResult.data.guidePrice,
              matchStatus: ProductMatchStatus.MATCHED
            });
          } else {
            unmatchedRows.push({
              rowIndex: i + 2, // Excel行号从1开始，加上标题行
              productName,
              sku,
              quantity,
              reason: "未找到匹配的产品"
            });
          }
        } catch (error) {
          unmatchedRows.push({
            rowIndex: i + 2,
            productName,
            sku,
            quantity,
            reason: "产品匹配失败"
          });
        }
      }

      // 保存匹配的产品到询报价单
      if (matchedProducts.length > 0) {
        await this.saveQuoteProducts(quoteRequestId, matchedProducts);
      }

      return this.easyResponse({
        data: {
          quoteRequestId,
          matchedProducts,
          unmatchedRows,
          totalRows: dataRows.length,
          matchedCount: matchedProducts.length,
          unmatchedCount: unmatchedRows.length
        }
      });

    } catch (error) {
      console.error("文件解析失败:", error);
      throw new Error("文件解析失败，请检查文件格式");
    }
  }

  /**
   * 产品匹配
   */
  async matchProduct(productName: string, sku: string, quantity: number): Promise<any> {
    const searchParams = {
      keyword: sku || productName,
      page: 1,
      size: 1,
      brandId: 941 // 华大品牌ID
    };

    const searchResult = await this.easyHttp.get("/api/product/search", searchParams, this.getDefaultHeaders());
    
    if (searchResult.data?.content?.length > 0) {
      const products = this.getProductAllSku(searchResult.data.content);
      
      // 精确匹配SKU
      if (sku) {
        const exactMatch = products.find(item => item.sku === sku);
        if (exactMatch) {
          return this.easyResponse({ data: exactMatch });
        }
      }
      
      // 如果没有精确匹配，返回第一个结果
      if (products.length > 0) {
        return this.easyResponse({ data: products[0] });
      }
    }

    return this.easyResponse({ data: null });
  }

  /**
   * 获取产品所有SKU（复用快速订单的逻辑）
   */
  private getProductAllSku(products: any[]): any[] {
    const results: any[] = [];
    products.forEach(product => {
      if (product.skus && product.skus.length > 0) {
        product.skus.forEach(sku => {
          results.push({
            ...sku,
            productName: product.productName,
            productNo: product.productNo,
            brandId: product.brandId,
            brandName: product.brandName,
            productImage: product.productImage
          });
        });
      }
    });
    return results;
  }

  /**
   * 保存询报价产品
   */
  private async saveQuoteProducts(quoteRequestId: number, products: IQuoteProduct[]): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.post(`/api/quote-requests/${quoteRequestId}/products`, { products }, this.getDefaultHeaders()),
      "保存询报价产品失败"
    );
  }

  /**
   * 获取询报价单列表
   */
  async getQuoteRequestList(criteria: Partial<IQuoteQueryDto>, memberId?: string): Promise<any> {
    const params = { ...criteria, memberId };
    return this.easyResponse(
      await this.easyHttp.get("/api/quote-requests", params, this.getDefaultHeaders()),
      "获取询报价单列表失败"
    );
  }

  /**
   * 获取询报价单详情
   */
  async getQuoteRequestDetail(quoteRequestId: number, memberId?: string): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.get(`/api/quote-requests/${quoteRequestId}`, { memberId }, this.getDefaultHeaders()),
      "获取询报价单详情失败"
    );
  }

  /**
   * 更新询报价产品
   */
  async updateQuoteProducts(
    quoteRequestId: number,
    products: Partial<IQuoteProduct>[],
    memberId?: string
  ): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.put(`/api/quote-requests/${quoteRequestId}/products`, { products, memberId }, this.getDefaultHeaders()),
      "更新询报价产品失败"
    );
  }

  /**
   * 生成报价单
   */
  async generateQuote(
    quoteRequestId: number,
    products: Partial<IQuoteProduct>[],
    validDays: number = 30,
    remark?: string,
    memberId?: string
  ): Promise<any> {
    const requestData = {
      products,
      validDays,
      remark,
      memberId
    };

    return this.easyResponse(
      await this.easyHttp.post(`/api/quote-requests/${quoteRequestId}/generate-quote`, requestData, this.getDefaultHeaders()),
      "生成报价单失败"
    );
  }

  /**
   * 从询报价单创建快速订单
   */
  async createQuickOrderFromQuote(
    quoteRequestId: number,
    selectedProductIds: number[],
    orderData: any,
    memberId?: string
  ): Promise<any> {
    const requestData = {
      selectedProductIds,
      ...orderData,
      memberId
    };

    return this.easyResponse(
      await this.easyHttp.post(`/api/quote-requests/${quoteRequestId}/create-order`, requestData, this.getDefaultHeaders()),
      "创建订单失败"
    );
  }

  /**
   * 删除询报价单
   */
  async deleteQuoteRequest(quoteRequestId: number, memberId?: string): Promise<any> {
    return this.easyResponse(
      await this.easyHttp.delete(`/api/quote-requests/${quoteRequestId}`, { memberId }, this.getDefaultHeaders()),
      "删除询报价单失败"
    );
  }
}
