import React from "react";
import { But<PERSON>, Space } from "antd";
import { FormOutlined, DeleteOutlined, CloudDownloadOutlined, SearchOutlined, ReloadOutlined, AppstoreOutlined, PlusOutlined } from "@ant-design/icons";
import { SizeType } from "antd/lib/config-provider/SizeContext";
import style from "./index.module.less";

interface leftOpt {
  add?: boolean;
  del?: boolean;
  download?: boolean;
  edit?: boolean;
}
interface CrudOperationProps {
  size?: SizeType;
  crudInstance: any;
  showRightAction: boolean;
  showLeftAction?: leftOpt;
}
// 快捷crud栏
const CrudOperation = (props: CrudOperationProps) => {
  const crud = props.crudInstance;
  const searchToggleEvent = () => {
    crud.setSearchToggle();
  };
  const refreshQueryEvent = () => {
    crud.toQuery();
  };

  return (
    <>
      <div className={style.crudOperationWrapper}>
        <div className="crud-opts">
          <div className="crud-opts-left">
            {props.showLeftAction?.add ? <Button icon={<PlusOutlined />}>新增</Button> : null}
            {props.showLeftAction?.edit ? (
              <Button type="default" icon={<FormOutlined />}>
                编辑
              </Button>
            ) : null}
            {props.showLeftAction?.del ? (
              <Button ghost danger icon={<DeleteOutlined />}>
                删除
              </Button>
            ) : null}
            {props.showLeftAction?.download ? (
              <Button ghost type="primary" icon={<CloudDownloadOutlined />}>
                导出
              </Button>
            ) : null}
          </div>
          <div className="crud-opts-right">
            {props.showRightAction ? (
              <Space.Compact block size={props.size}>
                <Button className="quick-btn" title="搜索开闭" type="default" onClick={searchToggleEvent} icon={<SearchOutlined />} />
                <Button className="quick-btn" title="刷新" type="default" onClick={refreshQueryEvent} icon={<ReloadOutlined />} />
                <Button className="quick-btn" title="列设置" type="default" icon={<AppstoreOutlined />} />
              </Space.Compact>
            ) : null}
          </div>
        </div>
      </div>
    </>
  );
};
CrudOperation.defaultProps = {
  size: "middle",
  showRightAction: true,
  showLeftAction: {
    add: false,
    del: false,
    edit: false,
    download: false,
  },
};
export default CrudOperation;
