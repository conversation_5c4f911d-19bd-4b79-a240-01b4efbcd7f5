import React, { useContext, useEffect, useRef, useState } from "react";
import { SProps } from "ssr-types-react";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";

import { Button, Descriptions, Tooltip, Modal, Tabs, notification, Affix } from "antd";
import { QuestionCircleOutlined, UploadOutlined, VideoCameraOutlined } from "@ant-design/icons";
import { useOss } from "@/hooks/useOss";
import { useLoading } from "@/hooks/useLoading";

import Product from "./components/Product";
import Address from "./components/Address";
import Receipt from "./components/Receipt";
import OrderRemark from "./components/OrderRemark";
import BatchImportModal from "./components/BatchImportModal";
import TmConfirmModal from "@/components/TmConfirmModal";
import VideoPlayer from "@/components/VideoPlayer";

import { ghmallGuidePrice2Show } from "@/utils/price-format.util";

import style from "./index.module.less";

import * as tradeCrud from "@/apis/trade/trade";

export default function QuickOrder(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const useOssHook = useOss();
  const useLoadingHook = useLoading();
  const orderRemarkFormRef = useRef();

  const [products, setProducts] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [selectedSendType, setSelectedSendType] = useState(1);
  const [selectedReceipt, setSelectedReceipt] = useState(null);
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalDiscountPrice, setTotalDiscountPrice] = useState(0);
  const [batchImportModalVisible, setBatchImportModalVisible] = useState(false);
  const [importDataSource, setImportDataSource] = useState([]);
  const [videoModalVisible, setVideoModalVisible] = useState(false);

  useEffect(() => {
    if (!state?.userLoginState) {
      window.location.href = `/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`;
    }
  }, [state?.userLoginState]);
  /**
   * 产品数据源
   * @param dataSource 数据源
   */
  const handleDataSourceChange = dataSource => {
    // console.log("dataSource", dataSource);
    const _totalPrice = dataSource.reduce((sum, item) => sum + Number(item.discountPrice) * Number(item.quantity), 0);
    const _totalDiscountPrice = dataSource.reduce((sum, item) => sum + (Number(item.guidePrice) - Number(item.discountPrice)) * Number(item.quantity), 0);
    // 检查是否有产品价格为0
    const hasZeroPriceProduct = dataSource.some(item => item.discountPrice === 0);

    setProducts(dataSource);
    setTotalAmount(hasZeroPriceProduct ? 0 : _totalPrice);
    setTotalDiscountPrice(hasZeroPriceProduct ? 0 : _totalDiscountPrice);
  };

  /**
   * 收货地址
   * @param address 收货地址
   */
  const handleAddressChange = address => {
    setSelectedAddress(address);
  };

  /**
   * 收货地址
   * @param address 收货地址
   */
  const handleReceiptChange = receipt => {
    setSelectedReceipt(receipt);
  };

  /**
   * 检查是否有危险产品
   * @returns
   */
  const handleCheckHasDangerInOrderProducts = () => {
    return products?.some(item => item.isDanger) || false;
  };

  /**
   * 提交订单
   */
  const handleSubmit = async () => {
    const form = orderRemarkFormRef?.current?.getFieldsValue(true);
    const confirm = await TmConfirmModal({
      title: "温馨提醒",
      content: (
        <>
          {handleCheckHasDangerInOrderProducts() ? (
            <span>
              您的下单产品包含: <span style={{ color: "red" }}>液体/危化学品</span>，如果您未提供过相关资质材料，请及时联系业务员/客服~
            </span>
          ) : (
            <span>商品等信息，已确认无误</span>
          )}
        </>
      ),
      confirmText: "提交订单",
    });
    if (confirm !== "confirm") {
      return;
    }
    // 组装产品数据
    const cartProducts = products.map(item => {
      return {
        productId: item.productId,
        productQuantity: item.quantity,
        productSku: item.sku,
        price: item.discountPrice,
        remark: item.remark || "",
        skuId: item.id,
        isDanger: false,
      };
    });
    const tradeVo = {
      ...form,
      receivingAddressId: selectedAddress?.id,
      receiptId: selectedReceipt?.id || null,
      products: cartProducts,
      tradeType: "QUICK_ORDER",
      sendType: selectedSendType,
    };

    if (!tradeVo.receivingAddressId) {
      notification.warning({ message: "请选择一个收货地址!" });
      return;
    }
    useLoadingHook.showLoading("订单提交中...");
    const [err, res] = await tradeCrud
      .quickOrderCreate(tradeVo)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      useLoadingHook.hideLoading();
      return notification.error({ message: err?.data?.message || "创建订单失败，请稍后再试！" });
    }
    notification.success({ message: res?.message || "订单创建成功！" });
    setTimeout(async () => {
      useLoadingHook.hideLoading();
      // 弹窗拦截
      await TmConfirmModal({
        title: "温馨提醒",
        content: (
          <>
            <div>订单已提交成功，您的专属业务员会尽快审核订单，请耐心等待！</div>
          </>
        ),
        confirmText: "确认",
      });
      window.location.href = `/ucenter/trade/order?status=0`;
    }, 100);
  };

  /**
   * 批量导入数据源
   * @param dataSource 数据源
   */
  const handleBatchImportDataSourceChange = dataSource => {
    // handleDataSourceChange(dataSource);
    setImportDataSource(dataSource);
  };

  return (
    <>
      <div className={style.wrapper}>
        <div className="wrap">
          <div className="header">
            <h2>华大自营品牌快速订购</h2>
            <p>通过输入产品名称或产品代码来添加单个产品，或者选择批量导入多个产品。选定送货地址后，即可快速提交订单。</p>
          </div>
          <div className="body">
            <div className="main">
              <div className="section">
                <div className="section-header">
                  <h2 className="section-header__title">订购产品</h2>
                  <div className="section-header__control">
                    <Button icon={<UploadOutlined />} size="small" danger onClick={() => setBatchImportModalVisible(true)}>
                      批量导入
                    </Button>
                    <BatchImportModal visible={batchImportModalVisible} onClose={() => setBatchImportModalVisible(false)} onDataSourceChange={handleBatchImportDataSourceChange} />
                  </div>
                </div>
                <div className="section-body">
                  <Product onDataSourceChange={handleDataSourceChange} importDataSource={importDataSource} />
                </div>
              </div>

              <div className="section">
                <div className="section-header">
                  <h2 className="section-header__title">订单备注（选填）</h2>
                  <div className="section-header__control"></div>
                </div>
                <div className="section-body">
                  <OrderRemark orderRemarkFormRef={orderRemarkFormRef} />
                </div>
              </div>
            </div>
            <div className="side">
              {products.length > 0 && (
                <Affix className="submit-box" offsetTop={0}>
                  <div className="side-box" style={{ borderBottom: "1px dashed #eaeaea" }}>
                    <div className="side-box__body">
                      <Button type="primary" size="large" style={{ width: "100%" }} onClick={handleSubmit}>
                        提交订单
                      </Button>
                      <Descriptions
                        style={{ marginTop: 16 }}
                        column={1}
                        labelStyle={{ flex: 1, textAlign: "left", color: "#999" }}
                        contentStyle={{ flex: 1, textAlign: "right", display: "flex", justifyContent: "flex-end" }}
                        size="small"
                      >
                        <Descriptions.Item label={`产品价格(${products.length}个产品)`}>￥{ghmallGuidePrice2Show(totalAmount)}</Descriptions.Item>
                        {totalDiscountPrice > 0 && <Descriptions.Item label="共优惠">￥{ghmallGuidePrice2Show(totalDiscountPrice)}</Descriptions.Item>}
                        <Descriptions.Item label="运费">
                          <Tooltip title="注意：实际运费或免运费，待业务员审核订单后确定！">
                            <span>待定</span>
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: "#999" }} />
                          </Tooltip>
                        </Descriptions.Item>
                        <Descriptions.Item label="总计">
                          <p style={{ color: "#e62129", fontSize: "20px" }}>
                            <span style={{ fontSize: "14px" }}>￥</span>
                            {ghmallGuidePrice2Show(totalAmount)}
                          </p>
                        </Descriptions.Item>
                      </Descriptions>
                    </div>
                  </div>
                </Affix>
              )}

              <Address onChange={handleAddressChange} onSendTypeChange={val => setSelectedSendType(val)} />
              <Receipt onChange={handleReceiptChange} />
            </div>
          </div>
        </div>
        <div className="wrap-footer-1"></div>
        <div className="wrap-footer-2"></div>
        <div className="floatbar">
          <p onClick={() => setVideoModalVisible(true)}>
            <VideoCameraOutlined />
            视频教程
          </p>
          <Modal title="视频教程" width={800} open={videoModalVisible} onCancel={() => setVideoModalVisible(false)} footer={null}>
            <Tabs
              type="card"
              items={[
                {
                  key: "1",
                  label: `快速订购`,
                  children: <VideoPlayer src="//ttimg.guanghuayigou.com/mall/web/images/9hfee3zx-20250124105616732.mp4" />,
                },
                {
                  key: "2",
                  label: `批量导入`,
                  children: <VideoPlayer src="//ttimg.guanghuayigou.com/mall/web/images/fzyem40r-20250124110425622.mp4" />,
                },
              ]}
            />
          </Modal>
        </div>
      </div>
    </>
  );
}
