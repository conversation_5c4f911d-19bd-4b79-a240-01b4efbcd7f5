import { Body, Controller, Del, Get, Inject, Post, Put, Query } from "@midwayjs/decorator";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { BaseController } from "@/controller/base.controller";
import { IShoppingCartService } from "@/service/trade/shopping-cart.service";
import { ICreate, IDelete, ISelectedAll, ISelectedOrNot, IUpdate } from "~/typings/data/trade/shopping-cart";
import { httpError } from "@midwayjs/core";

@Controller("/api/trade/shopping-cart", { middleware: [AuthenticationMiddleware] })
export class ShoppingCartController extends BaseController {
  @Inject("ShoppingCartService")
  shoppingCartService: IShoppingCartService;

  /**
   * @desc 获取购物车列表
   */
  @Get()
  async fetchList() {
    const { ctx } = this;
    const memberId = this.getMemberId();
    const res = await this.shoppingCartService.getList(memberId);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Post()
  async create(@Body() resource: ICreate | ICreate[]) {
    const { ctx } = this;
    let resourceTmp: any;
    if (resource.constructor === Array) {
      resourceTmp = resource;
    } else if (resource.constructor === Object) {
      resourceTmp = [resource];
    } else {
      throw new httpError.BadRequestError("请求参数不合法不被允许！");
    }
    if (resourceTmp.length === 0) {
      throw new httpError.BadRequestError("请求参数不合法，缺少必须的参数！");
    }
    const targetShoppingCart: ICreate[] = [];
    resourceTmp
      .map(cart => {
        cart.memberId = this.getMemberId();
        cart.quantity = Math.ceil(cart.quantity)
        return cart;
      })
      .forEach(cart => {
        const idx = targetShoppingCart.findIndex(newCart => newCart.productSkuId === cart.productSkuId);
        if (idx === -1) {
          targetShoppingCart.push(cart);
        } else {
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          targetShoppingCart[idx].quantity += Number(cart.quantity);
        }
      });
    const res = await this.shoppingCartService.create(targetShoppingCart);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Put()
  async update(@Body() resource: IUpdate) {
    const { ctx } = this;
    resource.memberId = this.getMemberId();
    const res = await this.shoppingCartService.update(resource);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Del()
  async delete(@Body() resource: IDelete) {
    const { ctx } = this;
    resource.member_id = this.getMemberId();
    const res = await this.shoppingCartService.delete(resource);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Post("/select")
  async selectedOrNot(@Body() resource: ISelectedOrNot) {
    const { ctx } = this;
    resource.memberId = this.getMemberId();
    const res = await this.shoppingCartService.selectedOrNot(resource);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Post("/select-all")
  async selectedAll(@Body() resource: ISelectedAll) {
    const { ctx } = this;
    resource.memberId = this.getMemberId();
    const res = await this.shoppingCartService.selectedAll(resource);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Get("/selected")
  async fetchSelectedDetailList(@Query("way") way?: string) {
    const { ctx } = this;
    const res = await this.shoppingCartService.getSelectedDetailList(this.getMemberId(), way);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }
}
