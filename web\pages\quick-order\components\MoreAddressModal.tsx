import React, { useState } from "react";
import { Modal, List, Tag, Input, Button, Switch, message } from "antd";
import styles from "../index.module.less";
import crudAddress from "@/apis/member/address";

const AddressModal = ({ visible, onClose, addressList, selectedAddress, onSelectAddress, getAddressList, onSetDefault }) => {
  const Search = Input.Search;
  const [keyword, setKeyword] = useState("");

  return (
    <Modal title="选择收货地址" width={800} open={visible} onCancel={onClose} footer={null} className={styles.moreModal}>
      <Search
        placeholder="请输入关键词"
        value={keyword}
        onChange={e => {
          setKeyword(e.target.value);
        }}
        onSearch={value => {
          setKeyword(value);
          getAddressList(value);
        }}
        allowClear
        size="large"
        style={{ marginBottom: "20px" }}
      />
      <List
        style={{ maxHeight: "500px", overflowY: "auto" }}
        itemLayout="horizontal"
        dataSource={addressList}
        renderItem={item => (
          <List.Item className={selectedAddress?.id === item?.id ? styles.listItemActive : styles.listItem} onClick={() => onSelectAddress(item)}>
            <List.Item.Meta
              title={
                <p>
                  {item.isDefault ? (
                    <Tag color="#87d068" style={{ marginRight: 10 }}>
                      默认
                    </Tag>
                  ) : null}
                  {item.aliasName ? (
                    <Tag color="gold" style={{ marginRight: 10 }}>
                      {item.aliasName}
                    </Tag>
                  ) : null}
                  <span className={styles.nickname}>{item.nickname}</span>
                  <span className={styles.phone}>{item.phone}</span>
                </p>
              }
              description={<span className={styles.addressDescription}>{`${item.provinceInfo?.name} ${item.cityInfo?.name} ${item.districtInfo?.name} ${item.address}`}</span>}
            />
            <div style={{ marginRight: "20px" }}>
              <Switch
                checkedChildren="默认"
                unCheckedChildren=""
                checked={item.isDefault}
                onChange={(val, event) => {
                  event.stopPropagation();
                  if (val) {
                    return Modal.confirm({
                      title: "设置默认地址",
                      content: "设置默认地址后，该地址将作为默认地址，其他地址将不再作为默认地址",
                      onOk: () => {
                        crudAddress.edit(item.id, { ...item, isDefault: 1 }).then(res => {
                          if (res.status === 200) {
                            message.success("配置成功");
                            getAddressList();
                          }
                        });
                      },
                    });
                  } else {
                    return Modal.confirm({
                      title: "取消默认地址",
                      content: "取消默认地址后，该地址将不再作为默认地址",
                      onOk: () => {
                        crudAddress.edit(item.id, { ...item, isDefault: 0 }).then(res => {
                          if (res.status === 200) {
                            message.success("配置成功");
                            getAddressList();
                          }
                        });
                      },
                    });
                  }
                }}
              />
            </div>
          </List.Item>
        )}
      />
    </Modal>
  );
};

export default AddressModal;
