/* 密码组件封装 */
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { getPrefixCls } from "@/utils/props-tools.util";
import { useIntlMessage } from "@/hooks/useIntlMessage";
import { $tools } from "@/utils/tools.util";
import { Form, Popover, Input } from "antd";
import { LockOutlined, CheckOutlined, EyeOutlined, EyeInvisibleOutlined, CloseOutlined, UnlockOutlined } from "@ant-design/icons";
import "./index.module.less";

interface PasswordProps {
  prefixCls: string;
  passwordLabel: string;
  repeatLabel: string;
  repeat: boolean;
  minLength: number;
  maxLength: number;
  complexity: boolean;
  complexityTip: string;
  level: object;
  rules: object;
  width: number;
  height: number;
  radius: number;
  bgColor: string;
}
type PasswordPartialProps = Partial<PasswordProps>;

const PasswordItem = forwardRef((props: PasswordPartialProps, ref: any) => {
  const { t } = useIntlMessage();
  const prefixCls = getPrefixCls("password", props.prefixCls);
  const langCls = getPrefixCls(`lang-zh`);
  // const prefixKey = getPrefixCls("password-key", props.prefixCls);
  // const prefixId = `${prefixKey}-${$tools.uid()}`;
  const level = props.level ?? {
    1: t("password.lv1"),
    2: t("password.lv2"),
    3: t("password.lv3"),
    4: t("password.lv4"),
  };
  const tip = props.complexityTip ?? t("password.tip");

  const passwordFormRef = Form.useFormInstance();

  const checkPassword = async (_rule: any, value: string) => {
    if ($tools.isEmpty(value)) {
      setParams(params => {
        params.password = {
          strength: 0,
          tips: null,
          length: false,
          format: false,
          complexity: false,
        };
        return { ...params };
      });
      throw t("password.setting");
    } else {
      setParams(params => {
        params.password.format = true;
        return { ...params };
      });
      if (props.minLength && value.length < props.minLength) {
        setParams(params => {
          params.password.length = false;
          params.password.strength = 0;
          params.password.tips = null;
          return { ...params };
        });
        throw t("password.least", { min: props.minLength });
      }
      if (props.complexity) {
        if ($tools.checkPassword(value)) {
          const strength = $tools.getPasswordStrength(value);
          setParams(params => {
            params.password.length = true;
            params.password.strength = strength;
            params.password.tips = level[strength];
            return { ...params };
          });
          if (strength <= 1) {
            setParams(params => {
              params.password.complexity = false;
              return { ...params };
            });
            throw tip;
          } else {
            setParams(params => {
              params.password.complexity = true;
              return { ...params };
            });
            return await Promise.resolve();
          }
        }
      } else {
        const strength = $tools.getPasswordStrength(value);
        setParams(params => {
          params.password.length = true;
          params.password.strength = strength;
          params.password.complexity = true;
          params.password.tips = level[strength];
          return { ...params };
        });
        return await Promise.resolve();
      }
    }
  };

  const checkRepeat = async (_rule: any, value: string) => {
    if ($tools.isEmpty(value)) {
      throw t("password.repeat");
    } else {
      if (params.form.validate.password !== value) {
        throw t("password.different");
      }
      return await Promise.resolve();
    }
  };

  const [params, setParams] = useState({
    visible: false,
    repeatVisible: false,
    password: {
      strength: 0,
      tips: null,
      length: false,
      format: false,
      complexity: false,
    },
    form: {
      validate: {
        password: null,
        repeat: null,
      },
      rules: {
        password: [{ required: true, validator: checkPassword }],
        repeat: [{ required: props.repeat, validator: checkRepeat }],
      },
    },
  });
  const rules = Object.assign({}, params.form.rules, props.rules);
  /* 方法事件 */
  const onInput = (evt: any) => {
    const val = evt.target.value;
    setParams(params => {
      params.form.validate.password = val;
      return { ...params };
    });
    /* 更新值 */
    if (props.repeat && params.form.validate.repeat) {
      passwordFormRef?.validateFields(["repeat"]);
    }
  };
  const onVisible = () => {
    setParams(params => {
      return {
        ...params,
        visible: !params.visible,
      };
    });
  };
  const onRepeatInput = (evt: any) => {
    const val = evt.target.value;
    setParams(params => {
      params.form.validate.repeat = val;
      return { ...params };
    });
    /* todo更新值 */
  };
  const onRepeatVisible = () => {
    setParams(params => {
      return {
        ...params,
        repeatVisible: !params.repeatVisible,
      };
    });
  };

  const renderPassword = (value: any, inputHandler: (...args: any) => any, visibleHandler: (...args: any) => any, visibleTmp: boolean, placeholder?: string) => {
    placeholder = placeholder ?? t("password.placeholder");

    let suffix = <EyeInvisibleOutlined onClick={visibleHandler} />;
    let prefix = <LockOutlined />;
    let inputType = "password";
    let className = `${prefixCls}-input`;
    if (visibleTmp) {
      suffix = <EyeOutlined onClick={visibleHandler} />;
      prefix = <UnlockOutlined />;
      inputType = "text";
      className = "";
    }
    return (
      <Input
        maxLength={props.maxLength}
        className={className}
        prefix={prefix}
        suffix={suffix}
        value={value}
        onInput={inputHandler}
        placeholder={placeholder}
        autoComplete="off"
        style={{
          width: $tools.convert2Rem(props.width),
          height: $tools.convert2Rem(props.height),
          borderRadius: $tools.convert2Rem(props.radius),
          background: props.bgColor ?? "",
        }}
        type={inputType}
      />
    );
  };

  const renderPopover = () => {
    const strengthCls = getPrefixCls("password-strength", props.prefixCls);
    return (
      <div className={`${prefixCls}-tips`}>
        <div className={`${strengthCls}-item`}>
          <span>{t("password.strong")}</span>
          <div className={`${strengthCls}-group`}>
            <i className={`${strengthCls}${params.password.strength > 0 ? " active" : ""}`} />
            <i className={`${strengthCls}${params.password.strength > 1 ? " active" : ""}`} />
            <i className={`${strengthCls}${params.password.strength > 2 ? " active" : ""}`} />
            <i className={`${strengthCls}${params.password.strength > 3 ? " active" : ""}`} />
          </div>
          <span className="theme-color" dangerouslySetInnerHTML={{ __html: params.password.tips ?? "" }} />
        </div>
        <div className={`${strengthCls}-item`}>
          {params.password.length ? <CheckOutlined className="success" /> : <CloseOutlined className="failed" />}
          <span>{t("password.size", { min: props.minLength, max: props.maxLength })}</span>
        </div>
        <div className={`${strengthCls}-item`}>
          {params.password.format ? <CheckOutlined className="success" /> : <CloseOutlined className="failed" />}
          <span>{t("password.format")}</span>
        </div>
        {props.complexity ? (
          <div className={`${strengthCls}-item`}>
            {params.password.complexity ? <CheckOutlined className="success" /> : <CloseOutlined className="failed" />}
            <span>{tip}</span>
          </div>
        ) : null}
      </div>
    );
  };

  const renderRepeat = () => {
    return props.repeat ? (
      <Form.Item name="repeat" label={props.repeatLabel} rules={rules.repeat}>
        {renderPassword(params.form.validate.repeat, onRepeatInput, onRepeatVisible, params.repeatVisible, t("password.repeat"))}
      </Form.Item>
    ) : null;
  };

  const validateFields = async (fields: []) => {
    return await passwordFormRef.validateFields(fields);
  };

  useImperativeHandle(ref, () => ({
    validateFields,
  }));

  return (
    <>
      <Popover trigger="focus" placement="right" content={renderPopover()} overlayClassName={langCls}>
        <Form.Item name="password" label={props.passwordLabel} rules={rules.password}>
          {renderPassword(params.form.validate.password, onInput, onVisible, params.visible)}
        </Form.Item>
      </Popover>
      {renderRepeat()}
    </>
  );
});

// 设置默认值，保证 props的值不为undefined，避免报错
PasswordItem.defaultProps = {
  repeat: true,
  complexity: true,
  minLength: 6,
  maxLength: 32,
  height: 40,
  radius: 5,
  passwordLabel: "密码",
  repeatLabel: "确认密码",
};
export default PasswordItem;
