import React, { useContext, useEffect, useState } from "react";
import style from "./index.module.less";
import { Ava<PERSON>, Button, List } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import { useOss } from "@/hooks/useOss";
import { useLoginModal } from "@/hooks/useLoginModal";
import crudNotifies from "@/apis/platform/notifies";
import { INotifiesArrayType } from "@/typings/notifies.interface";

/* 会员仪表盘 */
export const UcenterDasboard = () => {
  const { state } = useContext<IContext>(useStoreContext());
  const loginFormModalHooks = useLoginModal();

  const gotoAuthLogin = () => {
    loginFormModalHooks.open();
  };
  /** TODO 数据接口未实现 */
  const messageData = ["1、系统11111111111111111111111122", "2、系统公告公告公告公告公告公告公告公告公告公告公告"];
  const loginName = !state.userLoginState ? "访客" : state.userData?.companyName;
  const [notifyData, setNotifyData] = useState<INotifiesArrayType>();

  useEffect(() => {
    initSystemNotifies();
  }, []);

  const initSystemNotifies = async () => {
    const [err, res] = await crudNotifies
      .getNotifies({ isTop: 1, page: 1, size: 2 })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return;
    }
    setNotifyData(res.data);
  };

  return (
    <div className={style.wrapper}>
      <div className="ucenter-dashboard">
        <div className="user">
          <div className="user-avatar-area">
            <span className="avatar">
              <Avatar size="large" src={useOss().generateOssFullFilepath(state?.userData?.avatar)} icon={<UserOutlined />} />
            </span>
            <div className="say-hi">
              <p className="hi-name" title={loginName}>
                Hi&nbsp;·&nbsp;{loginName}
              </p>
              <p className="welcome">欢迎来到光华易购</p>
            </div>
          </div>
          {!state.userLoginState ? (
            <div className="user-auth-btn">
              <Button type="primary" onClick={gotoAuthLogin}>
                快捷登录/注册
              </Button>
            </div>
          ) : (
            <div className="user-has-login">
              <a href={"/ucenter/member"} target="_blank">
                尊贵的会员「{state.userData?.nickname}」
              </a>
            </div>
          )}
          <div className="user-quick-nav">
            <a target="_blank" href={"/ucenter"} className="icon-subtitle">
              <span className="icon-img order" />
              <span className="subtitle">我的订单</span>
            </a>
            <a target="_blank" href={"/ucenter/trade/shopping-cart"} className="icon-subtitle">
              <span className="icon-img cart" />
              <span className="subtitle">我的购物车</span>
            </a>

            <a target="_blank" href={"/ucenter/footprint"} className="icon-subtitle">
              <span className="icon-img footprint" />
              <span className="subtitle">我的足迹</span>
            </a>
            <a target="_blank" href={"/ucenter/message"} className="icon-subtitle">
              <span className="icon-img im" />
              <span className="subtitle">消息管理</span>
            </a>
          </div>
        </div>
        {/* 系统公告 */}
        <div className="system-message">
          <List
            size="small"
            header={
              <div className="message-header">
                <span className="title">消息公告</span>
                <a className="more" href="/ucenter/message">
                  更多
                </a>
              </div>
            }
            bordered={false}
            dataSource={notifyData?.content || []}
            renderItem={item => (
              <List.Item className="message-item">
                <a href={`/notifies/${item.code}`}>
                  <span className="content-title" title={item.title}>
                    {item.title}
                  </span>
                  <span className="created-date">{item.createdDate?.split(" ")[0]}</span>
                </a>
              </List.Item>
            )}
          />
        </div>
      </div>
      {/* 快捷登录  */}
      <loginFormModalHooks.FormModal />
    </div>
  );
};
export default UcenterDasboard;
