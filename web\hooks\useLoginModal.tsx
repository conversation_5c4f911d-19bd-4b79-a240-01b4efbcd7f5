import React, { useState } from "react";
import type { ModalProps } from "antd";
import { message, Modal } from "antd";
import type { FormProps } from "antd/es/form";
import { ILoginPasswordParams, ILoginSmsParams } from "@/typings/auth.interface";
import { SYSTEM_LOGIN_TYPES } from "@/constants/system";
import { loginByPassword, loginBySms } from "@/apis/auth";
import { encrypt } from "@/utils/rsa-encrypt.util";
import { debounce } from "lodash";
import LoginForm from "@/components/LoginForm";

/** 登录弹窗hook */
export const useLoginModal = (modalProps: ModalProps = {}) => {
  const [visible, setVisible] = useState(false);

  const open = () => {
    setVisible(true);
  };

  const close = (a?: any, b?: any) => {
    setVisible(false);
  };

  const FormModal = (slotProps: FormProps) => {
    const [loading, setLoading] = useState(false);

    // 触发提交登录
    const onFinish = debounce(async (values: ILoginPasswordParams | ILoginSmsParams) => {
      setLoading(true);
      let response;
      if (values.loginType === SYSTEM_LOGIN_TYPES.PASSWORD.value) {
        values = values as ILoginPasswordParams;
        response = await loginByPassword({ phone: values.phone, password: encrypt(values.password), companyName: values.companyName ? values.companyName : undefined })
          .then(res => [null, res])
          .catch(err => [err, null]);
      } else if (values.loginType === SYSTEM_LOGIN_TYPES.SMS.value) {
        values = values as ILoginSmsParams;
        response = await loginBySms({ phone: values.phone, smsCode: values.smsCode, companyName: values.companyName ? values.companyName : undefined })
          .then(res => [null, res])
          .catch(err => [err, null]);
      } else {
        setLoading(false);
        message.error("未处理的登录类型");
        return;
      }

      const [err, res] = response;
      if (err) {
        setLoading(false);
        message.error(err?.data?.message || "手机号或密码错误，登录失败！");
        return;
      }
      message.success(res.message || "登录成功");

      // 页面刷新
      close();
      history.go(0);
    }, 300);

    return (
      <Modal
        centered={true}
        maskClosable={false}
        title={"快速登录"}
        width={350}
        open={visible}
        onCancel={close}
        footer={null}
        wrapClassName="modal-wrap"
        okText="提交"
        {...modalProps}
        bodyStyle={{ padding: "0" }}
      >
        <LoginForm {...slotProps} onFinish={onFinish} loading={loading} />
      </Modal>
    );
  };

  return {
    FormModal,
    open,
  };
};
