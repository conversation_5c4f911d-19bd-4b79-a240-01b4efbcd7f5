import { Controller, Get, Inject, Post, Query, Param, File } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IReSourcesService } from "@/service/platform/resources.service";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { CommonUtil } from "@/utils/common.util";

/** 私有资源上传-加载传控制器 */
@Controller("/api/platform/resources", { middleware: [AuthenticationMiddleware] })
export class ResourcesController extends BaseController {
  @Inject("ResourcesService")
  resourcesService: IReSourcesService;

  /**
   * 私有加载图片资源
   * @param params
   */
  @Get("/:type/image")
  async loadImage(@Param("type") type: number, @Query("filepath") filepath: string) {
    CommonUtil.quickValidPrefixImageIsIsLegal(filepath, type);
    const result = await this.resourcesService.getPrivateImage(filepath);
    this.ctx.set("content-type", result.headers["content-type"]);
    this.ctx.body = result.data;
  }

  /**
   * 私有文件加载-下载
   * @param params
   */
  @Get("/:type/file")
  async loadFile(@Param("type") type: number, @Query("filepath") filepath: string) {
    if (!CommonUtil.checkFilePathTypeIsLegal(type)) {
      throw new Error("资源参数不合法,资源加载失败");
    }
    // 判断资源
    const result = await this.resourcesService.getPrivateFile(filepath);
    this.ctx.set("content-type", result.headers["content-type"]);
    this.ctx.body = result.data;
  }

  /**
   * @desc 图片-上传
   */
  @Post("/:type/image")
  async uploadImage(@Param("type") type: number, @File("file") file) {
    if (!CommonUtil.checkFilePathTypeIsLegal(type)) {
      throw new Error("资源参数不合法");
    }
    // 根据配置，关联资源目录存放
    const resourceOptions = CommonUtil.getFilePathByIsLegalType(type);
    if (!resourceOptions) {
      throw new Error("上传资源配置不匹配，无法上传");
    }

    const res = await this.resourcesService.uploadPrivateImage({
      uploadFilepath: file.data,
      filed: "upload_file",
      path: resourceOptions.value,
    });

    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 文件-上传
   */
  @Post("/:type/file")
  async uploadFile(@Param("type") type: number, @File("file") file) {
    if (!CommonUtil.checkFilePathTypeIsLegal(type)) {
      throw new Error("资源参数不合法");
    }
    // 根据配置，关联资源目录存放
    const resourceOptions = CommonUtil.getFilePathByIsLegalType(type);
    if (!resourceOptions) {
      throw new Error("上传资源配置不匹配，无法上传");
    }
    console.log("resourceOptions: ", resourceOptions);

    const res = await this.resourcesService.uploadPrivateFile({
      uploadFilepath: file.data,
      filed: "upload_file",
      path: resourceOptions.value,
    });
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }
}
