import { MidwayAppInfo, MidwayConfig } from "@midwayjs/core";

export default (appInfo: MidwayAppInfo): MidwayConfig => {
  return {
    // use for cookie sign key, should change to your own and keep security
    keys: "ghmall_ProdDy8EborjRY2ZCmKRlFK4IeF60JYBjOgFH4qB4p9iyFU=",
    // 配置静态文件
    // 日志配置
    midwayLogger: {
      default: {
        dir: process.env.MIDWAYJS_LOG_DIR || "./logs",
        maxSize: "30m",
        maxFiles: "31d",
        level: "info",
        consoleLevel: "warn",
      },
      clients: {
        coreLogger: {
          level: "warn",
          consoleLevel: "warn",
        },
        appLogger: {
          level: "info",
          consoleLevel: "info",
          format: info => {
            return `${info.timestamp} ${info.LEVEL} ${info.pid} ${info.labelText}${info.message}`;
          },
        },
        // 自定义业务日志
        businessLogger: {
          fileLogName: "business.log",
          level: "info",
          consoleLevel: "info",
          format: info => {
            return `${info.timestamp} ${info.LEVEL} ${info.pid} ${info.labelText} ${info.message}`;
          },
          contextFormat: info => {
            // 不生效，需要配置KOA上下文日志
            const ctx = info.ctx;
            return `${info.timestamp} ${info.LEVEL} ${info.pid} [${Date.now() - ctx.startTime}ms ${ctx.method}] ${ctx.logger_unq_id} ${info.message}`;
          },
        },
      },
    },
    koa: {
      contextLoggerFormat: info => {
        const ctx = info.ctx;
        return `${info.timestamp} ${info.LEVEL} ${info.pid} [${ctx.userId} - ${ctx.request_ip} - ${Date.now() - ctx.startTime}ms ${ctx.method}] ${ctx.logger_unq_id}  ${info.message}`;
      },
    },
  };
};
