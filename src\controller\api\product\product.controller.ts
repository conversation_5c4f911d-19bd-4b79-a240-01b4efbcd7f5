import { Controller, Get, Inject, Param, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IProductService } from "@/service/product/product.service";
import { productQueryListDto } from "~/typings/data/product/product";

@Controller("/api/products")
export class ProductController extends BaseController {
  @Inject("ProductService")
  productService: IProductService;

  @Get()
  async getList(@Query() criteria: Partial<productQueryListDto>) {
    const { ctx } = this;
    const res = await this.productService.getPageList(criteria);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 产品详情
   *
   * @param productNo 产品编号
   */
  @Get("/:product_no")
  async detail(@Param("product_no") productNo: number | string) {
    const { ctx } = this;
    const res = await this.productService.detail(productNo);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).sendSuccess();
  }
}
