import React, { memo, ReactNode } from "react";
import { Descriptions } from "antd";

interface IDescriptionItemType {
  label?: string;
  content?: ReactNode;
  span?: number;
  labelStyle?: object;
  contentStyle?: object;
}

interface IDescriptionsProps {
  title?: ReactNode;
  data?: IDescriptionItemType[];
  layout?: "horizontal" | "vertical";
  labelStyle?: object;
  contentStyle?: object;
  bordered?: boolean;
  column?: number;
  className2?: string;
  extra?: ReactNode;
}

/**
 * 描述组件-自定义封装
 * @params data 描述信息数据源
 */
export default memo(function TmDescriptions(props: IDescriptionsProps) {
  const {
    data,
    title,
    extra,
    className2 = "",
    column = 1,
    layout = "horizontal",
    bordered = true,
    labelStyle = { width: "130px", padding: "14px 16px", color: "#6D7278", border: "1px solid rgb(230, 235, 245)", background: "#f2f2f2" },
    contentStyle = { padding: "14px 16px", color: "#333333", border: "1px solid rgb(230, 235, 245)", background: "#ffffff" },
  } = props;

  return (
    <Descriptions title={title} className={`tm-descriptions ${className2}`} layout={layout} column={column} bordered={bordered} labelStyle={labelStyle} contentStyle={contentStyle} extra={extra}>
      {data?.map((item, index) => (
        <Descriptions.Item key={index} span={item.span} label={item.label} labelStyle={item.labelStyle} contentStyle={item.contentStyle}>
          {item.content}
        </Descriptions.Item>
      ))}
    </Descriptions>
  );
});
