import { Provide, Scope, <PERSON>opeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IAddressService } from "@/service/member/address.service";
import { addressCreateDto, addressQueryListDto, addressUpdateDto } from "~/typings/data/member/address";

@Provide("AddressService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class AddressServiceImpl extends BaseService implements IAddressService {
  /**
   * 获取订单分页列表
   *
   * @param memberId 会员ID
   * @param criteria 条件
   */
  async getPageList(memberId: string, criteria: Partial<addressQueryListDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/${memberId}/addresses`, criteria), "获取数据出错了，请稍后再试！");
  }

  async create(memberId: string, resource: addressCreateDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post(`/api/members/${memberId}/addresses`, resource), "获取数据出错了，请稍后再试！");
  }

  async delete(memberId: string, addressId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete(`/api/members/${memberId}/addresses/${addressId}`), "获取数据出错了，请稍后再试！");
  }

  async setDefault(memberId: string, addressId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.put(`/api/members/${memberId}/addresses/${addressId}/default`), "获取数据出错了，请稍后再试！");
  }

  async show(memberId: string, addressId: number): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/members/${memberId}/addresses/${addressId}`), "获取数据出错了，请稍后再试！");
  }

  async update(resource: addressUpdateDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.put(`/api/members/${resource.memberId}/addresses/${resource.addressId}`, resource), "获取数据出错了，请稍后再试！");
  }
}
