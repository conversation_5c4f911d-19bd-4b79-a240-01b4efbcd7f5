type IStorage = "localStorage" | "sessionStorage";

export class StorageUtil {
  private static instance: StorageUtil;
  private static type: IStorage;

  static getInstance(type?: IStorage) {
    if (!this.instance) {
      this.instance = new StorageUtil(type);
    }
    return this.instance;
  }

  private constructor(type?: IStorage) {
    StorageUtil.type = type || "localStorage";
  }

  public get(name: string) {
    if (StorageUtil.type === "localStorage") {
      const data = localStorage.getItem(name) || "";
      let newData;
      try {
        newData = JSON.parse(data);
      } catch (error) {
        newData = data;
      }
      return newData;
    } else {
      const data = sessionStorage.getItem(name) || "";
      let newData;
      try {
        newData = JSON.parse(data);
      } catch (error) {
        newData = data;
      }
      return newData;
    }
  }

  public set(name: string, data: any) {
    let newData;
    if (typeof data === "string") {
      newData = data || "";
    } else if (typeof data === "object") {
      newData = JSON.stringify(data) || "";
    } else if (typeof data === "number") {
      newData = data || 0;
    } else {
      newData = "";
    }
    if (StorageUtil.type === "localStorage") {
      return localStorage.setItem(name, newData);
    } else {
      return sessionStorage.setItem(name, newData);
    }
  }

  public remove(name: string) {
    if (StorageUtil.type === "localStorage") {
      return localStorage.removeItem(name);
    } else {
      return sessionStorage.removeItem(name);
    }
  }

  public clear() {
    if (StorageUtil.type === "localStorage") {
      localStorage.clear();
    } else {
      sessionStorage.clear();
    }
  }
}
export const $localStorage: StorageUtil = StorageUtil.getInstance();

/** 客户-token 键 */
export const MEMBER_TOKEN = "MEMBER_TOKEN";
/** 客户-信息 键 */
export const MEMBER_INFO = "MEMBER_INFO";
/** 历史路由-地址 键 */
export const HISTORY_URL = "HISTORY_URL";
/** 网站语言 键 */
export const WEBSITE_LANGUAGE = "WEBSITE_LANGUAGE";
/** 图像验证码缓存键 */
export const CAPTCHA_CACHE = "GH_CAPTCHA_CACHE";
/** 足迹记录-本地缓存键 */
export const FOOTPRINT_CACHE = "FOOT_PRINT";
/** 历史搜索缓存键 */
export const SEARCH_HISTORY = "SEARCH_HISTORY";
