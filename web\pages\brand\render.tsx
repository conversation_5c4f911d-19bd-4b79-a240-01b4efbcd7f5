import React, { useContext, useEffect, useRef, useState } from "react";
import { SProps } from "ssr-types-react";
import style from "./index.module.less";
import { IContext } from "ssr-types";
import { useStoreContext } from "ssr-common-utils";
import { Button, Popover } from "antd";
import { useOss } from "@/hooks/useOss";
import img20180825110215945 from "@@img/brand/20180825110215945.png";
import img20181109030213705 from "@@img/brand/20181109030213705.png";
import img2018110902574888 from "@@img/brand/2018110902574888.png";
import img20181109025721847 from "@@img/brand/20181109025721847.png";
import img20181109025733260 from "@@img/brand/20181109025733260.png";
import img20181109025746381 from "@@img/brand/20181109025746381.png";
import img2018110902584065 from "@@img/brand/2018110902584065.png";
import img20181109025815137 from "@@img/brand/20181109025815137.png";
import img20180825110152868 from "@@img/brand/20180825110152868.png";
import img20180825110132451 from "@@img/brand/20180825110132451.png";
import img20180825110120595 from "@@img/brand/20180825110120595.png";
import nimg70_1 from "@@img/brand/nimg70_1.png";

export default function Brand(props: SProps) {
  const myRef = useRef(null);
  const { state } = useContext<IContext>(useStoreContext());
  const [selfOwnedBrands, setSelfOwnedBrands] = useState<any>([]);
  const [proxyBrands, setProxyBrands] = useState<any>([]);
  const useOssHook = useOss();

  useEffect(() => {
    // 初始化：自主品牌、代理品牌
    if (state.layoutInitData?.hotBrandIntroduce) {
      state.layoutInitData.hotBrandIntroduce?.sort((a, b) => a.sort_order - b.sort_order);
      setSelfOwnedBrands(state.layoutInitData.hotBrandIntroduce.filter(brand => brand.brand_type === "owner"));
      setProxyBrands(state.layoutInitData.hotBrandIntroduce.filter(brand => brand.brand_type === "proxy"));
    }
  }, []);

  /** 弹窗品牌信息 */
  const handleOpenPreview = item => {
    // window.open(moreLink);
  };

  const handleTest = e => {
    e.stopPropagation();
    return false;
  };

  return (
    <>
      <div className={style.wrapper}>
        <div className="brand-wrap">
          <div className="section section-1">
            <div className="section-header">
              <h2 className="section-header-title">光华科技企业品牌</h2>
            </div>
            <div className="section-body">
              <div className="image-area">
                <img src={img20180825110215945} alt="光华科技企业品牌" />
              </div>
              <div className="text-area">
                <p>
                  <img alt="" src={img20181109030213705} style={{ width: "30px", height: "15px" }} />
                  ——取自“光华”两字拼音首字母；
                  <img alt="" src={img2018110902574888} style={{ width: "50px", height: "16px" }} />
                  ——取自英文“technology”，代表“科技”。
                  <img alt="" src={img20181109025721847} style={{ width: "45px", height: "17px" }} />
                  寓意光华科技以科技创新作为企业核心竞争力。
                  <img alt="" src={img20181109025733260} style={{ width: "45px", height: "17px" }} />
                  统领旗下
                  <img alt="" src={img20181109025746381} style={{ height: "17px", width: "34px" }} />、
                  <img alt="" src={img2018110902584065} style={{ width: "47px", height: "17px" }} />、
                  <img alt="" src={img20181109025815137} style={{ height: "17px", width: "26px" }} />
                  产品品牌。
                </p>
              </div>
            </div>
          </div>

          <div className="section section-2">
            <div className="section-header">
              <h2 className="section-header-title">
                <img src={nimg70_1} alt="" />
                旗下产品品牌
              </h2>
            </div>
            <div className="section-body">
              <div className="list">
                <ul>
                  <li>
                    <div className="ico">
                      <img src={img20180825110152868} alt="金属化合物、表面处理添加剂、锂电材料、其他专用化学品" />
                    </div>
                    <div className="name">金属化合物、表面处理添加剂、锂电材料、其他专用化学品</div>
                    <div className="msg">“广东省著名商标”，产品被认定为“国家重点新产品”、“ 广东省自主创新产品”、“ 广东省高新技术产品”、“ 广东省名牌产品”。</div>
                    <a href="/search?brandId=951" className="button">
                      查看品牌相关产品
                    </a>
                  </li>
                  <li>
                    <div className="ico">
                      <img src={img20180825110132451} alt="PCB湿制程药水" />
                    </div>
                    <div className="name">PCB湿制程药水</div>
                    <div className="msg">PCB行业知名品牌，为PCB湿制程提供深度配套的药水产品，大部分产品被认定为广东省高新技术产品，部分产品技术指标达到国际领先水平。</div>
                  </li>
                  <li>
                    <div className="ico">
                      <img src={img20180825110120595} alt="化学试剂" />
                    </div>
                    <div className="name">化学试剂</div>
                    <div className="msg">“广东省著名商标”，产品以通用无机试剂、通用有机试剂、特效试剂、其他专用化学品为主，共计超过1300多个品种，2500多个品规。</div>
                    <a href="/search?brandId=941" className="button">
                      查看品牌相关产品
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="section section-2">
            <div className="section-header">
              <h2 className="section-header-title">代理品牌专区</h2>
            </div>
            <div className="section-body">
              <ul className="proxy">
                {proxyBrands.map((item, index) => (
                  <li key={index}>
                    <Popover placement="topLeft" content={<div style={{ width: "360px", minHeight: "60px" }}>{item.description}</div>}>
                      <a href={item?.link || `/search?keyword=&brandId=${item.brand_id}`}>
                        {item.logo_path ? <img src={useOssHook.generateOssFullFilepath(item.logo_path)} className="category-img" alt="brand-img" /> : <div className="category-img">{item.title}</div>}
                        <div className="category-item-mask">
                          <span>{item.title}</span>
                          <Button size="small" danger ghost onClick={handleTest}>
                            查看
                          </Button>
                        </div>
                      </a>
                    </Popover>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* <div className="brand" ref={myRef}>
          <div className="item-box">
            <div className="title">自主品牌专区</div>
            <ul className="content company">
              {selfOwnedBrands.map((item, idx) => {
                return (
                  <React.Fragment key={idx}>
                    <li>
                      <a href={item?.link || `/search?keyword=&brandId=${item.brand_id}`}>
                        <div className="image-box">
                          <img src={useOssHook.generateOssFullFilepath(item.logo_path)} />
                          <div className="intro-hover-box">
                            <span>{item.title}</span>
                            <Button size="small" danger ghost onClick={handleTest}>
                              查看
                            </Button>
                          </div>
                        </div>
                      </a>
                      <a href={item?.link || `/search?keyword=&brandId=${item.brand_id}`}>
                        <div className="intro-box" onClick={() => handleOpenPreview(item)}>
                          <div className="title">{item.title}</div>
                          <div className="describe">{item.description}</div>
                        </div>
                      </a>
                    </li>
                  </React.Fragment>
                );
              })}
            </ul>
          </div>
          <div className="item-box clearfix">
            <div className="title">代理品牌专区</div>
            
          </div>
        </div> */}
      </div>
    </>
  );
}
