@linkColor: #ff1b1b;

.wrapper {
  :global {
    .title-wrapper {
      width: 100%;
      display: inline-flex;
      justify-content: space-between;
      .btn-wrapper {
        margin-right: 14px;
        .flex();
        gap: 16px;
        .btn-clear {
          border-radius: 2px;
        }
      }
    }
    .favorites {
      // 表格-行
      .item-custom-table {
        .brand,
        .sku {
          cursor: pointer;
          &:hover {
            color: @linkColor;
            text-decoration: underline;
          }
        }
        .sku,
        .price {
          flex: 1;
          text-align: center;
          color: @linkColor;
          .ellipsis(1);
        }
        .action {
          flex: 1;
          .flex-row(center, center);
          gap: 12px;
        }
      }

      .empty-tip {
        color: #6d7278;
        font-size: 14px;
      }
    }
  }
}
