import { Body, Controller, Inject, Post } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IOrderService } from "@/service/order/order.service";
import { OrderLogisticsQueryDto } from "@/dto/order-logistics-query.dto";
import { Validate } from "@midwayjs/validate";

@Controller("/api/order-logistics")
export class OrderLogisticsController extends BaseController {
  @Inject("OrderService")
  orderService: IOrderService;

  /**
   * @desc 查询订单物流信息
   */
  @Post()
  @Validate()
  async queryDelivery(@Body() orderLogisticsQueryDto: OrderLogisticsQueryDto) {
    const { ctx } = this;
    const res = await this.orderService.queryDelivery(orderLogisticsQueryDto);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }
}
