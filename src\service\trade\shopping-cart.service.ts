import { ICreate, IDelete, ISelectedAll, ISelectedOrNot, IUpdate } from "~/typings/data/trade/shopping-cart";

export interface IShoppingCartService {
  getList: (memberId: string) => Promise<any>;

  create: (params: ICreate[]) => Promise<any>;

  update: (params: IUpdate) => Promise<any>;

  delete: (params: IDelete) => Promise<any>;

  selectedOrNot: (params: ISelectedOrNot) => Promise<any>;

  selectedAll: (params: ISelectedAll) => Promise<any>;

  getSelectedDetailList: (memberId: string, way?: string) => Promise<any>;
}
