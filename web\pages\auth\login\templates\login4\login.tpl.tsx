import React, { memo } from "react";
import SimpleLayout from "@/components/layout/SimpleLayout";
import style from "./index.modules.less";
import { Carousel } from "antd";
import { DebouncedFunc } from "lodash";
import { ILoginPasswordParams, ILoginSmsParams } from "@/typings/auth.interface";
import LoginForm from "@/components/LoginForm";

interface Props {
  defaultForm?: ILoginPasswordParams | ILoginSmsParams;
  onFinish: DebouncedFunc<(values: any) => Promise<void>>;
  loginLoading?: boolean;
}

/** 背景图轮播-类型 */
interface bannerItem {
  url: string;
  imgPath: string;
  id: number;
  index: number;
}

export default memo(function LoginTpl(props: Props) {
  const { defaultForm, onFinish, loginLoading } = props;
  // 动态背景图
  const banners: bannerItem[] = [
    {
      url: "/",
      imgPath: `/images/login/login-bg1.jpg`,
      id: 1,
      index: 1,
    },
  ];

  return (
    <SimpleLayout>
      <div className={style.wrapper}>
        <div className="login-container">
          {/* 背景轮播 */}
          <Carousel effect="fade" autoplaySpeed={20000} autoplay className="login-carousel">
            {banners.map(item => (
              <div key={item.index} className="login-carousel-item">
                <img src={item.imgPath} alt="" />
              </div>
            ))}
          </Carousel>

          <div className="main-width">
            <LoginForm defaultForm={defaultForm} className2={"login-wrap"} onFinish={onFinish} loading={loginLoading} />
          </div>
        </div>
      </div>
    </SimpleLayout>
  );
});
