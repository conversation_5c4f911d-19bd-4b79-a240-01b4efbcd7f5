@contentLinkColor: #e02020;
@descriptionLinkColor: #0091ff;
@formDottedBorderColor: #c1c1c1;

.orderWrapper {
  :global {
    /*搜索区域*/
    .search-container {
      margin: 8px 0;
      .ant-form-item-label > label {
        height: 31px;
      }
      .date-range-picker {
        .ant-picker-range {
          width: 225px;
          //兼容IE11
          _:-ms-fullscreen,
          & {
            input {
              min-height: 22px;
            }
          }
        }
      }

      .order-no,
      .order-state {
        input {
          width: 150px;
        }
      }
    }

    /*内容区域*/
    .content-container {
      .order-content-item {
        margin-bottom: 20px;

        .ant-card-body {
          padding: 16px;
        }

        // 表格单元格-省略号设置
        .text-ellipsis {
          .ellipsis();
        }

        // 产品数量标题
        .quantity-row {
          padding: 8px;
          .quantity-header-title {
            .flex-row(space-between, center);
            font-size: 12px;
          }

          .quantity-body-title {
            padding: 0 16px;
            .flex-row(space-between, center);
          }
        }

        .quantity-header-tip {
          list-style: none;
        }

        // 产品包装
        .product-packing-unit {
          font-size: 12px;
        }

        .product-name {
          a {
            color: #eb6262;
          }
        }
        .product-detail-link {
          &:hover {
            color: @mallThemeColor;
            text-decoration: underline;
          }
        }
        .product-common-link {
          color: @mallThemeColor;
        }
        .item-state {
          font-weight: bold;
          color: #e02020;
          font-size: @font-size-16;
          cursor: pointer;
          border-bottom: 1px solid #e02020;
        }
        .item-title {
          color: #6d7278;

          &-label {
            margin-right: 36px;
            .sn {
              color: #333333;
              font-size: @font-size-14;
            }
          }
        }

        //描述
        .table-summary-custom {
          .invoice {
            font-size: @font-size-16;
            font-weight: 400;
            color: #6d7278;
            i {
              color: #f7b500;
            }
          }
          .summary-cell {
            &:last-child {
              text-align: left;
            }
          }
          i.showWeight {
            font-weight: bold;
            color: #e02020;
          }
        }
        .table-footer-custom {
          //动作
          &-action {
            text-align: right;
            margin-top: 15px;
            .ant-btn {
              width: 136px;
              height: 36px;
              border-radius: 18px;
              margin-left: 16px;

              span {
                font-weight: 400;
                font-size: 14px;
              }
            }

            .del {
              color: #e02020;
              border: 1px solid #e02020;

              &:hover {
                background: #ffe8e8;
              }
            }

            .detail {
              color: #fff;
              border: 1px solid #e5e5e5;
              background: linear-gradient(168deg, #ff7474 0%, #ff1b1b 100%);

              &:hover {
                background: linear-gradient(90deg, #ffbebe 0%, #ff5d5d 100%);
              }
            }
          }
        }
      }
      &-pagination {
        .flex-row(flex-end,center);
      }
      /*antd table*/
      .ant-table-content,
      .ant-table-footer {
        background: transparent;
      }
      .ant-card-head {
        min-height: 32px;
        .ant-card-head-wrapper {
          height: 32px;
        }
      }
      .ant-table-thead > tr > th {
        background: transparent;
      }

      // 子列表控制
      .item-custom-table {
        .remark {
        }
      }
    }

    /*取消原因*/
    .cancel-reason-modal {
      .reason-form {
        .ant-form-item-control {
          border: 1px dotted @formDottedBorderColor;
          padding: 4px 16px;
          .cancel-reason-group {
            display: flex;
            flex-direction: column;
          }
        }
      }
      .ant-modal-confirm-btns {
        text-align: center;
      }
    }
  }
}

:global {
  .red-tip {
    color: @contentLinkColor;
  }
  .blue-tip {
    color: @descriptionLinkColor;
  }
}
