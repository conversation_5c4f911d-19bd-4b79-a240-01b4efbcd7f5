import { Config, FORMAT, Inject, Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { IAuthService } from "@/service/auth.service";
import { BaseService } from "@/common/base/base.service";
import { JwtService } from "@midwayjs/jwt";
import jwtConstant from "@/common/constants/jwt.constant";
import { ILoginPasswordParams, ILoginSmsParams } from "~/typings/data/login";
import authConstant from "@/common/constants/auth.constant";
import { Context } from "@midwayjs/koa";
import { bsLoginDto } from "@/dto/auth.dto";

@Provide("AuthService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class AuthServiceImpl extends BaseService implements IAuthService {
  @Inject()
  jwtService: JwtService;

  @Config("jwt")
  jwtConfig;

  @Config("isProduction")
  isProduction;

  /**
   * 通过账号密码登录
   * @param data 请求参数
   */
  async loginByPassword(data: ILoginPasswordParams): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/auth/login", data), "账号或密码错误！");
  }

  /**
   * 通过短信验证码登录
   * @param data 请求参数
   */
  async loginBySms(data: ILoginSmsParams): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/auth/sms-login", data), "登录出错了，请稍后重试！");
  }

  /**
   * 注册
   *
   * @param data /
   */
  async register(data: object): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/auth/register", data), "注册失败，请稍后再试！");
  }

  /**
   * 通过邮箱注册
   *
   * @param data /
   */
  async registerByEmail(data: object): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/auth/email-register", data), "注册失败，请稍后再试！");
  }

  /**
   * 重置密码
   *
   * @param data /
   */
  async passwordReset(data: object): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/auth/find-pwd", data), "重置密码失败了，请稍后再试！");
  }

  /**
   * <p>通过邮箱重置密码</p>
   *
   * @param data /
   */
  async passwordResetByEmail(data: object): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/auth/email-find-pwd", data), "重置密码失败了，请稍后再试！");
  }

  /**
   * 通过手机号获取用户信息
   *
   * @param phone 手机号
   */
  async getMemberInfo(phone: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/auth/info?phone=${phone}`), "获取客户信息失败！");
  }

  /**
   * 通过手机号跟公司名称获取用户信息
   *
   * @param phone 手机号
   * @param companyName 公司名称
   */
  async getAccountInfo(phone: string, companyName: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/auth/accountInfo?phone=${phone}&companyName=${companyName}`), "获取客户信息失败！");
  }

  /**
   * 根据手机号获取全部用户信息
   *
   * @param phone 手机号
   */
  async getAccountInfoByPhone(phone: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/auth/allAccountInfoByPhone?phone=${phone}`), "获取客户信息失败！");
  }

  /**
   * 生成token
   *
   * @param payload
   */
  async genJwtToken(payload: any): Promise<any> {
    const accessToken = await this.jwtService.sign(payload);
    const refreshToken = await this.jwtService.sign(payload, {
      expiresIn: jwtConstant.token_expires_in,
    });
    // 写入到redis
    let tokenExpiresIn = this.jwtConfig.expiresIn;
    if (tokenExpiresIn.endsWith("d")) {
      tokenExpiresIn = 60 * 60 * 24 * parseInt(tokenExpiresIn.trimEnd("d"));
    } else {
      tokenExpiresIn = 60 * 60 * 24;
    }
    const memberId = payload?.member_id;
    await this.redisService.set(`${jwtConstant.online_token}${memberId}`, accessToken, "EX", tokenExpiresIn);
    return {
      accessToken,
      refreshToken,
    };
  }

  async logout(jwtToken: string): Promise<any> {
    const payload: any = this.jwtService.decode(jwtToken);
    const memberId = payload?.member_id || 0;
    await this.redisService.del([`${jwtConstant.online_token}${memberId}`]);
  }

  async getRedisJwtToken(memberId: string): Promise<any> {
    return this.redisService.get(`${jwtConstant.online_token}${memberId}`);
  }

  async parseJwtTokenToPayload(jwtToken: string): Promise<any> {
    return this.jwtService.decode(jwtToken);
  }

  async saveOrUpdateSession(ctx: Context, member: any): Promise<any> {
    // 写入session
    ctx.session.userLoginState = true;
    ctx.session.userData = member;
    ctx.session.maxAge = FORMAT.MS.ONE_DAY * authConstant.AUTH_LOGIN_EXPIRE_DAY;
    this.isProduction && ctx.rotateCsrfSecret();
  }

  /**
   * <p>通过授权code登录</p>
   *
   * @param dto /
   */
  async loginByAuthCode(dto: bsLoginDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/auth/one-click-login", dto));
  }
}
