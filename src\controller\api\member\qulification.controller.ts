import { Body, Controller, Get, Inject, Param, Post, Put } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { Validate } from "@midwayjs/validate";
import { IQualificationService } from "@/service/member/qualification.service";
import { qualificationAddDto, qualificationUpdateDto } from "~/typings/data/member/qualification";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";

@Controller("/api/ucenter/qualification", { middleware: [AuthenticationMiddleware] })
export class QualificationController extends BaseController {
  @Inject("QualificationService")
  qualificationService: IQualificationService;

  /**
   * @desc 获取客户资质信息
   */
  @Get()
  async getMemberQualifications() {
    const res = await this.qualificationService.getMemberQualifications(this.getMemberId());
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res?.data).send();
  }

  /**
   * @desc 新增
   */
  @Post()
  @Validate()
  async add(@Body() resource: qualificationAddDto) {
    resource.memberId = this.getMemberId();
    const res = await this.qualificationService.addQualification(resource);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 更新
   */
  @Put("/:id")
  @Validate()
  async update(@Param("id") id: number, @Body() resource: qualificationUpdateDto) {
    const res = await this.qualificationService.updateQualification(id, resource);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }
}
