import simpleRequest from "@/utils/simple.request.util";

export default abstract class AbstractStockStrategy implements IStockStrategy {
  abstract query(sku: string, quantity: number | undefined): Promise<any>;

  /**
   * 发起 post 请求
   *
   * @param url 请求URL
   * @param data /
   */
  public async post(url: string, data: object): Promise<any> {
    return simpleRequest({
      url: url,
      method: "POST",
      responseType: "json",
      data: data,
    });
  }

  /**
   * 爬虫获取网页处理
   *
   * @param url /
   * @param headers /
   */
  public async crawlerGet(url: string, headers?: object) {
    // todo 此处写发起爬虫请求
  }
}
