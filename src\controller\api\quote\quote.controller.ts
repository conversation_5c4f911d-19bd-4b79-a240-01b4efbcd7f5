import { Controller, Get, Post, Put, Delete, Body, Param, Query, Inject, File } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { IQuoteService } from "@/service/quote/quote.service";
import { 
  QuoteRequestCreateDto, 
  QuoteFileUploadDto, 
  QuoteProductUpdateDto, 
  GenerateQuoteDto, 
  CreateQuickOrderFromQuoteDto,
  QuoteQueryDto 
} from "@/dto/quote-request.dto";
import { Validate } from "@midwayjs/validate";

@Controller("/api/quote-requests", { middleware: [AuthenticationMiddleware] })
export class QuoteController extends BaseController {
  @Inject("QuoteService")
  quoteService: IQuoteService;

  /**
   * @desc 创建询报价单
   */
  @Post()
  @Validate()
  async createQuoteRequest(@Body() data: QuoteRequestCreateDto) {
    const memberId = this.getMemberId();
    const res = await this.quoteService.createQuoteRequest(data, memberId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 文件上传并匹配产品
   */
  @Post("/upload-and-match")
  @Validate()
  async uploadFileAndMatchProducts(@Body() data: QuoteFileUploadDto, @File("file") file) {
    if (!file) {
      throw new Error("请选择要上传的文件");
    }

    // 验证文件类型
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = file.filename.toLowerCase().substring(file.filename.lastIndexOf('.'));
    if (!allowedTypes.includes(fileExtension)) {
      throw new Error("只支持上传 Excel 文件（.xlsx, .xls）");
    }

    // 验证文件大小（限制为5MB）
    if (file.data.length > 5 * 1024 * 1024) {
      throw new Error("文件大小不能超过 5MB");
    }

    const memberId = this.getMemberId();
    const res = await this.quoteService.uploadFileAndMatchProducts(
      file.data,
      file.filename,
      data,
      memberId
    );
    
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 获取询报价单列表
   */
  @Get()
  async getQuoteRequestList(@Query() criteria: QuoteQueryDto) {
    const memberId = this.getMemberId();
    const res = await this.quoteService.getQuoteRequestList(criteria, memberId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 获取询报价单详情
   */
  @Get("/:id")
  async getQuoteRequestDetail(@Param("id") quoteRequestId: number) {
    const memberId = this.getMemberId();
    const res = await this.quoteService.getQuoteRequestDetail(quoteRequestId, memberId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 更新询报价产品
   */
  @Put("/:id/products")
  @Validate()
  async updateQuoteProducts(
    @Param("id") quoteRequestId: number,
    @Body() data: { products: QuoteProductUpdateDto[] }
  ) {
    const memberId = this.getMemberId();
    const res = await this.quoteService.updateQuoteProducts(
      quoteRequestId,
      data.products,
      memberId
    );
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 生成报价单
   */
  @Post("/:id/generate-quote")
  @Validate()
  async generateQuote(@Param("id") quoteRequestId: number, @Body() data: GenerateQuoteDto) {
    const memberId = this.getMemberId();
    const res = await this.quoteService.generateQuote(
      quoteRequestId,
      data.products,
      data.validDays,
      data.remark,
      memberId
    );
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 从询报价单创建快速订单
   */
  @Post("/:id/create-order")
  @Validate()
  async createQuickOrderFromQuote(
    @Param("id") quoteRequestId: number,
    @Body() data: CreateQuickOrderFromQuoteDto
  ) {
    const memberId = this.getMemberId();
    const res = await this.quoteService.createQuickOrderFromQuote(
      quoteRequestId,
      data.selectedProductIds,
      {
        receivingAddressId: data.receivingAddressId,
        receiptId: data.receiptId,
        remark: data.remark,
        sendType: data.sendType
      },
      memberId
    );
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 删除询报价单
   */
  @Delete("/:id")
  async deleteQuoteRequest(@Param("id") quoteRequestId: number) {
    const memberId = this.getMemberId();
    const res = await this.quoteService.deleteQuoteRequest(quoteRequestId, memberId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 产品匹配接口（用于手动匹配）
   */
  @Get("/match-product")
  async matchProduct(
    @Query("productName") productName: string,
    @Query("sku") sku: string,
    @Query("quantity") quantity: number = 1
  ) {
    const res = await this.quoteService.matchProduct(productName, sku, quantity);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }
}
