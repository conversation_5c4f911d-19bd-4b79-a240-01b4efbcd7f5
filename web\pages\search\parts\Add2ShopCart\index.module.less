.wrapper {
  :global {
    position: relative;
    // 购买数操作
    .purchase-shop-cart {
      .flex-row(flex-start, center);
      gap: 2px;
      .btn {
        .flex-center(row);
        width: 24px;
        height: 24px;
        line-height: 24px;
        background: #ffffff;
        border: 1px solid rgb(221, 221, 221);
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          background-color: #ff1b1b !important;
          border: 1px #ff1b1b !important;
          border-radius: 2px !important;
          color: #fff;
        }
        &:active {
          background-color: #ff7474 !important;
        }
      }
      .btn-add2cart {
        width: auto;
        background-color: #ff7474 !important;
        border: 1px #ff1b1b !important;
        border-radius: 2px !important;
        color: #fff;
      }
      .input-box {
        width: 64px;
        border: 1px solid rgb(221, 221, 221);
        border-radius: 2px;
        .ant-input-number-input {
          height: 24px;
          text-align: center;
        }
      }
    }
    //库存
    .inventory-box {
      margin-top: 5px;
    }
  }
}
