import { Controller, Get, Inject } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IProductCategoryService } from "@/service/product/category.service";

@Controller("/api/category")
export class CategoryController extends BaseController {
  @Inject("ProductCategoryService")
  productCategoryService: IProductCategoryService;

  /**
   * 产品分类层联接口
   */
  @Get("/cascade")
  async cascade() {
    const { ctx } = this;
    const result = await this.productCategoryService.cascade();
    return ctx.getResponseInstance(ctx).setResponseData(result).send();
  }
}
