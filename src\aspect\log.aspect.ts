/**
 * @description 日志拦截切片
 * <AUTHOR>
 * @date 2022/05/13
 */
import { Aspect, IMethodAspect, JoinPoint, Provide } from "@midwayjs/decorator";
import { httpError } from "@midwayjs/core";
import { AuthController as ApiAuthController } from "@/controller/api/auth.controller";

@Aspect([ApiAuthController], "!getCaptchaCode", 1)
@Provide()
export class LogAspect implements IMethodAspect {
  private readonly BEFORE_REQUEST = "「BEFORE_REQUEST」";
  private readonly AFTER_REQUEST = "「AFTER_REQUEST」";
  private readonly LOG_ERROR = "「LOG_ERROR」";

  async around(point: JoinPoint) {
    const startTime = Date.now();
    const ctx = point.target.ctx;
    const customLogger = ctx.getLogger("customLogger");
    customLogger.info("%s %j", this.BEFORE_REQUEST, LogAspect.requestBeforeLog(point));
    const result = await point.proceed(...point.args); // 执行原方法
    const endTime = Date.now();
    customLogger.info("%s %j", this.AFTER_REQUEST, LogAspect.requestAfterLog(result, endTime - startTime));
    return result;
  }

  async after(point: JoinPoint, result, error) {
    if (error) {
      if (await /not found/.test(error.message)) {
        throw new httpError.NotFoundError("找不到请求！");
      }
      if (error?.response?.status >= 500) {
        console.error("日志拦截响应：", error);
        const ctx = point.target.ctx;
        const customLogger = ctx.getLogger("customLogger");
        customLogger.error("%s %j", this.LOG_ERROR, LogAspect.requestAfterLog(error.message, 1));
      }
    }
  }

  private static requestBeforeLog(point: JoinPoint) {
    const ctx = point.target.ctx;
    const req = { ...point.args }[0]["request"];
    const reqBody = ctx.request.body;
    const reqQuery = ctx.query;
    const methodType = ctx.method;
    let requestHeaders = {};
    if (["POST", "PUT"].includes(methodType)) {
      requestHeaders = ctx.headers;
    }
    return {
      path: ctx.path,
      uri: req.url,
      method_type: ctx.method,
      method_name: point.methodName,
      contents: { ...reqBody, ...reqQuery },
      headers: requestHeaders,
    };
  }

  private static requestAfterLog(res: any, timeConsuming: number) {
    return {
      content: res,
      time_consuming: timeConsuming,
    };
  }
}
