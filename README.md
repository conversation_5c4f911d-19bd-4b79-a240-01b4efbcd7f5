# 光华易购商城前台项目
项目使用midwayjs【nodejs】作为中间层，并使用koa的web框架。使用react-ssr作为服务端渲染。

- koa
```doc
Koa 是一个非常轻量易用的 Web 框架。本章节内容，主要介绍在 Midway 中如何使用 Koa 作为上层框架，并使用自身的能力。
```
- react v17
- less
- antd ui

## 一、文档开发参考
官方文档请查看 [http://doc.ssr-fc.com/](http://doc.ssr-fc.com/)

## 二、启动项目
>初始化配置文件
```shell
cp .env.example .env
```
### 2-1、yarn[推荐]
```bash
# 安装依赖
yarn
# 运行husky
husky install
# 普通启动
yarn start
# 以vite启动
yarn start:vite
# 代码eslint fix
yarn lint:fix
# 生产环境打包
yarn prod
```

### 2-2、npm
```bash
#依赖安装
npm install 
# 运行husky
husky install
# 启动
npm start # 本地开发模式运行，单进程 支持 前端 HMR 前端静态资源走本地 webpack 服务
npm run prod # 模拟生产环境运行，多进程，前端资源走静态目录
npm run stop # 生产环境停止服务
```
### 2-3、配置文件
- 开发环境使用`config.default.ts`,此为默认配置文件;
- 正式环境使用`config.prod.ts`
- 建议本地开发,从`config.default.ts`复制一份`config.local.ts`进行配置，此文件已加入到 **.gitignore**
```shell
├── src
│   ├── config
│   │   ├── config.default.ts
│   │   ├── config.prod.ts
│   │   └── config.local.ts
```
## 三、目录结构
```shell
.
├── build # web目录构建产物，与 public 文件夹一样会设置为静态资源文件夹，非应用构建产物静态资源文件如图片/字体等资源建议放在 public 文件夹前端代码通过绝对路径引入
│   ├── client # 存放前端静态资源文件
│   └── server # 存放 external 后的服务端 bundle，
├── public # 作为静态资源目录存放静态资源文件
├── config.js # 定义应用的配置 (框架层面使用，生产环境需要)
├── config.prod.js # (可选) 若存在则视为生产环境的应用配置
├── f.yml # (可选)，仅在 Serverless 场景下使用，若调用 ssr deploy 检测到无此文件会自动创建
├── package.json
├── src # 存放服务端 Node.js 相关代码
│   └── index.tsx
├── tsconfig.json # 服务端 Node.js 编译配置文件
├── typings # 存放前后端公共类型文件
├── web # 存放前端组件相关代码
│   ├── components # 存放公共组件
│   │   └── header # 公共头部
│   │   │   ├── index.less
│   │   │   └── login.tpl.tsx
│   │   └── layout # 页面 html 布局
│   │       └── login.tpl.tsx # 页面 html 布局，仅在服务端被渲染
│   │       └── App.tsx # 页面具体的组件内容，用于初始化公共配置
│   │       └── fetch.ts # layout 级别的 fetch，用于获取所有页面的公共数据，将会在每一个页面级别的fetch 调用之前调用
│   ├── pages # pages目录下的文件夹会映射为前端路由表，存放页面级别的组件
│   │   ├── index # index文件夹映射为根路由 /index => /
│   │   │   ├── fetch.ts # 定义fetch文件用来统一服务端/客户端获取数据的方式，通过 __isBrowser__ 变量区分环境，会在首页服务端渲染以及前端路由切换时被调用
│   │   │   ├── index.less
│   │   │   └── render.tsx # 定义render文件用来定义页面渲染逻辑
│   │   └── detail
│   │   │   ├── fetch.ts
│   │   │   ├── index.less
│   │   │   └── render$id.tsx # 映射为 /detail/:id
│   │   │   └── user
│   │   │        ├── fetch.ts
│   │   │        └── render$id.tsx # 多级路由按照规则映射为 /detail/user/:id
│   │   │        └── render$user$id.tsx # 多参数路由映射为 /detail/user/:user/:id
│   │   ├── bar 
│   │   │   ├── fetch.ts
│   │   │   └── render.tsx
│   │   │   ├── fetch$id.ts
│   │   │   └── render$id.tsx # 当存在多个 render 类型的文件时，每个 render 文件对应与其同名的 fetch 文件，例如 render$id 对应 fetch$id
│   ├── tsconfig.json # web 目录下的 tsconfig 仅用于编辑器ts语法检测
```
## 鉴权
>需要登录鉴权认证的接口请加上`JwtPassportMiddleware` 中间件；
并且前端请求接口需要加上 **header**如下
```shell
Authorization: Bearer xxxx
```

## 四、提交规范
提交格式（注意冒号后面有空格）
```
<type>: <subject>
```
#### 4-1、常用的type类别
* feat: 新增feature
* fix: 修复bug
* docs: 仅仅修改了文档，比如README, CHANGELOG等等
* style: 仅仅修改了空格、格式缩进等等，不改变代码逻辑（空白，格式化，缺少分号等）
* refactor: 代码重构，没有加新功能或者修复bug
* perf: 优化相关，比如提升性能、体验
* test: 测试用例，包括单元测试、集成测试等
* revert: 版本回滚
* build: 影响构建系统或外部依赖项的更改（gulp，broccoli，npm）
* ci: 主要目的是修改项目继续集成流程
* chore: 不属于以上类型的其他类型


## 五、文件命名
* **常规**的文件夹/文件命名使用**全小写**形式，单词之间使用**下划线 _** 连接：home、activity_list
* **组件**文件夹/文件命名使用**大驼峰**形式（名词）：Address、Coupon
* **pages目录**页面文件也理解为页面级组件，所以大驼峰的形式命名。
  页面有分类文件夹，分类文件夹使用**全小写**形式，单词之间使用**下划线 _**
  例子如下:
```
pages/
├── home/    // 分类文件夹（小写）
│     ├──Demo1/ // 页面级组件
│     │   └──index.jsx/
│     ├──Demo2/ // 页面级组件
│     │   └──index.jsx/
│     └──Demo2/ // 页面级组件
│         └──index.jsx/
└── Demo/   // 页面级组件
    └──index.jsx/
```

## 六、ISSUE
### 6-1、不使用styled-components
>由于`css-in-js`目前会导致加载时样式闪烁问题，目前该框架作者没做这方面处理。请使用`css-modules`方式
```shell
# 作者解答的issue
https://github.com/zhangyuang/ssr/issues/95
```
