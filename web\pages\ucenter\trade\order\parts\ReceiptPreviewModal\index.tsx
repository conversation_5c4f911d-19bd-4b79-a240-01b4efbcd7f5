import React from "react";
import TmModal from "@/components/TmModal";
import style from "./index.module.less";
import TmDescriptions from "@/components/TmDescriptions";

interface IReceiptTProps {
  visible: boolean;
  setVisible: (visible) => void;
  receiptData: any;
  width?: number;
}

/** 发票信息弹窗 */
export default function ReceiptPreviewModal(props: IReceiptTProps) {
  const { visible, receiptData = [], setVisible } = props;

  return (
    <TmModal
      title="发票预览"
      visible={visible}
      footer={null}
      content={
        <div className={style.wrapper}>
          <TmDescriptions data={receiptData} />
        </div>
      }
      onCancel={() => setVisible(false)}
      onOk={() => setVisible(false)}
    />
  );
}
