import { IContext, SProps } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import style from "./index.module.less";
import React, { useContext, useRef } from "react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import { Button, Card, Empty, Modal, notification } from "antd";
import crudCcMall from "@/apis/member/cc-mall";
import { useLoading } from "@/hooks/useLoading";

export default function UcenterOldMall(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const cmid = state?.cmid;
  const ccMallFrontendDomain = state?.layoutInitData?.ccFrontendDomain;
  const myRef = useRef(null);
  /* ======================================= hook start ======================================= */
  const useLoadingHook = useLoading();
  /* ======================================= hook end ======================================= */

  const handleQuickLogin2oldMall = () => {
    Modal.confirm({
      content: `您确定登录到旧版商城个人中心吗？`,
      type: "warning",
      centered: true,
      closable: true,
      getContainer: () => myRef?.current ?? document.body,
      onOk: async () => {
        useLoadingHook.showLoading("登录请求中...");
        const [err, res] = await crudCcMall
          .login2mallUcenter()
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (err) {
          notification.warning({
            message: err?.data?.message || "网络异常，请重试！",
          });
          return;
        }
        useLoadingHook.hideLoading();
        window.open(res?.data?.data, "_blank");
      },
    });
  };
  const handleGo2oldMall = () => {
    window.open(ccMallFrontendDomain, "_blank");
  };
  return (
    <>
      <div className={style.wrapper} ref={myRef}>
        <UCenterCard title={"旧版商城"} />
        <Card className="sub-container" bordered>
          <Empty
            description={
              <div className="cc-container">
                {cmid ? (
                  <Button type="primary" ghost danger onClick={() => handleQuickLogin2oldMall()}>
                    一键登录到旧版商城个人中心
                  </Button>
                ) : (
                  <Button onClick={() => handleGo2oldMall()}>前往旧版商城</Button>
                )}
              </div>
            }
          />
        </Card>
      </div>
    </>
  );
}
