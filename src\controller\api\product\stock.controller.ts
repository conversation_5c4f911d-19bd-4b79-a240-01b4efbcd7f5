import { Controller, Get, Inject, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { IStockService } from "@/service/product/stock.service";
import { productMaterialStockDto } from "~/typings/data/product/product";

/** 产品库存 */
@Controller("/api/product/stock")
export class ProductStockController extends BaseController {
  @Inject("StockService")
  stockService: IStockService;

  /**
   * @desc 根据skuId查规格产品库存
   */
  @Get("/sku")
  async getSkuStockBySkuId(@Query() criteria: { skuId: number; inputVal: number }) {
    const { ctx } = this;
    const res = await this.stockService.getSkuStockBySkuId(criteria);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 个人中心-查库存列表-模糊搜索
   */
  @Get("/material")
  async getMaterialStocks(@Query() criteria: productMaterialStockDto) {
    const { ctx } = this;
    if (!criteria.blurry) {
      return ctx.getResponseInstance(ctx).sendFail("请输入关键字查询！", 400);
    }
    const res = await this.stockService.getMaterialStocks(criteria);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }
}
