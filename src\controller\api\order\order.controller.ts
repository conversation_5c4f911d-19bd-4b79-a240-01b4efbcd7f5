import { <PERSON>, Controller, Del, Get, Inject, Param, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { IOrderService } from "@/service/order/order.service";
import { orderProductsListDto, orderQueryListDto, orderProductsSalesHistoryListDto } from "~/typings/data/order";
import { IReSourcesService } from "@/service/platform/resources.service";

@Controller("/api/ucenter/orders", { middleware: [AuthenticationMiddleware] })
export class OrderController extends BaseController {
  @Inject("OrderService")
  orderService: IOrderService;

  @Inject("ResourcesService")
  resourcesService: IReSourcesService;

  /**
   * @desc 获取订单列表
   */
  @Get()
  async getList(@Query() criteria: Partial<orderQueryListDto>) {
    const { ctx } = this;
    const memberId = this.getMemberId();
    const res = await this.orderService.getOrderPageList(memberId, criteria);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 订单详情
   */
  @Get("/:order_no")
  async show(@Param("order_no") orderNo: string) {
    const { ctx } = this;
    const memberId = this.getMemberId();
    const res = await this.orderService.show(memberId, orderNo);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /**
   * 历史订购产品列表
   */
  @Get("/history-products")
  async productLists(@Query() criteria: Partial<orderProductsListDto>) {
    const { ctx } = this;
    const memberId = this.getMemberId();
    const res = await this.orderService.getOrderProductsLists(memberId, criteria);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Get("/products/sales-history")
  async getSalesHistory(@Query() criteria: Partial<orderProductsSalesHistoryListDto>) {
    const { ctx } = this;
    const memberId = this.getMemberId();
    const res = await this.orderService.getProductSalesHistory({ ...criteria, memberId });
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  @Del("/:order_no")
  async cancelOrder(@Param("order_no") orderNo: string, @Body() data: { reason: string }) {
    const { ctx } = this;
    const memberId = this.getMemberId();
    const res = await this.orderService.cancelOrder(memberId, orderNo, data);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /** 获取订单追踪信息 */
  @Get("/:order_no/tracking")
  async getOrderTracking(@Param("order_no") orderNo: string) {
    const { ctx } = this;
    const res = await this.orderService.getOrderTracking(orderNo);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }

  /** 订单合同下载 */
  @Get("/:order_no/download-contract")
  async downloadOrderContract(@Param("order_no") orderNo: string) {
    const { ctx } = this;
    const currentMemberId = this.getMemberId();
    // 根据orderNo查询
    const orderResult = await this.orderService.show(currentMemberId, orderNo);
    if (!orderResult || !orderResult?.data || !orderResult?.data?.attachments) {
      return ctx.getResponseInstance(ctx).sendFail("资源不存在,下载失败！", 404);
    }

    // 判断资源
    const { attachments } = orderResult.data;
    const result = await this.resourcesService.getPrivateFile(attachments.path);
    ctx.set("content-type", result.headers["content-type"]);
    ctx.body = result.data;
  }

  /**
   * 历史订单列表
   */
  @Get("/memberOrderHistory")
  async orderHistory(@Query() criteria: Partial<orderProductsListDto>) {
    const { ctx } = this;
    const memberId = this.getMemberId();
    const res = await this.orderService.getMemberOrderHistory(memberId);
    return ctx.getResponseInstance(ctx).setResponseData(res.data).send();
  }
}
