import React, { memo, useEffect } from "react";
import { useAdd2ShopCartInput } from "@/hooks/useAdd2ShopCart";
import { usePrevious } from "@/hooks/usePrevious";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import { InputNumber } from "antd";

interface EditableRowQuantityProps {
  record: any;
  callbackFunc?: any;
}

/**
 * Add2CartCnpt - 如果要购物车外，使用该组件，注意: record 数据结构为: { product: { ...sku }, quantity: 1 }
 */
export default memo(function Add2CartCnpt({ record, ...props }: EditableRowQuantityProps) {
  const useAdd2ShopCartInputHook = useAdd2ShopCartInput();
  const oldPurchaseQuantity = usePrevious(record.quantity);
  const shoppingCartHook = useShoppingCart();

  // 初始化
  useEffect(() => {
    if (record) {
      const step = Number(record?.product?.packingRatio || 1);
      useAdd2ShopCartInputHook.setCartQuantityStep(step);
      useAdd2ShopCartInputHook.setPurchaseQuantity(record.quantity);
    }
  }, [record]);

  useEffect(() => {
    if (useAdd2ShopCartInputHook.purchaseQuantity !== oldPurchaseQuantity && oldPurchaseQuantity !== undefined && useAdd2ShopCartInputHook.purchaseQuantity !== 0) {
      shoppingCartHook.update({ selected: 1, quantity: useAdd2ShopCartInputHook.purchaseQuantity, productSkuId: record.productSkuId }).then(_ => {
        props?.callbackFunc && props.callbackFunc();
      });
    }
  }, [useAdd2ShopCartInputHook.purchaseQuantity]);

  return (
    <div className="product-quantity">
      <button onClick={() => useAdd2ShopCartInputHook.add2CartReduceEvent()} className="btn btn-reduce">
        -
      </button>
      <InputNumber
        className="input-box"
        ref={useAdd2ShopCartInputHook.add2CartRef}
        onBlur={(e) => useAdd2ShopCartInputHook.add2CartBlurEvent(e)}
        onPressEnter={(e) => useAdd2ShopCartInputHook.add2CartBlurEvent(e)}
        precision={0}
        min={1}
        value={useAdd2ShopCartInputHook.purchaseQuantity}
        controls={false}
        defaultValue={record.quantity}
      />
      <button onClick={() => useAdd2ShopCartInputHook.add2CartPlusEvent()} className="btn btn-add">
        +
      </button>
    </div>
  );
})
