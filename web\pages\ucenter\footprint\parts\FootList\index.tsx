import { IProductSkuType } from "@/typings/product.interface";
import { price2Thousand } from "@/utils/price-format.util";
import { Button, List } from "antd";
import { onlyCsr } from "ssr-hoc-react";
import React from "react";

interface footPrintListProps {
  pageSize: number;
  footPrintHistory: Array<Partial<IProductSkuType>>;
  handleRemoveItem: (item: Partial<IProductSkuType>) => Promise<void>;
}

const FootPrintList = (props: footPrintListProps) => {
  const { pageSize, footPrintHistory, handleRemoveItem } = props;

  const renderListItem = (item: Partial<IProductSkuType>, index) => {
    return (
      <div className="product-item">
        <span className="item-child index">{index + 1}</span>
        <a className="item-child name" href={`/product/${item.originProductNo}`}>
          {item.productName}
        </a>
        <a className="item-child brand" href={`/search?brandId=${item.brandId}`}>
          {item.brandName}
        </a>
        <a className="item-child sku" href={`/product/${item.originProductNo}?sku=${item.sku}`}>
          {item.sku}
        </a>
        <span className="item-child cas">{item.cas}</span>
        <span className="item-child spec">{item.spec || `${item.packing}${item.unit ? "/" + item.unit : ""}`}</span>
        <span className="item-child price">￥{item.guidePrice === 0 ? "询价" : price2Thousand(item.guidePrice)}</span>
        <span className="item-child">
          <Button size="small" danger onClick={async () => await handleRemoveItem(item)}>
            移除
          </Button>
        </span>
      </div>
    );
  };

  return (
    <List
      itemLayout="vertical"
      size="large"
      pagination={{
        pageSize: pageSize,
      }}
      dataSource={footPrintHistory}
      renderItem={(item, index) => <List.Item key={item.id}>{renderListItem(item, index)}</List.Item>}
    />
  );
};

export default onlyCsr(FootPrintList) as any;
