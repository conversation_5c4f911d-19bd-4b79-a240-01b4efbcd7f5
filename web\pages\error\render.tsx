import React, { useEffect, useState } from "react";
import { SProps } from "ssr-types-react";
import { Result } from "antd";

export default function ErrorSimplePage(props: SProps) {
  const [params, setParams] = useState<any>(null);
  const getUrlParams = () => {
    const params = new URLSearchParams(props.location.search);
    setParams(params);
  };
  const renderSubTitle = () => {
    return (
      <>
        <div style={{ fontWeight: 600 }}>{params?.get("message") ?? "抱歉，您访问的网页出了点小问题了。"}</div>
        {params?.get("origin") ? <div>{`错误源地址：${params?.get("origin")}`}</div> : null}
      </>
    );
  };
  useEffect(() => {
    getUrlParams();
  }, []);
  return (
    <>
      <Result
        status="500"
        title="服务错误"
        subTitle={renderSubTitle()}
        extra={
          <a style={{ textDecoration: "underline" }} href="/">
            回到首页
          </a>
        }
      />
    </>
  );
}
