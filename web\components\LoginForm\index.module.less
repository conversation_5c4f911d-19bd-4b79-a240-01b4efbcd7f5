.wrapper {
  :global {
    .login {
      position: relative;
      width: 350px;
      overflow: hidden;
      padding: 25px 25px 23px;
      color: #6c6c6c;
      background-color: hsla(0, 0%, 100%, 0.9);
      border: 1px solid @auxiliary-bg-color;
      .login-tabs {
        color: @main-text-color;
        font-size: 16px;

        // 重写antd-tabs内部样式
        .ant-tabs-nav::before {
          border-bottom: none;
        }
        .ant-tabs-nav-list {
          .ant-tabs-tab + .ant-tabs-tab {
            margin: 0 0 0 10px;
          }

          .ant-tabs-tab {
            padding: 2px 0;
            color: #3c3c3c;
            font-size: 16px;
            font-weight: 600;

            .ant-tabs-tab-btn {
              color: #3c3c3c;
            }

            .ant-tabs-tab-active {
              color: #3c3c3c;
            }
          }

          .ant-tabs-ink-bar {
            background: #3c3c3c;
          }
        }
      }

      // 重写antd-form内部样式
      .ant-tabs-content {
        .ant-form-item {
          .ant-form-item-control-input-content {
            .ant-input-affix-wrapper-status-error:not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper {
              border-color: #fee;
            }

            .ant-input-affix-wrapper {
              position: relative;
              padding-left: 48px;
              transition: none !important;
              border-color: #d9d9d9 !important;

              .ant-input {
                font-size: 14px;
                color: #666666;
                text-align: left;
                height: 26px !important;

                &::placeholder {
                  color: #666666;
                }

                &.ant-input:hover {
                  border-color: red;
                }
              }

              // 图标背景
              .ant-input-prefix {
                position: absolute;
                left: -1px;
                top: -1px;
                width: 40px;
                height: 41px;
                background: #bbbbbb;
                display: flex;
                justify-content: center;
                align-items: center;
                box-sizing: border-box;

                .anticon {
                  font-size: 24px;
                  color: #ffffff;
                }
              }

              // input关关联激活状态重置
              &-focused {
                border: 1px solid #f9f9f9;
                box-shadow: none;
              }

              &:hover {
                box-shadow: unset;
              }

              &:not(.ant-input-affix-wrapper-disabled):hover {
                border-color: #cdcdcd;
              }
            }
          }
        }
      }

      // 账号密码登录form样式
      .login-form {
        .login-form-button.ant-btn-primary {
          width: 100% !important;
          background: #f40 !important;
          border-color: #f40 !important;
          color: #ffffff;
        }

        .tool-wrap {
          width: 100%;
          margin-top: 12px;
          text-align: right;

          a {
            margin-left: 6px;
            font-size: 10px;
          }

          a:hover {
            color: #f40;
          }
        }
      }

      // sms短信登录
      .sms-login-form {
        .site-form-item-icon {
          font-size: 20px !important;
        }
        // 图像验证码
        .captcha {
          margin-bottom: 0;
          .ant-row .ant-input-affix-wrapper {
          }
        }
        // sms验证码
        .sms-code {
          margin-bottom: 0;
          position: relative;
          .ant-input {
            background: #ffffff;
            font-size: 14px;
            width: 50%;
          }
          .sms-code-btn {
            display: inline-block;
            position: absolute;
            top: 0;
            right: 0;
            z-index: 1;
            font-size: 14px;
            color: #6c6c6c;
          }

          .ant-btn-text:hover,
          .ant-btn-text:focus {
            background: none;
            border-color: transparent;
          }
        }
      }

      // 扫码悬浮
      .login-qrcode {
        position: absolute;
        top: 2px;
        right: 2px;
        width: 48px;
        height: 48px;
        cursor: pointer;
        background: url("@@img/login-qrcode.svg") no-repeat 100% 0%;
        background-size: 100% 100%;
      }

      .form-select {
        .ant-form-item-control-input-content {
          display: flex;
          .form-select-icon {
            display: flex;
            width: 40px;
            height: 40px;
            background: #bbbbbb;
            font-size: 20px;
            line-height: 40px;
            color: #fff;
            align-items: center;
            justify-content: center;
          }
          .form-select-control {
            flex: 1;
            width: 100%;
            font-size: 14px;
          }
        }
      }
    }
  }
}
