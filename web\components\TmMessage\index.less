//index.less
.message-wrap{
  position: fixed;
  left: 50%;
  top: 50px;
  transform: translate(-50%);
  z-index: 10000;
  .message{
    height: 42px;
    min-width: 180px;
    padding: 0px 10px;
    font-size: 14px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    box-shadow: 0 0 8px #ddd;
    border: 1px solid #eee;
    transition: all 0.3ms ease-in-out;
    .icon {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 6px;
    }
  }
  .info {
    background: rgba(0,0,0,0.6);
    color: #fff;
    >.icon{
      background: #fff;
    }
  }
  .error {
    background: #fef0f0;
    color: #ff4d4f;
    >.icon{
      background: #ff4d4f;
    }
  }
  .warning {
    background: #fdf6ec;
    color: #faad14;
    >.icon{
      background: #faad14;
    }
  }
  .success {
    background: #f0f9eb;
    color: #52c41a;
    >.icon{
      background: #52c41a;
    }
  }
}
