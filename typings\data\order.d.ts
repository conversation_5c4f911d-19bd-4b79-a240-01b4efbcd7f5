import { baseQueryDto } from "./base";

export interface orderQueryListDto extends baseQueryDto{
  createdDate: string
  customerContactPhone: string,
  isExpired: number,//是否已过期:0-否;1-是
  isPaid: number,//是否已支付:0-否;1-是
  orderNo: string,
  orderState: number,
  blurry: string,//模糊查询产品名称|产品代码
  sourceChannel: number,
}

export interface orderProductsListDto extends baseQueryDto {
  blurry: string;
  createData: string[];
  memberId: string;
  orderBy: string;
  orderState: number;
  page: number;
  size: number;
  sku: string;
}

export interface orderProductsSalesHistoryListDto extends baseQueryDto {
  productSkuId: string;
  memberId: string;
}

/** 订单运费查询参数
 * sendType：
 * 不满足免运费的情况，配送方式：
 * 1.物流+自提(省外)
 * 2.物流+送货上门(省外)
 * 3.专车+送货上门(省内)
 * 4.专车+上门+免费(省内-只是货期较长)
 * 5-快递
 */
export interface orderFreightQueryDto {
  district?: string;    // 区镇
  city: string;         // 城市
  province: string;    // 省份
  productTotalNum: number;  // 订单产品总数
  productTotalPrice: number;// 订单产品总价
  isDanger: boolean;    // 产品是否包含危险品或液体
  sendType: number;  // 客户选择方式配送方式：1.只要物流(托运自提)2.物流+上门（包括广州）.
}


export interface orderLogisticsQueryDto {
  memberId: string;
  orderNo: string;
}

export interface orderHistoryQueryDto {
  memberId: string;
}
