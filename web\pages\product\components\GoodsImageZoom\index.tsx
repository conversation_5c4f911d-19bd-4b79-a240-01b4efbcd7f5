import React, { useRef, useState } from "react";
import ReactImageZoom from "react-image-zoom";
import style from "./index.module.less";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { Button, Carousel, message } from "antd";
import { CarouselRef } from "antd/es/carousel";
import { onlyCsr } from "ssr-hoc-react";

interface IProps {
  imageLists: string[];
  scale?: number;
}

export const GoodsImageZoom = (prop: IProps) => {
  // 页数
  const pageImageNum = 3;
  // 展示图片下标
  const [imgIndex, setImgIndex] = useState<number>(0);
  const zoomProps = {
    width: 400,
    height: 400,
    scale: prop.scale ?? 1.5,
    offset: {
      vertical: 0,
      horizontal: 10,
    },
    img: prop.imageLists[imgIndex],
    zoomPosition: "right",
    zoomStyle: "z-index: 9999;",
  };

  const carouselRef = useRef<any>();
  const handlePrevious = () => {
    carouselRef?.current?.prev();
  };

  const handleNext = () => {
    carouselRef?.current?.next();
  };

  const renderImagePage = () => {
    // 页数
    const pageNum = Math.ceil(prop.imageLists.length / pageImageNum);
    const temps = Array.from({ length: pageNum }, (val, key) => key + 1);
    return temps.map((item, idx) => {
      return (
        <div className="image-list-box" key={idx}>
          {prop.imageLists.slice(idx * pageImageNum, (idx + 1) * pageImageNum).map((item, index) => {
            return (
              <div key={index} onMouseOver={() => setImgIndex(idx * pageImageNum + index)} className={`image-list-box-item ${imgIndex === idx * pageImageNum + index ? "image-item-active" : ""}`}>
                <img src={item} alt="" />
              </div>
            );
          })}
        </div>
      );
    });
  };

  return (
    <>
      <div className={style.wrapper}>
        <div className="image-big-box">
          <ReactImageZoom {...zoomProps} />
        </div>
        <Carousel ref={carouselRef} infinite={false} dots={false} fade>
          {prop.imageLists.length && renderImagePage()}
        </Carousel>
        {/* {pageImageNum && (
          <div className="nav-btn-wrapper">
            <Button className="nav-icon list-to-left" type={"text"} icon={<LeftOutlined />} onClick={handlePrevious}></Button>
            <Button className="nav-icon list-to-right" type={"text"} icon={<RightOutlined />} onClick={handleNext}></Button>
          </div>
        )} */}
      </div>
    </>
  );
};

export default onlyCsr(GoodsImageZoom) as any;
