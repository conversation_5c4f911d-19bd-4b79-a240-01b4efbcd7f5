import React, { useContext, useEffect, useRef, useState, useMemo } from "react";
import { SProps } from "ssr-types-react";
// Hook
import { useStoreContext } from "ssr-common-utils";
import { useOss } from "@/hooks/useOss";
import { useLoading } from "@/hooks/useLoading";
import { useSendTypeRadio } from "./cnpts/useSendTypeRadio";
import { useShoppingCart } from "@/hooks/useShoppingCart";
import { useAgreementPrice } from "@/hooks/useAgreementPrice";
// Components
import AddressEditModal from "@/components/AddressEditModal";
import ReceiptFormModal from "@/components/ReceiptEditModal";
import TmConfirmModal from "@/components/TmConfirmModal";
import ProductSelectModal from "@/components/ProductSelectModal";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
// API
import * as orderCrud from "@/apis/order/order";
import * as crudAddress from "@/apis/member/address";
import * as tradeCrud from "@/apis/trade/trade";
import crudReceipt from "@/apis/member/receipt";
// UI
import { Card, Tag, message, Table, Tooltip, Form, Input, Space, Modal, notification, FormInstance, Steps, Button, InputNumber, AutoComplete } from "antd";
import { CloseCircleOutlined, CheckOutlined, DownOutlined, PlusCircleOutlined, UpOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { ColumnsType } from "antd/es/table";
import style from "./edit.module.less";
// TS
import { IContext } from "ssr-types";
import { IOrderDetailParams, IOrderProductColumnType, IOrderLogistic } from "@/typings/order.interface";
import { ICreateVo } from "~/typings/data/trade/trade";
import { IReceiptType, IReceivingAddressType } from "@/typings/member.interface";
import { tradeCartDataType } from "@/typings/shopping-cart.interface";
// Utils
import { price2Thousand, ghmallGuidePrice2Show } from "@/utils/price-format.util";
const { Search } = Input;

export default function EditOrder(props: SProps) {
  const { state } = useContext<IContext>(useStoreContext());
  const useLoadingHook = useLoading();
  const [form] = Form.useForm();
  const agreementPriceHook = useAgreementPrice();
  /* 不开发票配置 */
  const notNeedReceipt = {
    receiptType: 2,
    id: null,
  };
  /* OSS地址 */
  const useOssHook = useOss();
  /* 订单ID */
  const orderNo = state.orderNo ?? props?.match?.params["id"];
  /* 订单详情 */
  const [orderDetail, setOrderDetail] = useState<any>();
  /* 订单产品 */
  const [products, setProducts] = useState<any>([]);
  /* 展示更多地址 */
  const [showMoreAddress, setShowMoreAddress] = useState<boolean>(false);
  /* 展示编辑按钮 */
  const [showAddressEditBtn, setShowAddressEditBtn] = useState<any>("");
  /* 地址列表 */
  const [addresses, setAddresses] = useState<any>([]);
  /* 选中的地址 */
  const [selectedAddress, setSelectedAddress] = useState<any>({});
  /* 地址编辑弹窗-开关 */
  const [addressModalVisible, setAddressModalVisible] = useState<boolean>(false);
  /* 地址编辑弹窗-新增/编辑标记 */
  const [addressMode, setAddressMode] = useState<"add" | "edit">("add");
  /* 地址修改-选中项 */
  const [currentAddress, setCurrentAddress] = useState<IReceivingAddressType>();
  // 产品金额
  const [selectedFlowPrice, setSelectedFlowPrice] = useState<number>(0);
  const shoppingCartHook = useShoppingCart();
  const orderRemarkFormRef = useRef<FormInstance>(null);
  const [initialValues, setInitialValues] = useState<object>({
    exceptionContact: null,
    exceptionContactPhone: null,
    customerPo: null,
    deliveryRequirements: null,
    remark: null,
  });
  // 开票信息
  const [receiptList, setReceiptList] = useState<IReceiptType[]>([]);
  // 普通发票
  const [receiptNormal, setReceiptNormal] = useState<IReceiptType>();
  // 增值税发票
  const [receiptSpecial, setReceiptSpecial] = useState<IReceiptType>();
  // 选中的开票信息
  const [selectedReceipt, setSelectedReceipt] = useState<any>(notNeedReceipt);
  /* 展示开票编辑按钮 */
  const [showReceiptEditBtn, setShowReceiptEditBtn] = useState<any>("");
  const [receiptMode, setReceiptMode] = useState<"add" | "edit">("add");
  const [currentReceipt, setCurrentReceipt] = useState<IReceiptType>();
  const [receiptModalVisible, setReceiptModalVisible] = useState<boolean>(false);
  const [productSelectModalVisible, setProductSelectModalVisible] = useState<boolean>(false);
  const [receiptType, setReceiptType] = useState<number>(0);
  const { way } = state;
  // 开票信息情况
  const receiptTypeObj: any = [
    {
      typeName: "normal",
      typeValue: 1,
      name: "普通增值税发票",
      data: receiptNormal,
    },
    {
      typeName: "special",
      typeValue: 0,
      name: "13%增值税专用发票",
      data: receiptSpecial,
    },
    {
      typeName: "none",
      typeValue: 2,
      name: "不开票",
      data: notNeedReceipt,
    },
  ];
  const [deliveryOptions, setDeliveryOptions] = useState<string[]>([]);

  const addressRef = useRef<any>();
  // 提交页收集配送选项
  const [selectedSendType, setSelectedSendType] = useState(1);
  /* ======================================= hook start ======================================= */
  const useSendTypeRadioHook = useSendTypeRadio({
    sendTypeChangeCallback: val => {
      setSelectedSendType(val);
    },
  });

  // 总计
  const totalPrice = useMemo(() => {
    const priceMap = products.map(item => item.productTaxPrice * item.productQuantity);
    return priceMap.length > 0 ? priceMap.reduce((pre, cur) => pre + cur) : 0;
  }, [products]);

  // 初始化
  useEffect(() => {
    fetchMemberReceipt();
    fetchMemberAddresses();
    fetchDetail();
    fetchDeliveryRequirements();
  }, []);

  useEffect(() => {
    if (selectedAddress?.districtInfo?.name) {
      useSendTypeRadioHook.refresh(selectedAddress?.districtInfo?.name);
    }
  }, [selectedAddress?.districtInfo?.name]);

  useEffect(() => {
    if (receiptList.length > 0) {
      if (orderDetail?.receiptId) {
        const selectedReceiptInfo = receiptList?.find(item => item?.id === orderDetail?.receiptId);
        setSelectedReceipt(selectedReceiptInfo);
      } else {
        setSelectedReceipt(notNeedReceipt);
      }
    }

    if (orderDetail?.receivingAddressId && addresses.length > 0) {
      const selectedAddress = addresses?.find(item => item?.id === orderDetail?.receivingAddressId);
      setSelectedAddress(selectedAddress);
    }

    if (orderDetail) {
      if (orderDetail.submitStatus === 1) {
        // 弹窗拦截
        TmConfirmModal({
          title: "温馨提醒",
          content: (
            <>
              <div>当前订单状态不可编辑，如您需要编辑订单，请您联系您的专属业务员！</div>
            </>
          ),
          confirmText: "确认",
        }).then(() => {
          window.location.href = `/ucenter/trade/order?status=0`;
        });
      }
      setProducts(orderDetail.products);
      form.setFieldsValue({
        exceptionContact: orderDetail?.exceptionContact,
        exceptionContactPhone: orderDetail?.exceptionContactPhone,
        customerPo: orderDetail?.customerPo,
        deliveryRequirements: orderDetail?.deliveryRequirements,
        remark: orderDetail?.remark,
      });
    }

    console.log("products", orderDetail?.products);
  }, [form, orderDetail, receiptList, addresses]);

  // 产品列配置
  const tableColumns: ColumnsType<any> = [
    {
      title: "SKU",
      dataIndex: "productSku",
      key: "productSku",
      align: "center",
      render: (text, record, index) => (
        <a className="product-detail-link" href={`/product/${record.productNo}?sku=${record.productSku}`} target="_blank">
          {record.productSku}
        </a>
      ),
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      key: "productName",
      align: "center",
      render: (text, record, index) => <span className="product-name">{record.productName}</span>,
    },
    {
      title: "品牌",
      dataIndex: "productBrand",
      key: "productBrand",
      align: "center",
      render: (text, record) => <span>{record.productBrand}</span>,
    },
    {
      title: "包装",
      dataIndex: "productPacking",
      key: "productPacking",
      align: "center",
      render: (text, record, index) => <span>{!record.productPacking || record.productPacking === "1" ? "-" : record.productPacking}</span>,
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      align: "center",
      width: 140,
      render: (_, record) => <InputNumber min={1} defaultValue={record.productQuantity} onChange={e => updateQuantityChange(e, record)} />,
    },
    {
      title: "单位",
      dataIndex: "productUnit",
      key: "productUnit",
      align: "center",
      render: (text, record, index) => record.productUnit || "-",
    },
    {
      title: "单价",
      dataIndex: "productTaxPrice",
      key: "productTaxPrice",
      align: "center",
      render: (text, record, index) => <span>{ghmallGuidePrice2Show(record.productTaxPrice)}</span>,
    },
    {
      title: "小计",
      dataIndex: "totalPrice",
      key: "totalPrice",
      align: "center",
      render: (text, record, index) => `${record.productTaxPrice === 0 ? "询价" : price2Thousand(record.productTaxPrice * record.productQuantity)}`,
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record, index) => (
        <>
          <Space direction="horizontal" size="small">
            <Button ghost type="primary" size="small" onClick={async () => await delCurrentEvent(index)}>
              移除
            </Button>
          </Space>
        </>
      ),
    },
  ];

  /** 获取订单详情 */
  const fetchDetail = async (): Promise<void> => {
    useLoadingHook.showLoading("订单加载中...", "ucenterApp");
    const [err, res] = await orderCrud
      .show(orderNo)
      .then(res => [null, res])
      .catch(err => [err, null]);
    useLoadingHook.hideLoading("ucenterApp");
    if (err) {
      Modal.error({
        content: "网络异常，获取订单详情失败！",
        type: "error",
        centered: true,
        closable: false,
        maskClosable: false,
        keyboard: false,
        okText: "回到订单列表",
        onOk: async () => {
          window.location.href = "/ucenter/trade/order";
        },
      });
      return;
    }
    setOrderDetail({ ...orderDetail, ...res.data });
  };

  const updateQuantityChange = async (val, record) => {
    // 只有在用户登录时才查询协议价格
    if (state?.userLoginState && record && val) {
      try {
        const result = await agreementPriceHook.queryMemberVolumeDiscount(
          val,
          record.brandId?.toString() || "941", // 使用产品的品牌ID，默认华大品牌ID
          record.productSkuId
        );
        console.log('订单编辑页面协议价格查询结果:', result);

        // 如果查询成功且有协议价格配置，更新产品的价格
        if (result && result.orderQuantityConfiguration && result.productSkuDto?.discountPrice) {
          console.log('订单编辑页面更新协议价格:', result.productSkuDto.discountPrice);

          // 更新产品列表中对应产品的价格和数量
          setProducts(prevItems =>
            prevItems.map(item =>
              item.productSkuId === record.productSkuId
                ? { ...item, productQuantity: val, productTaxPrice: result.productSkuDto.discountPrice }
                : item
            )
          );
        } else {
          // 如果没有协议价格配置，只更新数量
          setProducts(prevItems =>
            prevItems.map(item =>
              item.productSkuId === record.productSkuId
                ? { ...item, productQuantity: val }
                : item
            )
          );
        }
      } catch (error) {
        console.error("订单编辑页面查询协议价格失败:", error);
        // 查询失败时仍然更新数量
        setProducts(prevItems =>
          prevItems.map(item =>
            item.productSkuId === record.productSkuId
              ? { ...item, productQuantity: val }
              : item
          )
        );
      }
    } else {
      // 未登录时只更新数量
      setProducts(prevItems =>
        prevItems.map(item =>
          item.productSkuId === record.productSkuId
            ? { ...item, productQuantity: val }
            : item
        )
      );
    }
  };

  const fetchMemberAddresses = async (queryName?) => {
    const [err, res] = await crudAddress
      .getList({ size: 200, queryName })
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return false;
    }
    const addressList = res?.data?.content ?? [];
    setAddresses(addressList);
    addressList.forEach((e, index) => {
      if (e.id === selectedAddress.id && index > 2) {
        setShowMoreAddress(true);
      }
    });
  };

  // 删除当前行产品
  const delCurrentEvent = async (index: number) => {
    if (products.length < 2) return message.warn("最少需要订购一个产品");
    Modal.confirm({
      content: "您确定删除该产品吗？",
      type: "warning",
      onOk: () => {
        setProducts(products.filter((_, i) => i !== index));
      },
    });
  };

  const fetchMemberReceipt = async () => {
    const [err, res] = await crudReceipt
      .getList()
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      return;
    }
    const receipts = res.data;
    setReceiptList(receipts);
    setReceiptNormal(receipts.find(item => item.receiptType === 1));
    setReceiptSpecial(receipts.find(item => item.receiptType === 0));
    receipts.forEach(e => {
      if (e.isDefault) {
        setSelectedReceipt(e);
      }
    });
  };

  // 选择地址
  const selectAddressEvent = async item => {
    setSelectedAddress(item);
  };

  // 修改地址
  const editAddressEvent = address => {
    setCurrentAddress(address);
    setAddressModalVisible(true);
    setAddressMode("edit");
  };

  /** 检测单品是否包含危化品标识 */
  const checkProductHasDangerFlag = product => {
    if (!product) {
      return false;
    }
    return product?.isDanger || product?.isPoison || product?.isExplode || product?.spec?.includes("L") || product?.spec?.includes("l");
  };

  /** 检测下单产品是否包含危化品 或 液体 */
  const handleCheckHasDangerInOrderProducts = () => {
    return products?.some(item => item.isDanger) || false;
  };

  // 删除地址
  const delAddressEvent = addressId => {
    Modal.confirm({
      content: "确定删除此条地址信息吗？",
      title: "警告",
      type: "warning",
      centered: true,
      closable: true,
      onOk: async () => {
        if (!addressId) {
          return;
        }
        const [err, res] = await crudAddress
          .remove(addressId)
          .then(res => [null, res])
          .catch(err => [err, null]);
        if (err) {
          message.error(err.data.message || "请求异常，删除失败！");
          return;
        }
        message.success("删除成功" || res.message);
        await fetchMemberAddresses();
      },
    });
  };

  // 新增地址
  const addAddressEvent = () => {
    setAddressModalVisible(true);
    setAddressMode("add");
  };

  // 选择开票事件
  const selectedReceiptEvent = item => {
    item?.data && setSelectedReceipt(item?.data);
  };

  // 开票编辑
  const editReceiptEvent = item => {
    setCurrentReceipt(item.data);
    setReceiptMode("edit");
    setReceiptModalVisible(true);
  };

  // 开票新增
  const addReceiptEvent = receiptType => {
    setReceiptMode("add");
    setReceiptModalVisible(true);
    setReceiptType(receiptType);
  };
  const handleReceiptCallBackFunc = async () => {
    await fetchMemberReceipt();
  };

  const handleAddProduct = data => {
    if (products.findIndex(item => item.productSkuId === data.productSkuId) > -1) {
      return message.warning("该产品已在订购产品列表中，请勿重复添加");
    }
    const _item = {};
    setProducts(prevItems => {
      return [...prevItems, data];
    });
    return message.success("添加成功");
  };
  const fetchDeliveryRequirements = async () => {
    try {
      const [err, res] = await orderCrud
        .getMemberOrderHistory()
        .then(res => [null, res])
        .catch(err => [err, null]);

      if (err) {
        console.error("获取历史订单数据失败", err);
        return;
      }

      // 从历史订单中提取不重复的送货要求
      const requirements = res?.data?.content?.map(order => order.deliveryRequirements).filter(req => req && req.trim() !== "");
      // 去重
      const uniqueRequirements = [...new Set(requirements)];
      setDeliveryOptions(uniqueRequirements as string[]);
    } catch (error) {
      console.error("获取历史订单送货要求失败", error);
    }
  };

  // 支付确定提交,创建订单
  const go2paySubmitEvent = async () => {
    // 表单校验
    const form = orderRemarkFormRef?.current?.getFieldsValue(true);

    // 弹窗拦截
    const confirm = await TmConfirmModal({
      title: "温馨提醒",
      content: (
        <>
          {handleCheckHasDangerInOrderProducts() ? (
            <span>
              您的下单产品包含: <span style={{ color: "red" }}>液体/危化学品</span>，如果您未提供过相关资质材料，请及时联系业务员/客服~
            </span>
          ) : (
            <span>商品等信息，已确认无误</span>
          )}
        </>
      ),
      confirmText: "提交订单",
    });
    if (confirm !== "confirm") {
      return;
    }
    // 组装产品数据
    const cartProducts = products.map(item => {
      return {
        id: item.id || null,
        productId: item.productId,
        productQuantity: item.productQuantity,
        productSku: item.productSku,
        price: item.productTaxPrice,
        remark: item.remark || "",
        skuId: item.productSkuId,
        isDanger: item.isDanger,
      };
    });
    console.log("cartProducts", cartProducts);
    const tradeVo: Partial<ICreateVo> = {
      ...form,
      receivingAddressId: selectedAddress.id,
      receiptId: selectedReceipt.id ?? null,
      products: cartProducts,
      tradeType: way ?? "CART",
      sendType: selectedSendType,
    };

    if (!tradeVo.receivingAddressId) {
      addressRef?.current?.scrollIntoView();
      notification.warning({ message: addresses.length ? "请选择一个收货地址!" : "请新增收货地址" });
      return;
    }
    delete tradeVo.initialValues;
    tradeVo.id = orderDetail.id;
    useLoadingHook.showLoading("订单提交中...");
    console.log("tradeVo", tradeVo);
    const [err, res] = await tradeCrud
      .edit(tradeVo)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      useLoadingHook.hideLoading();
      return notification.error({ message: err?.data?.message || "编辑订单失败，请稍后再试！" });
    }
    notification.success({ message: res?.message || "订单编辑成功！" });
    setTimeout(async () => {
      useLoadingHook.hideLoading();
      // 弹窗拦截
      await TmConfirmModal({
        title: "温馨提醒",
        content: (
          <>
            <div>订单编辑已提交成功，您的专属业务员会尽快审核订单，请耐心等待！</div>
          </>
        ),
        confirmText: "确认",
      });
      window.location.href = `/ucenter/trade/order?status=0`;
    }, 100);
  };

  const getDeliveryAddress = () => {
    let detailAddress = "";
    selectedAddress?.provinceInfo?.name && (detailAddress += selectedAddress.provinceInfo.name);
    selectedAddress?.cityInfo?.name && (detailAddress += selectedAddress.cityInfo.name);
    selectedAddress?.districtInfo?.name && (detailAddress += selectedAddress.districtInfo.name);
    selectedAddress?.address && (detailAddress += " " + selectedAddress?.address);
    return detailAddress;
  };

  const renderTitle = () => (
    <div className="title-wrapper">
      <span>编辑订单</span>
    </div>
  );

  return (
    <div className={style.wrapper}>
      <UCenterCard title={renderTitle()} />

      <div className="confirmWrapper">
        {/* ================收货地址====================== */}
        <Card
          ref={addressRef}
          title={
            <div style={{ lineHeight: "32px" }}>
              <span>收货地址</span>
              <Search placeholder="请输入关键词" onSearch={fetchMemberAddresses} allowClear style={{ width: 250, marginLeft: "20px" }} />
            </div>
          }
          className="pay-card-item"
          bodyStyle={{ padding: "10px 20px" }}
          extra={
            <a target="_blank" href={"/ucenter/member/address"} className="go2address">
              管理收货人地址
            </a>
          }
          bordered
        >
          <div className="address-manage">
            {addresses.map((item, idx) => {
              return (
                <div
                  key={idx}
                  onMouseEnter={() => setShowAddressEditBtn(idx)}
                  onMouseLeave={() => setShowAddressEditBtn("")}
                  onClick={async () => await selectAddressEvent(item)}
                  className={`address-manage-item ${selectedAddress.id === item.id ? "border-red" : undefined}`}
                  style={{ display: showMoreAddress || idx < 3 ? "block" : "none" }}
                >
                  <div>
                    <span>{item.nickname}</span>
                    {item.isDefault ? <Tag color="red">默认</Tag> : null}
                    {item.aliasName ? <Tag color="orange">{item.aliasName}</Tag> : null}
                  </div>
                  <div>
                    {item.phone}&nbsp;&nbsp;({item.companyName})
                  </div>
                  <div>
                    {`${item.provinceInfo?.name}${item.cityInfo?.name}${item.districtInfo?.name}`}&nbsp;{item.address}
                  </div>
                  <div className="edit-btn" style={{ display: showAddressEditBtn === idx ? "block" : "none" }}>
                    <span onClick={() => editAddressEvent(item)}>修改</span>
                    {!item.isDefault ? <span onClick={() => delAddressEvent(item.id)}>删除</span> : null}
                  </div>
                  <div className="corner-icon" style={{ display: selectedAddress.id === item.id ? "" : "none" }}>
                    <div />
                    <CheckOutlined />
                  </div>
                </div>
              );
            })}
            <div className="add-address" onClick={() => addAddressEvent()}>
              <PlusCircleOutlined />
              <div>添加新地址</div>
            </div>
          </div>
          {addresses.length > 3 ? (
            <div className="more-address" onClick={() => setShowMoreAddress(!showMoreAddress)}>
              {showMoreAddress ? (
                <>
                  收起地址
                  <UpOutlined />
                </>
              ) : (
                <>
                  更多地址
                  <DownOutlined />
                </>
              )}
            </div>
          ) : null}
        </Card>
        {/* ================发票信息====================== */}
        <Card
          title={
            <span>
              发票信息
              <span className="tips">
                <ExclamationCircleOutlined />
                &nbsp;开企业抬头发票须填写纳税人识别号，以免影响报销
              </span>
            </span>
          }
          extra={
            <a target="_blank" href={"/ucenter/member/receipt"} className="go2address">
              管理开票信息
            </a>
          }
          className="pay-card-item invoice-box"
          bodyStyle={{ padding: "10px 20px" }}
          bordered
        >
          <div className="invoice-box-content">
            {receiptTypeObj.map((item, idx) => {
              return (
                <div
                  key={idx}
                  onMouseEnter={() => setShowReceiptEditBtn(item.typeValue)}
                  onMouseLeave={() => setShowReceiptEditBtn("")}
                  onClick={() => selectedReceiptEvent(item)}
                  className={`invoice-box-content-item ${selectedReceipt.receiptType === item?.typeValue ? "border-red" : undefined}`}
                >
                  <>
                    <div className="type">
                      {item.name}&nbsp;{item?.data?.isDefault ? <Tag color="red">默认</Tag> : null}
                    </div>
                    <div className="vat-name">{item?.data ? `${item?.data?.vatName ?? ""}  ${item?.data?.vatId ?? ""}` : "暂未配置"}</div>
                    <div className="edit-btn" style={{ display: showReceiptEditBtn !== 2 && showReceiptEditBtn === item.typeValue ? "block" : "none" }}>
                      {item?.data ? <span onClick={() => editReceiptEvent(item)}>修改</span> : <span onClick={() => addReceiptEvent(item.typeValue)}>新增</span>}
                    </div>
                  </>
                  <div className="corner-icon" style={{ display: selectedReceipt.receiptType === item?.typeValue ? "" : "none" }}>
                    <div />
                    <CheckOutlined />
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
        {/* ================订单信息====================== */}
        <Card
          title={
            <span>
              订单备注<span className="sub-tip">(选填)</span>
            </span>
          }
          className="pay-card-item order-remark-box"
          bodyStyle={{ padding: "10px 20px" }}
          bordered
        >
          <Form ref={orderRemarkFormRef} form={form} name="orderRemarkForm" className="orderRemarkForm" initialValues={{ initialValues }} autoComplete="off" size="large">
            <Form.Item label="异常联系人" tooltip="订单异常联系人，如：张三 1866666666">
              <Input.Group compact>
                <Form.Item name="exceptionContact" noStyle>
                  <Input style={{ width: "50%" }} placeholder="请输入异常联系人" />
                </Form.Item>
                <Form.Item name="exceptionContactPhone" noStyle>
                  <Input style={{ width: "50%" }} placeholder="请输入异常联系人电话" />
                </Form.Item>
              </Input.Group>
            </Form.Item>
            <Form.Item label="客户PO号" name="customerPo" tooltip="客户PO号，将显示在出货单上">
              <Input placeholder="请输入客户PO号" />
            </Form.Item>
            <Form.Item label="送货要求" name="deliveryRequirements">
              <AutoComplete placeholder="请输入或选择送货要求" options={deliveryOptions.map(item => ({ value: item, label: item }))} />
            </Form.Item>
            <Form.Item name="remark" label="订单备注">
              <Input.TextArea style={{ width: "100%" }} rows={3} placeholder="提示：请勿填写有关支付、收货、发票方面的信息，填写对此订单的特殊处理要求。" maxLength={200} />
            </Form.Item>
          </Form>
        </Card>
        {/* ================订购产品====================== */}
        <Card
          title="订购产品"
          className="pay-card-item"
          bodyStyle={{ padding: "10px 20px" }}
          extra={
            <>
              <Button
                type="primary"
                onClick={() => {
                  setProductSelectModalVisible(true);
                }}
              >
                添加产品
              </Button>
            </>
          }
          bordered
        >
          <Table rowKey="productSkuId" size="middle" dataSource={products} columns={tableColumns} pagination={false} />
          {/* 订单价格汇总 */}
          <div className={`pay-price-box`}>
            {/* 客户手动选择配送方式 */}
            <div>
              <span>共 {products.length} 件商品，总商品金额：</span>
              <span>￥{products.findIndex(item => item.productTaxPrice === 0) === -1 ? price2Thousand(totalPrice) : "询价"}</span>
            </div>
            <div className="freight-box">
              <div className="left-area">
                <div>
                  <span>预定运费：</span>
                  <span className="freight-price">￥0.00</span>
                </div>
                <div className="tip">（注意：实际运费或免运费，待业务员审核订单后确定！）</div>
              </div>
              {/* 可选配送选项 */}
              <div className="send-type-box">
                <useSendTypeRadioHook.renderSendTypeSelector />
              </div>
            </div>
            <div>
              <span>应付金额：</span>
              <span className="actual-price">￥{products.findIndex(item => item.productTaxPrice === 0) === -1 ? price2Thousand(totalPrice) : "询价"}</span>
            </div>
          </div>
        </Card>
        {/* ================底部支付栏====================== */}
        <Card bodyStyle={{ padding: "0" }} className="pay-card-footer">
          <div className="pay-footer">
            <div className="pay-address">
              配送至：{getDeliveryAddress() || "-"}
              &nbsp;&nbsp;收货人：{selectedAddress?.nickname}&nbsp;{selectedAddress?.phone}
            </div>
            <div className="pay-submit" onClick={async () => await go2paySubmitEvent()}>
              提交订单
            </div>
          </div>
        </Card>
      </div>
      {/* 地址操作面板 */}
      <AddressEditModal
        callbackFunc={async () => await fetchMemberAddresses()}
        modalVisible={addressModalVisible}
        changeModalVisible={setAddressModalVisible}
        mode={addressMode}
        addressId={currentAddress?.id}
        companyName={state?.userData?.companyName}
      />
      {/* 开票操作弹窗面板 */}
      <ReceiptFormModal
        receiptType={currentReceipt?.receiptType ?? receiptType}
        callbackFunc={handleReceiptCallBackFunc}
        modalVisible={receiptModalVisible}
        changeModalVisible={setReceiptModalVisible}
        mode={receiptMode}
        receiptId={currentReceipt?.id}
      />
      {/* 产品选择弹窗面部 */}
      <ProductSelectModal modalVisible={productSelectModalVisible} changeModalVisible={setProductSelectModalVisible} onAddProduct={handleAddProduct} />
    </div>
  );
}
