@contentTimeColor: #666666;
@notifyTitleColor: #333333;
@notifyTitleFontSize: 30px;
@notifyDescriptionFontSize: 24px;
@notifyDescriptionColor: @regular-text-color;
@notifyMargin: 60px;
@borderColor: #dbdbdb;

.wrapper {
  :global {
    .notify-content {
      background-color: @main-bg-color-white;
      padding: 16px @notifyMargin;
      display: flex;
      flex-direction: column;
      .notify-head {
        .notify-title {
          font-size: @notifyTitleFontSize;
          font-weight: 600;
          text-align: center;
        }
        .notify-desc {
          font-size: 14px;
          text-align: center;
        }
        .notify-tips {
          font-size: 14px;
          .flex-row(space-between, center);
          border-bottom: 1px solid #f2f2f2;
          color: @contentTimeColor;
          span {
            display: inline-flex;
            align-items: center;
          }
          span.icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            .flex-center();
            border-radius: 2px;
            cursor: pointer;
            align-items: center;
          }
          .weibo {
            background: url("@@img/weibo.png") 0 0 no-repeat;
          }
          .wechat {
            background: url("@@img/wechat.png") 0 0 no-repeat;
          }
        }
      }

      .notify-content-box {
        margin-top: 16px;
        padding-bottom: 16px;
        font-size: 16px;
        // 富文本编辑器的内容
      }

      .notify-attach-box {
        ul {
          li {
            a {
              font-size: 21px;
              color: #666;
            }
          }
          li:hover {
            a {
              display: inline;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
