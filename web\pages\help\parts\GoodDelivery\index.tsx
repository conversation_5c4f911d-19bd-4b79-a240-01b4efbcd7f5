import React, { memo } from "react";

export default memo(function GoodDelivery() {
  return (
    <>
      <section className="article">
        <div className="article-paragraph">
          <h2 className="article-title">配送说明</h2>
          <div className="article-content">
            光华易购电子商务平台使用<strong className="article-link">【快递 / 物流 / 送货上门】</strong>三种配送方式，配送方式请在填写核对订单时进行选取。目前与光华易购电子商务平台合作的配送商可覆盖{" "}
            <strong>全国800+</strong> 个城市，保障商品安全快速运达。
          </div>
        </div>
        <div className="article-paragraph">
          <h3 className="article-subtitle">请您注意：</h3>
          <div className="article-content">
            <ul>
              <li>
                1.为了确保您所购买的商品能够安全送达您的手中，在收到商品时，请您务必检查所收包裹外包装以及封条是否完好后，再签收，如您签收有明显损坏迹象的外包装后再投诉货物有误或有损坏，恕我们不能受理。
              </li>
              <li>2.签收后，请你仔细检查所收商品与您订购的商品型号、数量是否一致，如有明显损坏迹象或与商品不符情况，请及时联系我们。我们会处理并承担由此而产生的运输费用，请您不必担心。</li>
              <li>3.光华易购除自已的配送系统外,亦有签约物流公司快递或托运到客户提定地点。</li>
            </ul>
          </div>
        </div>
        <div className="article-paragraph">
          <h3 className="article-subtitle">签收货物时请确认:</h3>
          <div className="article-content">
            <ul>
              <li>1. 产品名称及规格是否无误</li>
              <li>2. 产品数量是否无误</li>
              <li>3. 产品包装是否破损, 如有任何问题, 请立即联系我们的销售工程师, 且客户可选择拒收货物.</li>
            </ul>
          </div>
        </div>
      </section>
    </>
  );
});
