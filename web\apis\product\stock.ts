import request from "@/utils/request.util";
import { productMaterialStockDto } from "~/typings/data/product/product";

/**
 * 根据skuId 查库存结果
 * @param skuId
 * @param inputVal 输入对比值
 * @return 返回库存结果，如: { result: "" }
 */
export function getSkuStockBySkuId(skuId: number, inputVal: number) {
  return request({
    url: "/api/product/stock/sku",
    method: "get",
    params: { skuId, inputVal, storehouseNames: '汕头仓' },
  });
}

/**
 * 个人中心库存查询-列表-模糊搜索
 * @param params 查询参数
 */
export function getMaterialStocks(params: Partial<productMaterialStockDto>) {
  return request({
    url: "/api/product/stock/material",
    method: "get",
    params
  });
}

export default {
  getSkuStockBySkuId,
  getMaterialStocks
}
