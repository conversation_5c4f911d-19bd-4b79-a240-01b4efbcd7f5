import React, { useState, useEffect } from "react";
import { Button, message, Steps, Table, Modal, Upload, Alert, Tag } from "antd";
import type { UploadProps } from "antd";
import { DownloadOutlined, UploadOutlined, FileDoneOutlined, InboxOutlined } from "@ant-design/icons";
import { downloadFromOssFilepath } from "@/utils/download.util";
import { useLoading } from "@/hooks/useLoading";
import styles from "../index.module.less";
import readXlsxFile from "read-excel-file";
import searchCrud from "@/apis/search/search";

const BatchImportModal = ({ visible, onClose, onDataSourceChange }) => {
  const { Dragger } = Upload;
  const useLoadingHook = useLoading();
  const downloadListFilepath = "/file/订单产品批量导入模板.xlsx";
  const [current, setCurrent] = useState(0);
  const [productDataResults, setProductDataResults] = useState([]);
  const [errorMessages, setErrorMessages] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);

  const props: UploadProps = {
    multiple: false,
    maxCount: 1,
    accept: ".xls,.xlsx",
    onChange(info) {
      const { status } = info.file;
      if (status !== "uploading") {
        handleUploadTemplate(info.file.originFileObj);
      }
    },
    beforeUpload(e) {
      handleBeforeUpload(e);
    },
  };

  const columns = [
    {
      title: "产品名称",
      dataIndex: "skuName",
      key: "skuName",
      align: "center",
    },
    {
      title: "产品代码",
      dataIndex: "sku",
      key: "sku",
      align: "center",
    },
    {
      title: "单位",
      dataIndex: "unit",
      key: "unit",
      align: "center",
    },
    {
      title: "包装规格",
      dataIndex: "packing",
      key: "packing",
      align: "center",
    },
    {
      title: "包装系数",
      dataIndex: "packingRatio",
      key: "packingRatio",
      align: "center",
    },
    {
      title: "单价",
      dataIndex: "discountPrice",
      key: "discountPrice",
      align: "center",
      render: (text: number) => (Number(text) === 0 ? "询价" : text),
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      align: "center",
    },
    {
      title: "备注",
      dataIndex: "isBuy",
      key: "isBuy",
      align: "center",
      width: "80px",
      render: (text: string) => {
        return <div className="isBugTag">{text ? <Tag color="red">购买过</Tag> : "-"}</div>;
      },
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys, selectedRows) => {
      setSelectedRowKeys(selectedKeys);
      setSelectedRows(selectedRows);
    },
  };

  useEffect(() => {
    init();
  }, [visible]);

  useEffect(() => {
    if (productDataResults.length > 0) {
      setSelectedRowKeys(productDataResults.map(item => item.rowKey));
    }
  }, [productDataResults]);

  const init = () => {
    setCurrent(0);
    setProductDataResults([]);
    setErrorMessages([]);
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const handleDownloadTemplate = () => {
    downloadFromOssFilepath(downloadListFilepath, "快速订购-产品批量导入模板.xlsx");
    setTimeout(() => {
      setCurrent(1);
    }, 500);
  };

  const schema = {
    "产品代码（华大品牌）": {
      prop: "sku",
      type: String,
    },
    订购数量: {
      prop: "quantity",
      type: Number,
    },
  };

  const handleUploadTemplate = file => {
    readXlsxFile(file, { schema })
      .then(({ rows }) => {
        const promises = rows.map(item => getProductData(item.sku, item.quantity));
        useLoadingHook.showLoading("文件解析中，请稍候...", "batchImportModal");

        Promise.all(promises)
          .then(results => {
            const successfulResults = results
              .filter(result => result)
              .map((result, index) => ({
                ...result,
                rowKey: new Date().getTime() + index,
              }));
            const failedResults = results.filter(result => result === null);
            setProductDataResults(successfulResults); // 将成功的结果存储到状态中
            setSelectedRows(successfulResults);
            console.log("successfulResults", successfulResults);

            if (successfulResults.length === 0) {
              return message.error("未匹配到可导入产品代码，请检查后再上传");
            } else {
              setCurrent(2);
            }

            // 提示未找到数据的 SKU
            if (failedResults.length > 0) {
              const failedSkus = rows.filter((item, index) => results[index] === null).map(item => item.sku);
              console.log("failedSkus", failedSkus);
              setErrorMessages(failedSkus);
            }
          })
          .finally(() => {
            useLoadingHook.hideLoading("batchImportModal");
          });
      })
      .catch(error => {
        console.error("读取文件错误:", error);
        message.error("读取文件失败，请检查文件格式是否正确");
        useLoadingHook.hideLoading("batchImportModal");
      });
  };

  const getProductData = async (sku: string, quantity: number) => {
    try {
      const params = {
        keyword: sku,
        page: 1,
        size: 1,
        brandId: 941,
      };
      const [err, res] = await searchCrud
        .fetchSearchList(params)
        .then(res => [null, res])
        .catch(err => [err, null]);

      if (err) {
        message.error(`请求 SKU ${sku} 时发生错误: ${err.message}`);
        return null;
      }
      if (res.data.total === 0) {
        message.error(`未找到 SKU ${sku} 的相关产品`);
        return null;
      } else {
        const _searchResults = getProductAllSku(res.data.content);
        // 检查是否存在匹配的SKU
        const matchedProduct = _searchResults.find(item => item.sku === sku);
        if (!matchedProduct) {
          message.error(`未找到 SKU ${sku} 的相关产品`);
          return null;
        }
        return { ...matchedProduct, quantity };
      }
    } catch (error) {
      message.error(`搜索 SKU ${sku} 时失败: ${error.message}`);
      return null;
    }
  };

  const getProductAllSku = products => {
    return products.reduce((acc, product) => {
      if (product.skus) {
        const _sku = product.skus.map(sku => ({
          ...sku,
          productNo: product.productNo,
          productImage: product.headImage,
          isDanger: product.isDanger,
          isExplode: product.isExplode,
          isExplosion: product.isExplosion,
          isPoison: product.isPoison,
        }));
        const sortedSku = _sku.sort((a, b) => (b.isBuy === 1 ? 1 : 0) - (a.isBuy === 1 ? 1 : 0));
        return acc.concat(sortedSku);
      }
      return acc;
    }, []);
  };

  const handleBeforeUpload = file => {
    const isXls = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || file.type === "application/vnd.ms-excel";
    if (!isXls) {
      return message.error("只能上传xls/xlsx文件!");
    }
    const isLt2M = file.size / 1024 / 1024 < 1;
    if (!isLt2M) {
      return message.error("文件大小不能超过1MB!");
    }
    return false;
  };

  const handleSubmit = () => {
    console.log("selectedRows", selectedRows);
    if (selectedRows.length > 0) {
      onDataSourceChange(selectedRows);
      onClose();
    } else {
      message.warning("请选择需要导入的产品");
    }
  };

  return (
    <Modal
      title="批量导入订购产品"
      width={860}
      open={visible}
      onCancel={onClose}
      footer={
        productDataResults.length > 0 ? (
          <Button type="primary" onClick={handleSubmit} disabled={selectedRowKeys.length === 0}>
            确认添加
          </Button>
        ) : null
      }
      className={styles.moreModal}
    >
      <div id="batchImportModal">
        <Steps
          className={styles.steps}
          size="small"
          current={current}
          items={[
            {
              title: "下载模板",
              description: "按照格式填写数据",
            },
            {
              title: "上传文件",
              description: "上传不超过1M文件",
            },
            {
              title: "选择产品",
              description: "选择需要导入的产品",
            },
          ]}
        />
        <div>
          {current === 0 && (
            <div className={styles.stepContent}>
              <Button type="primary" size="large" icon={<DownloadOutlined />} onClick={handleDownloadTemplate}>
                下载导入模板
              </Button>
              <p>请先下载导入模板文件并按照格式填写数据</p>
              <p style={{ marginTop: "10px" }}>
                已有模板文件？前往
                <Button style={{ padding: "0 2px" }} type="link" danger onClick={() => setCurrent(1)}>
                  上传导入文件
                </Button>
              </p>
            </div>
          )}
          {current === 1 && (
            <div className={styles.stepContent}>
              <Dragger {...props} style={{ width: "600px", padding: "20px" }}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">单击或拖动文件到此区域进行上传</p>
                <p className="ant-upload-hint">只能上传xls/xlsx文件，且不超过1024kb</p>
              </Dragger>
            </div>
          )}
          {current === 2 && (
            <div className={styles.stepContent}>
              {errorMessages.length > 0 && (
                <Alert style={{ width: "100%", marginTop: "-15px" }} message={`【${errorMessages.join("、")}】产品代码未匹配到数据，请检查后再上传`} type="warning" closable showIcon />
              )}
              <Table
                style={{ marginTop: "15px" }}
                columns={columns}
                dataSource={productDataResults}
                rowSelection={rowSelection}
                sticky={{
                  offsetHeader: 0,
                }}
                rowKey="rowKey"
                pagination={false}
                size="small"
              />
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default BatchImportModal;
