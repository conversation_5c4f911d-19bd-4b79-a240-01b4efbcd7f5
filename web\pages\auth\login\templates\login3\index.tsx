import React, { memo, useState } from "react";
import { LockFilled, UserOutlined } from "@ant-design/icons";
import style from "./index.module.less";
import { Button, Carousel, Checkbox, Form, Input } from "antd";
import { DebouncedFunc } from "lodash";

interface Props {
  rules: any;
  defaultForm: object;
  onFinish: DebouncedFunc<(values: any) => Promise<void>>;
  onFinishFailed: (errorInfo: any) => void;
}

export default memo(function LoginTpl(props: Props) {
  const { rules, defaultForm, onFinish, onFinishFailed } = props;
  const [form] = Form.useForm();

  const [formType, setFormType] = useState("login");
  return (
    <div className={style.loginPage}>
      <div className="login-header">
        <div className="login-header-content">
          <a href="/" className="logo-box"></a>
        </div>
      </div>
      <div className="login-container">
        {/* 背景广告图 */}
        <Carousel effect="fade" autoplaySpeed={20000} autoplay className="login-carousel">
          <div className="login-carousel-item">
            <img src="//lili-system.oss-cn-beijing.aliyuncs.com/background.jpg" alt="背景图" />
          </div>
        </Carousel>
        <div className="form-box">
          <h2>登录到您的帐户</h2>
          <Form form={form} initialValues={defaultForm} autoComplete="on" onFinish={onFinish} onFinishFailed={onFinishFailed} className="login-form">
            <Form.Item name="phone" rules={rules.phoneRules}>
              <Input size="large" prefix={<UserOutlined style={{ color: "rgba(0,0,0,.25)" }} />} placeholder="手机号" />
            </Form.Item>
            <Form.Item name="password" rules={rules.passwordRules}>
              <Input.Password size="large" prefix={<LockFilled style={{ color: "rgba(0,0,0,.25)" }} />} placeholder="密码" autoComplete="new-password" />
            </Form.Item>
            <Form.Item name="remember" valuePropName="checked">
              <Checkbox>记住密码</Checkbox>
            </Form.Item>
            <Form.Item>
              <Button size="large" block type="primary" htmlType="submit" className="login-form-button">
                登录
              </Button>
            </Form.Item>
            <p className="agreement">
              点击登录，即表示您同意我们的 <a href="#">条款和条件！</a>
            </p>
            <p className="register">
              没有帐户？<a href="#">立即注册</a>
            </p>
          </Form>
        </div>
      </div>
      <div className="footer">
        <div className="a-links">
          <a href="">帮助</a>
          <a href="">隐私</a>
          <a href="">条款</a>
        </div>
        <span>© 2018 JHD广州市金华大化学试剂有限公司 版权所有，保留一切权利。</span>
      </div>
    </div>
  );
});
