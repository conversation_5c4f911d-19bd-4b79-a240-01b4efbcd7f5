import { Rule, RuleType } from "@midwayjs/validate";

export class SearchQueryDto {
  /**
   * 关键字搜索
   */
  @Rule(RuleType.string().required)
  keyword: string;

  /**
   * 品牌过滤
   */
  @Rule(RuleType.string())
  brandId: string;

  /**
   * 分类
   */
  @Rule(RuleType.string())
  categoryId: string;

  @Rule(RuleType.number())
  page: number;

  @Rule(RuleType.number())
  size: number = 10;

  /* 当前登录会员ID - 非必填 */
  memberId?: string;
  /* 当前登录客户ID - 非必填 */
  customerId?: string;
}
