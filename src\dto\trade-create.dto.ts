import { Rule, RuleType } from "@midwayjs/validate";
import { IProduct } from "~/typings/data/trade/trade";
import { findFreightTypeByValue } from "@/common/constants/freight.constant";
import { CustomHelpers } from "joi";

/** 订单-创建-参数校验 */
export class TradeCreateVoDto {
  /** 会员地址 */
  @Rule(RuleType.number().required())
  receivingAddressId: number;

  /** 客户选定的运输方式 */
  @Rule(
    RuleType.number()
      .required()
      .custom((value: number, helpers: CustomHelpers) => {
        if (!findFreightTypeByValue(value)) {
          return helpers.error("any.invalid");
        }
        return value;
      })
      .error(new Error("`sendType`运输方式不合法，请核对选择！"))
  )
  sendType: number;

  /** 客户传递的运费 */
  @Rule(RuleType.number())
  freightFee: number;

  @Rule(RuleType.array().required())
  products: IProduct[];

  @Rule(RuleType.string())
  remark: string;

  @Rule(RuleType.string().empty(""))
  /** tradeType-交易类型：CART=>购物车；BUY_NOW=>立即购买, */
  tradeType: string;

  @Rule(RuleType.string())
  /** 客户编号 */
  memberId: string;

  @Rule(RuleType.string())
  orderTitle: string;

  @Rule(RuleType.string())
  exceptionContact: string;

  @Rule(RuleType.string())
  exceptionContactPhone: string;

  @Rule(RuleType.string())
  deliveryRequirements: string;

  @Rule(RuleType.number())
  id: number;

  /** 订单来源渠道 */
  sourceChannel: number;

  @Rule(RuleType.number())
  /** 发票是否随货 */
  invoiceWithGoods: number;

  @Rule(RuleType.number())
  /** 运费类型 */
  freightType: number;

  /** 选择的物流方式-字符串 */
  transportType: string;

  /** 出货单选项 */
  @Rule(RuleType.number())
  deliveryOrderOptions: number;

  @Rule(RuleType.string())
  /** 客户PO号 */
  customerPo: string;

  @Rule(RuleType.boolean())
  /** 产品包含危化品 */
  isDanger: boolean;

  @Rule(RuleType.number().empty(null))
  /** 开票id-可为空 */
  receiptId: number;
}
