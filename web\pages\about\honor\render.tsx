import React, { useContext, useState } from "react";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";
import AboutNavigation from "@/pages/about/components/AboutNavigation";
import style from "./index.module.less";
import { Segmented, Card, Image } from "antd";
import LazyLoad from "react-lazyload";
import { useStoreContext } from "ssr-common-utils";
import { useOss } from "@/hooks/useOss";

const defaultTab = "all";
export default function ContactusIndex() {
  const { state } = useContext<IContext>(STORE_CONTEXT);
  const honorOptions = [
    {
      label: "全部",
      value: "all",
    },
    ...state.honorData.map(item => {
      return {
        label: item.title,
        value: item.classificationCode,
      };
    }),
  ];
  const [value, setValue] = useState<any>(defaultTab);
  const { state: Test } = useContext<IContext>(useStoreContext());
  const ossHook = useOss();
  return (
    <div className={style.wrapper}>
      <AboutNavigation />
      <div className="honor">
        {/* @ts-expect-error */}
        <Segmented block size="large" defaultValue={defaultTab} options={honorOptions} value={value} onChange={value => setValue(value)} />
        {state?.honorData
          .filter(item => item.classificationCode === value || value === "all")
          .map((honorItem, idx) => (
            <Card className="honor-content" key={idx} title={honorItem.title}>
              <ul className="honor-content-ul">
                {honorItem.honors.map((item, index) => (
                  <LazyLoad once key={index}>
                    <li>
                      <Card hoverable cover={<Image width={198} height={200} src={ossHook.generateOssFullFilepath(item.filepath)} fallback={item.discription} />}>
                        <Card.Meta title={item.title} />
                      </Card>
                    </li>
                  </LazyLoad>
                ))}
              </ul>
            </Card>
          ))}
      </div>
    </div>
  );
}
