import { Provide, Scope, <PERSON><PERSON><PERSON><PERSON> } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IProductService } from "@/service/product/product.service";
import { productQueryListDto } from "~/typings/data/product/product";

@Provide("ProductService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class ProductServiceImpl extends BaseService implements IProductService {
  async getPageList(criteria: Partial<productQueryListDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/products`, criteria));
  }

  async detail(productNo: number | string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/products/${productNo}`, null, this.getDefaultHeaders()));
  }
}
