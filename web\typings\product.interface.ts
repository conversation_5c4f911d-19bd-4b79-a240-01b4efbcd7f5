/** 产品SKU-明细类型 */
export interface ISku {
  erpMaterialCode: string;
  guidePrice: number;
  id: number;
  image: string;
  originalPrice: number;
  packing: string;
  productId: string;
  realStock: number;
  remark: string;
  sku: string;
  sortOrder: number;
  spec: string;
  state: number;
  stock: number;
  stockRemark: string;
  unit: string;
  weight: string;
}

/** 产品级SKU类型 */
export interface IProductSkuType {
  id: number;
  skuId: number;
  sku: string;
  stock: number;
  realStock: number;
  spec: string;
  guidePrice: number;
  originalPrice: number;
  packing: string;
  unit: string;
  weight: string;
  skuState: number;
  originProductNo: string;
  itemNo: string;
  brandId: number;
  brandName: string;
  cas: string;
  productName: string;
  productNameEn: string;
  headImage: string;
  productState: number;
  isDanger: number;
  isExplosion: number;
  isPoison: number;
  isExplode: number;
  description: string;
  discountPrice?: number;
}
