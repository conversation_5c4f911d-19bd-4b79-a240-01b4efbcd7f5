import { Provide, <PERSON>ope, <PERSON><PERSON><PERSON><PERSON> } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ISearchService } from "@/service/search/search.service";
import { SearchQueryDto } from "@/dto/search-query.dto";

@Provide("SearchService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class SearchServiceImpl extends BaseService implements ISearchService {
  /**
   * 普通搜索
   * @param criteria /
   */
  async searchFromSql(criteria: Partial<SearchQueryDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/product/search", criteria));
  }

  /**
   * 关键字联想
   * @param params /
   */
  async keywordRemindSearch(params: { keyword: string }): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/product/search/keyword-remind", params));
  }

  /**
   * es搜索
   * @param criteria
   */
  async searchFromEs(criteria: Partial<SearchQueryDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/product/search/es", criteria));
  }

  /** 获取关联筛选条件
   *
   * @param criteria /
   */
  async getGoodsRelated(criteria: Partial<SearchQueryDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/product/search/related", criteria));
  }

  /**
   * 获取搜索热词
   */
  async getHotWords(): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/product/search/hot-words"));
  }
}
