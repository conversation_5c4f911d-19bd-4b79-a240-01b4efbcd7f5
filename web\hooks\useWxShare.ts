import { useState } from "react";
import { getWxShare } from "@/apis/wechat";

interface IParams {
  gtitle?: string;
  gdesc?: string;
  glink?: string;
}
const useWxShare = (params?: IParams) => {
  let gtitle: any = params?.gtitle;
  let gdesc: any = params?.gdesc;
  let glink: any = params?.glink;
  const isWx = () => {
    if (__isBrowser__) {
      const ua = window.navigator.userAgent.toLowerCase();
      /** 是不是微信 */
      const isWeChat = Boolean(ua) && ua.indexOf("micromessenger") > 0;
      /** 是不是企业微信 */
      const isWeCom = Boolean(ua) && ua.indexOf("wxwork") > 0;
      return isWeChat || isWeCom;
    }
    return false;
  };
  if (__isBrowser__ && isWx()) {
    gtitle = document?.title;
    gdesc = `我正在看【${document?.title}】，分享给您浏览！`;
    glink = encodeURIComponent(location?.href?.split("#")[0]);
  }
  const [title] = useState<string>(gtitle);
  const [desc] = useState<string>(gdesc);
  const [link] = useState<string>(glink);
  if (__isBrowser__ && isWx()) {
    const wxInit = async (): Promise<void> => {
      const wx = require("weixin-js-sdk");
      // 在这里调用微信JS-SDK提供的接口进行分享配置
      const { data } = await getWxShare({ url: link });
      await wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: data.appId, // 必填，公众号的唯一标识
        timestamp: data.timestamp, // 必填，生成签名的时间戳
        nonceStr: data.nonceStr, // 必填，生成签名的随机串
        signature: data.signature, // 必填，签名
        jsApiList: ["updateAppMessageShareData", "updateTimelineShareData"], // 必填，需要使用的JS接口列表
      });

      await wx.ready(function () {
        // 自定义“分享给朋友”及“分享到QQ”按钮的分享内容
        wx.updateAppMessageShareData({
          title, // 分享标题
          desc, // 分享描述
          link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: "", // 分享图标
          success: function () {
            console.log("分享成功");
          },
          cancel: function () {
            console.log("取消分享");
          },
        });
        // 自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容
        wx.updateTimelineShareData({
          title, // 分享标题
          link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: "", // 分享图标
          success: function () {
            console.log("分享成功");
          },
          cancel: function () {
            console.log("取消分享");
          },
        });
      });
    };
    return {
      wxInit,
    };
  }
};

export default useWxShare;
