import { Controller, Get, Inject, Param } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IDictService } from "@/service/dict.servce";
import { COMMON_DICT_MAPPING } from "@/common/constants/common.constant";

@Controller("/ucenter/trade/order", { middleware: [AuthenticationMiddleware] })
export class OrderController extends BaseController {
  @Inject("DictService")
  dictService: IDictService;

  @Get()
  async index() {
    const { ctx } = this;
    const cancelReasonDictRes = await this.dictService.getDictByCode(COMMON_DICT_MAPPING.ORDER_CANCEL_REASON.value);
    ctx.cancelReasonDict = cancelReasonDictRes?.data?.detail || [];

    ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/:id")
  async detail(@Param("id") orderNo: string) {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "csr",
    });
  }

  @Get("/edit/:id")
  async edit(@Param("id") orderNo: string) {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "csr",
    });
  }
}
