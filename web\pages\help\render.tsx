import React, { useEffect, useState } from "react";
import style from "./index.module.less";
import "@/assets/css/myArticle.less";
import { Card, Menu } from "antd";
import BuyWay from "./parts/BuyWay";
import OnlineShop from "./parts/OnlineShop";
import GoodDelivery from "./parts/GoodDelivery";
import OrderInvoice from "./parts/OrderInvoice";
import ReturnGoods from "./parts/ReturnGoods";
import DangerChemical from "./parts/DangerChemical";
import PrivacyAgreementTemplate from "@/components/AgreementTemplates/PrivacyAgreementTemplate";

export default function HelpIndex() {
  const navItems = [
    {
      label: "帮助中心",
      key: "help",
      children: [
        {
          label: <a href={"/help#buy-way"}>订购方式</a>,
          key: "buy-way",
        },
        {
          label: <a href={"/help#online-shop"}>网站购物</a>,
          key: "online-shop",
        },
        {
          label: <a href={"/help#good-delivery"}>配送说明</a>,
          key: "good-delivery",
        },
        {
          label: <a href={"/help#good-delivery"}>发票制度</a>,
          key: "order-invoice",
        },
        {
          label: <a href={"/help#good-delivery"}>退换货</a>,
          key: "return-goods",
        },
        {
          label: <a href={"/help#danger-chemical"}>危化品须知</a>,
          key: "danger-chemical",
        },
        // 隐私协议+条款
        {
          label: <a href={"/help#privacy-agreement"}>隐私和条款</a>,
          key: "privacy-agreement",
        },
      ],
    },
  ];
  const articleMenuReactNodes = {
    "buy-way": <BuyWay />,
    "online-shop": <OnlineShop />,
    "good-delivery": <GoodDelivery />,
    "order-invoice": <OrderInvoice />,
    "return-goods": <ReturnGoods />,
    "danger-chemical": <DangerChemical />,
    "privacy-agreement": <PrivacyAgreementTemplate />,
  };
  const defaultMenuKey = "buy-way";

  const [active, setActive] = useState("");
  useEffect(() => {
    const helpActiveByUrl = location?.href.split("#")[1] || defaultMenuKey;
    console.log("初始化 - 获取地址 描点-切换到： ", helpActiveByUrl);
    helpActiveByUrl && handleChangeMenu({ key: helpActiveByUrl });
  }, []);

  const handleChangeMenu = ({ key }) => {
    if (!Object.keys(articleMenuReactNodes).includes(key)) {
      key = defaultMenuKey;
    }
    setActive(key);
  };

  return (
    <div className={style.wrapper}>
      <div className="help">
        {/* menu */}
        <Card className="help-nav">
          <Menu items={navItems} mode={"inline"} defaultOpenKeys={["help"]} onClick={menu => handleChangeMenu({ key: menu.key })} />
        </Card>
        {/* content */}
        <Card className="help-content">{articleMenuReactNodes[active]}</Card>
      </div>
    </div>
  );
}
