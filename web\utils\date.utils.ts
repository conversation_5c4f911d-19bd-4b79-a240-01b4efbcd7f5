/**
 * 日期数值是否需要补零显示
 * @param dateNumber 数字
 * @param need 是否小于10补零，默认开启
 */
export function dateNumberNeedAppendZero(dateNumber, need = true): number | string {
  if (!need) {
    return dateNumber;
  }
  return dateNumber < 10 ? "0" + dateNumber : dateNumber;
}

/**
 * Parse the time to string
 * @param {(object|string|number)} time 时间参数
 * @param {string} cFormat 格式方式, 默认: "{y}-{m}-{d} {h}:{i}:{s}"
 * @param {boolean} needAppendZero 小于10是否补”0“, 默认开启
 * @returns {string}
 */
export function parseTime(time: object | string | number, cFormat = "{y}-{m}-{d} {h}:{i}:{s}", needAppendZero = true) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "undefined" || time === null || time === "null") {
    return "";
  } else if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: dateNumberNeedAppendZero(parseInt(date.getMonth()) + 1, needAppendZero),
    d: dateNumberNeedAppendZero(date.getDate(), needAppendZero),
    h: dateNumberNeedAppendZero(date.getHours(), needAppendZero),
    i: dateNumberNeedAppendZero(date.getMinutes(), needAppendZero),
    s: dateNumberNeedAppendZero(date.getSeconds(), needAppendZero),
    a: dateNumberNeedAppendZero(date.getDay(), needAppendZero),
  };
  return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = value.toString();
    }
    return value || 0;
  });
}

/**
 * 获取距离【参考日期】前后的第n天的日期字符串,YYYY-MM-DD
 * @param day 距离前后天数, 正数代表【参考日期】之后的第n天, 负数代表【参考日期】之前的第n天
 * @param flagDate 参考日期,默认为今天,请传可以转换成有效时间的值
 * @param cFormat 时间格式化, 默认：{y}-{m}-{d} => "YYYY-MM-DD"
 * @return string 返回日期字符串, 目标天数对应的日期时间字符串
 */
export function getFlagDate2Date(day, flagDate = '', cFormat = "{y}-{m}-{d}") {
  if (typeof day !== 'number') {
    return parseTime(flagDate || new Date(), cFormat)
  }

  const startDate = flagDate ? new Date(flagDate) : new Date()
  const targetDate = new Date(startDate)
  targetDate.setDate(startDate.getDate() + day)
  return parseTime(targetDate, cFormat)
}
