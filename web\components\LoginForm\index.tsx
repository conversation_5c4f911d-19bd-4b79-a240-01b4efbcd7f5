import React, { useState } from "react";
import { Button, Form, Input, Tabs, Select, message } from "antd";
import { LockOutlined, TabletOutlined, UserOutlined, FileTextOutlined } from "@ant-design/icons";
import SmsCaptcha from "@/components/SmsCaptcha";
import { getAccountInfoByPhone } from "@/apis/auth";
import { SmsSendTypeEnum } from "@/enums/SmsSendTypeEnum";
import { SYSTEM_LOGIN_TYPES } from "@/constants/system";
import type { FormInstance } from "antd/es/form";
import style from "./index.module.less";
import { DebouncedFunc } from "lodash";
import { validateGhmallLoginAccount } from "@/utils/form-valid.util";

type PropsType = React.PropsWithChildren<{
  onFinish?: DebouncedFunc<(values: any) => Promise<void>>;
  className2?: string;
  defaultForm?: any;
  loading?: boolean;
}>;

// 登录表单校验
const rules = {
  account: [{ required: true, whitespace: true, message: "登录账号不能为空" }, { validator: validateGhmallLoginAccount }],
  phone: [
    { required: true, whitespace: true, message: "手机号不能为空" },
    { pattern: /^1([3-9][0-9])\d{8}$/, message: "请输入正确格式的手机号" },
  ],
  password: [
    { required: true, whitespace: true, message: "密码不能为空" },
    { max: 20, min: 6, message: "密码不能小于6位或大于20位" },
  ],
  companyName: [{ required: true, message: "请选择账号绑定的公司" }],
};

/** 基础登录表单 */
const LoginForm = (props: PropsType, ref?: React.ForwardedRef<FormInstance>) => {
  const { defaultForm, onFinish, className2, loading } = props;
  const [passwordForm] = Form.useForm();
  const [smsForm] = Form.useForm();
  const [phoneBindCustomers, setPhoneBindCustomers] = useState<object[]>([]);
  const [innerLoading, setInnerLoading] = useState<boolean>(false);

  /**
   * 密码登录
   * @param values
   */
  const onPasswordFinish = async values => {
    values.loginType = SYSTEM_LOGIN_TYPES.PASSWORD.value;
    setInnerLoading(true);
    if (values.companyName && onFinish) {
      console.log("values", values);
      setInnerLoading(false);
      onFinish(values);
    } else {
      try {
        const result = await getAccountInfoByPhone(values["phone"]);
        setInnerLoading(false);
        if (result.status === 200 && result.data?.data.length > 0) {
          if (result.data?.data.length === 1) {
            values.companyName = result.data?.data[0]?.companyName;
          } else {
            const _data = result.data?.data?.map(item => {
              return { label: item.companyName, value: item.companyName };
            });
            setPhoneBindCustomers(_data);
            return message.warning("系统检测到该账号关联多个公司，请选择登录的公司名称");
          }
        }
        onFinish && onFinish(values);
      } catch (error) {
        setInnerLoading(false);
        console.log("LoginErrorMessage", error);
      }
    }
  };

  /**
   * 短信登录
   * @param values
   */
  const onSmsFinish = async values => {
    values.loginType = SYSTEM_LOGIN_TYPES.SMS.value;
    setInnerLoading(true);
    if (values.companyName && onFinish) {
      setInnerLoading(false);
      onFinish(values);
    } else {
      try {
        const result = await getAccountInfoByPhone(values["phone"]);
        setInnerLoading(false);
        if (result.status === 200 && result.data?.data.length > 0) {
          if (result.data?.data.length === 1) {
            values.companyName = result.data?.data[0]?.companyName;
          } else {
            const _data = result.data?.data?.map(item => {
              return { label: item.companyName, value: item.companyName };
            });
            setPhoneBindCustomers(_data);
            return message.warning("系统检测到该账号关联多个公司，请选择登录的公司名称");
          }
        }
        onFinish && onFinish(values);
      } catch (error) {
        setInnerLoading(false);
        console.log("LoginErrorMessage", error);
      }
    }
  };

  // 检查手机号/邮箱绑定多客户情况
  const checkedPhoneBindCustomer = async (e, form) => {
    form
      .validateFields(["phone"])
      .then(res => {
        form.setFieldValue("companyName", null);
        setPhoneBindCustomers([]);
      })
      .catch(error => {});
  };

  /** 切换登录类型-清空输入和校验 */
  const handleChangeTab = (tab: string) => {
    if (tab === SYSTEM_LOGIN_TYPES.SMS.value) {
      passwordForm.resetFields();
    } else if (tab === SYSTEM_LOGIN_TYPES.PASSWORD.value) {
      smsForm.resetFields();
    }
    setPhoneBindCustomers([]);
  };

  // 密码输入框，提交前去除密码前后的空格
  const transformInputValue = value => {
    return value.trim();
  };

  // 内部组件
  const renderItems = [
    {
      label: "密码登录",
      key: "password",
      children: (
        <Form form={passwordForm} name="normal_login" className="login-form" size="large" initialValues={defaultForm} onFinish={e => onPasswordFinish(e)}>
          <Form.Item name="phone" rules={rules.account}>
            <Input
              prefix={<UserOutlined className="site-form-item-icon" />}
              placeholder="请输入手机号或邮箱"
              allowClear
              autoComplete="off"
              onChange={e => {
                checkedPhoneBindCustomer(e, passwordForm);
              }}
            />
          </Form.Item>
          {phoneBindCustomers.length > 1 && (
            <Form.Item className="form-select" name="companyName" rules={rules.companyName}>
              <Select className="form-select-control" options={phoneBindCustomers} placeholder="请选择公司" />
            </Form.Item>
          )}
          <Form.Item name="password" normalize={transformInputValue} rules={rules.password}>
            <Input.Password prefix={<LockOutlined className="site-form-item-icon" />} placeholder="请输入密码" />
          </Form.Item>
          <Form.Item className="login-btn-wrap">
            <Button loading={loading || innerLoading} type="primary" htmlType="submit" className="login-form-button">
              登录
            </Button>
            <div className="tool-wrap">
              <a className="login-form-forgot" href={"/auth/password-reset"}>
                忘记密码
              </a>
              <a href={"/auth/register"}>免费注册</a>
            </div>
          </Form.Item>
        </Form>
      ),
    },
    {
      label: "游客登录",
      key: "sms",
      children: (
        <Form form={smsForm} name="normal_login_sms" className="login-form sms-login-form" size="large" onFinish={async e => onSmsFinish(e)}>
          <Form.Item name="phone" rules={rules.phone}>
            <Input
              prefix={<TabletOutlined className="site-form-item-icon" />}
              placeholder="请输入手机号"
              allowClear
              autoComplete="off"
              onChange={e => {
                checkedPhoneBindCustomer(e, smsForm);
              }}
            />
          </Form.Item>
          {phoneBindCustomers.length > 1 && (
            <Form.Item className="form-select" name="companyName" rules={rules.companyName}>
              <Select className="form-select-control" options={phoneBindCustomers} placeholder="请选择公司" />
            </Form.Item>
          )}
          {/* ========== 短信验证码组件 ========== */}
          <SmsCaptcha uuid={"loginPassword"} captchaCodeLabel={""} smsCodeLabel={""} smsSendType={SmsSendTypeEnum.LOGIN} />
          <Form.Item>
            <Button loading={loading || innerLoading} type="primary" htmlType="submit" className="login-form-button">
              登录
            </Button>
            <div className="tool-wrap">
              <a href={"/auth/register"}>免费注册</a>
            </div>
          </Form.Item>
        </Form>
      ),
    },
  ];

  /** 渲染登录表单 */
  return (
    <div className={style.wrapper}>
      <div className={`login${className2 ? " " + className2 : ""}`}>
        <Tabs items={renderItems} className="login-tabs" onChange={handleChangeTab} />
        {/* TODO 扫码登录 */}
        <div className="login-qrcode" />
      </div>
    </div>
  );
};

export default LoginForm;
