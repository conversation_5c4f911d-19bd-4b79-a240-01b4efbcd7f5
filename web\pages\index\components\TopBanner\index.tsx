// 首页Banner-接口数据获取
import React, { useContext, useEffect, useState } from "react";
import style from "./index.module.less";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import TmBanner from "@/components/TmBanner";
import { IBannerType } from "~/typings/data/spread";
import { useOss } from "@/hooks/useOss";

export default function TopBanner() {
  const { state } = useContext<IContext>(useStoreContext());
  const ossHook = useOss();
  const [banner, setBanner] = useState<IBannerType[]>([]);

  useEffect(() => {
    if (!banner.length) {
      const banner = (state.indexData?.adv?.content || []).map(item => {
        item.image = ossHook.generateOssFullFilepath(item.image);
        return item;
      });
      setBanner(banner);
    }
  }, []);

  return (
    <div className={style.wrapper}>
      <div className="top-banner">
        <TmBanner banners={banner} clsName="top-banner-content" />
      </div>
    </div>
  );
}
