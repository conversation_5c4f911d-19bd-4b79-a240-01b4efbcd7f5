@descriptionTitleColor: #6f6f6f;
@descriptionLabelColor: #6d7278;
@descriptionMainPriceFontSize: 18px;
@descriptionLabelFontSize: 16px;
@descriptionTextFontSize: 14px;
@descriptionMinTextFontSize: 13px;
@descriptionContentColor: #333333;
@descriptionLinkColor: #0091ff;
@titleBackgroundColor: #e5e5e5;
@contentBorderColor: #f2f2f2;
@contentLinkColor: #e02020;
@contentBtnHoverColor: #ea6969;
@contentBackgroundWhiteColor: #ffffff;
@summary-content-width: 290px;
@const-money-width: 330px;
@content-top: 16px;
@content-margin: @content-top;
@receiptTypeColor: #f7b500;
@createDataColor: #333333;
@orderNoColor: #0091ff;

.wrapper {
  :global {
    .return2list {
      margin-left: 10px;
      border: 1px solid #d9d9d9;
      padding: 5px;
      color: #8c8c8c;
      font-size: 12px;
      &:hover {
        border: 1px solid #bfbfbf;
      }
    }
    .order-container {
      .flex-col(center, normal);
      > div > div:not(:last-child) {
        margin-bottom: @plate-margin-top;
        padding: 5px 0;
      }
      // 描述组件样式重置
      .ant-descriptions {
        .ant-descriptions-header {
          margin-bottom: 2px;
          .ant-descriptions-title {
            font-size: @descriptionTextFontSize;
            font-weight: 600;
            color: @descriptionTitleColor;
          }
        }
        .ant-descriptions-view {
          padding: 10px;
          background: #f9f9f9;
          .ant-descriptions-item {
            padding-bottom: 8px;
            .ant-descriptions-item-label {
              color: @descriptionLabelColor;
            }
            .ant-descriptions-item-content {
              .receipt-type {
                color: @receiptTypeColor;
              }
              .create-date {
                color: @createDataColor;
              }
              .order-no {
                color: @orderNoColor;
              }
            }
          }
        }
      }

      // 订单追踪
      .order-tracking {
        width: 100%;
        padding-bottom: @content-top;
        .tracking-logs {
          max-height: 150px;
          overflow-y: auto;
          border: 1px solid @contentBorderColor;
          padding: @content-margin;
          .tracking-log-item {
            margin-top: 6px;
            color: @descriptionLabelColor;
            border-bottom: 1px solid @contentBorderColor;
            display: flex;
            flex-shrink: 0;
            .create-date {
              position: relative;
              padding-left: 12px;
              &:before {
                content: "";
                position: absolute;
                left: 0;
                top: 10px;
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: @contentLinkColor;
              }
            }
            .operator {
              margin: 0 @content-margin;
              color: @descriptionLinkColor;
            }
            .action {
              flex: 1;
            }
          }
        }
        .ant-empty {
          margin: 0;
          padding: 4px;
          border: 1px solid @contentBorderColor;
        }
      }

      // 产品列表
      .products-card {
        &-title {
          font-size: @descriptionTextFontSize;
          font-weight: 600;
          padding-bottom: 5px;
          color: @descriptionTitleColor;
        }
        .order-products {
          margin-top: 4px;
          border: 1px solid @contentBorderColor;
          // 红色高亮文本
          .text-red-highlight {
            padding-left: 4px;
            color: @contentLinkColor !important;
            .ellipsis();
            display: inline;
          }

          // 产品数量标题
          .quantity-row {
            padding: 8px;
            .quantity-header-title {
              .flex-row(space-between, center);
              font-size: 12px;
            }

            .quantity-body-title {
              padding: 0 22px;
              .flex-row(space-between, center);
            }
          }

          .quantity-header-tip {
            list-style: none;
          }

          // 产品包装
          .product-packing-unit {
            font-size: 12px;
          }

          // sku链接
          .product-detail-link {
            &:hover {
              color: #e02020;
              text-decoration: underline;
            }
          }

          // 表格单元格-省略号设置
          .text-ellipsis {
            .ellipsis();
          }

          // 表头
          .ant-table-thead {
            .ant-table-cell {
              tr th {
                background: orange !important;
                font-size: 12px !important;
              }
            }
          }
          .ant-table-tbody {
            tr td {
              padding-left: 0;
              padding-right: 0;
            }
          }
          // 表拓展-统计-产品数量、产品金额+优惠券+运费
          .ant-table-summary {
            // 移出边框线
            tr > td {
              border-bottom: none;
              padding: 4px;
            }
            .ant-table-cell:nth-child(2) {
              .summary-cell-row {
                width: @summary-content-width;
                font-size: @descriptionMinTextFontSize;
                .flex-row(space-between, center);
                padding-right: 16px;
                span {
                  color: @descriptionLabelColor;
                }
                .title {
                  width: 72px;
                  display: inline-block;
                  text-align: right;
                }
                .price-main {
                  font-size: @descriptionMainPriceFontSize;
                  color: @contentLinkColor;
                }
              }
            }
          }
        }
      }
    }
  }
}

:global {
  .red-tip {
    color: @contentLinkColor;
  }
  .blue-tip {
    color: @descriptionLinkColor;
  }
}
