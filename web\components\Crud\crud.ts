import { ArgTableDefaultProps } from "@/typings/crud.interface";
import { useState } from "react";
import { initData } from "@/apis/data";
import moment from "moment";
import { Form, notification } from "antd";

// CRUD配置
const CRUD = (props: ArgTableDefaultProps) => {
  const defaultOptions = {
    url: "",
    tag: "default",
    idField: "id",
    queryParams: {},
    crudMethod: {}, // crud方法
    pagination: {
      page: 1,
      size: props.pageSize ?? 10,
      total: 0,
    },
    time: 50,
    props: {
      searchToggle: false,
    },
  };
  const options = mergeOptions(defaultOptions, props);
  const data = {
    ...options,
    loading: false, // 整体loading
    tableData: [],
  };
  const methods = {
    setSearchToggle: () => {
      setCrud(item => {
        item.props.searchToggle = !item.props.searchToggle;
        return { ...item };
      });
    },
    getSearchToggle: (): boolean => {
      return !crud.props.searchToggle;
    },
    /* 配置查询参数 */
    setQueryParams: async (params: any) => {
      setCrud(item => {
        item.queryParams = { ...item.queryParams, ...params };
        return { ...item };
      });
      crud.queryParams = params;
    },
    /* 获取查询参数 */
    getQueryParams: () => {
      // 清除参数无值的情况
      Object.keys(crud.queryParams).forEach(item => {
        if (crud.queryParams[item] === null || crud.queryParams[item] === "") {
          crud.queryParams[item] = undefined;
        }
        if (typeof crud.queryParams[item] === "string") {
          crud.queryParams[item] = crud.queryParams[item].trim();
        }
        // 简单处理时间问题
        if (item.endsWith("dDate") && crud.queryParams[item]) {
          const formatStr = "YYYY-MM-DD";
          crud.queryParams[item] = `${moment(crud.queryParams[item]?.[0]).format(formatStr)}~${moment(crud.queryParams[item]?.[1]).format(formatStr)}`;
        }
      });
      return {
        page: crud.pagination.page,
        size: crud.pagination.size,
        sort: crud.sort,
        ...crud.queryParams,
      };
    },
    /* 查询 */
    toQuery: async () => {
      crud.pagination.page = 1;
      crud.refresh();
    },
    /* 加载数据 | 刷新数据 */
    refresh: async (): Promise<any> => {
      const requestParams = await crud.getQueryParams();
      return await new Promise((resolve, reject) => {
        setCrud(item => {
          item.loading = true;
          return { ...item };
        });
        initData(crud.url, requestParams)
          .then(res => {
            setTimeout(() => {
              setCrud(item => {
                item.pagination.total = res.data.total;
                item.tableData = res.data.content;
                item.loading = false;
                return { ...item };
              });
            }, crud.time);
            resolve(res);
          })
          .catch(err => {
            setCrud(item => {
              item.loading = false;
              return { ...item };
            });
            notification.error({ message: err?.data?.message ?? "网络错误，请稍后再试！" });
            // reject(err);
          });
      });
    },
    // 页改变处理
    pageChangeHandler: async (page: number, pageSize: number) => {
      await setCrud(item => {
        item.pagination.page = page;
        item.pagination.size = pageSize;
        return { ...item };
      });
      await crud.refresh();
    },
    handleOnkeydownEvent: async (e, fieldsValue?) => {
      if (e.keyCode === 13) {
        crud.data = [];
        fieldsValue && (await crud.setQueryParams(fieldsValue));
        await crud.refresh();
      }
    },
  };
  const defaultCrud = Object.assign({}, data, methods);
  const [crud, setCrud] = useState({ ...defaultCrud });
  return crud;
};
function mergeOptions(src, opts) {
  const optsRet = {
    ...src,
  };
  for (const key in src) {
    if (Object.hasOwnProperty.call(opts, key)) {
      optsRet[key] = opts[key];
    }
  }
  return optsRet;
}
export default CRUD;
