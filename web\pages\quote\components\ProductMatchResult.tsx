import React, { useState } from "react";
import { Table, Tag, Alert, Button, InputNumber, message, Image, Tooltip } from "antd";
import { CheckCircleOutlined, ExclamationCircleOutlined, EditOutlined } from "@ant-design/icons";
import { ghmallGuidePrice2Show } from "@/utils/price-format.util";
// 临时定义类型，避免路径问题
enum ProductMatchStatus {
  MATCHED = 'MATCHED',
  PARTIAL_MATCH = 'PARTIAL_MATCH',
  NOT_MATCHED = 'NOT_MATCHED',
  MANUAL_INPUT = 'MANUAL_INPUT'
}

interface ProductMatchResultProps {
  matchResult: any;
  quoteRequestId: number | null;
  onNext: () => void;
}

const ProductMatchResult: React.FC<ProductMatchResultProps> = ({ 
  matchResult, 
  quoteRequestId, 
  onNext 
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [editingKey, setEditingKey] = useState<string>("");
  const [products, setProducts] = useState(matchResult?.matchedProducts || []);

  // 处理数量变更
  const handleQuantityChange = (value: number, record: any) => {
    const newProducts = products.map(item =>
      item.id === record.id ? { ...item, quantity: value } : item
    );
    setProducts(newProducts);
  };

  // 获取状态标签
  const getStatusTag = (status: ProductMatchStatus) => {
    switch (status) {
      case ProductMatchStatus.MATCHED:
        return <Tag color="success" className="status-tag matched">已匹配</Tag>;
      case ProductMatchStatus.PARTIAL_MATCH:
        return <Tag color="warning" className="status-tag partial">部分匹配</Tag>;
      case ProductMatchStatus.NOT_MATCHED:
        return <Tag color="error" className="status-tag not-matched">未匹配</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "产品图片",
      dataIndex: "productImage",
      key: "productImage",
      width: 80,
      render: (image: string, record: any) => (
        <Image
          width={50}
          height={50}
          src={image || "/images/default-product.png"}
          alt={record.productName}
          style={{ objectFit: "cover", borderRadius: 4 }}
          fallback="/images/default-product.png"
        />
      ),
    },
    {
      title: "产品信息",
      key: "productInfo",
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.productName}
          </div>
          <div style={{ fontSize: 12, color: "#666" }}>
            SKU: {record.sku}
          </div>
          {record.brandName && (
            <div style={{ fontSize: 12, color: "#666" }}>
              品牌: {record.brandName}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "规格",
      dataIndex: "specification",
      key: "specification",
      width: 120,
      render: (spec: string, record: any) => (
        <div>
          {spec || record.packingRatio ? `${record.packingRatio}${record.unit || ""}` : "-"}
        </div>
      ),
    },
    {
      title: "询价数量",
      dataIndex: "quantity",
      key: "quantity",
      width: 120,
      render: (quantity: number, record: any) => (
        <InputNumber
          min={1}
          value={quantity}
          onChange={(value) => handleQuantityChange(value || 1, record)}
          style={{ width: "100%" }}
        />
      ),
    },
    {
      title: "预估价格",
      dataIndex: "estimatedPrice",
      key: "estimatedPrice",
      width: 120,
      render: (price: number) => (
        <span style={{ color: "#f50" }}>
          {price ? ghmallGuidePrice2Show(price) : "询价"}
        </span>
      ),
    },
    {
      title: "匹配状态",
      dataIndex: "matchStatus",
      key: "matchStatus",
      width: 100,
      render: (status: ProductMatchStatus) => getStatusTag(status),
    },
    {
      title: "操作",
      key: "action",
      width: 80,
      render: (_, record: any) => (
        <Tooltip title="编辑产品信息">
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={() => setEditingKey(record.id)}
          />
        </Tooltip>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.matchStatus === ProductMatchStatus.NOT_MATCHED,
    }),
  };

  // 未匹配产品表格列
  const unmatchedColumns = [
    {
      title: "行号",
      dataIndex: "rowIndex",
      key: "rowIndex",
      width: 80,
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      key: "productName",
    },
    {
      title: "SKU",
      dataIndex: "sku",
      key: "sku",
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      width: 100,
    },
    {
      title: "未匹配原因",
      dataIndex: "reason",
      key: "reason",
      render: (reason: string) => (
        <span style={{ color: "#ff4d4f" }}>{reason}</span>
      ),
    },
  ];

  const handleContinue = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一个产品继续！");
      return;
    }
    onNext();
  };

  return (
    <div className="match-result">
      {/* 匹配结果摘要 */}
      <div className="result-summary">
        <div className="summary-item">
          <span className="label">总行数:</span>
          <span className="value">{matchResult.totalRows}</span>
        </div>
        <div className="summary-item">
          <span className="label">匹配成功:</span>
          <span className="value success">{matchResult.matchedCount}</span>
        </div>
        <div className="summary-item">
          <span className="label">未匹配:</span>
          <span className="value error">{matchResult.unmatchedCount}</span>
        </div>
        <div className="summary-item">
          <span className="label">已选择:</span>
          <span className="value">{selectedRowKeys.length}</span>
        </div>
      </div>

      {/* 匹配成功的产品 */}
      {products.length > 0 && (
        <>
          <Alert
            message="产品匹配结果"
            description="请检查匹配的产品信息，确认数量，并选择需要询价的产品。"
            type="info"
            showIcon
            icon={<CheckCircleOutlined />}
            style={{ marginBottom: 16 }}
          />
          
          <Table
            className="match-table"
            columns={columns}
            dataSource={products}
            rowSelection={rowSelection}
            rowKey="id"
            pagination={false}
            size="small"
            scroll={{ x: 800 }}
          />
        </>
      )}

      {/* 未匹配的产品 */}
      {matchResult.unmatchedRows && matchResult.unmatchedRows.length > 0 && (
        <div className="unmatched-section">
          <Alert
            message="未匹配的产品"
            description={`以下 ${matchResult.unmatchedRows.length} 个产品未能匹配到系统中的产品，请检查产品名称或SKU是否正确。`}
            type="warning"
            showIcon
            icon={<ExclamationCircleOutlined />}
          />
          
          <Table
            columns={unmatchedColumns}
            dataSource={matchResult.unmatchedRows}
            rowKey="rowIndex"
            pagination={false}
            size="small"
            style={{ marginTop: 16 }}
          />
        </div>
      )}

      {/* 操作按钮 */}
      <div style={{ textAlign: "center", marginTop: 30 }}>
        <Button 
          type="primary" 
          size="large"
          onClick={handleContinue}
          disabled={selectedRowKeys.length === 0}
        >
          继续处理选中的产品 ({selectedRowKeys.length})
        </Button>
      </div>
    </div>
  );
};

export default ProductMatchResult;
