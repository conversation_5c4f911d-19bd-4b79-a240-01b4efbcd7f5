import request from "@/utils/request.util";

export function show(orderNo: string) {
  return request({
    url: `/api/ucenter/orders/${orderNo}`,
    method: "get",
  });
}

export function cancel(orderNo: string, reasonData) {
  return request({
    url: `/api/ucenter/orders/${orderNo}`,
    method: "delete",
    data: reasonData
  });
}

export function getOrderTracking(orderNo: string) {
  return request({
    url: `/api/ucenter/orders/${orderNo}/tracking`,
    method: "get",
  });
}

/** 订单合同下载 */
export function downloadOrderContract(orderNo: string) {
  return request({
    url: `/api/ucenter/orders/${orderNo}/download-contract`,
    method: "get",
    responseType: 'blob'
  })
}

export function getMemberOrderHistory() {
  return request({
    url: `/api/ucenter/orders/memberOrderHistory`,
    method: "get",
  });
}

export function getProductSalesHistory(params: any) {
  return request({
    url: `/api/ucenter/orders/products/sales-history`,
    method: "get",
    params
  });
}

export default {
  show,
  cancel,
  getOrderTracking,
  downloadOrderContract,
  getProductSalesHistory
};
