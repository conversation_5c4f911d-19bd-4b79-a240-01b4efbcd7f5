.videoContainer {
    position: relative;
    width: 100%;
    background-color: #000;
    overflow: hidden;
  }
  
  .video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  
  .customControls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.5));
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  
    &:hover {
      opacity: 1;
    }
  }
  
  .playButton {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      opacity: 0.8;
    }
    
    &:focus {
      outline: none;
    }
  } 