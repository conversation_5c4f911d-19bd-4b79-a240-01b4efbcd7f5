@alert-color: #ea7b79;
@errorTextFontSize: 13px;

.wrapper {
  :global {
    .credential {
      /*进度条*/
      .progress {
        @activeColor: #a4c75e;
        @normalColor: #bfbfbf;
        @itemHeight: 34px;
        @borderHeight: (@itemHeight / 2);
        display: flex;
        gap: 18px;
        font-size: 14px;
        &-item {
          cursor: pointer;
          width: 200px;
          height: @itemHeight;
          position: relative;
          text-align: center;
          line-height: @itemHeight;
          background-color: @normalColor;
          color: #fff;
          &:before {
            content: "";
            width: 0;
            height: 0;
            border-top: @borderHeight solid transparent;
            border-bottom: @borderHeight solid transparent;
            border-left: @borderHeight solid #fff;
            position: absolute;
            top: 0;
            left: 0;
          }
          &:after {
            content: "";
            width: 0;
            height: 0;
            border-top: @borderHeight solid transparent;
            border-bottom: @borderHeight solid transparent;
            border-left: @borderHeight solid @normalColor;
            position: absolute;
            top: 0;
            right: -@borderHeight;
          }
        }
        .current {
          background-color: @activeColor;
          &:after {
            border-left: @borderHeight solid @activeColor;
          }
        }
      }
      /* 资质提交表单 */
      .qualification-form {
        .ant-form-item-label {
          width: 108px !important;
          .form-label-required::before {
            display: inline-block;
            margin-right: 4px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: "*";
          }
        }
        .ant-space.need-upload-wrapper {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          flex-direction: row;
          align-items: baseline;
          /* form-错误信息设置长度 */
          .ant-form-item-explain-error {
            width: 100px !important;
            font-size: @errorTextFontSize;
          }
          .ant-space-item {
            width: 108px;
            /*图片上传项*/
            .upload-item-box {
              .ant-form-item-control-input-content {
                .flex-row(space-between, center);
              }
            }
          }
        }
        /*提交按钮*/
        .form-item-submit {
          margin-top: 12px;
          .ant-form-item-control-input-content {
            display: flex;
            justify-content: center;
          }
        }
        /* 示例 */
        .example-box {
          position: relative;
          top: 0;
          width: 342px;
          height: 136px;
          display: inline-flex;
          align-items: flex-start;
          padding: 4px 12px;
          .example-tips {
            width: 202px;
            color: #777;
            font-size: 12px;
            display: block;
            li {
              list-style: inside;
              &:first-child {
                color: #333;
                font-size: 13px;
                list-style: none;
              }
            }
          }
          .example-img {
            flex: 1;
            position: relative;
            margin-left: 16px;
            span {
              position: absolute;
              left: 0;
              top: 0;
              z-index: 2;
              padding: 4px;
              font-size: 12px;
              background: rgba(0, 0, 0, 0.5);
              color: #fff;
            }
            .ant-image {
              img {
                height: 120px;
              }
            }
            &:hover {
              span {
                display: none;
              }
            }
          }
        }
      }

      .blue-tip {
        color: #40a9ff;
      }

      /*问答与下载*/
      .qa-box {
        display: flex;
        .title {
          display: inline-block;
          font-size: 18px;
          border-bottom: 1px solid #ea7b79;
          margin-bottom: 10px;
        }
        ul {
          border-right: 1px solid #e6e4e4;
          width: 70%;
          li {
            margin-bottom: 10px;
            .question {
              color: #ea7b79;
              font-size: 16px;
            }
            .answer {
              font-size: 14px;
            }
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
        .data-download {
          flex: 1;
          padding-left: 10px;
          &-item {
            height: 50%;
          }
          .data-download-item-list {
            a {
              display: block;
              margin-top: 5px;
              &:hover {
                color: @mallThemeColor;
                text-decoration: underline;
              }
            }
          }
        }
      }
      /*弹出层*/
      .ant-card {
        margin-top: @plate-margin-top;
        .main-title {
          font-size: 18px;
        }
      }
    }
    .antModalBodyScroller(50px);
  }
}
