import { Rule, RuleType } from "@midwayjs/validate";

export class QuoteRequestCreateDto {
  @Rule(RuleType.string().required().min(1).max(200))
  title: string;

  @Rule(RuleType.string().optional().max(1000))
  description?: string;

  @Rule(RuleType.string().required().min(1).max(50))
  contactName: string;

  @Rule(RuleType.string().required().pattern(/^1[3-9]\d{9}$/))
  contactPhone: string;

  @Rule(RuleType.string().optional().email())
  contactEmail?: string;

  @Rule(RuleType.string().optional().max(100))
  companyName?: string;
}

export class QuoteFileUploadDto extends QuoteRequestCreateDto {
  // 文件上传会通过 @File 装饰器处理，这里不需要定义
}

export class QuoteProductUpdateDto {
  @Rule(RuleType.number().required().positive())
  id: number;

  @Rule(RuleType.number().required().positive())
  quantity: number;

  @Rule(RuleType.number().optional().positive())
  quotedPrice?: number;

  @Rule(RuleType.string().optional().max(500))
  remark?: string;
}

export class GenerateQuoteDto {
  @Rule(RuleType.number().required().positive())
  quoteRequestId: number;

  @Rule(RuleType.array().items(RuleType.object()).required())
  products: QuoteProductUpdateDto[];

  @Rule(RuleType.number().optional().min(1).max(365))
  validDays?: number;

  @Rule(RuleType.string().optional().max(1000))
  remark?: string;
}

export class CreateQuickOrderFromQuoteDto {
  @Rule(RuleType.number().required().positive())
  quoteRequestId: number;

  @Rule(RuleType.array().items(RuleType.number().positive()).required().min(1))
  selectedProductIds: number[];

  @Rule(RuleType.number().required().positive())
  receivingAddressId: number;

  @Rule(RuleType.number().optional().positive())
  receiptId?: number;

  @Rule(RuleType.string().optional().max(500))
  remark?: string;

  @Rule(RuleType.string().optional())
  sendType?: string;
}

export class QuoteQueryDto {
  @Rule(RuleType.number().optional().min(1))
  page?: number;

  @Rule(RuleType.number().optional().min(1).max(100))
  size?: number;

  @Rule(RuleType.string().optional().valid('DRAFT', 'PENDING', 'QUOTED', 'ACCEPTED', 'REJECTED', 'EXPIRED'))
  status?: string;

  @Rule(RuleType.string().optional().max(100))
  keyword?: string;

  @Rule(RuleType.string().optional().isoDate())
  startDate?: string;

  @Rule(RuleType.string().optional().isoDate())
  endDate?: string;
}
