.wrapper {
  :global {
    .paymentWrapper {
      background-color: @main-bg-color-white;
      padding: 10px 2px;
      .label {
        font-size: 24px;
        margin-bottom: 15px;
        padding-bottom: 10px;
      }
      .payment-head-box {
        border-bottom: 1px solid #d9d9d9;
        padding: 10px 20px;
        position: relative;
        .goto-my-order {
          position: absolute;
          bottom: 16px;
          right: 12px;
          span {
            text-decoration: underline;
          }
        }
        .need-pay {
          float: right;
          .price {
            color: @mallThemeColor;
          }
        }
        .sub-tips {
          margin-top: 5px;
          font-size: 18px;
          color: #8c8c8c;
        }
      }
      .pay-card-item {
        margin-top: 16px;
        &-title {
          font-size: 24px;
          margin-bottom: 15px;
          padding-bottom: 10px;
        }
        .ant-card-head-title {
          padding: 10px 0;
        }
        .ant-descriptions-item-label,
        .ant-descriptions-item-content {
          font-size: 16px;
        }
        .tip {
          color: @mallThemeColor;
        }
        .be-careful {
          color: #b7b7b7;
          padding: 10px 0 10px 55px;
          b {
            color: #595959;
          }
        }
        .be-careful2 {
          font-size: 16px;
          padding: 10px 0 10px 55px;
        }
      }
      .payment-content-box {
        padding: 10px 20px;
        border-bottom: 1px solid #d9d9d9;
        .ant-radio-wrapper {
          border: 1px solid #d5d5d5;
          padding: 5px;
        }
        .ant-radio-wrapper-checked {
          border-color: @mallThemeColor;
        }
        .box-item {
          > span {
            margin-left: 15px;
            position: relative;
          }
          > img {
            border-radius: 10px;
            width: 60px;
            height: 60px;
          }
        }
      }
      .payment-footer-box {
        padding: 10px 20px;
        .item-box {
          .flex-center();
        }
        .confirm-btn {
          font-size: 22px;
          text-align: center;
          background-color: @mallThemeColor;
          width: 150px;
          height: 50px;
          line-height: 50px;
          color: #fff;
          cursor: pointer;
        }
      }
    }
  }
}
:global {
  .paymethod-modal {
    .tip {
      width: 260px;
      margin-left: 86px;
      text-align: center;
      opacity: 0.96;
      font-size: 14px;
      color: #555555;
      line-height: 14px;
      margin-bottom: 8px;
      .second {
        font-size: 14px;
        color: #ed3f14b3;
        letter-spacing: 0;
        text-align: center;
        line-height: 21px;
      }
    }
    .canvas {
      width: 260px;
      height: 260px;
      margin: 10px 0 20px 86px;
      background: #ffffff;
      box-shadow: 0 2px 8px 0 rgb(0 0 0 / 10%);
      padding: 14px;
      img {
        width: 232px;
        height: 232px;
      }
    }
    .intro {
      height: 80px;
      .scan {
        position: absolute;
        left: 163px;
        bottom: 65px;
        width: 32px;
        height: 32px;
      }
      .tip2 {
        padding-left: 190px;
        text-align: left;
        .paymethod-text {
          color: #ed3f14ba;
        }
      }
    }
    .go-back {
      position: absolute;
      left: 14px;
      bottom: 15px;
      font-size: 14px;
      color: #0079f2;
      letter-spacing: 0;
      line-height: 14px;
      cursor: pointer;
    }
    .paymethod-img {
      position: absolute;
      top: 84px;
      right: 92px;
      width: 300px;
      height: 376px;
      img {
        width: 100%;
      }
    }
  }
}
