import { Controller, Get, Inject } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IMemberService } from "@/service/member/member.service";
import { IWalletService } from "@/service/member/wallet.service";

@Controller("/ucenter", { middleware: [AuthenticationMiddleware] })
export class IndexController extends BaseController {
  @Inject("MemberService")
  memberService: IMemberService;

  @Inject("WalletService")
  walletService: IWalletService;

  @Get()
  async index() {
    return this.ctx.redirect(`/ucenter/trade/order`);
  }

  @Get("/footprint")
  async footprint() {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/message")
  async message() {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/contactus")
  async contactus() {
    const result = await this.memberService.getMemberConsultant(this.getMemberId());
    this.ctx.memberContact = result?.data;

    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/my-wallet")
  async wallet() {
    const result = await this.walletService.getWallet(this.getMemberId());
    this.ctx.memberWalletData = result.data;

    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/old-mall")
  async oldMall() {
    const { ctx } = this;
    ctx.cmid = ctx.session?.userData?.cmid ?? null;
    ctx.body = await render<Readable>(ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
