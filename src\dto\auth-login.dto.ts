import { Rule, RuleType } from "@midwayjs/validate";
import { CustomHelpers } from "joi";

export class AuthLoginDto {
  @Rule(
    RuleType.string()
      .required()
      .custom((value: string, helpers: CustomHelpers) => {
        const phoneReg = /^1[3456789]\d{9}$/;
        const mailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
        if (!mailReg.test(value) && !phoneReg.test(value)) {
          return helpers.error("any.invalid");
        }
        return value;
      })
      .error(new Error("`phone`格式不正确，请输入正确的手机号或邮箱！"))
  )
  phone: string;

  @Rule(RuleType.string().required())
  password: string;

  @Rule(RuleType.string())
  companyName: string;
}
