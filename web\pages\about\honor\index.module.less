@honorTitleFontSize: 24px;
@honorTitleColor: #333333;
@honorTextFontSize: 14px;
@honorTextColor: #d13a3e;
@honorBorderColor: #f5f5f5;

.wrapper {
  :global {
    .flex-row();
    .honor {
      width: 100%;
      padding: 32px;
      background-color: #ffffff;
      .honor-content {
        margin-top: 48px;
        .ant-card-head {
          .ant-card-head-title {
            padding: 8px 0;
            font-size: @honorTitleFontSize;
            color: @honorTitleColor;
          }
        }
        .ant-card-body {
          .honor-content-ul {
            display: grid;
            grid-template-columns: repeat(auto-fill, 200px);
            grid-gap: 24px;
            justify-content: center;
            /*兼容IE11写法*/
            _:-ms-fullscreen,
            & {
              display: flex;
              flex-wrap: wrap;
              justify-content: flex-start;
              li {
                margin: 0 11px 21px 10px;
              }
            }
            li {
              width: 200px;
              border: 1px solid @honorBorderColor;
              .ant-card-body {
                padding: 12px;
                .ant-card-meta-title {
                  text-align: center;
                }
              }
            }
            // antd-card
            .ant-card {
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
}
