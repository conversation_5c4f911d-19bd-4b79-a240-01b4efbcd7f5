import { Controller, Get, Inject, Post, Query } from "@midwayjs/decorator";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { BaseController } from "@/controller/base.controller";
import { ICcMallService } from "@/service/member/cc-mall.service";

@Controller("/api/ucenter/cc-mall")
export class CcMallController extends BaseController {
  @Inject("CcMallService")
  ccMallService: ICcMallService;

  @Post("/quicklogin", { middleware: [AuthenticationMiddleware] })
  async quickLogin2mall() {
    const { ctx } = this;
    const cmid = ctx.session?.userData?.cmid ?? null;
    if (!cmid) {
      return ctx.getResponseInstance(ctx).sendFail("非法请求，当前客户未关联旧商城，请联系客服处理！", 400);
    }
    const res = await this.ccMallService.generateQuickLoginUrl(cmid);
    return ctx.getResponseInstance(ctx).setResponseData(res).send();
  }

  @Get("/code-verify")
  async findCmidByVerifyCode(@Query("code") code: string) {
    const { ctx } = this;
    const res = await this.ccMallService.findCodeValue(code);
    return ctx.getResponseInstance(ctx).setResponseData(res).send();
  }
}
