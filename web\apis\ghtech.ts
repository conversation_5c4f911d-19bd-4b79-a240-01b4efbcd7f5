import request from "@/utils/request.util";
import { IMSDSRelationParams, ICOARelationParams } from "~/typings/data/ghtech/ghtech";

/**
 * 获取coa
 * @param productName
 */
export function getProductCoa(productName: string) {
  return request({
    url: "/api/ghtech/coa",
    params: {
      productName,
    },
  });
}

/**
 * 产品名称获取MSDS
 * @param productName
 */
export function getMSDSByProductName(productName: string) {
  return request({
    url: "/api/ghtech/msds",
    method: "get",
    params: {
      productName,
    },
  });
}

/** MSDS单个获取 */
export function getProductMSDSByRelation(params: Partial<IMSDSRelationParams>) {
  return request({
    url: "/api/ghtech/msds-relation",
    method: "get",
    params
  })
}

/** MSDS单个获取 */
export function getProductCOAByRelation(params: ICOARelationParams) {
  return request({
    url: "/api/ghtech/coa-relation",
    method: "get",
    params
  })
}

export default {
  getProductCoa,
  getMSDSByProductName,
  getProductMSDSByRelation,
  getProductCOAByRelation
};
