.wrapper {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  :global {
    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 8px;

      .ant-card-head {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-bottom: none;
        border-radius: 8px 8px 0 0;
        
        .ant-card-head-title {
          color: #fff;
          font-size: 18px;
          font-weight: 600;
        }

        .ant-card-extra {
          .ant-btn-primary {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            font-weight: 500;
            
            &:hover {
              background: rgba(255, 255, 255, 0.3);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }

      .ant-card-body {
        padding: 30px;
      }
    }

    .search-area {
      background: #fafafa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid #f0f0f0;

      .ant-space {
        width: 100%;
        flex-wrap: wrap;
      }

      .ant-input,
      .ant-select-selector,
      .ant-picker {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        
        &:focus,
        &:hover {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }

      .ant-btn {
        border-radius: 6px;
        font-weight: 500;
        
        &.ant-btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
          
          &:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
          }
        }
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid #f0f0f0;
      }

      .ant-table-tbody > tr {
        &:hover > td {
          background: #f8f9ff !important;
        }

        > td {
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;
        }
      }

      .ant-table-pagination {
        margin: 20px 0 0 0;
        
        .ant-pagination-item {
          border-radius: 6px;
          
          &.ant-pagination-item-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            
            a {
              color: #fff;
            }
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          border-radius: 6px;
        }
      }
    }

    .ant-tag {
      border-radius: 4px;
      font-size: 12px;
      padding: 2px 8px;
      font-weight: 500;
      
      &.ant-tag-success {
        background: #f6ffed;
        border-color: #b7eb8f;
        color: #52c41a;
      }
      
      &.ant-tag-warning {
        background: #fff7e6;
        border-color: #ffd591;
        color: #fa8c16;
      }
      
      &.ant-tag-error {
        background: #fff2f0;
        border-color: #ffccc7;
        color: #ff4d4f;
      }
      
      &.ant-tag-processing {
        background: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
      }
      
      &.ant-tag-default {
        background: #fafafa;
        border-color: #d9d9d9;
        color: #666;
      }
    }

    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.ant-btn-text {
        padding: 4px 8px;
        height: auto;
        
        &:hover {
          background: rgba(102, 126, 234, 0.1);
          color: #667eea;
        }
        
        &.ant-btn-dangerous:hover {
          background: rgba(255, 77, 79, 0.1);
          color: #ff4d4f;
        }
      }
    }

    // 链接样式
    a {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        color: #5a6fd8;
        text-decoration: underline;
      }
    }

    // 空状态样式
    .ant-empty {
      padding: 40px 0;
      
      .ant-empty-description {
        color: #999;
      }
    }

    // 加载状态样式
    .ant-spin-container {
      min-height: 200px;
    }

    // 响应式设计
    @media (max-width: 768px) {
      .container {
        padding: 0 10px;
      }

      .ant-card-body {
        padding: 20px !important;
      }

      .search-area {
        padding: 15px !important;
        
        .ant-space {
          .ant-space-item {
            margin-bottom: 8px;
          }
        }
        
        .ant-input,
        .ant-select,
        .ant-picker {
          width: 100% !important;
        }
      }

      .ant-table {
        .ant-table-content {
          overflow-x: auto;
        }
        
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 12px;
          font-size: 12px;
        }
      }
    }

    @media (max-width: 480px) {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 16px !important;
        }
        
        .ant-card-extra {
          .ant-btn {
            font-size: 12px;
            padding: 4px 8px;
            height: auto;
          }
        }
      }
    }
  }
}
