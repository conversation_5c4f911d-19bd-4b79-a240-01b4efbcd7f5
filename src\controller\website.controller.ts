import { Controller, Get, HttpCode } from "@midwayjs/decorator";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { BaseController } from "@/controller/base.controller";

@Controller("/")
export class WebsiteController extends BaseController {
  @Get("/report")
  @HttpCode(200)
  async coa(): Promise<void> {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }
}
