import { Body, Controller, Del, Get, Inject, Param, Post, Put, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { AuthenticationMiddleware } from "@/middleware/authentication.middleware";
import { IAddressService } from "@/service/member/address.service";
import { addressCreateDto, addressQueryListDto, addressUpdateDto } from "~/typings/data/member/address";
import { Validate } from "@midwayjs/validate";

@Controller("/api/ucenter/addresses", { middleware: [AuthenticationMiddleware] })
export class AddressController extends BaseController {
  @Inject("AddressService")
  addressService: IAddressService;

  /**
   * @desc 地址列表
   */
  @Get()
  async getLists(@Query() criteria: Partial<addressQueryListDto>) {
    const res = await this.addressService.getPageList(this.getMemberId(), criteria);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 详情
   */
  @Get("/:id/detail")
  async show(@Param("id") addressId: number) {
    const res = await this.addressService.show(this.getMemberId(), addressId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 新增
   */
  @Post()
  @Validate()
  async create(@Body() resource: addressCreateDto) {
    const res = await this.addressService.create(this.getMemberId(), resource);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 更新
   */
  @Put("/:id")
  @Validate()
  async update(@Param("id") addressId: number, @Body() resource: addressUpdateDto) {
    resource.addressId = addressId;
    resource.memberId = this.getMemberId();
    const res = await this.addressService.update(resource);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 默认
   */
  @Put("/:id/default")
  async setDefault(@Param("id") addressId: number) {
    const res = await this.addressService.setDefault(this.getMemberId(), addressId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }

  /**
   * @desc 删除
   */
  @Del("/:id")
  async delete(@Param("id") addressId: number) {
    const res = await this.addressService.delete(this.getMemberId(), addressId);
    return this.ctx.getResponseInstance(this.ctx).setResponseData(res.data).send();
  }
}
