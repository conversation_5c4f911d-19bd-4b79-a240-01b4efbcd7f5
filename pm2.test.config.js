// 用于正式环境部署PM2配置
module.exports = {
  apps: [
    {
      name: 'node-ghmall-portal_test1', // 进程列表中的进程名称。默认值是没有扩展名的脚本文件名
      script: 'bootstrap.js', // 要启动的脚本路径，必填字段
      max_memory_restart: '250M', // 如果超出内存量，重新启动应用
      instances: '4', // 应用启动实例个数，仅在cluster模式有效，默认值是 1；将应用程序分布在所有CPU核心上,可以是整数或负数
      instance_var: 'INSTANCE_ID',
      exec_mode: 'cluster', // 应用程序启动模式，支持fork和cluster模式，默认是fork；
      watch: false, // 监听模式，不能单纯的设置为true，易导致无限重启，因为日志文件在变化，需要排除对其的监听；启用或禁用观察模式，或使用数组指定监控的目录
      merge_logs: true, // 集群情况下，可以合并日志
      error_file: './logs/pm2-err.log',
      out_file: './logs/pm2-out.log',
      log_date_format: 'YYYY-MM-DD HH:mm Z', // 日期格式
      env: {
        NODE_ENV: 'production',
        BAIDU_TJ_ENABLED: '0'  //是否开启百度统计
      }
    }
  ]
}
