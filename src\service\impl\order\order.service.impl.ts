import { Provide, Scope, ScopeEnum } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { IOrderService } from "@/service/order/order.service";
import { orderProductsListDto, orderQueryListDto, orderProductsSalesHistoryListDto } from "~/typings/data/order";
import { OrderLogisticsQueryDto } from "@/dto/order-logistics-query.dto";

@Provide("OrderService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class OrderServiceImpl extends BaseService implements IOrderService {
  /**
   * 获取订单分页列表
   *
   * @param memberId 会员ID
   * @param criteria 条件
   */
  async getOrderPageList(memberId: string, criteria: Partial<orderQueryListDto>): Promise<any> {
    // 获取当前登录会员ID
    return this.easyResponse(await this.easyHttp.get(`/api/orders/${memberId}/lists`, criteria));
  }

  async show(memberId: string, orderNo: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/orders/${memberId}/detail/${orderNo}`));
  }

  /**
   * 历史订单产品列表
   * @param memberId
   * @param criteria
   */
  async getOrderProductsLists(memberId: string, criteria: Partial<orderProductsListDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/order/products/${memberId}/lists`, criteria));
  }

  async getProductSalesHistory(criteria: Partial<orderProductsSalesHistoryListDto>): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/order/products/sales-history`, criteria));
  }

  /**
   * 取消订单
   * @param memberId
   * @param orderNo
   */
  async cancelOrder(memberId: string, orderNo: string, data: { reason: string }): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete(`/api/orders/${memberId}/cancel`, Object.assign({ member_id: memberId, order_no: orderNo }, data)), "操作出错了，请稍后再试！");
  }

  /**
   * 获取订单追踪信息
   * @param orderNo /
   */
  async getOrderTracking(orderNo: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/orders/${orderNo}/tracking-logs`), "操作出错了，请稍后再试！");
  }

  /**
   * <p>查询订单发货物流信息</p>
   *
   * @param orderLogisticsQueryDto /
   */
  async queryDelivery(orderLogisticsQueryDto: OrderLogisticsQueryDto): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/api/orders/quick/query-delivery", orderLogisticsQueryDto));
  }

  // 异步获取会员订单历史
  async getMemberOrderHistory(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get(`/api/orders/memberOrderHistory/${memberId}?page=0&size=20`));
  }
}
