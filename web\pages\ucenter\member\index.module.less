@headCardBgColor: #ff4401; // #ff9188;
@contentTop: 32px;
@simplyInfoTextColor: #ffffff;
@simplyInfoBigFontSize: 18px;
@simplyInfoTitleFontSize: 16px;
@simplyInfoTextFontSize: 14px;
@simplyBtnFontSize: 10px;
@borderColor: #fad2ce;
@contentColor: #333333;
@contentLabelColor: #6d7278;
@contentPaddingOrMargin: 16px;

.wrapper {
  :global {
    .member-container {
      .member-head {
        .flex-row(center, center);
        height: 140px;
        margin: 16px 0;
        &-card {
          .flex-row(normal, flex-start);
          width: 380px;
          height: 100%;
          background-color: @headCardBgColor;
          border-radius: 4px;
          padding: 24px;
          .info-box {
            width: 100%;
            margin-left: 24px;
            .flex-col(center, flex-start);
            color: @simplyInfoTextColor;
            font-size: @simplyInfoTextFontSize;
            position: relative;
            > span {
              &:first-child {
                font-size: @simplyInfoBigFontSize;
                padding-right: 56px;
              }
              display: block;
              width: 100%;
              .ellipsis(1);
            }
            /** 修改密码 */
            .btn-mdf-pass {
              position: absolute;
              top: 0;
              right: 0;
              color: @simplyInfoTextColor;
              &:hover {
                text-decoration: underline;
              }
            }
          }
          .avatar-box {
            .flex-col(flex-start, center);
            gap: 6px;
            height: 100%;
            .avatar {
              height: 62px;
              width: 62px;
            }
            button {
              font-size: 12px;
              color: @contentLabelColor;
            }
          }
        }
        &-other {
          flex: 1;
          height: 100%;
          ul {
            display: flex;
            justify-content: flex-end;
            flex-wrap: wrap;
            li {
              .flex-center(row);
              box-sizing: border-box;
              margin: 0 0 28px 20px;
              width: 170px;
              height: 56px;
              border: 1px solid #e7e7e7;
              border-radius: 2px;
              font-size: @simplyInfoTextFontSize;
              color: @contentLabelColor;
              .icon {
                margin-right: 10px;
                font-size: 18px;
                transition: all 0.1s ease-in-out;
              }
              &:hover {
                cursor: pointer;
                box-shadow: 2px 4px 12px 2px #d5d5d5;

                .icon {
                  margin-right: 12px;
                  color: @contentColor;
                }
                a {
                  color: @contentColor;
                }
              }
            }
          }
        }
      }
      .member-information-box {
        .form-submit {
          background: #ff1b1b;
        }
        margin-top: @contentTop;
        // 重置card头部栏样式
        .ant-card-head {
          padding: 0 @contentPaddingOrMargin !important;
          .ant-card-head-title {
            padding: 8px 0 !important;
          }
        }
        .member-information-header {
          width: 100%;
          .flex-row(space-between, center);
          .btn-mdf-info {
            span {
              text-decoration: underline;
            }
          }
        }
        .member-show {
          .ant-descriptions-item {
            padding-bottom: 8px;
          }
          // 省市区
          .member-pcd {
            .ant-cascader {
              width: 100%;
            }
            .ant-select-arrow {
              display: none;
            }
            .ant-select-selector {
              padding: 0;
              border: unset;
              .ant-select-selection-item {
                color: @contentColor !important;
                line-height: unset;
                transition: unset;
              }
            }
          }
        }
      }
    }
  }
}
