@themeColor: #ff5c58;
.ucenterCardWrapper {
  :global {
    .ucenter-content-head {
      display: flex;
      cursor: pointer;
      margin-bottom: 20px;
      .title {
        padding-left: 14px;
      }

      /*以下修改antd card 默认样式*/
      .ant-card-head {
        width: 100%;
        padding: 0;
        height: 50px;
        &::before {
          content: "";
          width: 3px;
          height: 60%;
          top: 25%;
          background: @themeColor;
          position: absolute;
          left: 0;
        }
      }
      .ant-card-extra {
        color: @themeColor;
      }
      .ant-card-head-tabs {
        display: flex;
        position: absolute;
        width: 100%;
        top: 1px;
        .ant-tabs-nav {
          padding-left: 100px;
          .ant-tabs-nav-list {
            .ant-tabs-tab {
              font-size: 14px;
              &:hover {
                color: @themeColor;
              }
              &.ant-tabs-tab-active {
                .ant-tabs-tab-btn {
                  color: @themeColor;
                }
              }
            }
            .ant-tabs-ink-bar {
              background-color: @themeColor !important;
            }
          }
        }
      }
    }
  }
}
