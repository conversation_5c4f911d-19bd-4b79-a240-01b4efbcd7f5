import React, { useContext } from "react";
import { IContext, LayoutProps } from "ssr-types-react";
import App from "./App";
import { useStoreContext } from "ssr-common-utils";
import { useLayoutSeoSetting } from "@/hooks/useLayoutSeoSetting";

const Layout = (props: LayoutProps) => {
  // 注：Layout 只会在服务端被渲染，不要在此运行客户端有关逻辑
  const { injectState } = props;
  const { injectCss, injectScript } = props.staticList!;
  const { state } = useContext<IContext>(useStoreContext());
  const patternSeo = useLayoutSeoSetting(state).execute()

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=0, maximum-scale=1,user-scalable=yes,shrink-to-fit=no" />
        <meta name="theme-color" content="#000000" />
        <meta name="keywords" content={patternSeo?.keyword || '' } />
        <meta name="description" content={patternSeo?.description || ''} />
        <title>{patternSeo?.title || ''}</title>
        {injectCss}
      </head>
      <body>
        <div id="app">
          <App {...props} />
        </div>
        {injectState}
        {injectScript}
      </body>
    </html>
  );
};

export default Layout;
