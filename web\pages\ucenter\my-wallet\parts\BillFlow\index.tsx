import { DatePicker, Form, Table } from "antd";
import RROperation from "@/components/Crud/RROperation";
import React, { useEffect } from "react";
import CRUD from "@/components/Crud/crud";
import { ColumnsType } from "antd/es/table";
import { price2Thousand } from "@/utils/price-format.util";
import MyCrudPagination from "@/components/Crud/Pagination";
import style from "./index.module.less";
import { IBillFlowType } from "@/typings/wallet.interface";
import walletConstants from "@/constants/wallet";
import CrudOperation from "@/components/Crud/CrudOperation";

export default function BillFlowIndex() {
  const [searchForm] = Form.useForm();
  const RangePicker: any = DatePicker.RangePicker;
  const crud = CRUD({ url: "/api/ucenter/my-wallet/bill-flow", pageSize: 10 });
  const columnsOptions: ColumnsType<IBillFlowType> = [
    {
      title: "序号",
      width: 55,
      align: "center",
      render: (text, record, index) => <span>{index + 1}</span>,
    },
    {
      title: "收支类型",
      width: 160,
      align: "center",
      key: "serviceType",
      render: (text, record, index) => <span className="blue-tip">{walletConstants.WALLET_SERVICE_TYPE[record.serviceType]}</span>,
    },
    {
      title: "交易金额",
      width: 130,
      align: "center",
      key: "money",
      dataIndex: "money",
      render: (text, record, index) => <span className="red-tip">{price2Thousand(record.money)}</span>,
    },
    {
      title: "当前余额",
      width: 130,
      align: "center",
      key: "balance",
      dataIndex: "balance",
      render: (text, record, index) => <span className="red-tip">￥{price2Thousand(record.balance)}</span>,
    },
    {
      title: "流水详情",
      align: "left",
      className: "wall-flow-description",
      key: "detail",
      dataIndex: "detail",
      ellipsis: true,
    },
    {
      title: "发生时间",
      width: 150,
      align: "center",
      key: "detail",
      dataIndex: "createdDate",
      ellipsis: true,
    },
  ];

  useEffect(() => {
    crud.refresh();
  }, []);

  return (
    <div className={style.wrapper}>
      <div className="search-bar">
        {crud.getSearchToggle() ? (
          <Form layout="inline" form={searchForm} size="small">
            <Form.Item className="date-range-picker" label="创建时间" name="createdDate">
              <RangePicker size="middle" format={"YYYY-MM-DD"} placeholder={["起始时间", "结束时间"]} />
            </Form.Item>
            <RROperation size="middle" crudInstance={crud} />
          </Form>
        ) : null}
      </div>
      <CrudOperation crudInstance={crud} />
      <Table rowClassName="item-custom-table" rowKey={"id"} size="small" dataSource={crud.tableData} columns={columnsOptions} pagination={false} />
      <div className="content-container-pagination">
        <MyCrudPagination crudInstance={crud} />
      </div>
    </div>
  );
}
