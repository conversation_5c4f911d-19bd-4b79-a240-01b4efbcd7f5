@formItemLabelWidth: 118px;
@btn-normal-color: #f5222d;
@btn-hover-color: #ff4d4f;

.wrapper {
  :global {
    .register {
      .flex-col(center, center);
      color: #303030;
      .register-title {
        color: #303030;
        font-size: 30px;
        font-weight: 400;
        padding: 24px 0 18px;
      }
      .register-tabs {
        .ant-tabs-nav-wrap {
          .flex-center();
        }
        // 重写antd-tabs内部样式
        .ant-tabs-nav::before {
          border-bottom: none;
        }
        .ant-tabs-nav-list {
          .ant-tabs-tab + .ant-tabs-tab {
            margin: 0 0 0 10px;
          }

          .ant-tabs-tab {
            padding: 2px 0;
            font-size: 16px;
          }
        }
      }
      .register-form {
        box-sizing: border-box;
        .ant-form-item {
          margin-bottom: 14px;
        }
        // input-group-连接样式
        .ant-input-group-compact {
          .ant-form-item {
            margin-bottom: 8px;
          }
        }
        // 控制文本宽度和内容宽度
        .ant-form-item-row {
          .ant-form-item-label {
            width: @formItemLabelWidth;
          }
          .ant-form-item-control {
            width: 370px;
          }
        }
        // 图像验证码
        .captcha-code {
          margin-bottom: 0;
          &-img {
            cursor: pointer;
          }
        }
        // 短信验证码
        .sms-code {
          .sms-btn {
            font-size: 14px;
            color: #6c6c6c;
            padding: 0;
            cursor: pointer;
          }
          .ant-btn-text:hover,
          .ant-btn-text:focus {
            color: rgba(0, 0, 0, 0.85);
            background: none;
            border-color: transparent;
          }
        }
        // 业务员选择-绑定
        .customer-principal {
          margin-bottom: 10px;
        }

        // 协议
        .register-agreement {
          margin-left: @formItemLabelWidth;
          margin-bottom: 24px;
          .ant-form-item-control-input {
            min-height: 24px;
          }
          .agreement-teams {
            color: #666;
            span {
              cursor: pointer;
              &:hover {
                color: red;
              }
            }
          }
        }

        .company-address-label {
          &::before {
            display: inline-block;
            margin-right: 4px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: "*";
          }
        }
        // 提交
        .register-submit {
          margin-left: @formItemLabelWidth;
          .ant-form-item-row {
            justify-content: flex-end;
          }
          &-btn {
            .flex-row(center, center);
            width: 100%;
            background-color: @btn-normal-color;
            color: #fff;
            outline: none;
            border-color: @btn-normal-color;
            &:hover {
              border-color: @btn-hover-color;
              background-color: @btn-hover-color;
            }
          }
          .tool-wrap {
            margin-top: 4px;
            text-align: right;
            font-size: 12px;
          }
        }
      }
    }
  }
}
