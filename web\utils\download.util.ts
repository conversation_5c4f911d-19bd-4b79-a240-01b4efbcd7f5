import { saveAs } from "file-saver";
import axios from "axios";
import { message } from "antd";

/**
 * 从oss公有下载附件
 *
 * @param filepathFull oss完整路径
 * @param saveFilename 保存的名称带上后缀
 */
export function downloadFromOssFilepath(filepathFull: string | undefined, saveFilename: string): void {
  if (!filepathFull) {
    message.warning("下载路径地址不能为空！");
  } else {
    filepathFull = filepathFull.replace(/%/g, "%25");
    axios({
      url: `${filepathFull}`,
      method: "get",
      responseType: "blob",
      withCredentials: true,
    })
      .then(res => {
        const blob = new Blob([res.data]);
        filepathFull && saveAs(blob, saveFilename);
      })
      .catch(err => {
        console.error("获取文件报错：", err);
        window.open(filepathFull);
      });
  }
}
