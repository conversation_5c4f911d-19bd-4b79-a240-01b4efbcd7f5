import React, { forwardRef, useImperativeHandle, useState, memo } from "react";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { message, Modal } from "antd";
import ModifyPasswordForm from "@/components/ModifyPasswordForm";
import style from './index.module.less';

interface Props {
  name?: string;
}

const ModifyPasswordFormModal = forwardRef((prop: Props, ref?: any) => {
  const [visible, setVisible] = useState(false);

  const setVisibleFunc = async (value: boolean) => {
    await setVisible(value);
  };

  /** 对外暴露Modal激活、关闭方法 */
  useImperativeHandle(ref, () => {
    return { setVisibleFunc };
  });

  const close = (a?: any, b?: any) => {
    setVisible(false);
  };

  const handleSettingSuccess = async () => {
    await message.success('恭喜，新密码设置成功~')
  }

  return (
    <Modal
      centered={true}
      maskClosable={false}
      title={<span><ExclamationCircleOutlined style={{ color: 'red' }}/>&nbsp;账户安全提醒</span>}
      width={480}
      open={visible}
      onCancel={close}
      footer={null}
      wrapClassName="modal-wrap"
      okText="提交"
      bodyStyle={{ padding: "0" }}
      className={style.wrapper}
    >
      <div className="modify-password-form">
        <ModifyPasswordForm title={<span>尊敬的用户，您当前账户使用的是初始密码。为了保障您账户安全，请设置新密码！</span>} success={handleSettingSuccess} />
      </div>
    </Modal>
  );
});

ModifyPasswordFormModal.defaultProps = {
  name: '客户密码修改弹窗'
};

export default memo(ModifyPasswordFormModal);
