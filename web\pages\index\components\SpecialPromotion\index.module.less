.wrapper {
  :global {
    .special-promotion {
      margin-top: @plate-margin-top;
      background-color: @main-bg-color-white;
      padding: 32px;
      .special-promotion-container {
        .flex-row(space-between, flex-start);
        width: 100%;
        height: 334px;
        margin-top: 16px;
        .special-promotion-layout {
          display: flex;
          gap: 4px;

          .products {
            _:-ms-fullscreen,
            & {
              margin-left: 4px;
            }
            li {
              width: 348px;
              margin-bottom: 4px;
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}
