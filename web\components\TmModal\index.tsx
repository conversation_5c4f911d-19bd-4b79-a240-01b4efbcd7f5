import React, { ReactNode } from "react";
import { Empty, Modal, ModalProps } from "antd";
import style from "./index.module.less";

interface TmModalProps extends ModalProps {
  content: ReactNode;
}

/** 自定义-弹窗 */
export default function TmModal(props: TmModalProps) {
  return (
    <div className={style.wrapper}>
      <Modal {...props} wrapClassName="tm-modal" getContainer={false}>
        {props.content || <Empty />}
      </Modal>
    </div>
  );
}
