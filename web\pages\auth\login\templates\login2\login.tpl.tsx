import loginBackground from "@/pages/auth/login/components/login2/images/login-bg.jpg";
import { Button, Checkbox, Form, Input } from "antd";
import { LockFilled, UserOutlined } from "@ant-design/icons";
import React from "react";
import LoginModules from "./index.module.less";
import { DebouncedFunc } from "lodash";

interface Props {
  rules: any;
  defaultForm: object;
  onFinish: DebouncedFunc<(values: any) => Promise<void>>;
  onFinishFailed: (errorInfo: any) => void;
}

function LoginTpl(props: Props) {
  const { rules, defaultForm, onFinish, onFinishFailed } = props;
  const [form] = Form.useForm();
  return (
    <div className={`${LoginModules.login} login`}>
      <div className="login-box">
        <div className="login-content">
          <div className="left-grid">
            <header className="login-header">
              <div className="login-header-desc">
                <img
                  src={"/logo.svg"}
                  style={{
                    width: "60px",
                    height: "60px",
                    marginRight: "10px",
                    border: "1px solid #d5d5d5",
                    borderRadius: "6px",
                  }}
                  alt=""
                />
                光华易购商城
              </div>
            </header>
            <img src={loginBackground} alt="" />
          </div>
          <div className="right-grid">
            <h2>登录到您的帐户</h2>
            <Form form={form} initialValues={defaultForm} autoComplete="on" onFinish={onFinish} onFinishFailed={onFinishFailed} className="login-form">
              <Form.Item name="phone" rules={rules.phoneRules}>
                <Input size="large" prefix={<UserOutlined style={{ color: "rgba(0,0,0,.25)" }} />} placeholder="手机号" />
              </Form.Item>
              <Form.Item name="password" rules={rules.passwordRules}>
                <Input.Password size="large" prefix={<LockFilled style={{ color: "rgba(0,0,0,.25)" }} />} placeholder="密码" autoComplete="new-password" />
              </Form.Item>
              <Form.Item name="remember" valuePropName="checked">
                <Checkbox>记住密码</Checkbox>
              </Form.Item>
              <Form.Item>
                <Button size="large" block type="primary" htmlType="submit" className="login-form-button">
                  登录
                </Button>
              </Form.Item>
            </Form>
            <p className="agreement">
              点击登录，即表示您同意我们的 <a href="#">条款和条件！</a>
            </p>
            <p className="register">
              没有帐户？<a href="#">立即注册</a>
            </p>
          </div>
        </div>
      </div>
      <div className="login-footer">
        <p>
          版权所有{" "}
          <a target="_blank" href="/">
            光华商城
          </a>
        </p>
      </div>
    </div>
  );
}
export default LoginTpl;
