export interface IWalletService {
  /** 客户财务-个人存款余额 */
  getWallet: (memberId: string) => Promise<any>;

  /** 客户财务-个人财务流水 */
  getWalletBillFlow: (memberId: string, param: any) => Promise<any>;

  /** 客户财务-个人余额充值订单 */
  getWalletRecharge: (memberId: string, param: any) => Promise<any>;

  /** 充值------创建会员余额充值交易 */
  addTradeRecharge: (data: { memberId: string; price: number }) => Promise<any>;

  /** 取消充值订单 */
  cancelRecharge: (params: { memberId: string; rechargeSn: string }) => Promise<any>;
}
