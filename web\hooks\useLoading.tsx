import { Spin } from "antd";
import React, { useEffect, useState } from "react";
import ReactDOM from "react-dom";

export const useLoading = () => {
  const [top, setTop] = useState<number>(0);
  useEffect(() => {
    setTop(document.documentElement.scrollTop);
  }, []);
  /**
   *
   * @param tip 提示语可输入React.ReactNode
   * @param elementId 指定插入的Id，如个人中心只在内容区loading则配置 elementId="ucenterApp"
   */
  const showLoading = (tip?: React.ReactNode, elementId?: string) => {
    if (!tip) {
      tip = "数据处理中...";
    }
    // 点击事件弹出层显示
    document.documentElement.style.position = "fixed";
    // 给body一个负的top值
    document.documentElement.style.top = -top + "px";
    const dom = document.createElement("div");
    dom.setAttribute("id", "tmLoading");
    if (elementId) {
      document.getElementById(elementId)?.appendChild(dom);
    } else {
      document.body.appendChild(dom);
    }
    ReactDOM.render(<Spin size="large" tip={tip} />, dom);
  };
  const hideLoading = (elementId?: string) => {
    const ele = document.getElementById("tmLoading");
    if (ele) {
      if (elementId) {
        document.getElementById(elementId)?.removeChild(ele);
      } else {
        document.body?.removeChild(ele);
      }
      // 弹出层消失点击事件
      document.documentElement.style.position = "static";
      // 回到我们点击的div位置
      window.scrollTo(0, top);
    }
  };
  return {
    showLoading,
    hideLoading,
  };
};
