import React, { memo, ReactComponentElement, ReactNode } from "react";
import { Carousel } from "antd";
import { IBannerType } from "~/typings/data/spread";
import { CarouselEffect } from "antd/es/carousel";

interface TmBannerProps {
  banners: IBannerType[];
  effect?: CarouselEffect | undefined;
  autoplay?: boolean;
  autoplaySpeed?: number;
  clsName?: string;
  styleObj?: object;
  childClassName?: string;
  childStyleObj?: object;
  arrows?: boolean;
  leftArrow?: ReactNode;
  rightArrow?: ReactComponentElement<any>;
}

export default memo(function TmBanner({ banners, effect = "fade", clsName = "", autoplay = true, autoplaySpeed = 3000, styleObj, childClassName, childStyleObj }: TmBannerProps) {
  return (
    <Carousel effect={effect} autoplaySpeed={autoplaySpeed} autoplay={autoplay} className={clsName} style={styleObj}>
      {banners.map(item => (
        <a key={item.id} href={item.url} target={item.url ? "_blank" : "_self"} className={childClassName} style={childStyleObj}>
          <img src={item.image} alt={item.title ?? ""} />
        </a>
      ))}
    </Carousel>
  );
});
