import React, { useContext } from "react";
import { TrademarkOutlined } from "@ant-design/icons";
import { Menu, Card } from "antd";
import style from "./index.module.less";
import { IContext } from "ssr-types";
import { STORE_CONTEXT } from "_build/create-context";

const AboutNavigation = () => {
  const { state } = useContext<IContext>(STORE_CONTEXT);

  const navItems = [
    {
      label: "关于我们",
      icon: <TrademarkOutlined />,
      key: "about-us",
      children: [
        {
          label: <a href={"/about"}>光华易购</a>,
          key: "about",
        },
        {
          label: <a href={"/about/honor"}>荣誉资质</a>,
          key: "honor",
        },
        {
          label: <a href={"/about/contactus"}>联系我们</a>,
          key: "contactus",
        },
      ],
    },
  ];

  return (
    <Card className={style.wrapper}>
      <Menu className="about-nav" items={navItems} mode={"inline"} selectedKeys={state?.navSelectedKeys} defaultOpenKeys={["about-us", "about"]} />
    </Card>
  );
};

export default AboutNavigation;
