@search-selected-row-color: #fef8f8;

.wrapper {
  :global {
    .search {
      color: @regular-text-color;
      //工具栏
      .tool-wrapper {
        .flex-row(flex-start,center);
        margin-top: @plate-margin-top;
        margin-bottom: @plate-margin-top;
        background-color: @main-bg-color-white;
        border-right: 1px solid #d9d9d9;
        border-left: 1px solid #d9d9d9;
        border-top: 1px solid #d9d9d9;

        > div:not(:last-child) {
          font-size: 14px;
          cursor: pointer;
          display: inline-block;
          text-align: center;
          height: 42px;
          width: 60px;
          line-height: 42px;
          border-right: 1px solid #d9d9d9;
          border-bottom: 1px solid #d9d9d9;
          &:first-child {
            width: 75px;
          }
        }
        .tool-result {
          flex: 1;
          text-align: right;
          font-size: 14px;
          line-height: 42px;
          height: 42px;
          padding-right: 10px;
          > span {
            color: #3497ce;
          }
        }
        .tool-active {
          background-color: #f5f5f5;
          color: #3497ce;
          border-bottom: none;
        }
      }
      // 产品信息
      .product-wrapper {
        .flex-row(flex-start, center);
        background-color: #ffffff;
        padding: 22px;
        .product-img {
          width: 96px;
          height: 96px;
          border: 1px solid #e4e4e4;
        }
        .product-spu {
          .flex-col(center, flex-start);
          gap: 16px;
          padding-left: 38px;
          font-size: 14px;
          font-weight: 350;
          line-height: 1;
          color: #6d7278;
          .product-name {
            font-size: 20px;
            font-weight: 700;
            > a {
              color: @mallThemeColor;
            }
          }
          .product-hazard-type > span {
            color: @mallThemeColor;
            cursor: pointer;
            .danger {
              font-size: 14px;
              color: @mallThemeColor;
              margin-left: 10px;
              &:before {
                content: "";
                display: inline-block;
                margin-right: 5px;
                width: 8px;
                height: 8px;
                background-color: #e02020;
              }
            }
            .hollow-out {
              &:before {
                background-color: #fff;
                border: 1px solid #e02020;
              }
            }
          }
        }
      }
      // 产品列表
      .result-wrapper {
        background: @main-bg-color-white;
        .my-pagination {
          .flex-row(center,center);
        }
        .product-table {
          // 表头
          .ant-table-thead {
            height: 42px;
            font-size: 14px;
            font-weight: 700;
            tr th {
              background-color: #f9e2e2;
              color: #6d7278;
              font-size: 14px;
              font-weight: 700;
              &:before {
                content: none;
              }
            }
          }
          // 表体
          .ant-table-tbody {
            .ant-table-cell {
              font-size: 14px;
              font-weight: 400;
              color: #6d7278;
            }
            .ant-table-cell-row-hover {
              background-color: #fef8f8;
            }
          }
          // 操作
          .table-action {
            text-align: center;
            > span {
              cursor: pointer;
            }
          }
          // 优惠价-操作栏
          .login-discount-price-box {
            .flex-row(center, center);
            .login-discount-price {
              .flex-col(center, flex-start);
              gap: 6px;
              font-size: 14px;
              .product-price-inventory {
                width: 100%;
                display: flex;
                justify-content: space-between;
                color: #e02020;
                position: relative;
                .inventory {
                  text-align: right;
                  color: #6d7278;
                }
                position: relative;
                .indulgence-icon-tip {
                  position: absolute;
                  left: -30px;
                  top: -10px;
                }
              }
            }
          }
          // 优惠价-操作栏-未登录-登录可见
          .need-login-btn {
            width: 136px;
            height: 36px;
            background: linear-gradient(90deg, #ff7474, #ff1b1b);
            color: #ffffff;
            border-radius: 18px;
            font-size: 14px;
            font-weight: 350;
            cursor: pointer;
          }
          .tagging {
            color: @mallThemeColor;
          }
          .product-detail-link {
            &:hover {
              color: #e02020;
              text-decoration: underline;
            }
          }
          .sku-box-wrapper {
            background-color: #fefcf2;
            .ant-descriptions-view {
              border-left: none;
            }
          }
          //sku table
          .product-sku-table {
            .isBuyTag {
              position: absolute;
              left: 0;
              top: 0;
              padding: 0 6px;
              background-color: #ffba00;
              background-color: #ff7474 !important;
              color: #fff;
              font-size: 12px;
              border-bottom-right-radius: 10px;
            }
            // 表头
            .ant-table-thead {
              height: 42px;
              font-size: 14px;
              font-weight: 700;
              tr th {
                background-color: #fefcf2;
                color: #595959;
                font-size: 14px;
                font-weight: 400;
                &:before {
                  content: none;
                }
              }
            }
            // 表体
            .ant-table-tbody {
              .ant-table-cell {
                font-size: 12px;
                color: #6d7278;
                background-color: #fefcf2;
              }
              .ant-table-cell-row-hover {
                background-color: #fefcf2;
              }
              // sku行选择高亮区分
              tr:hover {
                td {
                  background: @search-selected-row-color;
                }
              }
            }
            .product-sku-action-box {
              // 查看产品详情链接
              .product-detail-link {
                color: #e02020;
                text-decoration: underline;
              }
            }
            .sku {
              color: #ea7b79;
            }
            .product-sku-info {
              span {
                display: block;
              }
            }
          }
        }
      }
    }
    .ant-table-tbody > tr > td > .ant-table-wrapper:only-child .ant-table,
    .ant-table-tbody > tr > td > .ant-table-expanded-row-fixed > .ant-table-wrapper:only-child .ant-table {
      /* margin: -16px;*/
      margin: -16px -16px -16px -15px;
    }
    .ant-table .ant-table-expanded-row-fixed {
      position: relative;
      margin: -16px -16px;
      padding: 5px 0;
    }

    // 自定义购物车查库存拓展样式
    .header-expand {
      display: flex;
      z-index: 1;
      position: relative;
      .ant-btn {
        transform: scale(0.86);
      }
      .result-text {
        position: absolute;
        font-size: 10px;
        top: -25px;
        width: 60px;
      }
    }

    .indulgence-icon-tip {
      display: inline-flex;
      width: 32px;
      height: 32px;
      background: url("@@img/discount1.png") 50% 50% no-repeat;
      background-size: 34px auto;
      transform: translateY(6px);
    }
    .danger-icon-tip {
      display: inline-flex;
      width: 18px;
      height: 18px;
      background: url("@@img/hazardous_chemicals.png") 50% 50% no-repeat;
      background-size: 18px 18px;
      transform: translateY(2px);
    }
  }
}
