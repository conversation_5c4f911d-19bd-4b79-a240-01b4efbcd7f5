import React from "react";
import { Button, Form, Space } from "antd";
import { SearchOutlined, SyncOutlined } from "@ant-design/icons";
import { SizeType } from "antd/lib/config-provider/SizeContext";
import style from "./index.module.less";

interface RROperationProps {
  crudInstance: any;
  size?: SizeType;
  showResetBtn?: boolean;
  needIcon?: boolean;
}
// 搜索|重置
const RROperation = (props: RROperationProps) => {
  const crud = props.crudInstance;
  const crudFormRef = Form.useFormInstance();
  const toQueryEvent = async () => {
    await crud.setQueryParams(crudFormRef.getFieldsValue());
    await crud.toQuery();
  };
  const resetQueryEvent = async () => {
    crudFormRef.resetFields();
    await crud.setQueryParams(crudFormRef.getFieldsValue());
    await crud.toQuery();
  };
  return (
    <>
      <Space className={style.rrOperationWrapper}>
        <Button className="quick-btn" type="default" onClick={toQueryEvent} icon={props.needIcon ? <SearchOutlined /> : null} size={props.size}>
          查询
        </Button>
        {props.showResetBtn ? (
          <Button className="quick-btn" type="default" onClick={resetQueryEvent} icon={props.needIcon ? <SyncOutlined /> : null} size={props.size}>
            重置
          </Button>
        ) : null}
      </Space>
    </>
  );
};
RROperation.defaultProps = {
  needIcon: false,
  showResetBtn: true,
  size: "small",
};
export default RROperation;
