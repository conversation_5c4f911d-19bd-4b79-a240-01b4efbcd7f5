@contactusFontSize: 14px;
@contactusTitleFontSize: 16px;
@contactusColor: #666666;
@labelWidth: 48px;
@contactusLiHeight: 40px;

.wrapper {
  :global {
    .contactus-wrapper {
      width: 100%;
      .flex-row();
      flex-shrink: 0;
      .contactus-container {
        width: calc(100% - 202px);
        padding: 32px;
        background-color: @main-bg-color-white;
        font-size: @contactusTitleFontSize;
        color: @contactusColor;

        .article-paragraph {
          width: 100%;
        }

        .contract-us-box {
          width: 100%;
          .flex-row();
          gap: 16px;

          .article-link {
            font-size: @contactusFontSize;
          }

          .contactu-info {
            flex: 1;
            _:-ms-fullscreen,
            & {
              margin-left: 16px;
            }

            li {
              line-height: @contactusLiHeight;
            }

            .contactus-lable {
              display: inline-block;
              width: @labelWidth;
            }
          }

          .contactus-location {
            width: 500px;
            img {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
