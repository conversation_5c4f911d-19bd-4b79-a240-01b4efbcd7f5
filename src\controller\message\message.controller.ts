import { Controller, Get, Inject, Param, Query } from "@midwayjs/decorator";
import { BaseController } from "@/controller/base.controller";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { IMessageService } from "@/service/message/message.service";

@Controller("/message")
export class MessageController extends BaseController {
  @Inject("MessageService")
  messageService: IMessageService;

  @Get("/:memberId")
  async list(@Param("memberId") memberId: string, @Query("date") date: string): Promise<void> {
    const { ctx } = this;
    const res = await this.messageService.getPageList({
      memberId,
      date,
    });
    ctx.logistics = res.data;
    ctx.body = await render<Readable>(ctx, {
      stream: true,
    });
  }
}
