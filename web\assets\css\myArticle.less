/** 帮助中心、关于-模块-文章内容样式 */
@articleBackgroundColor: #ffffff;
@articleTitleColor: #5c89b1; // #5c89b1;
@articleSubTitleColor: #5c89b1; // #5c89b1;
@articleTipColor: #5c89b1;
@articleTextColor: #666666;
@articlePointerColor: #ff5c58;
@articleContentPadding: 16px;
@articleTitleFontSize: 24px;
@articleSubTitleFontSize: 18px;
@articleContentFontSize: 14px;
@articleTipFontSize: 12px;
@articleFlexGap: 16px;
@articleMarginTop: 4px;

.article {
  width: 100%;
  .flex-col(flex-start, flex-start);
  gap: 30px;
  .article-paragraph {
    _:-ms-fullscreen,
    & {
      margin-bottom: 30px;
    }
    .article-title {
      font-size: @articleTitleFontSize;
      color: @articleTitleColor;
    }
    .article-subtitle {
      padding-left: 18px;
      font-size: @articleSubTitleFontSize;
      color: @articleSubTitleColor;
      position: relative;
      &::before {
        content: "";
        width: 8px;
        height: 8px;
        background-color: @articlePointerColor;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 10px;
      }
      // 无提示原点的样式类型
      &.not-pointer-tip {
        padding-left: 0;
        &::before {
          content: unset;
        }
      }
    }
    .article-content {
      color: @articleTextColor;
      margin: 16px 0;
      ul {
        padding-left: @articleContentPadding;
      }
    }
    .article-extant-content {
      padding-left: @articleContentPadding;
      ul li {
        font-size: @articleContentFontSize;
      }
    }
  }
  .article-tip {
    color: @articleTipColor;
    font-size: @articleTipFontSize;
  }
  .article-link {
    color: @articleTitleColor;
  }
  .articl-underline-link {
    text-decoration: underline;
  }
  .article-flex-gap {
    .flex();
    gap: @articleFlexGap;
  }
  .article-top {
    margin-top: @articleMarginTop;
  }
}
