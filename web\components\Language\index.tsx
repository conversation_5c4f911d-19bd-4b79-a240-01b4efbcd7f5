import React, { memo, useContext } from "react";
import { GlobalOutlined } from "@ant-design/icons";
import { Dropdown, message } from "antd";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import style from "./index.module.less";
import { useIntl } from "react-intl";

export default memo(function Language() {
  const langItems = [
    {
      key: "zh",
      label: "简体中文",
    },
    {
      key: "en",
      label: "English",
    },
  ];
  const { dispatch, state } = useContext<IContext>(useStoreContext());
  const { locale } = state;
  const intl = useIntl();
  const t = id => intl.formatMessage({ id }); // 写成传参方式

  const onClick = async ({ key }) => {
    console.log(key, " ", locale);
    if (key === locale) {
      return;
    }

    // 改变状态里的 语言 进行切换
    await dispatch?.({
      type: "CHANGE_LOCALE",
      payload: {
        locale: key,
      },
    });
    message.success(t("lang.changeSuccess"));
  };

  return (
    <Dropdown menu={{ items: langItems, onClick }} disabled>
      <a className={style.language} onClick={e => e.preventDefault()}>
        <GlobalOutlined /> {t("language")}
      </a>
    </Dropdown>
  );
});
