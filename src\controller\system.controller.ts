import { Controller, Get, HttpCode } from "@midwayjs/decorator";
import { render } from "ssr-core-react";
import { Readable } from "stream";
import { BaseController } from "@/controller/base.controller";

@Controller("/")
export class SystemController extends BaseController {
  @Get("/404")
  @HttpCode(200)
  async notFoundPage(): Promise<void> {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/403")
  @HttpCode(200)
  async badRequestPage(): Promise<void> {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/500")
  @HttpCode(200)
  async serviceUnavailable(): Promise<void> {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/error")
  @HttpCode(200)
  async errorSimplePage(): Promise<void> {
    this.ctx.body = await render<Readable>(this.ctx, {
      stream: true,
      mode: "ssr",
    });
  }

  @Get("/healthy")
  @HttpCode(200)
  async monitorHealthy(): Promise<void> {
    const { ctx } = this;
    return ctx.getResponseInstance(ctx).sendSuccess("success");
  }
}
