
export interface qualificationUpdateDto{
  auditorReason: string;
  businessLicense: string;
  chemicalsCertificate: string;
  customerIdentity: number;
  customerPosition: number
  employmentZm: string;
  expiredDate: string;
  field1: string;
  field2: string;
  field3: string;
  hazardousLicense: string;
  idcardBack: string;
  idcardFront: string;
  letterOfAttorney: string;
  noDrugCertificate: string;
  productType: number
  purchaseCertificate: string;
  remark: string;
  safetyProductionLicense: string;
  salesContract: string;
  selectedFields: string;
  id?: number;
}

export interface qualificationAddDto extends Omit<qualificationUpdateDto, 'id'>{
  memberId: string;
}
