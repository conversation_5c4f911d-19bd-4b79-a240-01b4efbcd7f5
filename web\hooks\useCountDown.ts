import { useCallback, useEffect, useRef, useState } from "react";
import { $localStorage } from "@/utils/storage.util";

/** 接受两个参数,一个是时间,一个是到时间后执行的回调 */
export const useCountDown = (initCount: number, callback: Function) => {
  const [count, setCount] = useState(initCount);
  const timer = useRef<NodeJS.Timer>();

  // 设置启动函数
  const start = () => {
    // 设置倒计时时间
    setCount(initCount);
    // 开启定时器
    timer.current = setInterval(() => {
      setCount(count => count - 1);
    }, 1000);
  };
  const callbackFun = useCallback(() => {
    return callback();
  }, [callback]);

  useEffect(() => {
    // 时间为0,执行回调
    if (count === 0) {
      callbackFun();
      clearInterval(timer.current);
    }
  }, [callbackFun, count]);

  // 组件销毁的钩子,避免意外销毁组件,而定时器却没停止
  useEffect(() => {
    return () => {
      clearInterval(timer.current);
    };
  }, []);

  // 重置方法
  const reset = () => {
    setCount(0);
    clearInterval(timer.current);
  };

  return {
    count,
    start,
    reset,
  };
};

export const useCountDownCache = (initCount: number, id: string, callback: Function) => {
  const [count, setCount] = useState(initCount);
  const timer = useRef<NodeJS.Timer>();
  // 设置启动函数
  const start = () => {
    const sendEndTimeTmp = $localStorage.get(`timerStartTime-${id}`);
    if (sendEndTimeTmp) {
      initCount = sendEndTimeTmp;
    } else {
      $localStorage.set(`timerStartTime-${id}`, initCount);
    }
    // 设置倒计时时间
    setCount(initCount);
    // 开启定时器
    timer.current = setInterval(() => {
      setCount(count => {
        const tmp = count - 1;
        $localStorage.set(`timerStartTime-${id}`, tmp);
        return tmp;
      });
    }, 1000);
  };
  useEffect(() => {
    // 时间为0,执行回调
    if (count <= 0) {
      $localStorage.remove(`timerStartTime-${id}`);
      callback();
      clearInterval(timer.current);
    }
  }, [count]);

  // 组件销毁的钩子,避免意外销毁组件,而定时器却没停止
  useEffect(() => {
    return () => {
      clearInterval(timer.current);
    };
  }, []);

  // 重置方法
  const reset = () => {
    setCount(0);
    clearInterval(timer.current);
  };

  return {
    count,
    start,
    reset,
  };
};
