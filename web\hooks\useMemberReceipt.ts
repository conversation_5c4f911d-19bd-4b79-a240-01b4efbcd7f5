import crudReceipt from "@/apis/member/receipt";
import { useEffect, useRef, useState } from "react";
import { IReceiptType } from "@/typings/member.interface";
import { message, Modal, notification } from "antd";
import { useLoading } from "@/hooks/useLoading";

/** tab选项-defaultTab: normal | special */
const defaultTab = "normal";

/** 客户发票操作-hook */
export const useMemberReceipt = () => {
  const [actTabKey, setActTabKey] = useState(defaultTab);
  const [receiptList, setReceiptList] = useState<IReceiptType[]>();
  const [currentReceipt, setCurrentReceipt] = useState<IReceiptType>();
  const loadingHooks = useLoading();
  const ucardRef = useRef<any>();

  useEffect(() => {
    setCurrentReceipt(receiptList?.find(item => (actTabKey === "normal" ? item.receiptType === 1 : item.receiptType === 0)));
  }, [actTabKey, receiptList]);

  /**
   * 1.初始化发送请求
   * 2.保存列表数据
   * 3.根据tab，分解列表数据
   */
  const initReceiptData = async () => {
    loadingHooks.showLoading();
    const [err, res] = await crudReceipt
      .getList()
      .then(res => [null, res])
      .catch(err => [err, null]);
    loadingHooks.hideLoading();
    if (err) {
      notification.warning({ message: "网络异常，获取发票详情数据失败，请重试！" });
      return;
    }
    setReceiptList(res.data);
    setActTabKey(defaultTab);
  };

  /** 设置默认发票 */
  const handleSetDefaultReceipt = e => {
    Modal.confirm({
      content: `确定将 ${actTabKey === "normal" ? "普通发票" : "13%增值税发票"} 设置默认发票项吗？`,
      type: "warning",
      onOk: async () => {
        // 否变是
        console.log("确定，否变是 ", currentReceipt?.isDefault);
        if (currentReceipt?.isDefault === 0) {
          const [err, res] = await crudReceipt
            .setDefaultReceipt(currentReceipt.id)
            .then(res => [null, res])
            .catch(err => [err, null]);
          if (err) {
            return;
          }
          message.success(res.message || "设置成功!");
          await resetLoadReceiptData();
        }
      },
    });
  };

  /** 重置加载发票列表数据 */
  const resetLoadReceiptData = async () => {
    await initReceiptData();
    const _actTabKey = actTabKey;
    ucardRef.current.setActiveTabKey(defaultTab);
    _actTabKey !== defaultTab && ucardRef.current.setActiveTabKey(_actTabKey);
  };

  return {
    ucardRef,
    actTabKey,
    defaultTab,
    setActTabKey,
    currentReceipt,
    initReceiptData,
    resetLoadReceiptData,
    handleSetDefaultReceipt,
  };
};
