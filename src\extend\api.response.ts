import { Context } from "@midwayjs/koa";

interface setResponseData {
  setResponseData: <T>(this: T, data?: any) => Omit<T, "setResponseData">;
}
interface setResponseMessage {
  setMessage: <T>(this: T, message?: string) => Omit<T, "setMessage">;
}
interface setResponseStatusCode {
  setStatusCode: <T>(this: T, httpStatusCode?: number) => Omit<T, "setStatusCode">;
}
interface sendResponse {
  send;
  sendSuccess;
  sendFail;
  notSendSuccess1: <T>(this: T) => Omit<T, "notSendSuccess1">; // 避免重复链式
}

type IApiResponse = setResponseData & setResponseMessage & setResponseStatusCode & sendResponse;

export class ApiResponse implements IApiResponse {
  private static message: string;
  private static httpCode: number = 200;
  private static data: any;
  private ctx: Context;

  constructor(ctx: Context) {
    this.ctx = ctx;
    ApiResponse.data = null;
  }

  setResponseData<T>(this: T, data?: any): Omit<T, "setResponseData"> {
    if (typeof data === "string") {
      ApiResponse.data = { data };
    } else {
      ApiResponse.data = data;
    }
    return this;
  }

  setMessage<T>(this: T, message?: string): Omit<T, "setMessage"> {
    ApiResponse.message = message ?? "message";
    return this;
  }

  setStatusCode<T>(this: T, httpStatusCode?: number): Omit<T, "setStatusCode"> {
    ApiResponse.httpCode = httpStatusCode ?? 200;
    return this;
  }

  send() {
    this.ctx.status = ApiResponse.httpCode;
    this.ctx.body = { message: ApiResponse.message, data: ApiResponse.data, status: this.ctx.status };
  }

  // 快速响应成功
  sendSuccess(message?: string, statusCode?: number) {
    this.ctx.status = statusCode ?? ApiResponse.httpCode ?? 200;
    this.ctx.body = {
      message: message ?? ApiResponse.message ?? "success",
      data: ApiResponse.data,
      status: this.ctx.status,
    };
  }

  // 快速响应失败
  sendFail(message: string, statusCode: number) {
    this.ctx.status = statusCode ?? 400;
    this.ctx.body = {
      message: message ?? ApiResponse.message ?? "fail",
      status: this.ctx.status,
    };
  }

  notSendSuccess1<T>(this: T): Omit<T, "notSendSuccess1"> {
    return this;
  }
}
