/** 订单产品项 */
export interface IOrderProduct {
  createdDate: string;
  deliveryGoodsDate: string; // 	预约发货日期
  id: number; //	主键ID
  modifiedDate: string;
  orderNo: string; // 订单号
  productBrand: string; // 产品品牌
  productDeliveryDay: string; // 产品货期
  productId: number; //	关联产品ID
  productItemNo: string; //	产品供应商货号
  productName: string; //	产品名称
  productNo: string; //	产品官方编号
  productPacking: string; //	包装规格,如:25kg
  productPrice: number; // 产品不含税单价,单位:分
  productQuantity: number; //	产品订购数量
  productSku: string; //产品sku
  productSpec: string; //产品规格
  productTaxPrice: number; //	产品含税单价,单位:分
  productUnit: string; //	产品计量单位,如:KG,G,L
  purchaseStatus: number; //	采购状态:1-无需采购;2-部分采购;3-全部采购
  remark: string; //	备注
  rowNum: number; //	行号
}

/** 订单详情 */
export interface IOrderDetailParams {
  billmaker: string;
  collectionType: number; // 收款方式:11-线上支付;12-信用支付;13-货到付款;14-汇款;15-其它
  costMoney: number; // 实付金额,单位:元
  createdDate: string;
  customerCompany: string;
  customerContactAddress: string; // 客户公司联系地址
  customerContactName: string; // 客户联系人
  customerContactPhone: string; // 客户联系手机
  customerPo: string; //	客户PO号
  exceptionContact: string; // 	异常联系人
  exceptionContactPhone: string; // 	异常联系人电话
  freightFee: number; //	运费,单位:元
  freightType: number; // 运费类型,0-不显示此项;1-产品单价包含运费;2-指定运费
  id: number;
  invoiceWithGoods: number; // 发票是否随货,0-否;1-是
  memberId: number; // 下单客户会员ID
  modifiedDate: string;
  offerSubject: number; // 报价主体,1-金华大;2-光华;
  orderNo: string; // 合同订单号
  orderPlace: string; //	订单制作地,一般填入城市
  orderTitle: string; //	订单标题
  otherMoney: number; //	其它费用,单位:元
  principalId: string; //	订单负责人用户ID
  principalName: string; // 订单负责人
  products: Array<IOrderProduct>;
  remark: string; //	备注
  sourceChannel: number; //	订单来源渠道:1-后台;2-官网;3-小程序;4-app
  taxPriceType: number; //	税价类型,0-不含税单价;1-含税单价;
  totalMoney: number; //	总金额,单位:元
  transportType: string; //	运输方式
  receiptInfo: string; // 发票信息json
  receivingAddressInfo: string; // 收货地址信息json
  orderState: number; // 订单状态
  orderStateSub: number; // 订单子状态
  isPaid: number; // 是否支付
  paidTime: string; // 支付时间，在线付款才有值
  paymentReceivedAmount: number; // 付款金额
  paymentState: number; // 付款状态:0=>未收款;1=>部分付款;2=>全部收款;3=>部分退款;-1=>全部退款
  transaction_id: string; // 第三方支付流水号
  hasLogistics: boolean; //是否有物流信息
  outboundLogisticsDtos: Array<IOrderLogistic>;
}
/** 订单物流信息 */
export interface IOrderLogistic {
  id: number;
  orderNo: string;
  rowNum: number;
  productId: number;
  productNo: string;
  productItemNo: string;
  productSku: string;
  productName: string;
  productSpec: string;
  productBrand: string;
  productPacking: string;
  productUnit: string;
  productPrice: number;
  productTaxPrice: number;
  productQuantity: number;
  productPaymentQuantity: number;
  cancelQuantity: number;
  deliverQuantity: number;
  receiptQuantity: number;
  deliveryGoodsDate: string;
  productDeliveryDay: string;
  purchaseStatus: number;
  remark: string;
  productSkuId: string;
}

/** 订单产品table展示行结果 */
export interface IOrderProductColumnType {
  productSku: string;
  orderNo: string;
  productName: string;
  productSpec: string;
  productPrice: number;
  productTaxPrice: number;
  productQuantity: number;
  remark: string;
  productPacking: string;
  productUnit: string;
  productNo: string;
  cancelQuantity?: number;
  receiptQuantity?: number;
  deliverQuantity?: number;
  rowNum?: number;
}
