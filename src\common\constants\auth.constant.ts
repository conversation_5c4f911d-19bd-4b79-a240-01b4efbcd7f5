interface props {
  AUTH_LOGIN_EXPIRE_DAY: number;
  AUTH_CAPTCHA_CODE_EXPIRE: number;
  AUTH_LOGIN_IGNORE_URL: string[];
  SMS_LOGIN_CODE: string;
  SMS_REGISTER_CODE: string;
  SMS_PASSWORD_RESET_CODE: string;
  SMS_CODE_EXPIRE_TIME: number;
}
const authConstant: props = {
  // 登录有效期,天
  AUTH_LOGIN_EXPIRE_DAY: parseInt(process.env.AUTH_LOGIN_EXPIRE_DAY) || 1,
  // 图形验证码有效期:分钟
  AUTH_CAPTCHA_CODE_EXPIRE: parseInt(process.env.AUTH_CAPTCHA_CODE_EXPIRE) || 30,
  // 登录忽略URL集合
  AUTH_LOGIN_IGNORE_URL: ["/auth/*", "/api/auth/*"],
  SMS_LOGIN_CODE: "sms_login_code",
  SMS_REGISTER_CODE: "sms_register_code",
  SMS_PASSWORD_RESET_CODE: "sms_password_reset_code",
  // 短信有效期 30分钟
  SMS_CODE_EXPIRE_TIME: 60 * 30,
};
export default authConstant;
