import { orderFreightQueryDto } from "~/typings/data/order";
import centerCityDistrictData from "@/data/center-city-district.json";
import { FreightTypeConstant } from "@/common/constants/freight.constant";

interface IDangerCondition {
  startingQuantity: number; // 起步数量
  startingPrice: number; // 起步价
  msg: string; // 提示说明
}

interface IProvinceFreightType {
  level: string; // 级别
  provinces: string[]; // 省份
  freightPerPiece: number; // 运费单价
  centerAreaFreeDangerCondition?: IDangerCondition; // 中心区域
  noCenterAreaFreeDangerCondition?: IDangerCondition; // 非中心区域
  areaExpandCondition?: string[]; // 扩展区域
  needArtificial?: boolean; // 需要通行证
}

export default abstract class FreightCalculate {
  /** 6个基本的省份基本运费配置 */
  private static readonly PROVINCE_FREIGHT_CONFIGS: IProvinceFreightType[] = [
    {
      level: "A", // 省份级别
      provinces: ["广东"], // 级别省份字符串
      freightPerPiece: 20, // 每件的基本运费
      centerAreaFreeDangerCondition: {
        startingQuantity: 10,
        startingPrice: 5000,
        msg: "液体/危险品，广东中心市区，中心区县，满5000 免运费",
      }, // 中心市、区、重点镇免运费的条件，或关系
      noCenterAreaFreeDangerCondition: {
        startingQuantity: 20,
        startingPrice: 10000,
        msg: "液体/危险品，广东中心市区，非中心区县，满10000 免运费",
      }, // 非中心市、区、重点镇免运费的条件，或关系
    },
    {
      level: "B", // 省份级别
      provinces: ["广西", "广西壮族自治", "广西壮族自治区", "湖南", "江西", "福建", "浙江", "安徽", "贵州", "四川", "重庆", "上海", "江苏", "北京", "天津"], // 级别省份字符串
      freightPerPiece: 25, // 每件的基本运费
      centerAreaFreeDangerCondition: {
        startingQuantity: 15,
        startingPrice: 20000,
        msg: "液体/危险品，此区域，满20000 免运费",
      }, // 中心市、区、重点镇免运费的条件，或关系
      noCenterAreaFreeDangerCondition: {
        startingQuantity: 25,
        startingPrice: 30000,
        msg: "液体/危险品，此区域，满30000 免运费",
      }, // 非中心市、区、重点镇免运费的条件，或关系
    },
    {
      level: "C", // 省份级别
      provinces: ["湖北", "云南", "河南"], // 级别省份字符串
      freightPerPiece: 30, // 每件的基本运费
      centerAreaFreeDangerCondition: {
        startingQuantity: 15,
        startingPrice: 20000,
        msg: "液体/危险品，此区域，满20000 免运费",
      }, // 中心市、区、重点镇免运费的条件，或关系
      noCenterAreaFreeDangerCondition: {
        startingQuantity: 25,
        startingPrice: 30000,
        msg: "液体/危险品，此区域，满30000 免运费",
      }, // 非中心市、区、重点镇免运费的条件，或关系
    },
    {
      level: "D", // 省份级别
      provinces: ["河北", "山西", "陕西"], // 级别省份字符串
      freightPerPiece: 40, // 每件的基本运费
      centerAreaFreeDangerCondition: {
        startingQuantity: 20,
        startingPrice: 30000,
        msg: "液体/危险品，此区域，满30000 免运费",
      }, // 中心市、区、重点镇免运费的条件，或关系
      noCenterAreaFreeDangerCondition: {
        startingQuantity: 30,
        startingPrice: 50000,
        msg: "液体/危险品，此区域，满50000 免运费",
      }, // 非中心市、区、重点镇免运费的条件，或关系
    },
    {
      level: "E", // 省份级别
      provinces: ["山东", "青海", "内蒙古", "内蒙古自治", "内蒙古自治区", "甘肃", "辽宁", "吉林", "黑龙江"], // 级别省份字符串
      freightPerPiece: 50, // 每件的基本运费
      centerAreaFreeDangerCondition: {
        startingQuantity: 20,
        startingPrice: 30000,
        msg: "液体/危险品，此区域，满30000 免运费",
      }, // 中心市、区、重点镇免运费的条件，或关系
      noCenterAreaFreeDangerCondition: {
        startingQuantity: 30,
        startingPrice: 50000,
        msg: "液体/危险品，此区域，满50000 免运费",
      }, // 非中心市、区、重点镇免运费的条件，或关系
    },
    {
      level: "F", // 省份级别
      provinces: ["新疆", '新疆维吾尔自治', "新疆维吾尔自治区"], // 级别省份字符串
      freightPerPiece: 60, // 每件的基本运费
      centerAreaFreeDangerCondition: {
        startingQuantity: 30,
        startingPrice: 50000,
        msg: "液体/危险品，此区域，满50000 免运费",
      }, // 中心市、区、重点镇免运费的条件，或关系
      noCenterAreaFreeDangerCondition: {
        startingQuantity: 50,
        startingPrice: 80000,
        msg: "液体/危险品，此区域，满80000 免运费",
      }, // 非中心市、区、重点镇免运费的条件，或关系
    },
    {
      level: "G", // 省份级别
      provinces: [], // 级别省份字符串
      needArtificial: true,
      freightPerPiece: 1,
    },
  ];

  /** 箱数，暂时默认为12瓶为一箱 */
  private static readonly DEFAULT_BOX_PRODUCT_COUNT: number = 12;

  /**
   * 免运费
   *
   * @private
   */
  private static readonly FREE_FREIGHT: number = 0;

  /**
   * 送货上门-费用，默认 0，即送货上门到户附加费
   * @private
   */
  static toHomeFee: number = 0;

  /**
   * 送货上门-卸货的费用-50元
   * @private
   */
  static unloadFee: number = 50;

  /**
   * 初始运费值
   *
   * @private
   */
  private static readonly FREIGHT_INIT: number = 0.01;

  public static executeLogic(criteria: orderFreightQueryDto) {
    let { province, city, district, productTotalNum, productTotalPrice, isDanger, sendType } = criteria;
    let msg = "";
    // 运费需要人工核价, 默认 0.01
    let freight = this.FREIGHT_INIT;
    // 最终计算的运费费用
    let totalFee = this.FREIGHT_INIT;
    // 获取包装产品所需要的箱子
    const needPackingBoxCount = this.getNeedPackingBoxCount(productTotalNum);

    // 省、市后缀处理，区级不处理
    province = province?.replace(/[(省)|(市)|(区)]$/, ""); // 香港区、澳门区 city级: xxx[岛,龙,界]
    city = city?.replace(/[(市)|(州)|(盟)|(区)|(县)]$/, "");

    // 获取当前省份配置的基础运费信息
    const currentFreightOptions = this.getProvinceFreightOptionsByProvince(province);
    // 是否位于中心城市
    const isCenterCity = FreightCalculate.isCenterCity(city);
    // 是否位于区中心 | 县中心
    const isCenterDistrict = FreightCalculate.isCenterDistrict(district);

    // 步骤一、是否包含包邮条件
    if (isDanger) {
      let { freight: _freight, msg: _msg } = this.calculateDangerDiffConditionFee(currentFreightOptions, needPackingBoxCount, productTotalPrice, isCenterCity, isCenterDistrict);
      // 1、若是广州区域需单独处理
      if (city.includes("广州") && freight === this.FREIGHT_INIT && ["越秀区", "海珠区", "荔湾区", "天河区", "白云区", "黄埔区", "番禺区"].includes(district)) {
        _msg = "液体/危险品，广东广州，中心城区，满3000 免运费";
        if (needPackingBoxCount > 10 || productTotalPrice > 3000) {
          _freight = this.FREE_FREIGHT;
        } else {
          productTotalPrice < 3000 && (_freight = 80);
          productTotalPrice < 1000 && (_freight = 150);
        }
      }
      freight = _freight;
      msg = _msg || "液体/危险品";
    } else {
      // 非危化品等类别
      msg = "非液体/非危险品，满1000 免运费";
      if (productTotalPrice >= 1000) {
        freight = 0;
      } else if (productTotalPrice >= 500) {
        freight = 30;
      } else {
        freight = 50;
      }
    }
    // 步骤二、不符合上面条件
    if (freight === this.FREIGHT_INIT) {
      freight = this.calculateNotFreeDiff(currentFreightOptions, needPackingBoxCount, city, district, isCenterCity, isCenterDistrict);
    } else {
      // 符合包邮条件，送货上门和卸货费用置0
      this.toHomeFee = 0;
      this.unloadFee = 0;
    }
    if (freight !== this.FREIGHT_INIT) {
      // 免运费
      freight === this.FREE_FREIGHT && (totalFee = this.FREE_FREIGHT);
      if (sendType === FreightTypeConstant.LOGISTICS_SELF_PICKUP.value) {
        // 自提
        totalFee = freight;
      } else {
        // 送货上门
        this.toHomeFee !== this.FREIGHT_INIT && (totalFee = freight + this.toHomeFee + this.unloadFee);
      }
    }
    return {
      freight: totalFee,
      message: msg || "(非液体/非危险品，满1000 免运费)",
    };
  }

  private static calculateNotFreeDiff(
    currentFreightOptions: IProvinceFreightType,
    needPackingBoxCount: number,
    city: string,
    district: string,
    isCenterCity: boolean,
    isCenterDistrict: boolean
  ): number {
    let freight = needPackingBoxCount * currentFreightOptions.freightPerPiece;
    // 运费 = 产品数量 x 每件运费
    if (currentFreightOptions.needArtificial) {
      // 人工计价
      freight = this.FREIGHT_INIT;
      this.toHomeFee = this.FREIGHT_INIT;
    } else {
      this.toHomeFee = isCenterCity || isCenterDistrict ? 250 : 450;
    }
    // 若是广州区域单独处理
    if (currentFreightOptions.level === "A") {
      // 送货上门的费用
      if (["揭阳", "梅州"].includes(city)) {
        this.toHomeFee = 120;
      } else if (["肇庆", "四会", "惠州", "佛山"].includes(city) || district.includes("顺德")) {
        this.toHomeFee = 300;
      } else if (["增城区", "从化区", "南沙区"].includes(district)) {
        this.toHomeFee = 450;
      } else {
        this.toHomeFee = 200;
      }
    }
    return freight;
  }

  /**
   * 计算危化品运费逻辑
   *
   * @param currentFreightOptions /
   * @param orderQuantity /
   * @param totalPrice /
   * @param isCenterCity /
   * @param isCenterDistrict /
   * @private
   */
  private static calculateDangerDiffConditionFee(currentFreightOptions: IProvinceFreightType, orderQuantity: number, totalPrice: number, isCenterCity: boolean, isCenterDistrict: boolean): any {
    const { level, centerAreaFreeDangerCondition, noCenterAreaFreeDangerCondition } = currentFreightOptions;
    if (level === "G") {
      return {
        freight: this.FREIGHT_INIT,
        msg: "此地区需要人工核算运费，请联系业务员。",
      };
    }
    // 是否中心城市或中心城区
    const isCenterArea = isCenterCity || isCenterDistrict;
    // 中心省市镇-免邮判断
    const isCenterDangerCondition = orderQuantity > centerAreaFreeDangerCondition.startingQuantity || totalPrice > centerAreaFreeDangerCondition.startingPrice;
    // 非中心省市镇-免邮判断
    const isCenterNotDangerCondition = orderQuantity > noCenterAreaFreeDangerCondition.startingQuantity || totalPrice > noCenterAreaFreeDangerCondition.startingPrice;
    let returnObj: {};

    // 中心市区县镇 并且是满足免运费条件
    if (isCenterArea && isCenterDangerCondition) {
      returnObj = {
        freight: FreightCalculate.FREE_FREIGHT,
        msg: centerAreaFreeDangerCondition.msg,
      };
    } else if (isCenterNotDangerCondition) {
      returnObj = {
        freight: FreightCalculate.FREE_FREIGHT,
        msg: noCenterAreaFreeDangerCondition.msg,
      };
    } else {
      returnObj = {
        freight: this.FREIGHT_INIT,
        msg: isCenterArea ? centerAreaFreeDangerCondition.msg : noCenterAreaFreeDangerCondition.msg,
      };
    }
    return returnObj;
  }

  /**
   * 是否为中心城市
   *
   * @param city 不带'市'后缀的城市
   * @protected
   */
  private static isCenterCity(city: string): boolean {
    return centerCityDistrictData.cities.includes(city);
  }

  /**
   * <p>判断是否为重点镇、区域中心</p>
   *
   * @param district /
   * @protected /
   */
  private static isCenterDistrict(district): boolean {
    return centerCityDistrictData.districts.includes(district);
  }

  /**
   * <p>获取包装产品所需要的箱子数量</p>
   *
   * @param productNumber /
   * @private /
   */
  private static getNeedPackingBoxCount(productNumber: number): number {
    return Math.ceil(productNumber / FreightCalculate.DEFAULT_BOX_PRODUCT_COUNT);
  }

  /**
   * <p>根据省份获取对应的基础运费信息</p>
   *
   * @param province
   * @protected
   */
  private static getProvinceFreightOptionsByProvince(province: string): IProvinceFreightType {
    return FreightCalculate.PROVINCE_FREIGHT_CONFIGS.find(item => item.provinces.includes(province)) ?? FreightCalculate.PROVINCE_FREIGHT_CONFIGS.slice(-1)[0];
  }
}
