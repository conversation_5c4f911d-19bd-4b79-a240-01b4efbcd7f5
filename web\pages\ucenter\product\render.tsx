import React, { useEffect } from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import CRUD from "@/components/Crud/crud";
import { DatePicker, Form, Input, Space, Spin, Table } from "antd";
import RROperation from "@/components/Crud/RROperation";
import CrudOperation from "@/components/Crud/CrudOperation";
import { findOrderDescByValue } from "@/constants/order";
import { price2Thousand } from "@/utils/price-format.util";
import MyCrudPagination from "@/components/Crud/Pagination";
import { ColumnsType } from "antd/es/table";
import { parseTime } from "@/utils/date.utils";
import style from "./index.module.less";

export default function UcenterProductIndex(props: SProps) {
  const crud = CRUD({ url: "/api/ucenter/orders/history-products", pageSize: 10 });
  const dataSource = crud.tableData;
  const [searchForm] = Form.useForm();
  const RangePicker: any = DatePicker.RangePicker;

  const columns: ColumnsType<any> = [
    {
      title: "序号",
      width: 50,
      align: "center",
      key: "idx",
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: "产品编号",
      width: 120,
      align: "center",
      key: "productItemNo",
      render: (text, record, index) => (
        <a href={`/product/${record.productNo}`} className="product-item-no">
          {record.productItemNo}
        </a>
      ),
    },
    {
      title: "产品信息",
      width: 220,
      align: "center",
      key: "productName",
      render: (text, record, index) => (
        <a href={`/product/${record.productNo}?sku=${record.productSku}`} className="product-desc">
          {record?.productName}
        </a>
      ),
    },
    {
      title: "包装",
      width: 120,
      align: "center",
      key: "productPacking",
      render: (text, record, index) => <span>{record?.productPacking + `${record?.productUnit ? "/" + record?.productUnit : ""}`}</span>,
    },
    {
      title: "单价",
      width: 120,
      align: "center",
      key: "productTaxPrice",
      render: (text, record, index) => <span className="product-price">{price2Thousand(record.productTaxPrice)}</span>,
    },
    {
      title: "数量",
      width: 80,
      align: "center",
      key: "productQuantity",
      dataIndex: "productQuantity",
    },
    {
      title: "订购日期",
      width: 120,
      align: "center",
      key: "orderCreatedDate",
      render: (text, record, index) => <span>{parseTime(record.orderCreatedDate, "{y}-{m}-{d}")}</span>,
    },
    {
      title: "发货状态",
      width: 100,
      align: "center",
      key: "orderState",
      render: (text, record, index) => <span>{findOrderDescByValue(record?.orderState)}</span>,
    },
    {
      title: "订单编号",
      width: 120,
      align: "center",
      key: "orderNo",
      render: (text, record, index) => (
        <a href={`/ucenter/trade/order/${record?.orderNo}`} className="order-no">
          {record?.orderNo}
        </a>
      ),
    },
  ];

  /** 初始化历史产品表格数据 */
  useEffect(() => {
    crud.refresh();
  }, []);

  return (
    <div className={style.wrapper}>
      <UCenterCard title={"历史产品"} />
      <Space align="center" style={{ marginBottom: 16 }}>
        {crud.getSearchToggle() ? (
          <div className="search-container">
            <Form layout="inline" form={searchForm} size="small">
              <Form.Item className="blurry" label="关键字" name="blurry">
                <Input style={{ width: "300px" }} size="middle" placeholder="产品名称|产品SKU|订单编号" autoComplete="off" allowClear />
              </Form.Item>
              <Form.Item className="date-range-picker" label="下单时间" name="createdDate">
                <RangePicker size="middle" format={"YYYY-MM-DD"} placeholder={["起始日期", "结束时间"]} />
              </Form.Item>
              <RROperation size="middle" crudInstance={crud} />
            </Form>
          </div>
        ) : null}
      </Space>
      <div className="content-container">
        <CrudOperation crudInstance={crud} />
        {/* 内容区域 */}
        <Spin size="default" spinning={crud.loading}>
          <Table rowClassName="item-custom-table" rowKey={"id"} size="small" dataSource={dataSource} columns={columns} pagination={false} />
        </Spin>
        <div className="content-container-pagination">
          <MyCrudPagination crudInstance={crud} />
        </div>
      </div>
    </div>
  );
}
