@formItemLabelWidth: 100px;
@btn-normal-color: #f5222d;
@btn-hover-color: #ff4d4f;

.wrapper {
  :global {
    .password-reset {
      .flex-col(center, center);
      .reset-title {
        color: #303030;
        font-size: 30px;
        font-weight: 400;
        padding: 25px;
      }
      .reset-form {
        box-sizing: border-box;
        // 控制文本宽度和内容宽度
        .ant-form-item-row {
          .ant-form-item-label {
            width: @formItemLabelWidth;
          }
          .ant-form-item-control {
            width: 370px;
          }
        }
        /*submit提交*/
        .form-submit {
          margin-left: @formItemLabelWidth;
          .ant-form-item-row {
            justify-content: flex-end;
          }
          &-btn {
            .flex-col(center, center);
            width: 100%;
            background-color: @btn-normal-color;
            color: #fff;
            outline: none;
            border-color: @btn-normal-color;
            &:hover {
              border-color: @btn-hover-color;
              background-color: @btn-hover-color;
            }
          }
        }
      }
      .forget-password-tabs {
        .ant-tabs-nav-wrap {
          .flex-center();
        }
        // 重写antd-tabs内部样式
        .ant-tabs-nav::before {
          border-bottom: none;
        }
        .ant-tabs-nav-list {
          .ant-tabs-tab + .ant-tabs-tab {
            margin: 0 0 0 10px;
          }

          .ant-tabs-tab {
            padding: 2px 0;
            font-size: 16px;
          }
        }
      }
    }
  }
}
