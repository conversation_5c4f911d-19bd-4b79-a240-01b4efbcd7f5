import { Provide, Scope, <PERSON><PERSON><PERSON><PERSON> } from "@midwayjs/decorator";
import { BaseService } from "@/common/base/base.service";
import { ICreate, IDelete, ISelectedAll, ISelectedOrNot, IUpdate } from "~/typings/data/trade/shopping-cart";
import { IShoppingCartService } from "@/service/trade/shopping-cart.service";

@Provide("ShoppingCartService")
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class shoppingCartServiceImpl extends BaseService implements IShoppingCartService {
  async getList(memberId: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/trade/cart", { member_id: memberId }));
  }

  async create(params: ICreate[]): Promise<any> {
    let reqUrl = "/api/trade/cart";
    if (params[0]?.cartType) {
      reqUrl += `?cartType=${params[0].cartType}`;
    }
    return this.easyResponse(await this.easyHttp.post(reqUrl, params));
  }

  async update(params: IUpdate): Promise<any> {
    return this.easyResponse(await this.easyHttp.put("/api/trade/cart", params));
  }

  async delete(params: IDelete): Promise<any> {
    return this.easyResponse(await this.easyHttp.delete("/api/trade/cart", params));
  }

  async selectedAll(params: ISelectedAll): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/api/trade/cart/select-all", params));
  }

  async getSelectedDetailList(memberId: string, way?: string): Promise<any> {
    return this.easyResponse(await this.easyHttp.get("/api/trade/cart/selected", { member_id: memberId, way }));
  }

  async selectedOrNot(params: ISelectedOrNot): Promise<any> {
    return this.easyResponse(await this.easyHttp.post("/api/trade/cart/select", params));
  }
}
