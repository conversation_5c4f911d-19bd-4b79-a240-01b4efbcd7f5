@borderActiveColor: #e02020;
@borderActiveDefault: #d5d5d5;
@labelColor: #545557;

.wrapper {
}

:global {
  .trade-recharge-modal {
    background: orange;
    .ant-modal-content {
      .ant-modal-body {
        padding: 0;
        // 支付提示
        .payment-tip {
          color: #6d7278;
          font-size: 18px;
          padding: 12px 24px;
        }
        // 表单
        .payment-wrapper {
          padding: 12px !important;
          .ant-descriptions-item-label {
            width: 96px;
            display: flex;
            justify-content: flex-end;
            color: @labelColor;
          }
          .payment-group {
            .flex-row();
            flex-wrap: wrap;
            gap: 12px;
            width: 100%;
            border-color: orange;
            .ant-radio-wrapper {
              padding: 8px;
              border: 1px solid @borderActiveDefault;
              _:-ms-fullscreen,
              & {
                margin-right: 12px;
                &:last-child {
                  margin-right: 0;
                }
              }
            }
            .box-item {
              width: 132px;
              span {
                margin-left: 4px;
              }
              img {
                width: 36px;
                border-radius: 10px;
              }
            }
            .ant-radio-wrapper-checked {
              border-color: @borderActiveColor;
            }
          }
          .btn-wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 12px;
            button {
              -:-ms-fullscreen,
              & {
                margin-right: 16px;
                &:last-child {
                  margin-right: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}
