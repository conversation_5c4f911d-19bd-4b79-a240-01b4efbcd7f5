import request from "@/utils/request.util";
import { qualificationAddDto, qualificationUpdateDto } from "~/typings/data/member/qualification";

export function getMemberQualifications() {
  return request({
    url: `/api/ucenter/qualification`,
    method: "get",
  });
}

export function addNewQualification(data: Partial<qualificationAddDto>) {
  return request({
    url: `/api/ucenter/qualification/`,
    method: "post",
    data,
  });
}

/** 根据id修改资质上传信息 */
export function editQualification(data: Partial<qualificationUpdateDto>) {
  const id = data?.id;

  return request({
    url: `/api/ucenter/qualification/${id}`,
    method: "put",
    data,
  });
}

export default {
  addNewQualification,
  getMemberQualifications,
  editQualification
};
