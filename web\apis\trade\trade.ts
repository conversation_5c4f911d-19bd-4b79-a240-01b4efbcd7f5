import request from "@/utils/request.util";
import { ICreateVo, IPayGatewayVo } from "~/typings/data/trade/trade";

export function create(data: Partial<ICreateVo>) {
  return request({
    url: "/api/trade/create",
    method: "post",
    data,
  });
}

export function edit(data: Partial<ICreateVo>) {
  return request({
    url: "/api/trade/commit",
    method: "post",
    data,
  });
}

export function payGateway(paymentMethod: string, data: IPayGatewayVo) {
  return request({
    url: `/api/payment/cashier/pay/${paymentMethod}`,
    method: "post",
    data,
  });
}

export function quickOrderCreate(data: Partial<ICreateVo>) {
  return request({
    url: "/api/trade/quickOrderCreate",
    method: "post",
    data,
  });
}

export default { create, payGateway, quickOrderCreate };
