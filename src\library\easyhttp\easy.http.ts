import { HttpService } from "@midwayjs/axios";
import { Inject, Logger, Provide } from "@midwayjs/decorator";
import { Context } from "@midwayjs/koa";
import { ILogger } from "@midwayjs/logger";
import { HttpCallable } from "@/library/easyhttp/http.callable";
import { AxiosRequestConfig, AxiosResponse, Method } from "axios";
import { httpError } from "@midwayjs/core";
import { IpUtil } from "@/utils/ip.util";

/**
 * @description 封装http调用以便记录请求日志
 *
 * <AUTHOR>
 * @date 22/5/14
 */
@Provide()
export class EasyHttp extends HttpCallable {
  @Inject()
  httpService: HttpService;

  @Inject()
  ctx: Context;

  @Logger("businessLogger")
  logger: ILogger;

  private static readonly API_AFTER_REQUEST = "API_AFTER_REQUEST";
  private static readonly API_BEFORE_REQUEST = "API_BEFORE_REQUEST";

  private customLogger: ILogger;

  /**
   * 请求封装
   *
   * @param method 'GET,PUT,POST,DELETE'
   * @param url  请求URL
   * @param data 请求参数
   * @param headers 请求头
   */
  async send(method: Method, url: string, data?: object | any, headers?: any): Promise<any> {
    this.customLogger = this.ctx?.getLogger ? this.ctx?.getLogger("businessLogger") : this.logger;
    // 传递IP到接口层
    headers = {
      "X-Forwarded-For": this.ctx?.request ? IpUtil.getClientIP(this.ctx.request) : null,
      ...headers,
    };
    const options: AxiosRequestConfig = {
      url,
      method,
      headers,
    };
    if (["GET", "DELETE"].includes(method.toUpperCase())) {
      options.params = data;
    } else {
      const qs = require("qs");
      const FormData = require("form-data");
      const form = new FormData();
      const fs = require("fs");
      if (headers) {
        switch (headers["Content-Type"]) {
          case "multipart/form-data":
            if (data?.filed && data?.uploadFilepath) {
              form.append(data?.filed, fs.createReadStream(data?.uploadFilepath));
              data?.path && form.append("path", data.path);
            } else {
              data?.forEach((item, key) => {
                form.append(key, item);
              });
            }
            options.data = form;
            break;
          case "application/x-www-form-urlencoded":
            options.data = qs.stringify(data);
            break;
          default:
            options.data = data;
        }
      } else {
        options.data = data;
      }
    }
    this.beforeRequest(options);
    const [err, res] = await this.httpService
      .request(<AxiosResponse>options)
      .then(data => [null, data])
      .catch(err => [err, null]);
    let httpStatusCode = res?.status ?? 0;
    let httpResponseData = res?.data?.content || res?.data?.record || res?.data?.message || res?.data;
    if (res?.data?.content === null || res?.data?.record === null) {
      httpResponseData = null;
    }
    if (res?.data?.pageSize) {
      httpResponseData = res.data;
    }
    let httpResponseMessage = res?.data?.message ?? "default message！";
    if (err) {
      httpStatusCode = err?.response?.status ?? 500;
      if (err?.response?.data?.errorMessage) {
        httpResponseMessage = `参数错误：${JSON.stringify(err?.response?.data?.errorMessage)}`;
      } else {
        httpResponseMessage = err?.response?.data?.message ?? err?.message ?? "请求远程服务失败，请稍候再试！";
      }
      if (err?.response?.status >= 500) {
        this.customLogger.error("%s %j", EasyHttp.API_AFTER_REQUEST, err);
        console.error("请求接口服务失败，请稍候再试！错误信息为：", err, httpStatusCode);
        throw new httpError.InternalServerErrorError("请求远程服务失败，请稍候再试！");
      } else {
        this.customLogger.warn("%s %j", EasyHttp.API_AFTER_REQUEST, err);
      }
    }
    // get请求不记录响应日志,避免日志过大
    method.toUpperCase() !== "GET" && this.afterRequest(httpStatusCode, httpResponseData);
    return {
      statusCode: httpStatusCode,
      message: httpResponseMessage,
      data: httpResponseData,
    };
  }

  private beforeRequest(options: any): void {
    this.customLogger.info("%s %j", EasyHttp.API_BEFORE_REQUEST, options);
  }

  private afterRequest(status: number, content: any): void {
    const req = { status, content };
    this.customLogger.info("%s %j", EasyHttp.API_AFTER_REQUEST, req);
  }
}
