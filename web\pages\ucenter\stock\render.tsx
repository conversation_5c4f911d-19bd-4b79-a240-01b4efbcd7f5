import React from "react";
import { SProps } from "ssr-types-react";
import UCenterCard from "@/pages/ucenter/cnpts/UCenterCard";
import CRUD from "@/components/Crud/crud";
import { Form, Input, message, Space, Spin, Table } from "antd";
import RROperation from "@/components/Crud/RROperation";
import CrudOperation from "@/components/Crud/CrudOperation";
import MyCrudPagination from "@/components/Crud/Pagination";
import { ColumnsType } from "antd/es/table";
import style from "./index.module.less";
import { debounce } from "lodash";
import crudStock from "@/apis/product/stock";

/**
 * 个人中心库存查询 - 光华产品
 * @param props
 * @constructor
 */
export default function UcenterStockIndex(props: SProps) {
  const crud = CRUD({ url: "/api/product/stock/material", pageSize: 20 });
  const dataSource = crud.tableData?.map((item, index) => {
    return {
      ...item,
      index: `${index + 1}`,
    };
  });
  const [searchForm] = Form.useForm();
  // 获取产品库存
  const getProductStock = debounce(async (e, stock) => {
    const queryNum = Number(e);
    if (queryNum < 1) {
      message.warning("查询数量须大于 0！");
      return;
    }
    const [err, res] = await crudStock
      .getSkuStockBySkuId(stock.skuId, queryNum)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (err) {
      message.error(err?.data?.message ?? "网络异常，请重试~");
      return;
    }
    return await message.warning(res.data[stock.skuId]);
  }, 300);
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      width: 55,
      align: "center",
      key: "index",
      dataIndex: "index",
    },
    {
      title: "品牌",
      width: 120,
      align: "center",
      key: "brandName",
      dataIndex: "brandName",
    },
    {
      title: "产品名称",
      width: 160,
      align: "center",
      key: "productName",
      ellipsis: true,
      render: (text, record, index) => (
        <a title={record.mid} href={`/search?keyword=${record.productName}`} className="product-desc">
          {record?.productName}
        </a>
      ),
    },
    {
      title: "物料编码",
      width: 120,
      align: "center",
      key: "sku",
      render: (text, record, index) => (
        <a href={`/search?keyword=${record.sku}`} className="blue-tip">
          {record.sku}
        </a>
      ),
    },
    {
      title: "包装",
      width: 120,
      align: "center",
      key: "packing",
      dataIndex: "packing",
    },
    {
      title: "批次号",
      width: 120,
      align: "center",
      key: "batchCode",
      render: (text, record, index) => <span title={record.id}>{record.batchCode || " - "}</span>,
    },
    {
      title: "操作",
      width: 250,
      align: "center",
      key: "action",
      render: (text, record, index) => (
        <div className="stock-query-box">
          <Input.Search placeholder="请输入查询数量" enterButton={<span>查询</span>} onSearch={async e => await getProductStock(e, record)} />
        </div>
      ),
    },
  ];

  return (
    <div className={style.wrapper}>
      <UCenterCard title={"库存查询"} />
      <Space align="center">
        {crud.getSearchToggle() ? (
          <div className="search-container">
            <Form layout="inline" form={searchForm} size="small">
              <Form.Item className="blurry" name="blurry">
                <Input size="middle" placeholder="请输入产品名称 | SKU" autoComplete="off" allowClear />
              </Form.Item>
              <RROperation showResetBtn={false} size="middle" crudInstance={crud} />
            </Form>
          </div>
        ) : null}
      </Space>
      <div className="content-container">
        <CrudOperation crudInstance={crud} />
        {/* 内容区域 */}
        <Spin size="default" spinning={crud.loading}>
          <Table rowClassName="item-custom-table" size="small" rowKey={"id"} dataSource={dataSource} columns={columns} pagination={false} />
        </Spin>
        {!!dataSource.length && (
          <div className="pagination">
            <MyCrudPagination crudInstance={crud} />
          </div>
        )}
      </div>
    </div>
  );
}
