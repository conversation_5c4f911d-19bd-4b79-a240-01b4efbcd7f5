:global {
  .login {
    background: #fff;
    min-height: 100vh;
    background: linear-gradient(to left, #f5f5f5 50%, #fff 50%);
    padding: 3em 0;

    &-box {
      width: 65%;
      margin: 0 auto;
      .login-content {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;

        .left-grid {
          padding-right: 3em;
          -webkit-flex-basis: 50%;

          .login-header {
            &-desc {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 45px;
              margin: 1em 0 0.5em 0;
              color: #333;
              font-weight: 700;
            }
          }
        }

        .right-grid {
          flex-basis: 50%;
          -webkit-flex-basis: 50%;
          box-sizing: border-box;
          padding: 3em 4em;

          h2 {
            font-size: 24px;
            letter-spacing: 1px;
            text-align: center;
          }

          p {
            padding-bottom: 30px;
            margin: 0;
            color: #666;
            letter-spacing: 1px;
            line-height: 1.8em;
            font-size: 16px;
            font-weight: 400;
          }

          .agreement,
          .register {
            padding-top: 20px;
            padding-bottom: 0;
            line-height: 1.5em;
            font-size: 13px;

            a {
              color: #ff4040;
              font-size: 11px;
              text-decoration: underline;
            }
          }
        }
      }
    }

    &-footer {
      p {
        color: #666;
        text-align: center;
        letter-spacing: 1px;
        font-size: 15px;
        margin-top: 2em;
      }

      p a {
        color: #333;
      }
    }
  }
}
