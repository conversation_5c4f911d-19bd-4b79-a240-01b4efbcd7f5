import React, { memo, useEffect, useRef, useState, useCallback } from "react";
import TmModal from "@/components/TmModal";
import style from "./index.module.less";
import { Button, Col, Form, FormInstance, Input, message, Radio, Row, Spin } from "antd";
import TmAreaCascade from "@/components/TmAreaCascade";
import crudAddress from "@/apis/member/address";
import areaCrud from "@/apis/platform/area";
import { debounce } from "lodash";
import { IReceivingAddressType } from "@/typings/member.interface";
import { useResetFormOnCloseModal } from "@/hooks/useResetFormOnCloseModal";

interface IAddressEditModalProps {
  modalVisible: boolean;
  changeModalVisible: (bool: boolean) => void;
  mode: "edit" | "add";
  addressId: number | undefined;
  callbackFunc?: any;
  companyName?: string;
}

const defaultAddressForm: Partial<IReceivingAddressType> = {
  address: "",
  aliasName: "",
  companyName: "",
  email: "",
  fax: "",
  isDefault: 0,
  label: "",
  nickname: "",
  phone: "",
  telephone: "",
  zipCode: "",
  area: [19, 197, 1880],
};
const defaultLabelArray = ["办公室", "仓库", "实验室"];

export default memo(function AddressEditModal(props: IAddressEditModalProps) {
  const { mode, addressId, modalVisible, changeModalVisible, companyName } = props;

  const isEditMode = mode === "edit";
  const title = isEditMode ? "编辑地址" : "新增地址";
  const validateArea = (rule, value, callback) => {
    if (!value || !Array.isArray(value) || value.length !== 3 || value.some(v => v === null || v === "" || v === undefined)) {
      callback(new Error("请选择省市区"));
    } else {
      callback();
    }
  };
  const rules = {
    username: [
      { required: true, message: "收货人不能为空" },
      { size: 6, message: "收货人名字不能长于6位" },
    ],
    phone: [
      { required: true, message: "手机号不能为空!" },
      { size: 11, message: "请填入11位手机号" },
      { pattern: /^1([3-9][0-9])\d{8}$/, message: "请输入正确格式的手机号" },
    ],
    companyName: [{ required: true, whitespace: true, message: "单位/公司不能为空!" }],
    // 省市区选择器
    area: [
      { required: true, message: "请选择省市区" },
      {
        validator: validateArea,
      },
    ],
    address: [{ required: true, whitespace: true, message: "请输入详细街道、门牌号" }],
    matchAddress: [{ required: true, whitespace: true, message: "请输入详细的收货地址" }],
  };
  /* ======================================= use state start======================================= */
  const [form] = Form.useForm();
  useResetFormOnCloseModal({
    form,
    visible: modalVisible,
  });
  const [loading, setLoading] = useState(false);
  const currentFormRef = useRef<FormInstance>(null);
  const tmAreaCascadeRef = useRef<any>(null);
  const [isMatchAddress, setIsMatchAddress] = useState<boolean>(false);
  /* ======================================= use state end======================================= */

  /* ======================================= 监听|mounted start======================================= */
  useEffect(() => {
    if (modalVisible) {
      form.setFieldsValue({
        ...defaultAddressForm,
        companyName: companyName ?? "",
      });
      mode === "edit" && fetchAddressDetailEvent();
      setIsMatchAddress(mode === "edit");
    }
  }, [modalVisible]);

  /* ======================================= 监听|mounted end======================================= */

  /* ======================================= method start======================================= */
  // 获取地址详情
  const fetchAddressDetailEvent = async () => {
    const [err1, res1] = await crudAddress
      .show(addressId)
      .then(res => [null, res])
      .catch(err => [err, null]);
    if (!err1) {
      const address = res1.data;
      address.area = [address?.provinceId, address?.cityId, address?.districtId];
      form.setFieldsValue({
        ...address,
      });
      tmAreaCascadeRef?.current?.setTargetValFunc(address?.area);
    }
  };
  /** 取消操作 */
  const handleCancel = () => {
    changeModalVisible(false);
  };

  /** 提交-表单校验通过 */
  const handleFinish = async form => {
    setLoading(true);
    // 检查 form.area 是否存在且是数组
    if (form?.area && Array.isArray(form.area)) {
      form.area[0] && (form.provinceId = form.area[0]);
      form.area[1] && (form.cityId = form.area[1]);
      form.area[2] && (form.districtId = form.area[2]);
    }
    let response;
    if (!isEditMode) {
      // 新增操作
      response = await crudAddress
        .add(form)
        .then(res => [null, res])
        .catch(err => [err, null]);
    } else {
      if (!addressId) {
        setLoading(false);
        return;
      }
      response = await crudAddress
        .edit(addressId, form)
        .then(res => [null, res])
        .catch(err => [err, null]);
    }
    setLoading(false);
    const [err, res] = response;
    if (err) {
      message.success(err.data.message || "操作失败");
    } else {
      props?.callbackFunc && props.callbackFunc();
      handleCancel();
      message.success(res.message || "操作成功");
    }
  };

  /** 省市区选择回调 */
  const handleMonitorAreaChange = async e => {
    form.setFieldValue("area", e);
    await form.validateFields(["area"]);
  };

  /** 更换地址标签 */
  const handleSwitchLabel = e => {
    const aliaName = form.getFieldValue("aliasName");
    if (!aliaName || defaultLabelArray.includes(aliaName)) {
      form.setFieldValue("aliasName", e.target.value);
    }
  };

  /** 关键字联想查询 */
  const handleMatchAddress = useCallback(
    debounce(async e => {
      const _address = e.target.value.trim();
      if (!_address) {
        return;
      }
      setLoading(true);
      const [err, res] = await areaCrud
        .getAreaInfo(_address)
        .then(res => [null, res])
        .catch(err => [err, null]);
      if (res && res.status === 200) {
        const area = [res.data?.contactProvinceInfo?.id || null, res.data?.contactCityInfo?.id || null, res.data?.contactDistrictInfo?.id || null];
        form.setFieldsValue({
          area,
          address: res.data?.matchingAddress,
        });
        setIsMatchAddress(true);
        setTimeout(() => {
          tmAreaCascadeRef?.current?.setTargetValFunc(area);
        }, 100);
      }
      setLoading(false);
    }, 300),
    []
  );
  const handleClearArea = () => {
    form.setFieldsValue({
      area: [],
    });
  };
  /* ======================================= method end======================================= */

  const renderFormContent = () => (
    <>
      <Spin className={style.wrapper} spinning={loading}>
        <Form ref={currentFormRef} form={form} scrollToFirstError className="address-form" onFinish={handleFinish} autoComplete="off" size="middle">
          <Row>
            <Col span={12}>
              <Form.Item name="nickname" label="收货人" rules={rules.username}>
                <Input placeholder="请输入收货人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="phone" label="手机号" rules={rules.phone}>
                <Input placeholder="请输入联系手机号码" />
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={12}>
              <Form.Item name="telephone" label="固话">
                <Input placeholder="请输入固定电话" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="email" label="邮箱">
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={12}>
              <Form.Item name="fax" label="传真">
                <Input placeholder="请输入传真号码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="zipCode" label="邮编">
                <Input placeholder="请输入邮政编码" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="companyName" label="收货单位" tooltip="收货单位: 企业请填写公司名称,事业单位请填写单位名称,个人请填写姓名" rules={rules.companyName}>
            <Input placeholder="请输入收货单位/公司" />
          </Form.Item>

          {isMatchAddress ? (
            <>
              <Form.Item name="area" label="所在省市区" rules={rules.area}>
                <TmAreaCascade ref={tmAreaCascadeRef} onMonitorCascadeChange={handleMonitorAreaChange} onClearEvent={handleClearArea} placeholder="省/市/区" />
              </Form.Item>

              <Form.Item name="address" label="详细地址" rules={rules.address}>
                <Input placeholder="请输入详细地址,如xxx街道xxx号" />
              </Form.Item>
            </>
          ) : (
            <Form.Item name="matchAddress" label="收货地址" rules={rules.matchAddress}>
              <Input placeholder="请输入收货地址,如xx省xx市xx区xxx街道xxx号" onBlur={handleMatchAddress} />
            </Form.Item>
          )}

          <Form.Item
            name="aliasName"
            label="地址标签"
            extra={
              <Form.Item label="标签" name="label" className="form-label-box" noStyle={true}>
                <Radio.Group size={"small"} onChange={handleSwitchLabel}>
                  {defaultLabelArray.map((label, index) => (
                    <Radio.Button key={index} value={label}>
                      {label}
                    </Radio.Button>
                  ))}
                </Radio.Group>
              </Form.Item>
            }
          >
            <Input placeholder="输入地址标签" />
          </Form.Item>

          <Form.Item name="isDefault" label="默认收货地">
            <Radio.Group className="tm-radios" buttonStyle="solid">
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item>
            <div
              className="address-btn-box"
              style={{
                display: "flex",
                justifyContent: "center",
                gap: "36px",
              }}
            >
              <Button
                size={"middle"}
                onClick={handleCancel}
                style={{
                  width: "120px",
                  padding: "4px",
                  fontSize: "14px",
                  borderRadius: "4px",
                }}
              >
                取消
              </Button>
              <Button
                size={"middle"}
                danger={isEditMode}
                type="primary"
                htmlType="submit"
                style={{
                  width: "120px",
                  padding: "4px",
                  fontSize: "14px",
                  borderRadius: "4px",
                }}
              >
                {isEditMode ? "修改" : "保存"}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Spin>
    </>
  );
  return modalVisible ? (
    <TmModal
      centered
      maskClosable={false}
      keyboard={!isEditMode}
      title={title}
      width={580}
      open={modalVisible}
      content={renderFormContent()}
      footer={null}
      onOk={() => handleCancel()}
      onCancel={() => handleCancel()}
    />
  ) : null;
});
