import React, { useState, useEffect, useRef, useContext } from "react";
import { Button, Descriptions, Image, Table, message, Spin, InputNumber, Input, Empty, Tag } from "antd";
import { DeleteOutlined, PlusOutlined, HistoryOutlined } from "@ant-design/icons";
import { IContext } from "ssr-types-react";
import { useStoreContext } from "ssr-common-utils";
import searchCrud from "@/apis/search/search";
import { useOss } from "@/hooks/useOss";
import { useAgreementPrice } from "@/hooks/useAgreementPrice";
import { ghmallGuidePrice2Show } from "@/utils/price-format.util";

import ProductSearchModal from "./ProductSearchModal";
import SalesHistoryDialog from "@/components/SalesHistoryDialog";

import style from "./product.module.less";
import Image_Error from "@@img/quick-order/nopic.png";

export default ({ onDataSourceChange, importDataSource }) => {
  const Search = Input.Search;
  const { state } = useContext<IContext>(useStoreContext());
  const [dataSource, setDataSource] = useState<readonly any[]>([]);
  const [productLoading, setProductLoading] = useState<boolean>(false);
  const [tableSelectedKeys, setTableSelectedKeys] = useState<React.Key[] | null>(null);
  const [isAddProduct, setIsAddProduct] = useState<boolean>(false);
  const [historyPriceVisible, setHistoryPriceVisible] = useState<boolean>(false);
  const [currentHistoryProduct, setCurrentHistoryProduct] = useState<any>(null);
  const [searchModalVisible, setSearchModalVisible] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<[]>([]);
  const [formData, setFormData] = useState<any>({
    packingRatio: 1,
    skuName: "",
  });
  const ossService = useOss();
  const agreementPriceHook = useAgreementPrice();

  // 添加数量输入框的 ref
  const quantityInputRef = useRef(null);

  const handleViewHistoryPrice = record => {
    console.log("record", record);
    setCurrentHistoryProduct(record);
    setHistoryPriceVisible(true);
  };

  // 处理数量变化时的协议价格查询
  const handleQuantityChange = async (quantity: number, record: any) => {
    // 只有在用户登录时才查询协议价格
    if (state?.userLoginState && record) {
      try {
        const result = await agreementPriceHook.queryMemberVolumeDiscount(
          quantity,
          "941", // 华大品牌ID
          record.id
        );
        // console.log('协议价格查询结果:', result);

        // 如果查询成功且有协议价格配置，更新产品的折扣价
        if (result && result.orderQuantityConfiguration && result.productSkuDto?.discountPrice) {
          // console.log('更新协议价格:', result.productSkuDto.discountPrice);

          // 更新dataSource中对应产品的价格
          const updatedDataSource = dataSource.map(item =>
            item.rowKey === record.rowKey
              ? { ...item, discountPrice: result.productSkuDto.discountPrice, quantity }
              : item
          );
          setDataSource(updatedDataSource);
        } else {
          // 如果没有协议价格配置，只更新数量
          const updatedDataSource = dataSource.map(item =>
            item.rowKey === record.rowKey
              ? { ...item, quantity }
              : item
          );
          setDataSource(updatedDataSource);
        }
      } catch (error) {
        // console.error("查询协议价格失败:", error);
        // 查询失败时仍然更新数量
        const updatedDataSource = dataSource.map(item =>
          item.rowKey === record.rowKey
            ? { ...item, quantity }
            : item
        );
        setDataSource(updatedDataSource);
      }
    } else {
      // 未登录时只更新数量
      const updatedDataSource = dataSource.map(item =>
        item.rowKey === record.rowKey
          ? { ...item, quantity }
          : item
      );
      setDataSource(updatedDataSource);
    }
  };

  // 华大牌产品代码正则验证
  const isValidProductCode = (code: string) => {
    const productCodeRegex = /^\d\.\d{5}\.\d{3}$/;
    return productCodeRegex.test(code);
  };

  useEffect(() => {
    onDataSourceChange(dataSource);
  }, [dataSource]);

  useEffect(() => {
    if (importDataSource.length > 0) {
      setDataSource(dataSource.concat(importDataSource));
    }
  }, [importDataSource]);

  // 搜索产品
  const handleSearchProduct = async (keyword: string) => {
    const _keyword = keyword.trim();
    if (!_keyword) {
      return;
    }
    setSearchModalVisible(false);
    setProductLoading(true);
    try {
      const params = {
        keyword: _keyword,
        page: 1,
        size: 100,
        brandId: 941,
      };
      const [err, res] = await searchCrud
        .fetchSearchList(params)
        .then(res => [null, res])
        .catch(err => [err, null]);
      console.log("res", res);
      if (err) {
        return [];
      }
      if (res.data.total === 0) {
        message.error("未找到相关产品，请重新输入");
      } else {
        const _searchResults = getProductAllSku(res.data.content);
        console.log("_searchResults", _searchResults);
        if (_searchResults.length > 1) {
          if (isValidProductCode(_keyword)) {
            handleSelectedProduct({ ..._searchResults.find(item => item.sku === _keyword), rowKey: new Date().getTime() });
          } else {
            setSearchResults(_searchResults);
            setSearchModalVisible(true);
          }
        } else {
          handleSelectedProduct({ ..._searchResults[0], rowKey: new Date().getTime() });
        }
      }
    } catch (error) {
      message.error("搜索产品失败");
      return [];
    } finally {
      setProductLoading(false);
    }
  };

  // 处理检索结果
  const getProductAllSku = products => {
    return products.reduce((acc, product) => {
      if (product.skus) {
        const _sku = product.skus.map(sku => ({
          ...sku,
          productNo: product.productNo,
          productImage: product.headImage,
          isDanger: product.isDanger,
          isExplode: product.isExplode,
          isExplosion: product.isExplosion,
          isPoison: product.isPoison,
        }));
        const sortedSku = _sku.sort((a, b) => {
          if ((b.isBuy === 1) !== (a.isBuy === 1)) {
            return (b.isBuy === 1 ? 1 : 0) - (a.isBuy === 1 ? 1 : 0);
          }
          return (a.unit || "").localeCompare(b.unit || "");
        });
        return acc.concat(sortedSku);
      }
      return acc;
    }, []);
  };

  // 处理产品选择
  const handleSelectedProduct = async product => {
    console.log("product", product);
    const initialQuantity = product.packingRatio ? Number(product.packingRatio) : 1;
    product.quantity = initialQuantity;

    // 如果用户已登录，尝试获取初始数量的区间价格
    if (state?.userLoginState && product.id) {
      try {
        const result = await agreementPriceHook.queryMemberVolumeDiscount(
          initialQuantity,
          "941", // 华大品牌ID
          product.id
        );
        console.log('产品选择时的协议价格查询结果:', result);

        // 如果查询成功且有协议价格配置，更新产品的折扣价
        if (result && result.orderQuantityConfiguration && result.productSkuDto?.discountPrice) {
          console.log('产品选择时更新协议价格:', result.productSkuDto.discountPrice);
          product.discountPrice = result.productSkuDto.discountPrice;
        }
      } catch (error) {
        console.error("产品选择时查询协议价格失败:", error);
      }
    }

    setFormData({ ...formData, ...product });
    setSearchModalVisible(false);
    // 使用 setTimeout 确保在 DOM 更新后聚焦
    setTimeout(() => {
      quantityInputRef.current?.focus();
    }, 0);
  };

  const handleAddProduct = async () => {
    let productToAdd = { ...formData };

    // 如果用户已登录，尝试获取区间价格
    if (state?.userLoginState && formData.id && formData.quantity) {
      try {
        const result = await agreementPriceHook.queryMemberVolumeDiscount(
          formData.quantity,
          "941", // 华大品牌ID
          formData.id
        );
        console.log('添加产品时的协议价格查询结果:', result);

        // 如果查询成功且有协议价格配置，更新产品的折扣价
        if (result && result.orderQuantityConfiguration && result.productSkuDto?.discountPrice) {
          console.log('添加产品时更新协议价格:', result.productSkuDto.discountPrice);
          productToAdd.discountPrice = result.productSkuDto.discountPrice;
        }
      } catch (error) {
        console.error("添加产品时查询协议价格失败:", error);
      }
    }

    const updatedDataSource = [...dataSource, productToAdd];
    console.log("updatedDataSource", updatedDataSource);
    console.log("formData", productToAdd);
    setDataSource(updatedDataSource);
    setIsAddProduct(false);
    setFormData({ packingRatio: 1 });
  };

  // 产品信息DOM渲染
  const renderProdcutDOM = item => {
    return (
      <div className={style.productCard}>
        <div className="productImage">
          <Image src={ossService.generateOssFullFilepath(item.productImage)} alt={item.skuName} onError={e => (e.target.src = Image_Error)} />
        </div>
        <div className="productInfo">
          <div className="productInfoHeader">
            {item.isDanger ? (
              <Tag className="productTag" color="#faad14">
                危化品
              </Tag>
            ) : null}
            {item.isExplode ? (
              <Tag className="productTag" color="#cd201f">
                易制爆
              </Tag>
            ) : null}
            {item.isPoison ? (
              <Tag className="productTag" color="#531dab">
                易制毒
              </Tag>
            ) : null}
            {item.isExplosion ? (
              <Tag className="productTag" color="red">
                爆炸品
              </Tag>
            ) : null}
            <Button
              className="productName"
              type="link"
              onClick={() => {
                window.open(`/product/${item.productNo}?sku=${item.sku}`, "_blank");
              }}
            >
              {item.skuName}
            </Button>
          </div>

          <Descriptions className="productDesc" column={2} size="small">
            <Descriptions.Item label="产品代码">{item.sku || "-"}</Descriptions.Item>
            <Descriptions.Item label="规格">{item.packing || "-"}</Descriptions.Item>
            <Descriptions.Item label="包装系数">{item.packingRatio || "-"}</Descriptions.Item>
            <Descriptions.Item label="单位">{item.unit || "-"}</Descriptions.Item>
          </Descriptions>
        </div>
      </div>
    );
  };

  // 库存信息渲染
  const renderStockDOM = ({ existingStock, quantity }) => {
    if (existingStock === 0) {
      return (
        <Tag style={{ fontSize: "12px", marginRight: "0px" }} color="red">
          咨询客服
        </Tag>
      );
    } else if (Number(existingStock) <= Number(quantity)) {
      return (
        <Tag style={{ fontSize: "12px", marginRight: "0px" }} color="orange">
          库存紧张
        </Tag>
      );
    }
    return (
      <Tag style={{ fontSize: "12px", marginRight: "0px" }} color="green">
        有现货
      </Tag>
    );
  };

  // 价格信息渲染
  const renderPriceDOM = record => {
    const items = [
      {
        key: "1",
        label: "价格",
        hidden: false,
        children: ghmallGuidePrice2Show(record?.guidePrice),
      },
      {
        key: "2",
        label: "优惠价",
        hidden: record?.guidePrice === record?.discountPrice,
        children: ghmallGuidePrice2Show(record?.discountPrice),
      },
      {
        key: "3",
        label: "小计",
        hidden: false,
        children: ghmallGuidePrice2Show(record?.discountPrice * record?.quantity),
      },
    ];
    return (
      <>
        <div className={style.priceInfo}>
          {items.map(item => {
            if (item.hidden) return "";
            return (
              <div className="item" key={item.key}>
                <label>{item.label}：</label>
                <span>{item?.children || "-"}</span>
                {item.key === "2" && record.isBuy && (
                  <HistoryOutlined
                    style={{ marginLeft: "5px", cursor: "pointer", color: "#e62129" }}
                    title="查看历史价格"
                    onClick={() => {
                      handleViewHistoryPrice(record);
                    }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </>
    );
  };

  const columns = [
    {
      title: "行号",
      dataIndex: "index",
      width: 50,
      align: "center",
      render: (_, __, index) => index + 1,
    },
    {
      title: "产品",
      dataIndex: "keyword",
      key: "keyword",
      align: "center",
      render: (text, record) => renderProdcutDOM(record),
    },
    {
      title: "库存",
      dataIndex: "stock",
      key: "stock",
      width: 86,
      align: "center",
      render: (text, record) => renderStockDOM(record),
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      align: "center",
      width: 80,
      render: (text, record) => {
        return (
          <div className={style.quantityInput}>
            <InputNumber
              value={record.quantity}
              precision={0}
              min={1}
              keyboard={true}
              step={Number(record.packingRatio) || 1}
              style={{ width: "100%" }}
              onChange={value => {
                if (value) {
                  handleQuantityChange(value, record);
                }
              }}
            />
            {record.packingRatio && <span className="quantityInputText">{(Number(text) / Number(record.packingRatio) || 0).toFixed(2)}件</span>}
          </div>
        );
      },
    },
    {
      title: "价格(CNY)",
      dataIndex: "totalPrice",
      key: "totalPrice",
      align: "right",
      width: 150,
      render: (text, record) => renderPriceDOM(record),
    },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows) => {
      setTableSelectedKeys(selectedRowKeys);
      console.log(`selectedRowKeys: ${selectedRowKeys}`, "selectedRows: ", selectedRows);
    },
  };

  // 删除选择产品
  const handleDeleteProduct = () => {
    const updatedDataSource = [...dataSource];
    // 使用 filter 方法删除选中的产品
    const filteredDataSource = updatedDataSource.filter(item => !tableSelectedKeys.includes(item.rowKey));
    console.log("filteredDataSource", filteredDataSource);
    setDataSource(filteredDataSource);
    setTableSelectedKeys(null);
  };

  // 控制按钮渲染
  const renderControlDOM = () => {
    return (
      <div className={style.controlArea}>
        {tableSelectedKeys && tableSelectedKeys.length > 0 ? (
          <div className="control control-delete" onClick={() => handleDeleteProduct()}>
            <DeleteOutlined />
            <p>删除选择产品</p>
          </div>
        ) : null}
        <div className="control control-add" onClick={() => setIsAddProduct(true)}>
          <PlusOutlined />
          <p>添加订购产品</p>
        </div>
      </div>
    );
  };

  // 添加订购产品DOM渲染
  const renderAddProductDOM = () => {
    return (
      <div className={style.addProductTable}>
        <div className="tableItem">
          <div className="tableHeader">
            <div className="tableHeaderTitle">
              添加订购产品<span>*</span>
            </div>
          </div>
          <div className="tableBody">
            <ProductSearchModal
              visible={searchModalVisible}
              searchResults={searchResults}
              onCancel={() => setSearchModalVisible(false)}
              onSelect={product => {
                handleSelectedProduct(product);
              }}
            >
              <Search
                value={formData.skuName}
                onChange={e => setFormData({ ...formData, skuName: e.target.value })}
                placeholder="输入产品名称/产品代码，按【回车】或点击【搜索】开始搜索"
                onPressEnter={e => {
                  handleSearchProduct(e.target?.value);
                }}
                onSearch={value => {
                  handleSearchProduct(value);
                }}
                enterButton="搜索"
                allowClear
              />
            </ProductSearchModal>
          </div>
        </div>
        <div className="tableItem">
          <div className="tableHeader">
            <div className="tableHeaderTitle">
              数量<span>*</span>
            </div>
          </div>
          <div className="tableBody">
            <InputNumber
              ref={quantityInputRef}
              value={formData.quantity}
              min={1}
              step={Number(formData.packingRatio)}
              onChange={async value => {
                setFormData({ ...formData, quantity: value });

                // 如果用户已登录且有选中的产品，查询区间价格
                if (state?.userLoginState && formData.id && value) {
                  try {
                    const result = await agreementPriceHook.queryMemberVolumeDiscount(
                      value,
                      "941", // 华大品牌ID
                      formData.id
                    );
                    console.log('表单数量变化时的协议价格查询结果:', result);

                    // 如果查询成功且有协议价格配置，更新表单中的折扣价
                    if (result && result.orderQuantityConfiguration && result.productSkuDto?.discountPrice) {
                      console.log('表单中更新协议价格:', result.productSkuDto.discountPrice);
                      setFormData(prev => ({ ...prev, discountPrice: result.productSkuDto.discountPrice }));
                    }
                  } catch (error) {
                    console.error("表单数量变化时查询协议价格失败:", error);
                  }
                }
              }}
              onPressEnter={() => {
                handleAddProduct();
              }}
            />
          </div>
        </div>
        <div className="tableItem">
          <div className="tableHeader"></div>
          <div className="tableBody">
            {formData?.id ? (
              <Button type="primary" onClick={handleAddProduct}>
                添加
              </Button>
            ) : null}
            <Button onClick={() => setIsAddProduct(false)}>取消</Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Spin spinning={productLoading}>
      {isAddProduct ? renderAddProductDOM() : renderControlDOM()}
      <Table rowKey="rowKey" columns={columns} controlled={true} dataSource={dataSource} pagination={false} rowSelection={{ type: "checkbox", ...rowSelection }} />
      {/* 历史售价弹窗 */}
      <SalesHistoryDialog visible={historyPriceVisible} currentProduct={currentHistoryProduct} onClose={() => setHistoryPriceVisible(false)} />
    </Spin>
  );
};
